#!/usr/bin/env python3
"""
权重集成适配器 - Phase 3: 无缝集成现有系统
将新的智能权重管理系统与现有彩票预测系统集成
"""

from typing import Dict, List, Any, Optional, Tuple
import logging
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.config.lottery_config import LotteryConfig

# 导入权重管理组件
from .weight_manager_adapter import WeightManagerAdapter, PerformanceMetrics

# 可选导入（需要numpy）
try:
    from .weight_optimizer import WeightOptimizer, OptimizationResult

    OPTIMIZER_AVAILABLE = True
except ImportError:
    WeightOptimizer = None
    OptimizationResult = None
    OPTIMIZER_AVAILABLE = False

try:
    from .adaptive_weight_learner import AdaptiveWeightLearner, LearningConfig

    LEARNER_AVAILABLE = True
except ImportError:
    AdaptiveWeightLearner = None
    LearningConfig = None
    LEARNER_AVAILABLE = False


class WeightIntegrationAdapter:
    """权重集成适配器 - 统一权重管理接口"""

    def __init__(self, enable_learning: bool = True, enable_optimization: bool = True):
        self.logger = logging.getLogger(__name__)

        # 初始化组件
        self.weight_manager = WeightManagerAdapter()

        # 条件初始化优化器和学习器
        if enable_optimization and OPTIMIZER_AVAILABLE:
            self.weight_optimizer = WeightOptimizer(target_hit_rate=0.4)
        else:
            self.weight_optimizer = None
            if enable_optimization and not OPTIMIZER_AVAILABLE:
                self.logger.warning(
                    "Weight optimizer requested but numpy not available"
                )

        if enable_learning and LEARNER_AVAILABLE:
            self.adaptive_learner = AdaptiveWeightLearner()
        else:
            self.adaptive_learner = None
            if enable_learning and not LEARNER_AVAILABLE:
                self.logger.warning(
                    "Adaptive learner requested but numpy not available"
                )

        # 集成状态
        self.integration_enabled = True
        self.last_optimization_time = 0
        self.optimization_frequency = 10  # 每10次预测优化一次
        self.prediction_count = 0

        # 性能跟踪
        self.performance_buffer = []
        self.weight_history = []

        # 权重类别映射
        self.weight_categories = {
            # 集成权重
            "bayesian": "ensemble",
            "markov1": "ensemble",
            "markov2": "ensemble",
            "frequency": "ensemble",
            "trend": "ensemble",
            "correlation": "ensemble",
            # 模型权重
            "markov": "model",
            "bayes": "model",
            "neural": "model",
            # 特征权重
            "red_odd_even": "feature",
            "red_size": "feature",
            "blue_size": "feature",
            # 损失权重
            "red_odd_even_loss": "loss",
            "red_size_loss": "loss",
            "blue_size_loss": "loss",
            "red_numbers_loss": "loss",
            "blue_numbers_loss": "loss",
        }

        self.logger.info("权重集成适配器初始化完成")

    def get_integrated_weights(
        self, context: str = "prediction"
    ) -> Dict[str, Dict[str, float]]:
        """获取集成权重配置"""
        try:
            # 获取基础权重
            ensemble_weights = self.weight_manager.get_ensemble_weights()
            model_weights = self.weight_manager.get_model_weights()
            feature_weights = self.weight_manager.get_feature_weights()
            loss_weights = self.weight_manager.get_loss_weights()

            # 如果启用自适应学习，应用学习调整
            if self.adaptive_learner and len(self.performance_buffer) > 0:
                all_weights = {
                    **ensemble_weights,
                    **model_weights,
                    **feature_weights,
                    **loss_weights,
                }
                latest_performance = (
                    self.performance_buffer[-1] if self.performance_buffer else 0.0
                )

                # 自适应学习调整
                adjusted_weights = self.adaptive_learner.learn_from_experience(
                    all_weights, latest_performance
                )

                # 分类调整后的权重
                ensemble_weights.update(
                    {k: v for k, v in adjusted_weights.items() if k in ensemble_weights}
                )
                model_weights.update(
                    {k: v for k, v in adjusted_weights.items() if k in model_weights}
                )
                feature_weights.update(
                    {k: v for k, v in adjusted_weights.items() if k in feature_weights}
                )
                loss_weights.update(
                    {k: v for k, v in adjusted_weights.items() if k in loss_weights}
                )

            integrated_weights = {
                "ensemble": ensemble_weights,
                "model": model_weights,
                "feature": feature_weights,
                "loss": loss_weights,
            }

            return integrated_weights

        except Exception as e:
            self.logger.error(f"获取集成权重失败: {e}")
            return self._get_fallback_weights()

    def get_current_weights(self) -> Dict[str, Dict[str, float]]:
        """
        获取当前权重配置 - 验证脚本兼容性方法

        Returns:
            Dict[str, Dict[str, float]]: 当前权重配置
        """
        try:
            return self.weight_manager.get_all_weights()
        except Exception as e:
            self.logger.error(f"获取当前权重失败: {e}")
            return self._get_fallback_weights()

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标

        Returns:
            Dict[str, Any]: 性能指标
        """
        try:
            return self.weight_manager.get_performance_metrics()
        except Exception as e:
            self.logger.error(f"获取性能指标失败: {e}")
            return {"error": str(e)}

    def get_learning_status(self) -> Dict[str, Any]:
        """
        获取学习状态

        Returns:
            Dict[str, Any]: 学习状态
        """
        try:
            if self.adaptive_learner:
                return {
                    "learning_enabled": True,
                    "learning_rate": getattr(
                        self.adaptive_learner, "learning_rate", 0.1
                    ),
                    "exploration_rate": getattr(
                        self.adaptive_learner, "exploration_rate", 0.1
                    ),
                    "total_episodes": getattr(
                        self.adaptive_learner, "total_episodes", 0
                    ),
                }
            else:
                return {
                    "learning_enabled": False,
                    "reason": "Adaptive learner not available",
                }
        except Exception as e:
            self.logger.error(f"获取学习状态失败: {e}")
            return {"error": str(e)}

    def get_optimization_insights(self) -> Dict[str, Any]:
        """
        获取优化建议

        Returns:
            Dict[str, Any]: 优化建议
        """
        try:
            if self.weight_optimizer:
                return {
                    "optimization_enabled": True,
                    "target_hit_rate": getattr(
                        self.weight_optimizer, "target_hit_rate", 0.4
                    ),
                    "last_optimization": self.last_optimization_time,
                    "optimization_frequency": self.optimization_frequency,
                }
            else:
                return {
                    "optimization_enabled": False,
                    "reason": "Weight optimizer not available",
                }
        except Exception as e:
            self.logger.error(f"获取优化建议失败: {e}")
            return {"error": str(e)}

    def provide_performance_feedback(
        self, performance_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        提供性能反馈

        Args:
            performance_data: 性能数据

        Returns:
            Dict[str, Any]: 反馈结果
        """
        try:
            # 更新性能反馈
            result = self.update_performance_feedback(performance_data)

            # 增加预测计数
            self.prediction_count += 1

            # 检查是否需要优化
            if self.prediction_count % self.optimization_frequency == 0:
                self._perform_periodic_optimization()

            return {
                "feedback_processed": True,
                "prediction_count": self.prediction_count,
                "performance_updated": result,
                "next_optimization_in": self.optimization_frequency
                - (self.prediction_count % self.optimization_frequency),
            }
        except Exception as e:
            self.logger.error(f"性能反馈处理失败: {e}")
            return {"feedback_processed": False, "error": str(e)}

    def optimize_weights_for_target(
        self, target_metric: str, current_performance: float
    ) -> Dict[str, Any]:
        """
        针对目标指标优化权重

        Args:
            target_metric: 目标指标名称
            current_performance: 当前性能

        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            if self.weight_optimizer:
                # 执行权重优化
                optimization_result = self.manual_optimize_weights()
                return {
                    "optimization_triggered": True,
                    "target_metric": target_metric,
                    "current_performance": current_performance,
                    "optimization_result": optimization_result,
                }
            else:
                return {
                    "optimization_triggered": False,
                    "reason": "Weight optimizer not available",
                }
        except Exception as e:
            self.logger.error(f"权重优化失败: {e}")
            return {"optimization_triggered": False, "error": str(e)}

    def update_performance_feedback(
        self,
        hit_rate_2_plus_1: float,
        accuracy: float = 0.0,
        additional_metrics: Optional[Dict[str, float]] = None,
    ):
        """更新性能反馈"""
        try:
            # 记录性能
            self.performance_buffer.append(hit_rate_2_plus_1)
            if len(self.performance_buffer) > 100:
                self.performance_buffer.pop(0)

            # 更新权重管理器性能
            metrics = PerformanceMetrics(
                accuracy=accuracy,
                hit_rate_2_plus_1=hit_rate_2_plus_1,
                stability=self._calculate_stability(),
                confidence=self._calculate_confidence(additional_metrics),
            )

            # 为所有权重更新性能
            all_weights = self.get_integrated_weights()
            for category, weights in all_weights.items():
                for weight_name in weights:
                    self.weight_manager.update_weight_performance(
                        weight_name, hit_rate_2_plus_1, accuracy
                    )

            # 增加预测计数
            self.prediction_count += 1

            # 定期优化
            if (
                self.weight_optimizer
                and self.prediction_count % self.optimization_frequency == 0
            ):
                self._perform_periodic_optimization()

            self.logger.debug(f"性能反馈已更新: 2+1命中率={hit_rate_2_plus_1:.3f}")

        except Exception as e:
            self.logger.error(f"更新性能反馈失败: {e}")

    def _calculate_stability(self) -> float:
        """计算性能稳定性"""
        if len(self.performance_buffer) < 3:
            return 0.8  # 默认稳定性

        recent_performances = self.performance_buffer[-10:]
        variance = sum(
            (x - sum(recent_performances) / len(recent_performances)) ** 2
            for x in recent_performances
        ) / len(recent_performances)
        stability = 1.0 / (1.0 + variance)
        return min(1.0, max(0.0, stability))

    def _calculate_confidence(
        self, additional_metrics: Optional[Dict[str, float]]
    ) -> float:
        """计算预测置信度"""
        if not additional_metrics:
            return 0.7  # 默认置信度

        # 基于多个指标计算置信度
        confidence_factors = []

        if "prediction_consistency" in additional_metrics:
            confidence_factors.append(additional_metrics["prediction_consistency"])

        if "model_agreement" in additional_metrics:
            confidence_factors.append(additional_metrics["model_agreement"])

        if confidence_factors:
            return sum(confidence_factors) / len(confidence_factors)
        else:
            return 0.7

    def _perform_periodic_optimization(self):
        """执行定期权重优化"""
        try:
            if not self.weight_optimizer or len(self.performance_buffer) < 10:
                return

            # 获取当前权重
            current_weights = {}
            all_weights = self.get_integrated_weights()
            for category, weights in all_weights.items():
                current_weights.update(weights)

            # 执行优化
            optimization_result = self.weight_optimizer.optimize_for_2_plus_1(
                current_weights,
                self.weight_categories,
                self.performance_buffer[-20:],  # 使用最近20次性能
            )

            if optimization_result.success:
                # 应用优化结果
                self._apply_optimized_weights(optimization_result.optimized_weights)
                self.logger.info(
                    f"权重优化完成，改进: {optimization_result.improvement:.4f}"
                )
            else:
                self.logger.warning("权重优化未产生改进")

        except Exception as e:
            self.logger.error(f"定期优化失败: {e}")

    def _apply_optimized_weights(self, optimized_weights: Dict[str, float]):
        """应用优化后的权重"""
        try:
            for weight_name, weight_value in optimized_weights.items():
                self.weight_manager.weight_manager.set_weight(
                    weight_name, weight_value, save=False
                )

            # 保存配置
            self.weight_manager.weight_manager.save_config()

        except Exception as e:
            self.logger.error(f"应用优化权重失败: {e}")

    def manual_optimize_weights(self, strategy: str = "adaptive_learning"):
        """手动触发权重优化"""
        if not self.weight_optimizer:
            raise ValueError("权重优化器未启用或numpy不可用")

        try:
            # 获取当前权重
            current_weights = {}
            all_weights = self.get_integrated_weights()
            for category, weights in all_weights.items():
                current_weights.update(weights)

            # 执行优化
            result = self.weight_optimizer.optimize_for_2_plus_1(
                current_weights, self.weight_categories, self.performance_buffer
            )

            if result.success:
                self._apply_optimized_weights(result.optimized_weights)
                self.logger.info(f"手动优化完成，改进: {result.improvement:.4f}")

            return result

        except Exception as e:
            self.logger.error(f"手动优化失败: {e}")
            raise

    def get_weight_recommendations(self) -> Dict[str, Any]:
        """获取权重调整建议"""
        try:
            recommendations = {
                "optimizer_suggestions": {},
                "learner_insights": {},
                "integration_status": self.get_integration_status(),
            }

            # 优化器建议
            if self.weight_optimizer and self.performance_buffer:
                current_weights = {}
                all_weights = self.get_integrated_weights()
                for category, weights in all_weights.items():
                    current_weights.update(weights)

                current_performance = (
                    self.performance_buffer[-1] if self.performance_buffer else 0.0
                )
                suggestions = self.weight_optimizer.suggest_weight_adjustments(
                    current_weights, self.weight_categories, current_performance
                )
                recommendations["optimizer_suggestions"] = suggestions

            # 学习器洞察
            if self.adaptive_learner:
                insights = self.adaptive_learner.get_learning_insights()
                recommendations["learner_insights"] = insights

            return recommendations

        except Exception as e:
            self.logger.error(f"获取权重建议失败: {e}")
            return {"error": str(e)}

    def get_integration_status(self) -> Dict[str, Any]:
        """获取集成状态"""
        status = {
            "integration_enabled": self.integration_enabled,
            "components": {
                "weight_manager": self.weight_manager.get_status(),
                "optimizer_enabled": self.weight_optimizer is not None,
                "learner_enabled": self.adaptive_learner is not None,
            },
            "performance_tracking": {
                "prediction_count": self.prediction_count,
                "performance_buffer_size": len(self.performance_buffer),
                "last_optimization": self.last_optimization_time,
                "optimization_frequency": self.optimization_frequency,
            },
        }

        if self.performance_buffer:
            status["performance_tracking"].update(
                {
                    "current_performance": self.performance_buffer[-1],
                    "average_performance": sum(self.performance_buffer)
                    / len(self.performance_buffer),
                    "performance_trend": self._get_performance_trend(),
                }
            )

        return status

    def _get_performance_trend(self) -> str:
        """获取性能趋势"""
        if len(self.performance_buffer) < 5:
            return "insufficient_data"

        recent = self.performance_buffer[-5:]
        earlier = (
            self.performance_buffer[-10:-5]
            if len(self.performance_buffer) >= 10
            else self.performance_buffer[:-5]
        )

        if not earlier:
            return "insufficient_data"

        recent_avg = sum(recent) / len(recent)
        earlier_avg = sum(earlier) / len(earlier)

        if recent_avg > earlier_avg + 0.02:
            return "improving"
        elif recent_avg < earlier_avg - 0.02:
            return "declining"
        else:
            return "stable"

    def _get_fallback_weights(self) -> Dict[str, Dict[str, float]]:
        """获取后备权重配置（从LotteryConfig获取）"""
        return {
            "ensemble": LotteryConfig.ENSEMBLE_WEIGHTS.copy(),
            "model": LotteryConfig.MODEL_WEIGHTS.copy(),
            "feature": LotteryConfig.FEATURE_WEIGHTS.copy(),
            "loss": LotteryConfig.LOSS_WEIGHTS.copy(),
        }

    def save_integration_state(self, filepath: str):
        """保存集成状态"""
        try:
            state_data = {
                "performance_buffer": self.performance_buffer,
                "prediction_count": self.prediction_count,
                "last_optimization_time": self.last_optimization_time,
                "integration_enabled": self.integration_enabled,
            }

            # 保存权重管理器状态
            self.weight_manager.weight_manager.save_config()

            # 保存学习器状态
            if self.adaptive_learner:
                learner_path = filepath.replace(".json", "_learner.json")
                self.adaptive_learner.save_learning_state(learner_path)

            import json

            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"集成状态已保存到 {filepath}")

        except Exception as e:
            self.logger.error(f"保存集成状态失败: {e}")

    def load_integration_state(self, filepath: str):
        """加载集成状态"""
        try:
            import json

            with open(filepath, "r", encoding="utf-8") as f:
                state_data = json.load(f)

            self.performance_buffer = state_data.get("performance_buffer", [])
            self.prediction_count = state_data.get("prediction_count", 0)
            self.last_optimization_time = state_data.get("last_optimization_time", 0)
            self.integration_enabled = state_data.get("integration_enabled", True)

            # 加载学习器状态
            if self.adaptive_learner:
                learner_path = filepath.replace(".json", "_learner.json")
                if Path(learner_path).exists():
                    self.adaptive_learner.load_learning_state(learner_path)

            self.logger.info(f"集成状态已从 {filepath} 加载")

        except Exception as e:
            self.logger.error(f"加载集成状态失败: {e}")


def create_weight_integration_adapter(
    enable_learning: bool = True, enable_optimization: bool = True
) -> WeightIntegrationAdapter:
    """创建权重集成适配器实例"""
    return WeightIntegrationAdapter(enable_learning, enable_optimization)


# 兼容性接口
class LegacyWeightInterface:
    """传统权重接口适配器"""

    def __init__(self, integration_adapter: WeightIntegrationAdapter):
        self.adapter = integration_adapter

    def get_ensemble_config(self) -> Dict[str, float]:
        """获取集成配置（兼容旧接口）"""
        weights = self.adapter.get_integrated_weights()
        return weights.get("ensemble", {})

    def get_model_weights(self) -> Dict[str, float]:
        """获取模型权重（兼容旧接口）"""
        weights = self.adapter.get_integrated_weights()
        return weights.get("model", {})

    def update_performance(self, hit_rate: float):
        """更新性能（简化接口）"""
        self.adapter.update_performance_feedback(hit_rate)


if __name__ == "__main__":
    # 测试权重集成适配器
    adapter = create_weight_integration_adapter()

    print("权重集成适配器测试")
    print("=" * 50)

    # 获取集成权重
    weights = adapter.get_integrated_weights()
    print("集成权重配置:")
    for category, category_weights in weights.items():
        print(f"  {category}: {category_weights}")

    # 模拟性能反馈
    print("\n模拟性能反馈...")
    for i in range(5):
        hit_rate = 0.2 + i * 0.05
        adapter.update_performance_feedback(hit_rate, accuracy=hit_rate * 0.8)
        print(f"  第{i+1}次: 2+1命中率={hit_rate:.3f}")

    # 获取状态
    status = adapter.get_integration_status()
    print(f"\n集成状态:")
    print(f"  预测次数: {status['performance_tracking']['prediction_count']}")
    print(
        f"  当前性能: {status['performance_tracking'].get('current_performance', 0):.3f}"
    )
    print(
        f"  性能趋势: {status['performance_tracking'].get('performance_trend', 'unknown')}"
    )

    # 获取建议
    recommendations = adapter.get_weight_recommendations()
    print(f"\n权重建议:")
    if "learner_insights" in recommendations:
        insights = recommendations["learner_insights"]
        if "recommendations" in insights:
            for rec in insights["recommendations"]:
                print(f"  - {rec}")

    print("\n权重集成适配器测试完成！")
