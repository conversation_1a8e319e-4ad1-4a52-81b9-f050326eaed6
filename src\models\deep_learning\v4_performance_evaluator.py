"""
V4.0 Transformer性能评估器
Performance Evaluator for V4.0 Transformer
评估训练好的模型性能
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
import json
import time
from datetime import datetime
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, "..", "..", "..")
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# TensorFlow导入
try:
    import tensorflow as tf
    from tensorflow import keras
    from sklearn.preprocessing import StandardScaler
    import pickle

    TF_AVAILABLE = True
except ImportError:
    print("[WARN] TensorFlow或sklearn不可用，使用简化模式")
    TF_AVAILABLE = False


class V4PerformanceEvaluator:
    """V4.0 Transformer性能评估器"""

    def __init__(self):
        self.project_root = Path(project_root)
        self.data_path = self.project_root / "data" / "raw" / "dlt_data.csv"
        self.models_dir = self.project_root / "models" / "v4_trained"

        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # 加载最新模型
        self.model = None
        self.scaler = None
        self.load_latest_model()

    def load_latest_model(self):
        """加载最新训练的模型"""
        if not TF_AVAILABLE:
            print("[ERROR] TensorFlow不可用，无法加载模型")
            return False

        try:
            # 查找最新的模型文件 - 包括所有版本
            model_files = (
                list(self.models_dir.glob("v4_optimized_*.h5"))
                + list(self.models_dir.glob("v4_improved_*.h5"))
                + list(self.models_dir.glob("v4_final_*.h5"))
            )
            if not model_files:
                print("[ERROR] 未找到训练好的模型文件")
                return False

            latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
            print(f"[INFO] 加载模型: {latest_model}")

            # 加载模型
            self.model = keras.models.load_model(str(latest_model))

            # 查找对应的scaler文件
            if "improved" in latest_model.stem:
                model_time = latest_model.stem.split("_")[-1]
                scaler_files = list(
                    self.models_dir.glob(f"scaler_improved_{model_time}.pkl")
                )
            elif "final" in latest_model.stem:
                model_time = latest_model.stem.split("_")[-1]
                scaler_files = list(
                    self.models_dir.glob(f"scaler_final_{model_time}.pkl")
                )
            else:
                model_time = latest_model.stem.split("_")[-1]
                scaler_files = list(self.models_dir.glob(f"scaler_{model_time}.pkl"))

            if scaler_files:
                scaler_path = scaler_files[0]
                with open(scaler_path, "rb") as f:
                    self.scaler = pickle.load(f)
                print(f"[INFO] 加载标准化器: {scaler_path}")
            else:
                print("[WARN] 未找到对应的标准化器文件")

            print("[OK] 模型加载成功")
            return True

        except Exception as e:
            print(f"[ERROR] 模型加载失败: {e}")
            return False

    def load_and_prepare_data(self) -> pd.DataFrame:
        """加载并准备评估数据"""
        print("[INFO] 加载评估数据...")

        # 读取数据
        df = pd.read_csv(self.data_path, header=None)
        df.columns = [
            "期号",
            "红球1",
            "红球2",
            "红球3",
            "红球4",
            "红球5",
            "蓝球1",
            "蓝球2",
            "日期",
        ]

        # 数据类型转换
        numeric_cols = [
            "期号",
            "红球1",
            "红球2",
            "红球3",
            "红球4",
            "红球5",
            "蓝球1",
            "蓝球2",
        ]
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors="coerce")

        # 删除空值
        df = df.dropna().reset_index(drop=True)

        # 特征工程
        df = self._enhanced_feature_engineering(df)

        print(f"[OK] 数据准备完成: {len(df)} 期")
        return df

    def _enhanced_feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """增强特征工程 - 与最终优化器保持一致"""
        red_balls = ["红球1", "红球2", "红球3", "红球4", "红球5"]
        blue_balls = ["蓝球1", "蓝球2"]

        # 基础比例特征
        df["红球奇数个数"] = df[red_balls].apply(
            lambda row: sum(x % 2 == 1 for x in row), axis=1
        )
        df["红球大数个数"] = df[red_balls].apply(
            lambda row: sum(x > 17 for x in row), axis=1
        )
        df["蓝球大数个数"] = df[blue_balls].apply(
            lambda row: sum(x > 6 for x in row), axis=1
        )

        # 统计特征
        df["红球和值"] = df[red_balls].sum(axis=1)
        df["红球均值"] = df[red_balls].mean(axis=1)
        df["红球方差"] = df[red_balls].var(axis=1)
        df["红球跨度"] = df[red_balls].max(axis=1) - df[red_balls].min(axis=1)
        df["红球标准差"] = df[red_balls].std(axis=1)

        df["蓝球和值"] = df[blue_balls].sum(axis=1)
        df["蓝球跨度"] = df[blue_balls].max(axis=1) - df[blue_balls].min(axis=1)

        # 区间分布特征
        for i, (start, end) in enumerate(
            [(1, 7), (8, 14), (15, 21), (22, 28), (29, 35)]
        ):
            df[f"红球区间{i+1}个数"] = df[red_balls].apply(
                lambda row: sum(start <= x <= end for x in row), axis=1
            )

        # 连号特征
        df["红球连号个数"] = df[red_balls].apply(self._count_consecutive, axis=1)
        df["蓝球连号个数"] = df[blue_balls].apply(self._count_consecutive, axis=1)

        # 增强历史趋势特征
        for window in [3, 5, 10]:
            for col in ["红球奇数个数", "红球大数个数", "蓝球大数个数"]:
                df[f"{col}_趋势{window}"] = (
                    df[col].rolling(window=window, min_periods=1).mean()
                )
                df[f"{col}_波动{window}"] = (
                    df[col].rolling(window=window, min_periods=1).std().fillna(0)
                )

        # 新增特征：周期性特征
        df["红球奇偶平衡度"] = df["红球奇数个数"].apply(lambda x: abs(x - 2.5))
        df["红球大小平衡度"] = df["红球大数个数"].apply(lambda x: abs(x - 2.5))

        # 新增特征：号码分布特征
        df["红球最大值"] = df[red_balls].max(axis=1)
        df["红球最小值"] = df[red_balls].min(axis=1)
        df["红球中位数"] = df[red_balls].median(axis=1)

        return df

    def _count_consecutive(self, row) -> int:
        """计算连号个数"""
        sorted_nums = sorted(row)
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i + 1] - sorted_nums[i] == 1:
                consecutive_count += 1
        return consecutive_count

    def prepare_evaluation_sequences(
        self, df: pd.DataFrame, test_periods: int = 100
    ) -> Tuple[np.ndarray, Dict[str, np.ndarray], List[int]]:
        """准备评估序列数据"""
        print(f"[INFO] 准备评估序列数据，测试期数: {test_periods}")

        sequences = []
        targets = {
            "red_odd_even": [],
            "red_size": [],
            "blue_size": [],
            "red_numbers": [],
            "blue_numbers": [],
        }
        period_numbers = []

        seq_len = 30  # 与最终优化器保持一致

        # 选择所有特征列（除了原始球号和期号、日期）
        feature_cols = [
            col
            for col in df.columns
            if col
            not in [
                "期号",
                "红球1",
                "红球2",
                "红球3",
                "红球4",
                "红球5",
                "蓝球1",
                "蓝球2",
                "日期",
            ]
        ]

        # 确保所有特征列都存在
        available_features = [col for col in feature_cols if col in df.columns]

        # 使用最后test_periods期作为测试数据
        start_idx = len(df) - test_periods

        for i in range(start_idx, len(df)):
            if i < seq_len:
                continue

            # 输入序列特征
            sequence_features = []
            for j in range(i - seq_len, i):
                row_features = [df.iloc[j][col] for col in available_features]
                sequence_features.append(row_features)

            sequences.append(sequence_features)
            period_numbers.append(df.iloc[i]["期号"])

            # 目标值（当前期的实际结果）
            target_row = df.iloc[i]

            # 比例目标
            targets["red_odd_even"].append(target_row["红球奇数个数"])
            targets["red_size"].append(target_row["红球大数个数"])
            targets["blue_size"].append(target_row["蓝球大数个数"])

            # 号码目标
            red_balls = [target_row[f"红球{k}"] for k in range(1, 6)]
            blue_balls = [target_row[f"蓝球{k}"] for k in range(1, 3)]

            red_target = np.zeros(35)
            blue_target = np.zeros(12)

            for ball in red_balls:
                if 1 <= ball <= 35:
                    red_target[int(ball) - 1] = 1
            for ball in blue_balls:
                if 1 <= ball <= 12:
                    blue_target[int(ball) - 1] = 1

            targets["red_numbers"].append(red_target)
            targets["blue_numbers"].append(blue_target)

        if not sequences:
            raise ValueError("无法生成评估序列")

        X = np.array(sequences, dtype=np.float32)

        # 特征标准化（使用训练时的scaler）
        if self.scaler:
            X_reshaped = X.reshape(-1, X.shape[-1])
            X_scaled = self.scaler.transform(X_reshaped)
            X = X_scaled.reshape(X.shape)

        # 转换目标
        y = {}
        for key in targets:
            y[key] = np.array(targets[key])

        print(f"[OK] 评估序列准备完成: {X.shape[0]} 个序列")
        return X, y, period_numbers

    def evaluate_model_performance(
        self, X: np.ndarray, y: Dict[str, np.ndarray], period_numbers: List[int]
    ) -> Dict[str, Any]:
        """评估模型性能"""
        print("\n=== V4.0 Transformer性能评估 ===")

        if not self.model:
            print("[ERROR] 模型未加载")
            return {"success": False, "error": "模型未加载"}

        try:
            # 模型预测
            start_time = time.time()
            predictions = self.model.predict(X, verbose=0)
            prediction_time = time.time() - start_time

            # 解析预测结果
            results = {
                "total_samples": len(X),
                "prediction_time": prediction_time,
                "avg_prediction_time": prediction_time / len(X),
                "accuracy_metrics": {},
                "detailed_results": [],
            }

            # 计算各任务准确率
            for i, period in enumerate(period_numbers):
                period_result = {
                    "period": int(period),
                    "actual": {},
                    "predicted": {},
                    "correct": {},
                }

                # 红球奇偶比
                actual_red_odd = int(y["red_odd_even"][i])
                pred_red_odd = np.argmax(predictions["red_odd_even"][i])
                period_result["actual"]["red_odd_even"] = actual_red_odd
                period_result["predicted"]["red_odd_even"] = pred_red_odd
                period_result["correct"]["red_odd_even"] = (
                    actual_red_odd == pred_red_odd
                )

                # 红球大小比
                actual_red_size = int(y["red_size"][i])
                pred_red_size = np.argmax(predictions["red_size"][i])
                period_result["actual"]["red_size"] = actual_red_size
                period_result["predicted"]["red_size"] = pred_red_size
                period_result["correct"]["red_size"] = actual_red_size == pred_red_size

                # 蓝球大小比
                actual_blue_size = int(y["blue_size"][i])
                pred_blue_size = np.argmax(predictions["blue_size"][i])
                period_result["actual"]["blue_size"] = actual_blue_size
                period_result["predicted"]["blue_size"] = pred_blue_size
                period_result["correct"]["blue_size"] = (
                    actual_blue_size == pred_blue_size
                )

                # 红球号码预测（取概率最高的5个）
                red_probs = predictions["red_numbers"][i]
                pred_red_balls = np.argsort(red_probs)[-5:] + 1  # 转换为1-35
                actual_red_balls = np.where(y["red_numbers"][i] == 1)[0] + 1
                red_hits = len(set(pred_red_balls) & set(actual_red_balls))
                period_result["actual"]["red_balls"] = sorted(actual_red_balls.tolist())
                period_result["predicted"]["red_balls"] = sorted(
                    pred_red_balls.tolist()
                )
                period_result["correct"]["red_ball_hits"] = red_hits

                # 蓝球号码预测（取概率最高的2个）
                blue_probs = predictions["blue_numbers"][i]
                pred_blue_balls = np.argsort(blue_probs)[-2:] + 1  # 转换为1-12
                actual_blue_balls = np.where(y["blue_numbers"][i] == 1)[0] + 1
                blue_hits = len(set(pred_blue_balls) & set(actual_blue_balls))
                period_result["actual"]["blue_balls"] = sorted(
                    actual_blue_balls.tolist()
                )
                period_result["predicted"]["blue_balls"] = sorted(
                    pred_blue_balls.tolist()
                )
                period_result["correct"]["blue_ball_hits"] = blue_hits

                results["detailed_results"].append(period_result)

            # 计算总体准确率
            red_odd_correct = sum(
                1 for r in results["detailed_results"] if r["correct"]["red_odd_even"]
            )
            red_size_correct = sum(
                1 for r in results["detailed_results"] if r["correct"]["red_size"]
            )
            blue_size_correct = sum(
                1 for r in results["detailed_results"] if r["correct"]["blue_size"]
            )

            total_red_hits = sum(
                r["correct"]["red_ball_hits"] for r in results["detailed_results"]
            )
            total_blue_hits = sum(
                r["correct"]["blue_ball_hits"] for r in results["detailed_results"]
            )

            results["accuracy_metrics"] = {
                "red_odd_even_accuracy": red_odd_correct / len(period_numbers),
                "red_size_accuracy": red_size_correct / len(period_numbers),
                "blue_size_accuracy": blue_size_correct / len(period_numbers),
                "red_ball_hit_rate": total_red_hits / (len(period_numbers) * 5),
                "blue_ball_hit_rate": total_blue_hits / (len(period_numbers) * 2),
                "overall_ratio_accuracy": (
                    red_odd_correct + red_size_correct + blue_size_correct
                )
                / (len(period_numbers) * 3),
            }

            results["success"] = True

            # 打印结果
            print(f"[RESULTS] 评估完成，测试样本: {len(period_numbers)} 期")
            print(
                f"  红球奇偶比准确率: {results['accuracy_metrics']['red_odd_even_accuracy']:.2%}"
            )
            print(
                f"  红球大小比准确率: {results['accuracy_metrics']['red_size_accuracy']:.2%}"
            )
            print(
                f"  蓝球大小比准确率: {results['accuracy_metrics']['blue_size_accuracy']:.2%}"
            )
            print(
                f"  红球号码命中率: {results['accuracy_metrics']['red_ball_hit_rate']:.2%}"
            )
            print(
                f"  蓝球号码命中率: {results['accuracy_metrics']['blue_ball_hit_rate']:.2%}"
            )
            print(
                f"  总体比例准确率: {results['accuracy_metrics']['overall_ratio_accuracy']:.2%}"
            )
            print(f"  平均预测时间: {results['avg_prediction_time']:.4f}秒")

            return results

        except Exception as e:
            print(f"[ERROR] 性能评估失败: {e}")
            import traceback

            traceback.print_exc()
            return {"success": False, "error": str(e)}

    def generate_prediction_report(self, results: Dict[str, Any]) -> str:
        """生成预测报告"""
        if not results.get("success"):
            return "评估失败，无法生成报告"

        report_lines = [
            "# V4.0 Transformer 性能评估报告",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 总体性能指标",
            f"- 测试样本数: {results['total_samples']} 期",
            f"- 总预测时间: {results['prediction_time']:.2f} 秒",
            f"- 平均预测时间: {results['avg_prediction_time']:.4f} 秒/期",
            "",
            "## 准确率指标",
            f"- 红球奇偶比准确率: {results['accuracy_metrics']['red_odd_even_accuracy']:.2%}",
            f"- 红球大小比准确率: {results['accuracy_metrics']['red_size_accuracy']:.2%}",
            f"- 蓝球大小比准确率: {results['accuracy_metrics']['blue_size_accuracy']:.2%}",
            f"- 红球号码命中率: {results['accuracy_metrics']['red_ball_hit_rate']:.2%}",
            f"- 蓝球号码命中率: {results['accuracy_metrics']['blue_ball_hit_rate']:.2%}",
            f"- 总体比例准确率: {results['accuracy_metrics']['overall_ratio_accuracy']:.2%}",
            "",
            "## 详细预测结果（最近10期）",
            "| 期号 | 红球奇偶 | 红球大小 | 蓝球大小 | 红球命中 | 蓝球命中 |",
            "|------|----------|----------|----------|----------|----------|",
        ]

        # 显示最近10期的详细结果
        recent_results = results["detailed_results"][-10:]
        for r in recent_results:
            red_odd_status = "✓" if r["correct"]["red_odd_even"] else "✗"
            red_size_status = "✓" if r["correct"]["red_size"] else "✗"
            blue_size_status = "✓" if r["correct"]["blue_size"] else "✗"

            report_lines.append(
                f"| {r['period']} | {red_odd_status} {r['actual']['red_odd_even']}/{r['predicted']['red_odd_even']} | "
                f"{red_size_status} {r['actual']['red_size']}/{r['predicted']['red_size']} | "
                f"{blue_size_status} {r['actual']['blue_size']}/{r['predicted']['blue_size']} | "
                f"{r['correct']['red_ball_hits']}/5 | {r['correct']['blue_ball_hits']}/2 |"
            )

        return "\n".join(report_lines)

    def run_complete_evaluation(self, test_periods: int = 100) -> Dict[str, Any]:
        """运行完整评估流程"""
        print("V4.0 Transformer 性能评估系统")
        print("=" * 60)

        if not self.model:
            print("[ERROR] 模型未加载，无法进行评估")
            return {"success": False, "error": "模型未加载"}

        try:
            # 1. 数据准备
            print("\n[STEP 1] 数据加载和准备")
            df = self.load_and_prepare_data()

            # 2. 序列数据准备
            print("\n[STEP 2] 评估序列准备")
            X, y, period_numbers = self.prepare_evaluation_sequences(df, test_periods)

            # 3. 性能评估
            print("\n[STEP 3] 模型性能评估")
            results = self.evaluate_model_performance(X, y, period_numbers)

            if results.get("success"):
                # 4. 生成报告
                print("\n[STEP 4] 生成评估报告")
                report = self.generate_prediction_report(results)

                # 保存报告
                report_path = (
                    self.models_dir
                    / f"evaluation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
                )
                with open(report_path, "w", encoding="utf-8") as f:
                    f.write(report)

                print(f"[OK] 评估报告已保存: {report_path}")
                results["report_path"] = str(report_path)
                results["report_content"] = report

                print(f"\n[SUCCESS] V4.0性能评估完成！")

            return results

        except Exception as e:
            print(f"\n[ERROR] 评估流程失败: {e}")
            import traceback

            traceback.print_exc()
            return {"success": False, "error": str(e)}


def main():
    """主函数"""
    evaluator = V4PerformanceEvaluator()
    results = evaluator.run_complete_evaluation(test_periods=50)  # 测试最近50期

    if results.get("success"):
        print("\n" + "=" * 60)
        print("评估报告预览:")
        print("=" * 60)
        print(results.get("report_content", "")[:1000] + "...")

    return results.get("success", False)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
