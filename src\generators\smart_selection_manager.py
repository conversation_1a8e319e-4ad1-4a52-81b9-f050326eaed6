#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能选号管理器 - Phase 4 选号系统优化
统一管理所有选号生成器，实现智能选择和融合
"""

import random
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
from collections import defaultdict, Counter
import time

from src.utils.data_utils import state_to_ratio


class SmartNumberSelectionManager:
    """智能选号管理器 - 统一管理和优化所有选号生成器"""
    
    def __init__(self):
        """初始化智能选号管理器"""
        self.red_range = list(range(1, 36))
        self.blue_range = list(range(1, 13))
        
        # 生成器注册表
        self.generators = {}
        self.generator_weights = {}
        self.generator_performance = defaultdict(lambda: {'success': 0, 'total': 0, 'avg_time': 0})
        
        # 比例约束配置
        self.ratio_constraints = {
            'red_odd_even': {'strict': True, 'tolerance': 0},  # 严格执行奇偶比
            'red_size': {'strict': True, 'tolerance': 0},      # 严格执行大小比
            'blue_size': {'strict': True, 'tolerance': 0}      # 严格执行蓝球大小比
        }
        
        # 多样性配置
        self.diversity_config = {
            'min_difference': 3,        # 最小差异号码数
            'avoid_consecutive': True,  # 避免连续号码
            'balance_distribution': True # 平衡号码分布
        }
        
        # 杀号集成配置
        self.kill_integration = {
            'strict_filter': True,      # 严格过滤杀号
            'multi_layer_check': True,  # 多层杀号检查
            'fallback_strategy': True   # 杀号失效时的回退策略
        }
        
        print("    [成功] 智能选号管理器初始化完成")
    
    def register_generator(self, name: str, generator: Any, weight: float = 1.0, 
                          category: str = "standard") -> None:
        """
        注册选号生成器
        
        Args:
            name: 生成器名称
            generator: 生成器实例
            weight: 初始权重
            category: 生成器类别
        """
        self.generators[name] = {
            'instance': generator,
            'weight': weight,
            'category': category,
            'active': True
        }
        self.generator_weights[name] = weight
        print(f"    [注册] 选号生成器 {name} 已注册，权重: {weight}")
    
    def smart_generate_numbers(self, 
                             red_odd_even_state: str,
                             red_size_state: str, 
                             blue_size_state: str,
                             kill_info: Dict[str, List[int]],
                             historical_data: Optional[List] = None,
                             target_count: int = 10,
                             seed: int = None) -> List[Tuple[List[int], List[int]]]:
        """
        智能生成号码组合
        
        Args:
            red_odd_even_state: 红球奇偶比状态
            red_size_state: 红球大小比状态
            blue_size_state: 蓝球大小比状态
            kill_info: 杀号信息
            historical_data: 历史数据
            target_count: 目标生成数量
            seed: 随机种子
            
        Returns:
            List[Tuple[List[int], List[int]]]: 生成的号码组合列表
        """
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
        
        print(f"  [智能选号] 开始生成 {target_count} 组号码...")
        print(f"  [约束条件] 红球奇偶比: {red_odd_even_state}, 红球大小比: {red_size_state}, 蓝球大小比: {blue_size_state}")
        
        # 提取杀号信息
        red_kills = kill_info.get('red_universal', [])
        blue_kills = kill_info.get('blue_universal', [])
        print(f"  [杀号应用] 红球杀号: {red_kills}, 蓝球杀号: {blue_kills}")
        
        combinations = []
        attempts = 0
        max_attempts = target_count * 3  # 最大尝试次数
        
        while len(combinations) < target_count and attempts < max_attempts:
            attempts += 1
            
            # 智能选择生成器
            generator_name = self._select_best_generator()
            
            try:
                # 生成单组号码
                red_balls, blue_balls = self._generate_single_combination(
                    generator_name, red_odd_even_state, red_size_state, 
                    blue_size_state, red_kills, blue_kills, historical_data, 
                    seed + attempts if seed else None
                )
                
                # 验证约束条件
                if self._validate_combination(red_balls, blue_balls, red_odd_even_state, 
                                            red_size_state, blue_size_state):
                    # 检查多样性
                    if self._check_diversity(red_balls, blue_balls, combinations):
                        combinations.append((red_balls, blue_balls))
                        print(f"    [成功] 第{len(combinations)}组: 红球{red_balls} 蓝球{blue_balls} (生成器: {generator_name})")
                    else:
                        print(f"    [跳过] 多样性检查未通过 (生成器: {generator_name})")
                else:
                    print(f"    [跳过] 约束验证未通过 (生成器: {generator_name})")
                    
            except Exception as e:
                print(f"    [警告] 生成器 {generator_name} 失败: {e}")
                continue
        
        if len(combinations) < target_count:
            print(f"  [警告] 仅生成了 {len(combinations)}/{target_count} 组号码")
            # 使用回退策略补充
            combinations.extend(self._fallback_generation(
                target_count - len(combinations), red_odd_even_state, 
                red_size_state, blue_size_state, red_kills, blue_kills
            ))
        
        print(f"  [完成] 智能选号生成完成，共 {len(combinations)} 组")
        return combinations[:target_count]
    
    def _select_best_generator(self) -> str:
        """智能选择最佳生成器"""
        if not self.generators:
            raise ValueError("没有注册的生成器")
        
        # 基于权重和性能选择
        active_generators = {name: info for name, info in self.generators.items() 
                           if info['active']}
        
        if not active_generators:
            raise ValueError("没有活跃的生成器")
        
        # 计算综合得分
        scores = {}
        for name, info in active_generators.items():
            perf = self.generator_performance[name]
            success_rate = perf['success'] / max(perf['total'], 1)
            weight = info['weight']
            
            # 综合得分 = 权重 * 成功率 * 随机因子
            scores[name] = weight * (0.5 + success_rate * 0.5) * random.uniform(0.8, 1.2)
        
        # 选择得分最高的生成器
        best_generator = max(scores.items(), key=lambda x: x[1])[0]
        return best_generator
    
    def _generate_single_combination(self, generator_name: str, red_odd_even_state: str,
                                   red_size_state: str, blue_size_state: str,
                                   red_kills: List[int], blue_kills: List[int],
                                   historical_data: Optional[List], seed: Optional[int]) -> Tuple[List[int], List[int]]:
        """使用指定生成器生成单组号码"""
        generator_info = self.generators[generator_name]
        generator = generator_info['instance']
        
        start_time = time.time()
        
        try:
            # 根据生成器类型调用不同的方法
            if hasattr(generator, 'generate_numbers_with_insights'):
                # InsightBasedGenerator
                red_balls, blue_balls = generator.generate_numbers_with_insights(
                    red_odd_even_state, red_size_state, blue_size_state,
                    historical_data, red_kills, blue_kills, seed or 0
                )
            elif hasattr(generator, 'generate_numbers_by_state'):
                # NumberGenerator
                red_balls, blue_balls = generator.generate_numbers_by_state(
                    red_odd_even_state, red_size_state, blue_size_state,
                    {'red': red_kills, 'blue': blue_kills}, seed or 0, historical_data
                )
            elif hasattr(generator, 'generate_enhanced_numbers'):
                # AdvancedNumberGenerator
                red_balls, blue_balls = generator.generate_enhanced_numbers(
                    red_odd_even_state, red_size_state, blue_size_state,
                    red_kills, blue_kills, seed or 0, historical_data
                )
            else:
                # 通用接口
                red_balls, blue_balls = generator.generate_numbers(
                    red_odd_even_state, red_size_state, blue_size_state,
                    red_kills, blue_kills, seed or 0
                )
            
            # 记录性能
            elapsed_time = time.time() - start_time
            self._update_generator_performance(generator_name, True, elapsed_time)
            
            return sorted(red_balls), sorted(blue_balls)
            
        except Exception as e:
            # 记录失败
            elapsed_time = time.time() - start_time
            self._update_generator_performance(generator_name, False, elapsed_time)
            raise e
    
    def _validate_combination(self, red_balls: List[int], blue_balls: List[int],
                            red_odd_even_state: str, red_size_state: str, 
                            blue_size_state: str) -> bool:
        """验证号码组合是否符合约束条件"""
        try:
            # 验证红球奇偶比
            if not self._validate_red_odd_even_ratio(red_balls, red_odd_even_state):
                return False
            
            # 验证红球大小比
            if not self._validate_red_size_ratio(red_balls, red_size_state):
                return False
            
            # 验证蓝球大小比
            if not self._validate_blue_size_ratio(blue_balls, blue_size_state):
                return False
            
            return True
            
        except Exception as e:
            print(f"    [警告] 约束验证失败: {e}")
            return False

    def _validate_red_odd_even_ratio(self, red_balls: List[int], target_state: str) -> bool:
        """验证红球奇偶比"""
        try:
            target_odd, target_even = state_to_ratio(target_state)
            actual_odd = sum(1 for ball in red_balls if ball % 2 == 1)
            actual_even = len(red_balls) - actual_odd

            return actual_odd == target_odd and actual_even == target_even
        except:
            return False

    def _validate_red_size_ratio(self, red_balls: List[int], target_state: str) -> bool:
        """验证红球大小比"""
        try:
            target_big, target_small = state_to_ratio(target_state)
            actual_big = sum(1 for ball in red_balls if ball >= 18)
            actual_small = len(red_balls) - actual_big

            return actual_big == target_big and actual_small == target_small
        except:
            return False

    def _validate_blue_size_ratio(self, blue_balls: List[int], target_state: str) -> bool:
        """验证蓝球大小比"""
        try:
            target_big, target_small = state_to_ratio(target_state)
            actual_big = sum(1 for ball in blue_balls if ball >= 7)
            actual_small = len(blue_balls) - actual_big

            return actual_big == target_big and actual_small == target_small
        except:
            return False

    def _check_diversity(self, red_balls: List[int], blue_balls: List[int],
                        existing_combinations: List[Tuple[List[int], List[int]]]) -> bool:
        """检查号码组合的多样性"""
        if not existing_combinations:
            return True

        for existing_red, existing_blue in existing_combinations:
            # 检查红球差异
            red_diff = len(set(red_balls) - set(existing_red))
            if red_diff < self.diversity_config['min_difference']:
                return False

            # 检查蓝球差异
            blue_diff = len(set(blue_balls) - set(existing_blue))
            if blue_diff < 1:  # 蓝球至少要有1个不同
                return False

        # 检查连续号码
        if self.diversity_config['avoid_consecutive']:
            if self._has_too_many_consecutive(red_balls):
                return False

        return True

    def _has_too_many_consecutive(self, numbers: List[int]) -> bool:
        """检查是否有过多连续号码"""
        if len(numbers) < 3:
            return False

        sorted_numbers = sorted(numbers)
        consecutive_count = 1
        max_consecutive = 1

        for i in range(1, len(sorted_numbers)):
            if sorted_numbers[i] == sorted_numbers[i-1] + 1:
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count)
            else:
                consecutive_count = 1

        return max_consecutive > 3  # 超过3个连续号码认为不合适

    def _fallback_generation(self, count: int, red_odd_even_state: str,
                           red_size_state: str, blue_size_state: str,
                           red_kills: List[int], blue_kills: List[int]) -> List[Tuple[List[int], List[int]]]:
        """回退生成策略"""
        combinations = []

        for i in range(count):
            try:
                # 使用简单但可靠的生成策略
                red_balls = self._simple_red_generation(red_odd_even_state, red_size_state, red_kills, i)
                blue_balls = self._simple_blue_generation(blue_size_state, blue_kills, i)

                if red_balls and blue_balls:
                    combinations.append((red_balls, blue_balls))
                    print(f"    [回退] 第{len(combinations)}组: 红球{red_balls} 蓝球{blue_balls}")
            except Exception as e:
                print(f"    [警告] 回退生成失败: {e}")
                continue

        return combinations

    def _simple_red_generation(self, odd_even_state: str, size_state: str,
                             kills: List[int], seed_offset: int) -> List[int]:
        """简单红球生成"""
        try:
            target_odd, target_even = state_to_ratio(odd_even_state)
            target_big, target_small = state_to_ratio(size_state)

            # 创建候选池
            candidates = [n for n in self.red_range if n not in kills]

            # 分类候选
            odd_candidates = [n for n in candidates if n % 2 == 1]
            even_candidates = [n for n in candidates if n % 2 == 0]
            big_candidates = [n for n in candidates if n >= 18]
            small_candidates = [n for n in candidates if n < 18]

            # 智能选择
            random.seed(hash(str(seed_offset)) % 2**32)
            selected = []

            # 先满足奇偶比
            selected.extend(random.sample(odd_candidates, min(target_odd, len(odd_candidates))))
            selected.extend(random.sample(even_candidates, min(target_even, len(even_candidates))))

            # 调整大小比（如果可能）
            if len(selected) == 5:
                return sorted(selected)

            # 补充到5个
            remaining = [n for n in candidates if n not in selected]
            needed = 5 - len(selected)
            selected.extend(random.sample(remaining, min(needed, len(remaining))))

            return sorted(selected[:5])

        except Exception as e:
            print(f"    [警告] 简单红球生成失败: {e}")
            return []

    def _simple_blue_generation(self, size_state: str, kills: List[int], seed_offset: int) -> List[int]:
        """简单蓝球生成"""
        try:
            target_big, target_small = state_to_ratio(size_state)

            # 创建候选池
            candidates = [n for n in self.blue_range if n not in kills]

            # 分类候选
            big_candidates = [n for n in candidates if n >= 7]
            small_candidates = [n for n in candidates if n < 7]

            # 智能选择
            random.seed(hash(str(seed_offset + 100)) % 2**32)
            selected = []

            # 满足大小比
            selected.extend(random.sample(big_candidates, min(target_big, len(big_candidates))))
            selected.extend(random.sample(small_candidates, min(target_small, len(small_candidates))))

            return sorted(selected[:2])

        except Exception as e:
            print(f"    [警告] 简单蓝球生成失败: {e}")
            return []

    def _update_generator_performance(self, generator_name: str, success: bool, elapsed_time: float):
        """更新生成器性能统计"""
        perf = self.generator_performance[generator_name]
        perf['total'] += 1
        if success:
            perf['success'] += 1

        # 更新平均时间
        if perf['total'] == 1:
            perf['avg_time'] = elapsed_time
        else:
            perf['avg_time'] = (perf['avg_time'] * (perf['total'] - 1) + elapsed_time) / perf['total']

    def get_performance_summary(self) -> Dict[str, Dict[str, float]]:
        """获取性能摘要"""
        summary = {}
        for name, perf in self.generator_performance.items():
            if perf['total'] > 0:
                summary[name] = {
                    'success_rate': perf['success'] / perf['total'],
                    'total_calls': perf['total'],
                    'avg_time': perf['avg_time']
                }
        return summary

    def adjust_weights_by_performance(self):
        """根据性能调整生成器权重"""
        for name, perf in self.generator_performance.items():
            if name in self.generators and perf['total'] >= 5:  # 至少5次调用后才调整
                success_rate = perf['success'] / perf['total']

                # 根据成功率调整权重
                if success_rate > 0.8:
                    self.generators[name]['weight'] *= 1.1  # 提升权重
                elif success_rate < 0.3:
                    self.generators[name]['weight'] *= 0.9  # 降低权重

                # 限制权重范围
                self.generators[name]['weight'] = max(0.1, min(3.0, self.generators[name]['weight']))

        print("    [优化] 生成器权重已根据性能调整")
