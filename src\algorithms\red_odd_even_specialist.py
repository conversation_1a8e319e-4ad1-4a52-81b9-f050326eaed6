#!/usr/bin/env python3
"""
红球奇偶比专家预测器
专门针对红球奇偶比预测进行深度优化
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import Counter
import logging
from src.utils.utils import parse_numbers, calculate_odd_even_ratio, ratio_to_state
from src.analysis.advanced_feature_engineer import AdvancedFeatureEngineer


class RedOddEvenSpecialist:
    """红球奇偶比专家预测器"""

    def __init__(self):
        """初始化专家预测器"""
        self.logger = logging.getLogger(__name__)
        self.feature_engineer = AdvancedFeatureEngineer()
        self.prediction_cache = {}

        # 预测器权重（基于历史表现调整）
        self.predictor_weights = {
            "enhanced_frequency": 0.25,
            "advanced_markov": 0.25,
            "pattern_recognition": 0.20,
            "correlation_based": 0.15,
            "ensemble_meta": 0.15,
        }

    def predict_ratio(
        self, data: pd.DataFrame, ratio_type: str = "red_odd_even"
    ) -> Dict[str, Any]:
        """
        预测红球奇偶比

        Args:
            data: 历史数据
            ratio_type: 比率类型

        Returns:
            预测结果字典
        """
        if ratio_type != "red_odd_even":
            return {"prediction": "2:3", "confidence": 0.0, "method": "unsupported"}

        # 提取高级特征
        features = self.feature_engineer.extract_all_features(data)

        # 多种预测方法
        predictions = {}

        # 1. 增强频率预测器
        predictions["enhanced_frequency"] = self._enhanced_frequency_predictor(
            data, features
        )

        # 2. 高级马尔科夫预测器
        predictions["advanced_markov"] = self._advanced_markov_predictor(data, features)

        # 3. 模式识别预测器
        predictions["pattern_recognition"] = self._pattern_recognition_predictor(
            data, features
        )

        # 4. 关联特征预测器
        predictions["correlation_based"] = self._correlation_based_predictor(
            data, features
        )

        # 5. 集成元预测器
        predictions["ensemble_meta"] = self._ensemble_meta_predictor(
            predictions, features
        )

        # 加权投票
        final_prediction = self._weighted_voting(predictions)

        return final_prediction

    def _enhanced_frequency_predictor(
        self, data: pd.DataFrame, features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """增强频率预测器 - 考虑时间衰减和趋势"""
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)

        if len(states) < 10:
            return {
                "prediction": "2:3",
                "confidence": 0.1,
                "method": "enhanced_frequency",
            }

        # 时间衰减权重
        weights = np.exp(-0.1 * np.arange(len(states))[::-1])  # 越近期权重越大

        # 加权频率统计
        weighted_counts = {}
        for state, weight in zip(states, weights):
            weighted_counts[state] = weighted_counts.get(state, 0) + weight

        # 趋势调整
        trend_direction = features.get("trend_direction", "stable")
        trend_strength = features.get("trend_strength", 0.0)

        if trend_direction == "increasing" and trend_strength > 0.3:
            # 趋势向高奇数比调整
            for state in ["4:1", "5:0"]:
                weighted_counts[state] = weighted_counts.get(state, 0) * 1.2
        elif trend_direction == "decreasing" and trend_strength > 0.3:
            # 趋势向低奇数比调整
            for state in ["0:5", "1:4"]:
                weighted_counts[state] = weighted_counts.get(state, 0) * 1.2

        # 选择最高权重的状态
        if weighted_counts:
            best_state = max(weighted_counts.items(), key=lambda x: x[1])[0]
            total_weight = sum(weighted_counts.values())
            confidence = weighted_counts[best_state] / total_weight

            return {
                "prediction": best_state,
                "confidence": confidence,
                "method": "enhanced_frequency",
            }

        return {"prediction": "2:3", "confidence": 0.1, "method": "enhanced_frequency"}

    def _advanced_markov_predictor(
        self, data: pd.DataFrame, features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """高级马尔科夫预测器 - 使用2阶马尔科夫链"""
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)

        if len(states) < 5:
            return {"prediction": "2:3", "confidence": 0.1, "method": "advanced_markov"}

        # 尝试2阶马尔科夫链
        markov_2nd = features.get("markov_2nd_order", {})
        if len(states) >= 3 and markov_2nd:
            current_context = tuple(states[-2:])
            if current_context in markov_2nd:
                transitions = markov_2nd[current_context]
                best_next = max(transitions.items(), key=lambda x: x[1])
                return {
                    "prediction": best_next[0],
                    "confidence": best_next[1],
                    "method": "advanced_markov_2nd",
                }

        # 回退到1阶马尔科夫链
        markov_1st = features.get("markov_1st_order", {})
        if len(states) >= 2 and markov_1st:
            current_state = states[-1]
            if current_state in markov_1st:
                transitions = markov_1st[current_state]
                best_next = max(transitions.items(), key=lambda x: x[1])
                return {
                    "prediction": best_next[0],
                    "confidence": best_next[1] * 0.8,  # 降低置信度
                    "method": "advanced_markov_1st",
                }

        return {"prediction": "2:3", "confidence": 0.1, "method": "advanced_markov"}

    def _pattern_recognition_predictor(
        self, data: pd.DataFrame, features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """模式识别预测器 - 识别反转和震荡模式"""
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)

        if len(states) < 5:
            return {
                "prediction": "2:3",
                "confidence": 0.1,
                "method": "pattern_recognition",
            }

        recent_states = states[-4:]  # 最近4期

        # 检测反转模式
        reversal_patterns = features.get("reversal_patterns", {})
        if len(recent_states) >= 3:
            # 检查是否符合反转模式 A->B->A，预测下一个为B
            if (
                recent_states[-3] == recent_states[-1]
                and recent_states[-3] != recent_states[-2]
            ):
                predicted_state = recent_states[-2]
                confidence = reversal_patterns.get(
                    f"{recent_states[-3]}->{recent_states[-2]}->{recent_states[-1]}",
                    0.1,
                )
                return {
                    "prediction": predicted_state,
                    "confidence": min(confidence * 2, 0.8),
                    "method": "pattern_reversal",
                }

        # 检测震荡模式
        oscillation_patterns = features.get("oscillation_patterns", {})
        if len(recent_states) >= 4:
            # 检查ABAB模式
            if (
                recent_states[-4] == recent_states[-2]
                and recent_states[-3] == recent_states[-1]
                and recent_states[-4] != recent_states[-3]
            ):
                predicted_state = recent_states[-4]  # 继续震荡
                confidence = oscillation_patterns.get("ABAB", 0.1)
                return {
                    "prediction": predicted_state,
                    "confidence": min(confidence * 3, 0.7),
                    "method": "pattern_oscillation",
                }

        # 连续性分析
        consecutive_length = features.get("recent_consecutive_length", 1)
        if consecutive_length >= 3:
            # 连续3期以上，预测可能反转
            current_state = states[-1]
            all_states = ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]
            other_states = [s for s in all_states if s != current_state]

            # 选择历史上最常见的其他状态
            state_counts = Counter(states)
            best_other = max(other_states, key=lambda s: state_counts.get(s, 0))

            return {
                "prediction": best_other,
                "confidence": min(0.1 + consecutive_length * 0.1, 0.6),
                "method": "pattern_consecutive_reversal",
            }

        return {"prediction": "2:3", "confidence": 0.1, "method": "pattern_recognition"}

    def _correlation_based_predictor(
        self, data: pd.DataFrame, features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """关联特征预测器 - 基于与其他特征的关联"""
        states = []
        size_states = []

        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)

            # 奇偶比
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            states.append(ratio_to_state((red_odd, red_even)))

            # 大小比
            red_small = sum(1 for x in red_balls if x <= 17)
            red_large = sum(1 for x in red_balls if x >= 18)
            size_states.append(ratio_to_state((red_small, red_large)))

        if len(states) < 5:
            return {
                "prediction": "2:3",
                "confidence": 0.1,
                "method": "correlation_based",
            }

        # 基于大小比预测奇偶比
        conditional_probs = features.get("conditional_probs", {})
        if conditional_probs and len(size_states) > 0:
            current_size_state = size_states[-1]
            if current_size_state in conditional_probs:
                size_to_odd_even = conditional_probs[current_size_state]
                if size_to_odd_even:
                    best_odd_even = max(size_to_odd_even.items(), key=lambda x: x[1])
                    return {
                        "prediction": best_odd_even[0],
                        "confidence": best_odd_even[1],
                        "method": "correlation_size_to_odd_even",
                    }

        # 基于关联度的预测
        correlation = features.get("odd_even_size_correlation", 0.0)
        if abs(correlation) > 0.3:  # 有显著关联
            # 根据关联方向调整预测
            current_size_state = size_states[-1] if size_states else "2:3"

            if correlation > 0:  # 正相关
                # 大小比高 -> 奇偶比也倾向于高
                size_order = {
                    "0:5": 0,
                    "1:4": 1,
                    "2:3": 2,
                    "3:2": 3,
                    "4:1": 4,
                    "5:0": 5,
                }
                size_level = size_order.get(current_size_state, 2)

                odd_even_states = ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]
                predicted_state = odd_even_states[min(size_level, 5)]
            else:  # 负相关
                size_order = {
                    "0:5": 0,
                    "1:4": 1,
                    "2:3": 2,
                    "3:2": 3,
                    "4:1": 4,
                    "5:0": 5,
                }
                size_level = size_order.get(current_size_state, 2)

                odd_even_states = ["5:0", "4:1", "3:2", "2:3", "1:4", "0:5"]
                predicted_state = odd_even_states[min(size_level, 5)]

            return {
                "prediction": predicted_state,
                "confidence": min(abs(correlation), 0.6),
                "method": "correlation_based",
            }

        return {"prediction": "2:3", "confidence": 0.1, "method": "correlation_based"}

    def _ensemble_meta_predictor(
        self, predictions: Dict[str, Dict[str, Any]], features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """集成元预测器 - 基于其他预测器的一致性"""
        if len(predictions) < 2:
            return {"prediction": "2:3", "confidence": 0.1, "method": "ensemble_meta"}

        # 统计预测结果
        prediction_counts = {}
        confidence_sum = {}

        for method, pred_result in predictions.items():
            if method == "ensemble_meta":  # 避免递归
                continue

            pred_state = pred_result.get("prediction", "2:3")
            pred_conf = pred_result.get("confidence", 0.0)

            prediction_counts[pred_state] = prediction_counts.get(pred_state, 0) + 1
            confidence_sum[pred_state] = confidence_sum.get(pred_state, 0) + pred_conf

        if not prediction_counts:
            return {"prediction": "2:3", "confidence": 0.1, "method": "ensemble_meta"}

        # 选择最多预测器支持的状态
        most_supported = max(prediction_counts.items(), key=lambda x: x[1])
        support_count = most_supported[1]
        predicted_state = most_supported[0]

        # 计算置信度：支持度 + 平均置信度
        avg_confidence = confidence_sum[predicted_state] / support_count
        consensus_confidence = support_count / len(predictions)

        final_confidence = (avg_confidence + consensus_confidence) / 2

        return {
            "prediction": predicted_state,
            "confidence": min(final_confidence, 0.8),
            "method": f"ensemble_meta_support_{support_count}",
        }

    def _weighted_voting(
        self, predictions: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """加权投票决定最终预测"""
        if not predictions:
            return {"prediction": "2:3", "confidence": 0.1, "method": "default"}

        # 计算加权分数
        weighted_scores = {}
        total_weight = 0

        for method, pred_result in predictions.items():
            if method not in self.predictor_weights:
                continue

            pred_state = pred_result.get("prediction", "2:3")
            pred_conf = pred_result.get("confidence", 0.0)
            method_weight = self.predictor_weights[method]

            # 加权分数 = 方法权重 × 预测置信度
            score = method_weight * pred_conf
            weighted_scores[pred_state] = weighted_scores.get(pred_state, 0) + score
            total_weight += method_weight

        if not weighted_scores:
            # 增加默认预测的多样性
            import random

            default_predictions = ["1:4", "2:3", "3:2", "4:1"]
            default_weights = [0.15, 0.35, 0.35, 0.15]  # 2:3和3:2更常见
            default_prediction = random.choices(
                default_predictions, weights=default_weights
            )[0]
            return {
                "prediction": default_prediction,
                "confidence": 0.1,
                "method": "default_diversified",
            }

        # 增加预测多样性：不总是选择最高分，而是基于概率选择
        import random

        # 计算每个预测的概率
        total_score = sum(weighted_scores.values())
        if total_score <= 0:
            # 如果所有分数都是0或负数，使用均匀分布
            predictions_list = list(weighted_scores.keys())
            final_state = random.choice(predictions_list)
            final_confidence = 0.2
        else:
            # 基于分数的概率选择，但增加随机性
            predictions_probs = []
            predictions_list = []

            for state, score in weighted_scores.items():
                prob = max(0, score / total_score)  # 确保概率非负
                predictions_probs.append(prob)
                predictions_list.append(state)

            # 增加随机性：80%概率选择最优，20%概率随机选择
            if random.random() < 0.8:
                # 基于权重的概率选择
                final_state = random.choices(
                    predictions_list, weights=predictions_probs
                )[0]
            else:
                # 完全随机选择
                final_state = random.choice(predictions_list)

            final_score = weighted_scores[final_state]
            # 归一化置信度
            final_confidence = (
                min(final_score / total_weight, 0.9) if total_weight > 0 else 0.2
            )

        # 收集使用的方法
        used_methods = []
        for method, pred_result in predictions.items():
            if pred_result.get("prediction") == final_state:
                used_methods.append(method)

        return {
            "prediction": final_state,
            "confidence": final_confidence,
            "method": f"specialist_ensemble_{'+'.join(used_methods[:3])}",
        }
