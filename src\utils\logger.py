"""
日志工具模块
提供简单的日志功能
"""

import logging
import sys
from typing import Optional


def setup_logger(
    name: str = "lottery_predictor", level: str = "INFO"
) -> logging.Logger:
    """
    设置日志器

    Args:
        name: 日志器名称
        level: 日志级别

    Returns:
        logging.Logger: 配置好的日志器
    """
    logger = logging.getLogger(name)

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    logger.setLevel(getattr(logging, level.upper()))

    # 创建控制台处理器
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(getattr(logging, level.upper()))

    # 创建格式器
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    handler.setFormatter(formatter)

    # 添加处理器到日志器
    logger.addHandler(handler)

    return logger


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器

    Args:
        name: 日志器名称

    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(f"lottery_predictor.{name}")


# 默认日志器
default_logger = setup_logger()


def log_info(message: str, logger: Optional[logging.Logger] = None):
    """记录信息日志"""
    if logger is None:
        logger = default_logger
    logger.info(message)


def log_warning(message: str, logger: Optional[logging.Logger] = None):
    """记录警告日志"""
    if logger is None:
        logger = default_logger
    logger.warning(message)


def log_error(message: str, logger: Optional[logging.Logger] = None):
    """记录错误日志"""
    if logger is None:
        logger = default_logger
    logger.error(message)


def log_debug(message: str, logger: Optional[logging.Logger] = None):
    """记录调试日志"""
    if logger is None:
        logger = default_logger
    logger.debug(message)
