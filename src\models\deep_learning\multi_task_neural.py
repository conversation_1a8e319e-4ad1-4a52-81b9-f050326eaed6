"""
多任务神经网络预测器

同时预测多个彩票相关任务：
- 红球杀号概率
- 蓝球杀号概率
- 奇偶比预测
- 大小比预测
- 号码生成概率

通过多任务学习提高整体预测性能。
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import logging
from dataclasses import dataclass
from enum import Enum

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, models, optimizers, losses

    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
    from sklearn.multioutput import MultiOutputClassifier, MultiOutputRegressor

from ...core.interfaces import (
    IStandardPredictor,
    PredictionResult,
    PredictionType,
    BallType,
)
from ...core.base import StandardBasePredictor


class TaskType(Enum):
    """任务类型枚举"""

    KILL_RED = "kill_red"  # 红球杀号
    KILL_BLUE = "kill_blue"  # 蓝球杀号
    ODD_EVEN_RATIO = "odd_even"  # 奇偶比
    SIZE_RATIO = "size_ratio"  # 大小比
    NUMBER_PROB = "number_prob"  # 号码概率


@dataclass
class MultiTaskConfig:
    """多任务配置参数"""

    # 网络架构
    shared_layers: List[int] = None  # 共享层维度
    task_specific_layers: Dict[str, List[int]] = None  # 任务特定层
    dropout_rate: float = 0.3

    # 训练参数
    learning_rate: float = 0.001
    batch_size: int = 32
    epochs: int = 100
    validation_split: float = 0.2
    early_stopping_patience: int = 12

    # 任务权重
    task_weights: Dict[str, float] = None

    # 数据参数
    sequence_length: int = 25

    # 正则化
    l1_reg: float = 0.0001
    l2_reg: float = 0.0001

    def __post_init__(self):
        if self.shared_layers is None:
            self.shared_layers = [256, 128, 64]

        if self.task_specific_layers is None:
            self.task_specific_layers = {
                "kill_red": [32, 16],
                "kill_blue": [16, 8],
                "odd_even": [32, 16],
                "size_ratio": [32, 16],
                "number_prob": [64, 32],
            }

        if self.task_weights is None:
            self.task_weights = {
                "kill_red": 1.0,
                "kill_blue": 1.0,
                "odd_even": 0.8,
                "size_ratio": 0.8,
                "number_prob": 1.2,
            }


class MultiTaskNeuralPredictor(StandardBasePredictor):
    """多任务神经网络预测器"""

    def __init__(self, config: Optional[MultiTaskConfig] = None):
        super().__init__("多任务神经网络预测器", PredictionType.DEEP_LEARNING)
        self.config = config or MultiTaskConfig()
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.is_trained_flag = False
        self.task_encoders = {}  # 任务标签编码器

        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("TensorFlow未安装，将使用传统机器学习方法")
            self.fallback_models = {}

    def _prepare_multi_task_data(
        self, data: pd.DataFrame
    ) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """
        准备多任务训练数据

        Args:
            data: 历史数据

        Returns:
            X: 输入特征
            y_dict: 各任务的目标值字典
        """
        from sklearn.preprocessing import StandardScaler, LabelEncoder

        # 选择特征列
        if self.feature_columns is None:
            numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
            # 排除目标列
            exclude_cols = [
                "red_kill_target",
                "blue_kill_target",
                "odd_even_target",
                "size_ratio_target",
                "number_prob_target",
            ]
            self.feature_columns = [
                col for col in numeric_cols if col not in exclude_cols
            ]

        # 提取特征数据
        feature_data = data[self.feature_columns].values

        # 数据标准化
        if self.scaler is None:
            self.scaler = StandardScaler()
            feature_data = self.scaler.fit_transform(feature_data)
        else:
            feature_data = self.scaler.transform(feature_data)

        # 创建序列
        X = []
        y_dict = {task.value: [] for task in TaskType}

        seq_len = self.config.sequence_length

        for i in range(seq_len, len(feature_data)):
            X.append(feature_data[i - seq_len : i])

            # 为每个任务准备目标值
            current_row = data.iloc[i]

            # 红球杀号目标（假设是35个红球的杀号概率）
            if "red_kill_target" in data.columns:
                y_dict["kill_red"].append(current_row["red_kill_target"])
            else:
                # 生成模拟的杀号目标
                y_dict["kill_red"].append(np.random.randint(0, 2, 35))

            # 蓝球杀号目标（12个蓝球的杀号概率）
            if "blue_kill_target" in data.columns:
                y_dict["kill_blue"].append(current_row["blue_kill_target"])
            else:
                y_dict["kill_blue"].append(np.random.randint(0, 2, 12))

            # 奇偶比目标
            if "odd_even_target" in data.columns:
                y_dict["odd_even"].append(current_row["odd_even_target"])
            else:
                # 从红球数据推断奇偶比
                red_balls = self._extract_red_balls(current_row)
                if red_balls:
                    odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
                    y_dict["odd_even"].append(f"{odd_count}:{5-odd_count}")
                else:
                    y_dict["odd_even"].append("2:3")  # 默认值

            # 大小比目标
            if "size_ratio_target" in data.columns:
                y_dict["size_ratio"].append(current_row["size_ratio_target"])
            else:
                red_balls = self._extract_red_balls(current_row)
                if red_balls:
                    small_count = sum(1 for ball in red_balls if ball <= 17)
                    y_dict["size_ratio"].append(f"{small_count}:{5-small_count}")
                else:
                    y_dict["size_ratio"].append("2:3")  # 默认值

            # 号码概率目标
            if "number_prob_target" in data.columns:
                y_dict["number_prob"].append(current_row["number_prob_target"])
            else:
                # 生成号码出现概率
                y_dict["number_prob"].append(np.random.random(35))

        # 转换为numpy数组并编码标签
        X = np.array(X)

        for task_name, task_data in y_dict.items():
            if task_name in ["odd_even", "size_ratio"]:
                # 分类任务需要标签编码
                if task_name not in self.task_encoders:
                    self.task_encoders[task_name] = LabelEncoder()
                    y_dict[task_name] = self.task_encoders[task_name].fit_transform(
                        task_data
                    )
                else:
                    y_dict[task_name] = self.task_encoders[task_name].transform(
                        task_data
                    )
            else:
                y_dict[task_name] = np.array(task_data)

        return X, y_dict

    def _extract_red_balls(self, row: pd.Series) -> List[int]:
        """从数据行中提取红球号码"""
        red_balls = []
        for i in range(1, 6):  # 红球1到红球5
            col_name = f"红球{i}"
            if col_name in row:
                red_balls.append(int(row[col_name]))
        return red_balls

    def _build_multi_task_model(
        self, input_shape: Tuple[int, int], task_outputs: Dict[str, int]
    ) -> keras.Model:
        """
        构建多任务神经网络模型

        Args:
            input_shape: 输入形状
            task_outputs: 各任务的输出维度

        Returns:
            Keras模型
        """
        if not TENSORFLOW_AVAILABLE:
            return None

        # 输入层
        inputs = layers.Input(shape=input_shape)

        # 展平序列数据
        x = layers.Flatten()(inputs)

        # 共享层
        shared_features = x
        for units in self.config.shared_layers:
            shared_features = layers.Dense(
                units,
                activation="relu",
                kernel_regularizer=keras.regularizers.l1_l2(
                    l1=self.config.l1_reg, l2=self.config.l2_reg
                ),
            )(shared_features)
            shared_features = layers.Dropout(self.config.dropout_rate)(shared_features)

        # 任务特定分支
        task_outputs_dict = {}

        for task_name, output_dim in task_outputs.items():
            # 任务特定层
            task_features = shared_features

            if task_name in self.config.task_specific_layers:
                for units in self.config.task_specific_layers[task_name]:
                    task_features = layers.Dense(
                        units,
                        activation="relu",
                        kernel_regularizer=keras.regularizers.l1_l2(
                            l1=self.config.l1_reg, l2=self.config.l2_reg
                        ),
                    )(task_features)
                    task_features = layers.Dropout(self.config.dropout_rate)(
                        task_features
                    )

            # 输出层
            if task_name in ["odd_even", "size_ratio"]:
                # 分类任务
                task_output = layers.Dense(
                    output_dim, activation="softmax", name=f"{task_name}_output"
                )(task_features)
            else:
                # 回归任务
                if output_dim == 1:
                    activation = "linear"
                else:
                    activation = "sigmoid"  # 多输出概率

                task_output = layers.Dense(
                    output_dim, activation=activation, name=f"{task_name}_output"
                )(task_features)

            task_outputs_dict[f"{task_name}_output"] = task_output

        # 创建模型
        model = keras.Model(inputs=inputs, outputs=task_outputs_dict)

        # 编译模型
        optimizer = optimizers.Adam(learning_rate=self.config.learning_rate)

        # 定义损失函数
        losses_dict = {}
        metrics_dict = {}

        for task_name, output_dim in task_outputs.items():
            output_key = f"{task_name}_output"

            if task_name in ["odd_even", "size_ratio"]:
                losses_dict[output_key] = "sparse_categorical_crossentropy"
                metrics_dict[output_key] = ["accuracy"]
            else:
                if output_dim == 1:
                    losses_dict[output_key] = "mse"
                    metrics_dict[output_key] = ["mae"]
                else:
                    losses_dict[output_key] = "binary_crossentropy"
                    metrics_dict[output_key] = ["binary_accuracy"]

        model.compile(
            optimizer=optimizer,
            loss=losses_dict,
            loss_weights=self.config.task_weights,
            metrics=metrics_dict,
        )

        return model

    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """训练多任务模型"""
        try:
            self.logger.info("开始训练多任务神经网络...")

            # 准备数据
            X, y_dict = self._prepare_multi_task_data(data)

            if len(X) == 0:
                raise ValueError("训练数据不足，无法创建序列")

            self.logger.info(f"训练数据形状: X={X.shape}")
            for task, y in y_dict.items():
                self.logger.info(f"任务 {task}: y.shape={np.array(y).shape}")

            if not TENSORFLOW_AVAILABLE:
                # 使用传统机器学习方法
                X_flat = X.reshape(X.shape[0], -1)

                for task_name, y_data in y_dict.items():
                    if task_name in ["odd_even", "size_ratio"]:
                        model = RandomForestClassifier(
                            n_estimators=100, random_state=42
                        )
                    else:
                        if len(np.array(y_data).shape) > 1:
                            model = MultiOutputRegressor(
                                RandomForestRegressor(n_estimators=100, random_state=42)
                            )
                        else:
                            model = RandomForestRegressor(
                                n_estimators=100, random_state=42
                            )

                    model.fit(X_flat, y_data)
                    self.fallback_models[task_name] = model

                self.is_trained_flag = True
                return {"method": "fallback", "samples": len(X), "tasks": len(y_dict)}

            # 确定输出维度
            task_outputs = {}
            for task_name, y_data in y_dict.items():
                if task_name in ["odd_even", "size_ratio"]:
                    task_outputs[task_name] = len(np.unique(y_data))
                else:
                    y_array = np.array(y_data)
                    if len(y_array.shape) == 1:
                        task_outputs[task_name] = 1
                    else:
                        task_outputs[task_name] = y_array.shape[1]

            # 构建模型
            self.model = self._build_multi_task_model(X.shape[1:], task_outputs)

            # 准备训练目标
            y_train = {}
            for task_name, y_data in y_dict.items():
                y_train[f"{task_name}_output"] = np.array(y_data)

            # 设置回调
            callbacks_list = [
                keras.callbacks.EarlyStopping(
                    monitor="val_loss",
                    patience=self.config.early_stopping_patience,
                    restore_best_weights=True,
                ),
                keras.callbacks.ReduceLROnPlateau(
                    monitor="val_loss", factor=0.5, patience=6, min_lr=1e-7
                ),
            ]

            # 训练模型
            history = self.model.fit(
                X,
                y_train,
                batch_size=self.config.batch_size,
                epochs=self.config.epochs,
                validation_split=self.config.validation_split,
                callbacks=callbacks_list,
                verbose=0,
            )

            self.is_trained_flag = True

            # 返回训练结果
            final_loss = history.history["loss"][-1]
            final_val_loss = (
                history.history["val_loss"][-1]
                if "val_loss" in history.history
                else None
            )

            self.logger.info(f"多任务训练完成 - 总损失: {final_loss:.4f}")

            return {
                "method": "multi_task_neural",
                "samples": len(X),
                "tasks": len(task_outputs),
                "epochs_trained": len(history.history["loss"]),
                "final_loss": final_loss,
                "final_val_loss": final_val_loss,
                "model_params": self.model.count_params() if self.model else 0,
                "task_outputs": task_outputs,
            }

        except Exception as e:
            self.logger.error(f"多任务训练失败: {e}")
            return {"error": str(e)}

    def predict(
        self, data: pd.DataFrame, target_index: int, **kwargs
    ) -> PredictionResult:
        """执行多任务预测"""
        if not self.is_trained_flag:
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": "模型未训练", "predictions": {}},
            )

        try:
            # 准备预测数据
            seq_len = self.config.sequence_length
            if target_index < seq_len:
                return PredictionResult(
                    prediction_type=PredictionType.DEEP_LEARNING,
                    ball_type=BallType.RED,
                    value=None,
                    confidence=0.0,
                    metadata={
                        "success": False,
                        "error": f"目标索引{target_index}小于序列长度{seq_len}",
                        "predictions": {},
                    },
                )

            # 提取最近的序列
            recent_data = data.iloc[target_index - seq_len : target_index]
            feature_data = recent_data[self.feature_columns].values

            # 标准化
            feature_data = self.scaler.transform(feature_data)
            X = feature_data.reshape(1, seq_len, -1)

            predictions = {}

            if not TENSORFLOW_AVAILABLE:
                # 使用传统方法预测
                X_flat = X.reshape(1, -1)

                for task_name, model in self.fallback_models.items():
                    pred = model.predict(X_flat)[0]
                    predictions[f"{task_name}_prediction"] = (
                        pred.tolist() if isinstance(pred, np.ndarray) else pred
                    )
            else:
                # 使用多任务神经网络预测
                pred_dict = self.model.predict(X, verbose=0)

                for output_name, pred_values in pred_dict.items():
                    task_name = output_name.replace("_output", "")
                    pred = pred_values[0]

                    if task_name in ["odd_even", "size_ratio"]:
                        # 分类任务：返回类别和概率
                        class_idx = np.argmax(pred)
                        if task_name in self.task_encoders:
                            class_label = self.task_encoders[
                                task_name
                            ].inverse_transform([class_idx])[0]
                        else:
                            class_label = class_idx

                        predictions[f"{task_name}_prediction"] = {
                            "class": class_label,
                            "probabilities": pred.tolist(),
                            "confidence": float(np.max(pred)),
                        }
                    else:
                        # 回归任务
                        if len(pred.shape) == 0:
                            predictions[f"{task_name}_prediction"] = float(pred)
                        else:
                            predictions[f"{task_name}_prediction"] = pred.tolist()

            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=predictions,
                confidence=0.8,
                metadata={
                    "success": True,
                    "predictions": predictions,
                    "sequence_length": seq_len,
                    "feature_count": (
                        len(self.feature_columns) if self.feature_columns else 0
                    ),
                    "model_type": (
                        "MultiTaskNeural"
                        if TENSORFLOW_AVAILABLE
                        else "MultiTaskFallback"
                    ),
                    "tasks_count": len(predictions),
                },
            )

        except Exception as e:
            self.logger.error(f"多任务预测失败: {e}")
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": str(e), "predictions": {}},
            )

    def predict_batch(
        self, data: pd.DataFrame, target_indices: List[int], **kwargs
    ) -> List[PredictionResult]:
        """批量预测"""
        results = []
        for idx in target_indices:
            result = self.predict(data, idx, **kwargs)
            results.append(result)
        return results

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            "name": "多任务神经网络预测器",
            "version": "1.0.0",
            "is_trained": self.is_trained_flag,
            "tensorflow_available": TENSORFLOW_AVAILABLE,
            "config": {
                "shared_layers": self.config.shared_layers,
                "sequence_length": self.config.sequence_length,
                "learning_rate": self.config.learning_rate,
                "task_weights": self.config.task_weights,
            },
        }

        if TENSORFLOW_AVAILABLE:
            if self.model:
                info["model_params"] = self.model.count_params()
                info["model_layers"] = len(self.model.layers)
            info["fallback_models"] = None
        else:
            info["fallback_models"] = list(self.fallback_models.keys())

        return info
