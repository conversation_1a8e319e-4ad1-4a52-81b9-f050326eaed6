"""
Legacy Predictor Migration Script
识别并适配现有预测器到标准化接口
"""

import sys
from pathlib import Path
import pandas as pd
from typing import Dict, Any, List, Tuple
import importlib
import inspect

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.predictor_adapter import create_legacy_adapter
from src.core.specialized_adapters import create_specialized_adapter
from src.core.interfaces import BallType, PredictionType
from src.core.predictor_factory import get_predictor_factory


class LegacyPredictorMigrator:
    """旧预测器迁移器"""
    
    def __init__(self):
        self.identified_predictors = []
        self.adapted_predictors = {}
        self.migration_results = []
        
    def identify_legacy_predictors(self) -> List[Dict[str, Any]]:
        """识别需要适配的旧预测器"""
        print("=" * 60)
        print("识别旧预测器")
        print("=" * 60)
        
        # 已知的预测器模块和类
        predictor_candidates = [
            {
                'module': 'src.algorithms.enhanced_bayesian',
                'class': 'EnhancedBayesianPredictor',
                'type': 'bayesian'
            },
            {
                'module': 'src.algorithms.enhanced_ensemble',
                'class': 'EnhancedEnsembleLearning',
                'type': 'ensemble'
            },
            {
                'module': 'src.algorithms.enhanced_markov_bayes',
                'class': 'EnhancedMarkovBayesPredictor',
                'type': 'markov_bayes'
            },
            {
                'module': 'src.algorithms.red_odd_even_specialist',
                'class': 'RedOddEvenSpecialist',
                'type': 'specialist'
            },
            {
                'module': 'src.algorithms.comprehensive_prediction_system',
                'class': 'ComprehensivePredictionSystem',
                'type': 'comprehensive'
            },
            {
                'module': 'src.models.optimized_odd_even_predictor',
                'class': 'OptimizedOddEvenPredictor',
                'type': 'optimized'
            }
        ]
        
        identified = []
        
        for candidate in predictor_candidates:
            try:
                # 尝试导入模块
                module = importlib.import_module(candidate['module'])
                predictor_class = getattr(module, candidate['class'])
                
                # 检查类的方法
                methods = [method for method in dir(predictor_class) if not method.startswith('_')]
                has_predict = 'predict' in methods
                has_train = 'train' in methods
                
                # 检查是否已经实现标准接口
                from src.core.interfaces import IStandardPredictor, IPredictor
                is_standard = issubclass(predictor_class, IStandardPredictor)
                is_legacy = issubclass(predictor_class, IPredictor)
                
                info = {
                    'module': candidate['module'],
                    'class': candidate['class'],
                    'type': candidate['type'],
                    'predictor_class': predictor_class,
                    'has_predict': has_predict,
                    'has_train': has_train,
                    'is_standard': is_standard,
                    'is_legacy': is_legacy,
                    'needs_adaptation': has_predict and not is_standard,
                    'methods': methods
                }
                
                identified.append(info)
                
                status = "✅ 已标准化" if is_standard else ("🔄 需要适配" if info['needs_adaptation'] else "❓ 未知状态")
                print(f"* {candidate['class']}: {status}")
                print(f"  - 模块: {candidate['module']}")
                print(f"  - 有predict方法: {has_predict}")
                print(f"  - 有train方法: {has_train}")
                print(f"  - 实现IStandardPredictor: {is_standard}")
                print(f"  - 实现IPredictor: {is_legacy}")
                print()
                
            except Exception as e:
                print(f"* {candidate['class']}: ❌ 导入失败 - {e}")
                print()
        
        self.identified_predictors = identified
        return identified
    
    def create_sample_data(self) -> pd.DataFrame:
        """创建测试数据"""
        return pd.DataFrame({
            '期号': [25001, 25002, 25003, 25004, 25005],
            '红球1': [1, 5, 12, 8, 15],
            '红球2': [8, 12, 18, 15, 22],
            '红球3': [15, 19, 25, 22, 29],
            '红球4': [22, 26, 32, 29, 33],
            '红球5': [29, 33, 35, 35, 35],
            '蓝球1': [3, 7, 2, 5, 8],
            '蓝球2': [9, 11, 8, 12, 10]
        })
    
    def adapt_predictors(self) -> Dict[str, Any]:
        """适配需要适配的预测器"""
        print("=" * 60)
        print("适配旧预测器")
        print("=" * 60)
        
        sample_data = self.create_sample_data()
        adapted = {}
        
        for predictor_info in self.identified_predictors:
            if not predictor_info['needs_adaptation']:
                print(f"* {predictor_info['class']}: 跳过（不需要适配）")
                continue
                
            try:
                # 创建预测器实例
                predictor_class = predictor_info['predictor_class']
                
                # 尝试不同的初始化方式
                predictor_instance = None
                try:
                    # 尝试无参数初始化
                    predictor_instance = predictor_class()
                except Exception as e1:
                    try:
                        # 尝试带默认参数初始化
                        if 'Bayesian' in predictor_info['class']:
                            predictor_instance = predictor_class(['frequency', 'pattern'])
                        elif 'Markov' in predictor_info['class']:
                            predictor_instance = predictor_class('red')
                        else:
                            predictor_instance = predictor_class()
                    except Exception as e2:
                        print(f"* {predictor_info['class']}: ❌ 初始化失败 - {e1}, {e2}")
                        continue
                
                if predictor_instance is None:
                    continue
                
                # 创建适配器 - 使用专用适配器
                adapter = create_specialized_adapter(
                    predictor_instance,
                    predictor_type=None,  # 自动检测
                    name=f"适配的{predictor_info['class']}",
                    version="1.0"
                )
                
                # 测试适配器
                validation = adapter.validate_input(sample_data, 3)
                if validation.is_valid:
                    # 尝试预测 - 使用不同的参数组合
                    test_success = False
                    test_result = None
                    test_error = None

                    # 尝试多种预测方式
                    test_methods = [
                        lambda: adapter.predict(sample_data, 3, ball_type=BallType.RED),
                        lambda: adapter.predict(sample_data, 3),
                        lambda: adapter.predict(sample_data, target_index=3),
                    ]

                    for i, test_method in enumerate(test_methods):
                        try:
                            test_result = test_method()
                            test_success = True
                            print(f"* {predictor_info['class']}: ✅ 适配成功 (方法 {i+1})")
                            print(f"  - 预测结果: {test_result.value}")
                            print(f"  - 置信度: {test_result.confidence:.2f}")
                            break
                        except Exception as e:
                            test_error = e
                            continue

                    if test_success:
                        adapted[predictor_info['class']] = {
                            'adapter': adapter,
                            'original': predictor_instance,
                            'test_result': test_result,
                            'status': 'success'
                        }
                    else:
                        print(f"* {predictor_info['class']}: ⚠️ 适配成功但预测失败 - {test_error}")
                        adapted[predictor_info['class']] = {
                            'adapter': adapter,
                            'original': predictor_instance,
                            'test_result': None,
                            'status': 'partial'
                        }
                else:
                    print(f"* {predictor_info['class']}: ❌ 输入验证失败")
                
            except Exception as e:
                print(f"* {predictor_info['class']}: ❌ 适配失败 - {e}")
        
        self.adapted_predictors = adapted
        return adapted
    
    def generate_migration_report(self) -> str:
        """生成迁移报告"""
        report = []
        report.append("# Legacy Predictor Migration Report")
        report.append("=" * 50)
        report.append("")
        
        # 识别结果
        report.append("## 识别结果")
        report.append(f"总共识别预测器: {len(self.identified_predictors)}")
        
        needs_adaptation = [p for p in self.identified_predictors if p['needs_adaptation']]
        already_standard = [p for p in self.identified_predictors if p['is_standard']]
        
        report.append(f"需要适配: {len(needs_adaptation)}")
        report.append(f"已标准化: {len(already_standard)}")
        report.append("")
        
        # 适配结果
        report.append("## 适配结果")
        successful = [k for k, v in self.adapted_predictors.items() if v['status'] == 'success']
        partial = [k for k, v in self.adapted_predictors.items() if v['status'] == 'partial']
        
        report.append(f"成功适配: {len(successful)}")
        report.append(f"部分成功: {len(partial)}")
        report.append("")
        
        if successful:
            report.append("### 成功适配的预测器:")
            for name in successful:
                report.append(f"- {name}")
        
        if partial:
            report.append("### 部分成功的预测器:")
            for name in partial:
                report.append(f"- {name}")
        
        return "\n".join(report)


def main():
    """主函数"""
    print("Legacy Predictor Migration Tool")
    print("=" * 60)
    
    migrator = LegacyPredictorMigrator()
    
    # 步骤1: 识别预测器
    identified = migrator.identify_legacy_predictors()
    
    # 步骤2: 适配预测器
    adapted = migrator.adapt_predictors()
    
    # 步骤3: 生成报告
    print("\n" + "=" * 60)
    print("迁移报告")
    print("=" * 60)
    report = migrator.generate_migration_report()
    print(report)
    
    # 保存报告
    report_path = project_root / "docs" / "legacy_migration_report.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"\n报告已保存到: {report_path}")
    
    return migrator


if __name__ == "__main__":
    migrator = main()
