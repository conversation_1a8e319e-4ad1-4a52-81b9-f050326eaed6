#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正所有文件中的蓝球大小分界线
从错误的1-6小，7-12大 修正为 1-7小，8-12大
"""

import os
import re
from pathlib import Path

def find_and_replace_blue_boundary(file_path):
    """在单个文件中查找并替换蓝球分界线"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # 模式1: x <= 6 改为 x <= 7
        pattern1 = r'(\w+\s*<=\s*)6(\s*[^0-9])'
        if 'blue' in content.lower() or '蓝球' in content:
            matches = re.findall(pattern1, content)
            if matches:
                content = re.sub(pattern1, r'\g<1>7\g<2>', content)
                changes_made.append(f"将 <= 6 改为 <= 7")
        
        # 模式2: x > 6 改为 x > 7  
        pattern2 = r'(\w+\s*>\s*)6(\s*[^0-9])'
        if 'blue' in content.lower() or '蓝球' in content:
            matches = re.findall(pattern2, content)
            if matches:
                content = re.sub(pattern2, r'\g<1>7\g<2>', content)
                changes_made.append(f"将 > 6 改为 > 7")
        
        # 模式3: 注释中的分界线说明
        pattern3 = r'(蓝球[：:][^，。]*1-6[^，。]*小[^，。]*7-12[^，。]*大)'
        matches = re.findall(pattern3, content)
        if matches:
            content = re.sub(pattern3, lambda m: m.group(0).replace('1-6', '1-7').replace('7-12', '8-12'), content)
            changes_made.append(f"修正注释中的分界线说明")
        
        # 模式4: 英文注释中的分界线
        pattern4 = r'(1-6[^，。]*small[^，。]*7-12[^，。]*big)'
        matches = re.findall(pattern4, content, re.IGNORECASE)
        if matches:
            content = re.sub(pattern4, lambda m: m.group(0).replace('1-6', '1-7').replace('7-12', '8-12'), content, flags=re.IGNORECASE)
            changes_made.append(f"修正英文注释中的分界线说明")
        
        # 模式5: 配置中的分界点
        pattern5 = r'(blue_ball_small_max[^=]*=\s*)6'
        matches = re.findall(pattern5, content)
        if matches:
            content = re.sub(pattern5, r'\g<1>7', content)
            changes_made.append(f"修正配置中的small_max")
        
        pattern6 = r'(blue_ball_big_min[^=]*=\s*)7'
        matches = re.findall(pattern6, content)
        if matches:
            content = re.sub(pattern6, r'\g<1>8', content)
            changes_made.append(f"修正配置中的big_min")
        
        # 模式6: range(7, 13) 改为 range(8, 13)
        pattern7 = r'range\(7,\s*13\)'
        matches = re.findall(pattern7, content)
        if matches and ('blue' in content.lower() or '蓝球' in content):
            content = re.sub(pattern7, 'range(8, 13)', content)
            changes_made.append(f"修正range(7, 13)为range(8, 13)")
        
        # 模式7: range(1, 7) 改为 range(1, 8)
        pattern8 = r'range\(1,\s*7\)'
        matches = re.findall(pattern8, content)
        if matches and ('blue' in content.lower() or '蓝球' in content):
            content = re.sub(pattern8, 'range(1, 8)', content)
            changes_made.append(f"修正range(1, 7)为range(1, 8)")
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return changes_made
        
        return []
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return []

def scan_and_fix_project():
    """扫描整个项目并修正蓝球分界线"""
    project_root = Path(__file__).parent
    
    # 需要扫描的目录
    scan_dirs = [
        project_root / "src",
        project_root / "config", 
        project_root / "tests"
    ]
    
    total_files = 0
    modified_files = 0
    
    print("开始扫描和修正蓝球大小分界线...")
    print("=" * 60)
    
    for scan_dir in scan_dirs:
        if not scan_dir.exists():
            continue
            
        print(f"\n扫描目录: {scan_dir}")
        
        # 递归查找所有Python文件
        for py_file in scan_dir.rglob("*.py"):
            total_files += 1
            
            changes = find_and_replace_blue_boundary(py_file)
            
            if changes:
                modified_files += 1
                print(f"  [修改] {py_file.relative_to(project_root)}")
                for change in changes:
                    print(f"    - {change}")
    
    print("\n" + "=" * 60)
    print(f"扫描完成:")
    print(f"  总文件数: {total_files}")
    print(f"  修改文件数: {modified_files}")
    
    if modified_files > 0:
        print(f"\n建议:")
        print(f"1. 运行 python fix_blue_ball_prediction.py 验证修正效果")
        print(f"2. 运行 python test_main_system.py 测试系统集成")
        print(f"3. 检查修改的文件确保语法正确")
    else:
        print(f"\n未发现需要修正的蓝球分界线问题")

def verify_fixes():
    """验证修正效果"""
    print("\n验证修正效果...")
    
    # 重新运行蓝球预测分析
    try:
        import subprocess
        result = subprocess.run(['python', 'fix_blue_ball_prediction.py'], 
                              capture_output=True, text=True, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            print("[成功] 蓝球预测分析运行正常")
            
            # 提取准确率信息
            output = result.stdout
            if "改进预测器准确率" in output:
                lines = output.split('\n')
                for line in lines:
                    if "改进预测器准确率" in line:
                        print(f"[结果] {line.strip()}")
                        break
        else:
            print(f"[失败] 蓝球预测分析运行失败: {result.stderr}")
            
    except Exception as e:
        print(f"[错误] 验证过程出错: {e}")

def main():
    """主函数"""
    print("蓝球大小分界线修正工具")
    print("修正范围: 1-6小,7-12大 → 1-7小,8-12大")
    print("=" * 60)
    
    # 扫描和修正
    scan_and_fix_project()
    
    # 验证修正效果
    verify_fixes()
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
