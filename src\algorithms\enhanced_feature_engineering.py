"""
增强特征工程模块
为ML比值预测器提供更丰富和有效的特征
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings("ignore")


class EnhancedFeatureEngineering:
    """增强特征工程器"""

    def __init__(self, lookback_periods: int = 30):
        """
        初始化特征工程器

        Args:
            lookback_periods: 回看期数
        """
        self.lookback_periods = lookback_periods

    def create_enhanced_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        创建增强特征

        Args:
            data: 原始数据

        Returns:
            包含增强特征的数据
        """
        print(f"🔧 开始创建增强特征...")

        # 复制数据
        enhanced_data = data.copy()

        # 0. 数据清理 - 移除非数值列（除了期号）
        enhanced_data = self._clean_data(enhanced_data)

        # 1. 基础比值特征
        enhanced_data = self._add_basic_ratio_features(enhanced_data)

        # 2. 时间特征（如果还没有添加）
        if "year" not in enhanced_data.columns:
            enhanced_data = self._add_temporal_features(enhanced_data)

        # 3. 统计特征
        enhanced_data = self._add_statistical_features(enhanced_data)

        # 4. 趋势特征
        enhanced_data = self._add_trend_features(enhanced_data)

        # 5. 周期性特征
        enhanced_data = self._add_cyclical_features(enhanced_data)

        # 6. 相关性特征
        enhanced_data = self._add_correlation_features(enhanced_data)

        # 7. 组合特征
        enhanced_data = self._add_combination_features(enhanced_data)

        # 8. 间隔特征
        enhanced_data = self._add_interval_features(enhanced_data)

        print(f"✅ 特征工程完成，总特征数: {len(enhanced_data.columns)}")

        return enhanced_data

    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """清理数据，移除非数值列"""
        # 保留的列
        keep_columns = [
            "期号",
            "红球1",
            "红球2",
            "红球3",
            "红球4",
            "红球5",
            "蓝球1",
            "蓝球2",
        ]

        # 只保留存在的列
        available_columns = [col for col in keep_columns if col in data.columns]

        # 如果有日期列，先处理时间特征再移除
        if "日期" in data.columns or "开奖日期" in data.columns:
            data = self._add_temporal_features(data)

        # 移除非数值列
        for col in data.columns:
            if col not in available_columns and col not in [
                "year",
                "month",
                "day",
                "weekday",
                "quarter",
                "month_sin",
                "month_cos",
                "weekday_sin",
                "weekday_cos",
                "is_month_start",
                "is_month_end",
            ]:
                if data[col].dtype == "object" or col in ["日期", "开奖日期", "date"]:
                    data = data.drop(columns=[col], errors="ignore")

        return data

    def _add_basic_ratio_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加基础比值特征"""
        # 计算红球奇偶比
        for i in range(1, 6):
            data[f"red_{i}_is_odd"] = data[f"红球{i}"] % 2

        data["red_odd_count"] = data[[f"red_{i}_is_odd" for i in range(1, 6)]].sum(
            axis=1
        )
        data["red_even_count"] = 5 - data["red_odd_count"]
        data["red_odd_even_ratio"] = (
            data["red_odd_count"].astype(str) + ":" + data["red_even_count"].astype(str)
        )

        # 计算红球大小比
        from src.config.lottery_config import LotteryConfig
        for i in range(1, 6):
            data[f"red_{i}_is_big"] = data[f"红球{i}"].apply(lambda x: 1 if LotteryConfig.is_red_ball_large(x) else 0)

        data["red_big_count"] = data[[f"red_{i}_is_big" for i in range(1, 6)]].sum(
            axis=1
        )
        data["red_small_count"] = 5 - data["red_big_count"]
        data["red_size_ratio"] = (
            data["red_big_count"].astype(str)
            + ":"
            + data["red_small_count"].astype(str)
        )

        # 计算蓝球大小比
        for i in range(1, 3):
            data[f"blue_{i}_is_big"] = data[f"蓝球{i}"].apply(lambda x: 1 if LotteryConfig.is_blue_ball_large(x) else 0)

        data["blue_big_count"] = data[[f"blue_{i}_is_big" for i in range(1, 3)]].sum(
            axis=1
        )
        data["blue_small_count"] = 2 - data["blue_big_count"]
        data["blue_size_ratio"] = (
            data["blue_big_count"].astype(str)
            + ":"
            + data["blue_small_count"].astype(str)
        )

        return data

    def _add_temporal_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加时间特征"""
        date_column = None
        if "开奖日期" in data.columns:
            date_column = "开奖日期"
        elif "日期" in data.columns:
            date_column = "日期"

        if date_column:
            try:
                # 转换日期
                data["date"] = pd.to_datetime(data[date_column], errors="coerce")

                # 基础时间特征
                data["year"] = data["date"].dt.year.fillna(2024)
                data["month"] = data["date"].dt.month.fillna(6)
                data["day"] = data["date"].dt.day.fillna(15)
                data["weekday"] = data["date"].dt.weekday.fillna(3)
                data["quarter"] = data["date"].dt.quarter.fillna(2)

                # 周期性编码
                data["month_sin"] = np.sin(2 * np.pi * data["month"] / 12)
                data["month_cos"] = np.cos(2 * np.pi * data["month"] / 12)
                data["weekday_sin"] = np.sin(2 * np.pi * data["weekday"] / 7)
                data["weekday_cos"] = np.cos(2 * np.pi * data["weekday"] / 7)

                # 是否节假日附近
                data["is_month_start"] = (data["day"] <= 7).astype(int)
                data["is_month_end"] = (data["day"] >= 24).astype(int)
            except Exception as e:
                print(f"⚠️ 时间特征处理失败: {e}")
                # 使用默认值
                data["year"] = 2024
                data["month"] = 6
                data["day"] = 15
                data["weekday"] = 3
                data["quarter"] = 2
                data["month_sin"] = 0
                data["month_cos"] = 1
                data["weekday_sin"] = 0
                data["weekday_cos"] = 1
                data["is_month_start"] = 0
                data["is_month_end"] = 0
        else:
            # 没有日期列，使用默认值
            data["year"] = 2024
            data["month"] = 6
            data["day"] = 15
            data["weekday"] = 3
            data["quarter"] = 2
            data["month_sin"] = 0
            data["month_cos"] = 1
            data["weekday_sin"] = 0
            data["weekday_cos"] = 1
            data["is_month_start"] = 0
            data["is_month_end"] = 0

        return data

    def _add_statistical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加统计特征"""
        # 移动平均特征
        for window in [3, 5, 10, 20]:
            data[f"red_odd_count_ma_{window}"] = (
                data["red_odd_count"].rolling(window=window, min_periods=1).mean()
            )
            data[f"red_big_count_ma_{window}"] = (
                data["red_big_count"].rolling(window=window, min_periods=1).mean()
            )
            data[f"blue_big_count_ma_{window}"] = (
                data["blue_big_count"].rolling(window=window, min_periods=1).mean()
            )

        # 移动标准差
        for window in [5, 10, 20]:
            data[f"red_odd_count_std_{window}"] = (
                data["red_odd_count"].rolling(window=window, min_periods=1).std()
            )
            data[f"red_big_count_std_{window}"] = (
                data["red_big_count"].rolling(window=window, min_periods=1).std()
            )
            data[f"blue_big_count_std_{window}"] = (
                data["blue_big_count"].rolling(window=window, min_periods=1).std()
            )

        # 分位数特征
        for window in [10, 20]:
            for q in [0.25, 0.5, 0.75]:
                data[f"red_odd_count_q{int(q*100)}_{window}"] = (
                    data["red_odd_count"]
                    .rolling(window=window, min_periods=1)
                    .quantile(q)
                )

        return data

    def _add_trend_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加趋势特征"""
        # 计算趋势斜率
        for window in [5, 10, 20]:
            # 红球奇数趋势
            data[f"red_odd_trend_{window}"] = (
                data["red_odd_count"]
                .rolling(window=window, min_periods=2)
                .apply(
                    lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) >= 2 else 0
                )
            )

            # 红球大数趋势
            data[f"red_big_trend_{window}"] = (
                data["red_big_count"]
                .rolling(window=window, min_periods=2)
                .apply(
                    lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) >= 2 else 0
                )
            )

        # 变化率特征
        for lag in [1, 2, 3, 5]:
            data[f"red_odd_change_{lag}"] = data["red_odd_count"].diff(lag)
            data[f"red_big_change_{lag}"] = data["red_big_count"].diff(lag)
            data[f"blue_big_change_{lag}"] = data["blue_big_count"].diff(lag)

        return data

    def _add_cyclical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加周期性特征"""
        # 期号周期性
        if "期号" in data.columns:
            data["period_mod_7"] = data["期号"] % 7
            data["period_mod_10"] = data["期号"] % 10
            data["period_mod_30"] = data["期号"] % 30

        # 比值周期性
        data["red_odd_cycle_3"] = data["red_odd_count"] % 3
        data["red_odd_cycle_5"] = data["red_odd_count"] % 5
        data["red_big_cycle_3"] = data["red_big_count"] % 3
        data["red_big_cycle_5"] = data["red_big_count"] % 5

        return data

    def _add_correlation_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加相关性特征"""
        # 比值之间的相关性
        for window in [10, 20]:
            data[f"red_odd_big_corr_{window}"] = (
                data["red_odd_count"]
                .rolling(window=window, min_periods=5)
                .corr(data["red_big_count"])
            )
            data[f"red_blue_corr_{window}"] = (
                data["red_big_count"]
                .rolling(window=window, min_periods=5)
                .corr(data["blue_big_count"])
            )

        return data

    def _add_combination_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加组合特征"""
        # 比值组合
        data["red_odd_big_sum"] = data["red_odd_count"] + data["red_big_count"]
        data["red_odd_big_diff"] = data["red_odd_count"] - data["red_big_count"]
        data["red_odd_big_ratio"] = data["red_odd_count"] / (data["red_big_count"] + 1)

        # 总体特征
        data["total_big_count"] = data["red_big_count"] + data["blue_big_count"]
        data["total_odd_count"] = data["red_odd_count"]  # 蓝球没有奇偶概念

        return data

    def _add_interval_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加间隔特征"""
        # 计算上次出现特定比值的间隔
        for ratio in ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]:
            # 红球奇偶比间隔
            mask = data["red_odd_even_ratio"] == ratio
            data[f'red_odd_even_{ratio.replace(":", "_")}_interval'] = (
                self._calculate_interval(mask)
            )

            # 红球大小比间隔
            mask = data["red_size_ratio"] == ratio
            data[f'red_size_{ratio.replace(":", "_")}_interval'] = (
                self._calculate_interval(mask)
            )

        for ratio in ["0:2", "1:1", "2:0"]:
            # 蓝球大小比间隔
            mask = data["blue_size_ratio"] == ratio
            data[f'blue_size_{ratio.replace(":", "_")}_interval'] = (
                self._calculate_interval(mask)
            )

        return data

    def _calculate_interval(self, mask: pd.Series) -> pd.Series:
        """计算间隔期数"""
        intervals = pd.Series(index=mask.index, dtype=float)
        last_occurrence = -1

        for i, is_match in enumerate(mask):
            if is_match:
                last_occurrence = i
                intervals.iloc[i] = 0
            else:
                if last_occurrence >= 0:
                    intervals.iloc[i] = i - last_occurrence
                else:
                    intervals.iloc[i] = i + 1  # 从开始到现在的期数

        return intervals

    def get_feature_names(self, data: pd.DataFrame) -> List[str]:
        """获取特征名称列表"""
        # 排除原始列和目标列
        exclude_cols = [
            "期号",
            "红球1",
            "红球2",
            "红球3",
            "红球4",
            "红球5",
            "蓝球1",
            "蓝球2",
            "开奖日期",
            "date",
            "red_odd_even_ratio",
            "red_size_ratio",
            "blue_size_ratio",
        ]

        feature_cols = [col for col in data.columns if col not in exclude_cols]
        return feature_cols
