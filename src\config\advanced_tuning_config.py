"""
高级参数微调配置
基于训练分析的针对性优化配置
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
import logging


@dataclass
class AdvancedTuningConfig:
    """高级微调配置基类"""
    
    # 基础训练参数
    sequence_length: int = 30
    epochs: int = 150
    batch_size: int = 16
    initial_learning_rate: float = 0.002
    
    # 学习率调度
    use_lr_scheduler: bool = True
    lr_scheduler_type: str = "ReduceLROnPlateau"  # ReduceLROnPlateau, CosineAnnealing, Warmup
    lr_scheduler_factor: float = 0.5
    lr_scheduler_patience: int = 10
    lr_scheduler_min_lr: float = 1e-6
    
    # 早停配置
    early_stopping_patience: int = 20
    early_stopping_min_delta: float = 1e-4
    early_stopping_restore_best: bool = True
    
    # 正则化
    weight_decay: float = 0.01
    gradient_clip_norm: float = 1.0
    dropout_rate: float = 0.3
    
    # 数据配置
    train_ratio: float = 0.85
    validation_ratio: float = 0.10
    test_ratio: float = 0.05
    
    # 模型特定配置
    lstm_config: Dict[str, Any] = field(default_factory=dict)
    transformer_config: Dict[str, Any] = field(default_factory=dict)
    multi_task_config: Dict[str, Any] = field(default_factory=dict)
    
    # 训练策略
    use_mixed_precision: bool = True
    use_data_augmentation: bool = False
    augmentation_noise_std: float = 0.01
    
    # 优化器配置
    optimizer_type: str = "AdamW"  # Adam, AdamW, SGD, RMSprop
    optimizer_betas: tuple = (0.9, 0.999)
    optimizer_eps: float = 1e-8
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.lstm_config:
            self.lstm_config = self._get_default_lstm_config()
        if not self.transformer_config:
            self.transformer_config = self._get_default_transformer_config()
        if not self.multi_task_config:
            self.multi_task_config = self._get_default_multi_task_config()
    
    def _get_default_lstm_config(self) -> Dict[str, Any]:
        """获取默认LSTM配置"""
        return {
            "hidden_size": 256,
            "num_layers": 3,
            "dropout": self.dropout_rate,
            "bidirectional": False,
            "batch_first": True,
        }
    
    def _get_default_transformer_config(self) -> Dict[str, Any]:
        """获取默认Transformer配置"""
        return {
            "d_model": 384,
            "num_heads": 12,
            "num_layers": 6,
            "dim_feedforward": 1536,
            "dropout": self.dropout_rate * 0.5,  # Transformer通常用较小的dropout
            "activation": "gelu",
        }
    
    def _get_default_multi_task_config(self) -> Dict[str, Any]:
        """获取默认多任务配置"""
        return {
            "shared_hidden_size": 384,
            "task_hidden_size": 128,
            "num_shared_layers": 4,
            "num_task_layers": 2,
            "dropout": self.dropout_rate * 0.8,
        }


class TuningTemplates:
    """微调配置模板"""
    
    @staticmethod
    def get_aggressive_learning_config() -> AdvancedTuningConfig:
        """激进学习配置 - 针对学习率和早停问题"""
        config = AdvancedTuningConfig(
            # 更高的学习率
            initial_learning_rate=0.005,
            
            # 更大的耐心值
            early_stopping_patience=30,
            lr_scheduler_patience=15,
            
            # 减少正则化
            dropout_rate=0.2,
            weight_decay=0.005,
            
            # 更温和的学习率衰减
            lr_scheduler_factor=0.7,
            lr_scheduler_min_lr=5e-6,
            
            # 更多训练轮数
            epochs=200,
        )
        
        # 调整模型配置
        config.lstm_config.update({
            "hidden_size": 320,
            "num_layers": 4,
            "dropout": 0.2,
        })
        
        config.transformer_config.update({
            "d_model": 448,
            "num_heads": 14,
            "num_layers": 8,
            "dropout": 0.1,
        })
        
        return config
    
    @staticmethod
    def get_deep_model_config() -> AdvancedTuningConfig:
        """深度模型配置 - 增加模型容量"""
        config = AdvancedTuningConfig(
            # 更大的序列长度
            sequence_length=40,
            
            # 适中的学习率
            initial_learning_rate=0.003,
            
            # 更小的批次大小以适应大模型
            batch_size=12,
            
            # 更强的正则化防止过拟合
            dropout_rate=0.35,
            weight_decay=0.015,
            
            # 更大的耐心值
            early_stopping_patience=25,
        )
        
        # 更深更宽的模型
        config.lstm_config.update({
            "hidden_size": 512,
            "num_layers": 5,
            "dropout": 0.35,
        })
        
        config.transformer_config.update({
            "d_model": 512,
            "num_heads": 16,
            "num_layers": 10,
            "dim_feedforward": 2048,
            "dropout": 0.2,
        })
        
        config.multi_task_config.update({
            "shared_hidden_size": 512,
            "task_hidden_size": 256,
            "num_shared_layers": 6,
            "num_task_layers": 3,
        })
        
        return config
    
    @staticmethod
    def get_data_enhanced_config() -> AdvancedTuningConfig:
        """数据增强配置 - 改进数据处理"""
        config = AdvancedTuningConfig(
            # 更长的序列
            sequence_length=50,
            
            # 启用数据增强
            use_data_augmentation=True,
            augmentation_noise_std=0.02,
            
            # 更多的训练数据
            train_ratio=0.88,
            validation_ratio=0.08,
            test_ratio=0.04,
            
            # 适应数据增强的参数
            initial_learning_rate=0.0025,
            epochs=180,
            early_stopping_patience=35,
        )
        
        return config
    
    @staticmethod
    def get_ensemble_optimized_config() -> AdvancedTuningConfig:
        """集成优化配置 - 针对集成学习优化"""
        config = AdvancedTuningConfig(
            # 平衡的参数设置
            initial_learning_rate=0.0035,
            sequence_length=35,
            batch_size=14,
            
            # 多样性友好的设置
            dropout_rate=0.25,
            weight_decay=0.008,
            
            # 使用余弦退火学习率
            lr_scheduler_type="CosineAnnealing",
            
            # 更长的训练以获得多样性
            epochs=250,
            early_stopping_patience=40,
        )
        
        # 为集成设计的不同架构
        config.lstm_config.update({
            "hidden_size": 384,
            "num_layers": 4,
            "bidirectional": True,  # 双向LSTM增加多样性
        })
        
        return config
    
    @staticmethod
    def get_breakthrough_config() -> AdvancedTuningConfig:
        """突破性配置 - 尝试突破当前性能瓶颈"""
        config = AdvancedTuningConfig(
            # 非常激进的学习率
            initial_learning_rate=0.008,
            
            # 非常大的模型
            sequence_length=60,
            batch_size=8,  # 小批次以适应大模型
            
            # 最小的正则化
            dropout_rate=0.15,
            weight_decay=0.003,
            
            # 最大的耐心值
            early_stopping_patience=50,
            lr_scheduler_patience=20,
            
            # 最多的训练轮数
            epochs=300,
            
            # 使用Warmup学习率
            lr_scheduler_type="Warmup",
        )
        
        # 最大的模型配置
        config.lstm_config.update({
            "hidden_size": 768,
            "num_layers": 6,
            "dropout": 0.15,
        })
        
        config.transformer_config.update({
            "d_model": 768,
            "num_heads": 24,
            "num_layers": 12,
            "dim_feedforward": 3072,
            "dropout": 0.1,
        })
        
        return config


class TuningAnalyzer:
    """参数调优分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_training_bottleneck(self, training_history: Dict[str, List[float]]) -> Dict[str, str]:
        """分析训练瓶颈"""
        analysis = {}
        
        if not training_history:
            return {"error": "没有训练历史数据"}
        
        train_losses = training_history.get("train_loss", [])
        val_losses = training_history.get("val_loss", [])
        
        if not train_losses or not val_losses:
            return {"error": "训练历史数据不完整"}
        
        # 分析早停原因
        if len(train_losses) < 50:
            analysis["early_stopping"] = "训练过早停止，建议增加early_stopping_patience"
        
        # 分析损失趋势
        if len(train_losses) > 10:
            recent_train_loss = sum(train_losses[-5:]) / 5
            recent_val_loss = sum(val_losses[-5:]) / 5
            
            if recent_train_loss > 0.8:
                analysis["high_loss"] = "损失值较高，建议提高学习率或增加模型容量"
            
            if recent_val_loss - recent_train_loss > 0.1:
                analysis["overfitting"] = "可能过拟合，建议增加正则化"
            elif recent_train_loss - recent_val_loss > 0.05:
                analysis["underfitting"] = "可能欠拟合，建议减少正则化或增加模型容量"
        
        # 分析收敛情况
        if len(train_losses) > 20:
            early_loss = sum(train_losses[:10]) / 10
            late_loss = sum(train_losses[-10:]) / 10
            improvement = (early_loss - late_loss) / early_loss
            
            if improvement < 0.1:
                analysis["slow_convergence"] = "收敛缓慢，建议提高学习率"
        
        return analysis
    
    def suggest_config_adjustments(self, analysis: Dict[str, str]) -> AdvancedTuningConfig:
        """根据分析建议配置调整"""
        base_config = AdvancedTuningConfig()
        
        if "early_stopping" in analysis:
            base_config.early_stopping_patience = 40
            base_config.epochs = 250
        
        if "high_loss" in analysis:
            base_config.initial_learning_rate = 0.004
            base_config.lstm_config["hidden_size"] = 384
            base_config.transformer_config["d_model"] = 512
        
        if "overfitting" in analysis:
            base_config.dropout_rate = 0.4
            base_config.weight_decay = 0.02
        
        if "underfitting" in analysis:
            base_config.dropout_rate = 0.2
            base_config.weight_decay = 0.005
        
        if "slow_convergence" in analysis:
            base_config.initial_learning_rate = 0.005
            base_config.lr_scheduler_factor = 0.8
        
        return base_config
