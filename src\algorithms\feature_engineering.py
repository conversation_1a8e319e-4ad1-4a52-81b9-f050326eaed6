"""
机器学习特征工程模块
用于彩票比值预测的特征提取和处理
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings

warnings.filterwarnings("ignore")


class LotteryFeatureExtractor:
    """彩票特征提取器"""

    def __init__(self, lookback_periods: int = 20):
        """
        初始化特征提取器

        Args:
            lookback_periods: 回看期数，用于构建历史特征
        """
        self.lookback_periods = lookback_periods
        self.scaler = StandardScaler()
        self.label_encoders = {}

    def extract_features(self, data: pd.DataFrame, target_index: int) -> Dict[str, Any]:
        """
        提取指定期数的特征

        Args:
            data: 历史数据
            target_index: 目标期数索引（要预测的期数）

        Returns:
            特征字典
        """
        if target_index < self.lookback_periods:
            # 如果历史数据不足，使用所有可用数据
            start_index = 0
        else:
            start_index = target_index - self.lookback_periods

        # 获取历史数据段
        history_data = data.iloc[start_index:target_index]

        features = {}

        # 1. 基础比值特征
        features.update(self._extract_ratio_features(history_data))

        # 2. 趋势特征
        features.update(self._extract_trend_features(history_data))

        # 3. 周期性特征
        features.update(self._extract_cyclical_features(history_data, target_index))

        # 4. 统计特征
        features.update(self._extract_statistical_features(history_data))

        # 5. 序列模式特征
        features.update(self._extract_sequence_features(history_data))

        return features

    def _extract_ratio_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取比值相关特征"""
        features = {}

        if len(data) == 0:
            return features

        # 计算历史比值
        red_odd_even_ratios = []
        red_size_ratios = []
        blue_size_ratios = []

        for _, row in data.iterrows():
            # 解析号码
            red_nums = [int(row[f"红球{i}"]) for i in range(1, 6)]
            blue_nums = [int(row[f"蓝球{i}"]) for i in range(1, 3)]

            # 计算比值
            red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
            red_even_count = len(red_nums) - red_odd_count
            red_odd_even_ratios.append(f"{red_odd_count}:{red_even_count}")

            from src.config.lottery_config import LotteryConfig
            red_big_count = sum(1 for x in red_nums if LotteryConfig.is_red_ball_large(x))
            red_small_count = len(red_nums) - red_big_count
            red_size_ratios.append(f"{red_big_count}:{red_small_count}")

            blue_big_count = sum(1 for x in blue_nums if LotteryConfig.is_blue_ball_large(x))
            blue_small_count = len(blue_nums) - blue_big_count
            blue_size_ratios.append(f"{blue_big_count}:{blue_small_count}")

        # 最近N期比值特征
        for i in [1, 3, 5, 10]:
            if len(red_odd_even_ratios) >= i:
                features[f"red_odd_even_last_{i}"] = red_odd_even_ratios[-i]
                features[f"red_size_last_{i}"] = red_size_ratios[-i]
                features[f"blue_size_last_{i}"] = blue_size_ratios[-i]

        # 比值频率统计
        if red_odd_even_ratios:
            from collections import Counter

            red_odd_even_counter = Counter(red_odd_even_ratios)
            red_size_counter = Counter(red_size_ratios)
            blue_size_counter = Counter(blue_size_ratios)

            # 最常见的比值
            features["red_odd_even_most_common"] = red_odd_even_counter.most_common(1)[
                0
            ][0]
            features["red_size_most_common"] = red_size_counter.most_common(1)[0][0]
            features["blue_size_most_common"] = blue_size_counter.most_common(1)[0][0]

            # 比值多样性（熵）
            features["red_odd_even_entropy"] = self._calculate_entropy(
                red_odd_even_counter
            )
            features["red_size_entropy"] = self._calculate_entropy(red_size_counter)
            features["blue_size_entropy"] = self._calculate_entropy(blue_size_counter)

        return features

    def _extract_trend_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取趋势特征"""
        features = {}

        if len(data) < 3:
            return features

        # 计算数值化的比值序列
        red_odd_ratios = []
        red_size_ratios = []
        blue_size_ratios = []

        for _, row in data.iterrows():
            red_nums = [int(row[f"红球{i}"]) for i in range(1, 6)]
            blue_nums = [int(row[f"蓝球{i}"]) for i in range(1, 3)]

            red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
            red_big_count = sum(1 for x in red_nums if x > 17)
            blue_big_count = sum(1 for x in blue_nums if x > 6)

            red_odd_ratios.append(red_odd_count / len(red_nums))
            red_size_ratios.append(red_big_count / len(red_nums))
            blue_size_ratios.append(blue_big_count / len(blue_nums))

        # 趋势方向
        features["red_odd_trend"] = self._calculate_trend(red_odd_ratios)
        features["red_size_trend"] = self._calculate_trend(red_size_ratios)
        features["blue_size_trend"] = self._calculate_trend(blue_size_ratios)

        # 趋势强度
        features["red_odd_trend_strength"] = self._calculate_trend_strength(
            red_odd_ratios
        )
        features["red_size_trend_strength"] = self._calculate_trend_strength(
            red_size_ratios
        )
        features["blue_size_trend_strength"] = self._calculate_trend_strength(
            blue_size_ratios
        )

        return features

    def _extract_cyclical_features(
        self, data: pd.DataFrame, target_index: int
    ) -> Dict[str, float]:
        """提取周期性特征"""
        features = {}

        # 周期位置特征
        features["cycle_position_7"] = target_index % 7  # 周周期
        features["cycle_position_10"] = target_index % 10  # 十期周期
        features["cycle_position_30"] = target_index % 30  # 月周期

        # 正弦余弦编码周期特征
        features["cycle_sin_7"] = np.sin(2 * np.pi * (target_index % 7) / 7)
        features["cycle_cos_7"] = np.cos(2 * np.pi * (target_index % 7) / 7)
        features["cycle_sin_10"] = np.sin(2 * np.pi * (target_index % 10) / 10)
        features["cycle_cos_10"] = np.cos(2 * np.pi * (target_index % 10) / 10)

        return features

    def _extract_statistical_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取统计特征"""
        features = {}

        if len(data) < 2:
            return features

        # 计算数值化序列
        red_odd_ratios = []
        red_size_ratios = []
        blue_size_ratios = []

        for _, row in data.iterrows():
            red_nums = [int(row[f"红球{i}"]) for i in range(1, 6)]
            blue_nums = [int(row[f"蓝球{i}"]) for i in range(1, 3)]

            red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
            red_big_count = sum(1 for x in red_nums if x > 17)
            blue_big_count = sum(1 for x in blue_nums if x > 6)

            red_odd_ratios.append(red_odd_count)
            red_size_ratios.append(red_big_count)
            blue_size_ratios.append(blue_big_count)

        # 统计特征
        for name, values in [
            ("red_odd", red_odd_ratios),
            ("red_size", red_size_ratios),
            ("blue_size", blue_size_ratios),
        ]:
            if values:
                features[f"{name}_mean"] = np.mean(values)
                features[f"{name}_std"] = np.std(values)
                features[f"{name}_min"] = np.min(values)
                features[f"{name}_max"] = np.max(values)
                features[f"{name}_median"] = np.median(values)

                # 移动平均
                if len(values) >= 3:
                    features[f"{name}_ma3"] = np.mean(values[-3:])
                if len(values) >= 5:
                    features[f"{name}_ma5"] = np.mean(values[-5:])

        return features

    def _extract_sequence_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取序列模式特征"""
        features = {}

        if len(data) < 2:
            return features

        # 计算比值序列
        red_odd_even_ratios = []
        red_size_ratios = []
        blue_size_ratios = []

        from src.config.lottery_config import LotteryConfig

        for _, row in data.iterrows():
            red_nums = [int(row[f"红球{i}"]) for i in range(1, 6)]
            blue_nums = [int(row[f"蓝球{i}"]) for i in range(1, 3)]

            red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
            red_even_count = len(red_nums) - red_odd_count
            red_odd_even_ratios.append(f"{red_odd_count}:{red_even_count}")

            red_big_count = sum(1 for x in red_nums if LotteryConfig.is_red_ball_large(x))
            red_small_count = len(red_nums) - red_big_count
            red_size_ratios.append(f"{red_big_count}:{red_small_count}")

            blue_big_count = sum(1 for x in blue_nums if LotteryConfig.is_blue_ball_large(x))
            blue_small_count = len(blue_nums) - blue_big_count
            blue_size_ratios.append(f"{blue_big_count}:{blue_small_count}")

        # 连续相同比值长度
        features["red_odd_even_consecutive"] = self._get_consecutive_length(
            red_odd_even_ratios
        )
        features["red_size_consecutive"] = self._get_consecutive_length(red_size_ratios)
        features["blue_size_consecutive"] = self._get_consecutive_length(
            blue_size_ratios
        )

        # 比值变化模式
        features["red_odd_even_changes"] = self._count_changes(red_odd_even_ratios)
        features["red_size_changes"] = self._count_changes(red_size_ratios)
        features["blue_size_changes"] = self._count_changes(blue_size_ratios)

        return features

    def _calculate_entropy(self, counter) -> float:
        """计算熵值"""
        total = sum(counter.values())
        if total == 0:
            return 0.0

        entropy = 0.0
        for count in counter.values():
            if count > 0:
                p = count / total
                entropy -= p * np.log2(p)
        return entropy

    def _calculate_trend(self, values: List[float]) -> float:
        """计算趋势方向 (-1: 下降, 0: 平稳, 1: 上升)"""
        if len(values) < 2:
            return 0.0

        # 使用线性回归斜率
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0]

        if slope > 0.01:
            return 1.0
        elif slope < -0.01:
            return -1.0
        else:
            return 0.0

    def _calculate_trend_strength(self, values: List[float]) -> float:
        """计算趋势强度"""
        if len(values) < 2:
            return 0.0

        x = np.arange(len(values))
        slope, intercept = np.polyfit(x, values, 1)

        # 计算R²
        y_pred = slope * x + intercept
        ss_res = np.sum((values - y_pred) ** 2)
        ss_tot = np.sum((values - np.mean(values)) ** 2)

        if ss_tot == 0:
            return 0.0

        r_squared = 1 - (ss_res / ss_tot)
        return max(0.0, r_squared)

    def _get_consecutive_length(self, sequence: List[str]) -> int:
        """获取序列末尾连续相同元素的长度"""
        if not sequence:
            return 0

        last_value = sequence[-1]
        count = 0

        for value in reversed(sequence):
            if value == last_value:
                count += 1
            else:
                break

        return count

    def _count_changes(self, sequence: List[str]) -> int:
        """计算序列中变化的次数"""
        if len(sequence) < 2:
            return 0

        changes = 0
        for i in range(1, len(sequence)):
            if sequence[i] != sequence[i - 1]:
                changes += 1

        return changes

    def prepare_training_data(
        self, data: pd.DataFrame, min_history: int = 10
    ) -> Tuple[pd.DataFrame, Dict[str, pd.Series]]:
        """
        准备训练数据

        Args:
            data: 原始数据
            min_history: 最小历史期数

        Returns:
            特征DataFrame和目标变量字典
        """
        features_list = []
        targets = {"red_odd_even": [], "red_size": [], "blue_size": []}

        # 从min_history开始，确保有足够的历史数据
        for i in range(min_history, len(data)):
            # 提取特征（使用1到i期的数据）
            features = self.extract_features(data, i)

            # 获取目标值（第i期的实际比值）
            target_row = data.iloc[i]
            red_nums = [int(target_row[f"红球{j}"]) for j in range(1, 6)]
            blue_nums = [int(target_row[f"蓝球{j}"]) for j in range(1, 3)]

            # 计算目标比值
            red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
            red_even_count = len(red_nums) - red_odd_count
            targets["red_odd_even"].append(f"{red_odd_count}:{red_even_count}")

            red_big_count = sum(1 for x in red_nums if x > 17)
            red_small_count = len(red_nums) - red_big_count
            targets["red_size"].append(f"{red_big_count}:{red_small_count}")

            blue_big_count = sum(1 for x in blue_nums if x > 6)
            blue_small_count = len(blue_nums) - blue_big_count
            targets["blue_size"].append(f"{blue_big_count}:{blue_small_count}")

            features_list.append(features)

        # 转换为DataFrame
        features_df = pd.DataFrame(features_list)

        # 处理分类特征
        categorical_features = [
            col for col in features_df.columns if "last_" in col or "most_common" in col
        ]

        for col in categorical_features:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()

            # 处理缺失值
            features_df[col] = features_df[col].fillna("unknown")
            features_df[col] = self.label_encoders[col].fit_transform(
                features_df[col].astype(str)
            )

        # 处理数值特征的缺失值
        numerical_features = [
            col for col in features_df.columns if col not in categorical_features
        ]
        features_df[numerical_features] = features_df[numerical_features].fillna(0)

        # 标准化数值特征
        if numerical_features:
            features_df[numerical_features] = self.scaler.fit_transform(
                features_df[numerical_features]
            )

        # 转换目标变量
        target_series = {}
        for key, values in targets.items():
            if key not in self.label_encoders:
                self.label_encoders[key] = LabelEncoder()
            target_series[key] = pd.Series(
                self.label_encoders[key].fit_transform(values)
            )

        return features_df, target_series
