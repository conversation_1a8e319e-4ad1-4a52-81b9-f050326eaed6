#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选号系统性能分析对比
对比不同选号系统的准确率和性能
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置路径
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/selection_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SelectionSystemAnalyzer:
    """选号系统分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.results = {}
        self.test_data = None
        self.systems = {}
        
        # 创建结果目录
        self.results_dir = Path("results/selection_analysis")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("选号系统分析器初始化完成")
    
    def create_test_data(self, num_periods=100):
        """创建测试数据"""
        logger.info(f"创建测试数据，共{num_periods}期...")
        
        data = []
        for i in range(num_periods):
            period = f"250{i:02d}"
            
            # 生成随机但符合规律的开奖号码
            red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
            blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
            
            # 计算比例
            red_odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
            red_even_count = 5 - red_odd_count
            red_big_count = sum(1 for ball in red_balls if ball > 17)
            red_small_count = 5 - red_big_count
            
            blue_big_count = sum(1 for ball in blue_balls if ball > 6)
            blue_small_count = 2 - blue_big_count
            
            row = {
                '期号': period,
                '红球1': red_balls[0], '红球2': red_balls[1], '红球3': red_balls[2],
                '红球4': red_balls[3], '红球5': red_balls[4],
                '蓝球1': blue_balls[0], '蓝球2': blue_balls[1],
                '红球奇偶比': f"{red_odd_count}:{red_even_count}",
                '红球大小比': f"{red_big_count}:{red_small_count}",
                '蓝球大小比': f"{blue_big_count}:{blue_small_count}"
            }
            data.append(row)
        
        self.test_data = pd.DataFrame(data)
        logger.info(f"测试数据创建完成，共{len(self.test_data)}期")
        return self.test_data
    
    def initialize_systems(self):
        """初始化各个选号系统"""
        logger.info("初始化选号系统...")
        
        # 系统1: 基础随机选号（作为基准）
        self.systems['random'] = RandomSelector()
        
        # 系统2: 频率分析选号
        self.systems['frequency'] = FrequencySelector()
        
        # 系统3: 马尔可夫链选号
        self.systems['markov'] = MarkovSelector()
        
        # 系统4: 机器学习选号
        self.systems['ml'] = MLSelector()
        
        # 系统5: 集成选号（如果可用）
        try:
            from systems.number_selection_adapter import create_selection_adapter
            self.systems['ensemble'] = EnsembleSelector()
            logger.info("集成选号系统加载成功")
        except Exception as e:
            logger.warning(f"集成选号系统加载失败: {e}")
        
        logger.info(f"共初始化{len(self.systems)}个选号系统")
    
    def run_backtest(self, test_periods=20):
        """运行回测分析"""
        logger.info(f"开始回测分析，测试{test_periods}期...")
        
        if self.test_data is None:
            self.create_test_data()
        
        # 确保有足够的数据
        if len(self.test_data) < test_periods + 10:
            logger.warning("测试数据不足，重新生成更多数据")
            self.create_test_data(test_periods + 50)
        
        # 为每个系统运行回测
        for system_name, system in self.systems.items():
            logger.info(f"测试系统: {system_name}")
            
            system_results = {
                'predictions': [],
                'actual_results': [],
                'hit_rates': [],
                'red_hit_rates': [],
                'blue_hit_rates': [],
                'prediction_times': [],
                'periods': []
            }
            
            # 使用前面的数据作为训练，后面的数据作为测试
            train_end = len(self.test_data) - test_periods
            
            for i in range(test_periods):
                test_index = train_end + i
                if test_index >= len(self.test_data):
                    break
                
                # 获取训练数据
                train_data = self.test_data.iloc[:train_end + i]
                
                # 获取当期实际结果
                actual_row = self.test_data.iloc[test_index]
                actual_red = [actual_row[f'红球{j}'] for j in range(1, 6)]
                actual_blue = [actual_row[f'蓝球{j}'] for j in range(1, 3)]
                
                # 进行预测
                start_time = time.time()
                try:
                    predicted_red, predicted_blue = system.predict(train_data, actual_row['期号'])
                    prediction_time = time.time() - start_time
                    
                    # 计算命中率
                    red_hits = len(set(predicted_red) & set(actual_red))
                    blue_hits = len(set(predicted_blue) & set(actual_blue))
                    
                    red_hit_rate = red_hits / 5.0
                    blue_hit_rate = blue_hits / 2.0
                    total_hit_rate = (red_hits + blue_hits) / 7.0
                    
                    # 存储结果
                    system_results['predictions'].append({
                        'red_balls': predicted_red,
                        'blue_balls': predicted_blue
                    })
                    system_results['actual_results'].append({
                        'red_balls': actual_red,
                        'blue_balls': actual_blue
                    })
                    system_results['hit_rates'].append(total_hit_rate)
                    system_results['red_hit_rates'].append(red_hit_rate)
                    system_results['blue_hit_rates'].append(blue_hit_rate)
                    system_results['prediction_times'].append(prediction_time)
                    system_results['periods'].append(actual_row['期号'])
                    
                    logger.debug(f"期号{actual_row['期号']}: 总命中率{total_hit_rate:.3f}, "
                               f"红球{red_hit_rate:.3f}, 蓝球{blue_hit_rate:.3f}")
                    
                except Exception as e:
                    logger.error(f"系统{system_name}预测期号{actual_row['期号']}时出错: {e}")
                    continue
            
            self.results[system_name] = system_results
            
            # 计算系统总体性能
            if system_results['hit_rates']:
                avg_hit_rate = np.mean(system_results['hit_rates'])
                avg_red_hit_rate = np.mean(system_results['red_hit_rates'])
                avg_blue_hit_rate = np.mean(system_results['blue_hit_rates'])
                avg_time = np.mean(system_results['prediction_times'])
                
                logger.info(f"系统{system_name}性能:")
                logger.info(f"  平均总命中率: {avg_hit_rate:.3f}")
                logger.info(f"  平均红球命中率: {avg_red_hit_rate:.3f}")
                logger.info(f"  平均蓝球命中率: {avg_blue_hit_rate:.3f}")
                logger.info(f"  平均预测时间: {avg_time:.3f}秒")
        
        logger.info("回测分析完成")
    
    def generate_comparison_report(self):
        """生成对比报告"""
        logger.info("生成对比报告...")
        
        if not self.results:
            logger.error("没有测试结果，无法生成报告")
            return
        
        # 计算各系统的综合指标
        summary_data = []
        
        for system_name, results in self.results.items():
            if not results['hit_rates']:
                continue
            
            metrics = {
                '系统名称': system_name,
                '平均总命中率': np.mean(results['hit_rates']),
                '平均红球命中率': np.mean(results['red_hit_rates']),
                '平均蓝球命中率': np.mean(results['blue_hit_rates']),
                '命中率标准差': np.std(results['hit_rates']),
                '最高命中率': np.max(results['hit_rates']),
                '最低命中率': np.min(results['hit_rates']),
                '平均预测时间': np.mean(results['prediction_times']),
                '稳定性指数': 1.0 - (np.std(results['hit_rates']) / (np.mean(results['hit_rates']) + 1e-8)),
                '测试期数': len(results['hit_rates'])
            }
            summary_data.append(metrics)
        
        # 创建汇总表
        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('平均总命中率', ascending=False)
        
        # 保存详细报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = self.results_dir / f'selection_comparison_{timestamp}.xlsx'
        
        with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
            # 汇总表
            summary_df.to_excel(writer, sheet_name='系统性能汇总', index=False)
            
            # 各系统详细结果
            for system_name, results in self.results.items():
                if not results['hit_rates']:
                    continue
                
                detail_data = []
                for i in range(len(results['periods'])):
                    detail_data.append({
                        '期号': results['periods'][i],
                        '预测红球': str(results['predictions'][i]['red_balls']),
                        '实际红球': str(results['actual_results'][i]['red_balls']),
                        '预测蓝球': str(results['predictions'][i]['blue_balls']),
                        '实际蓝球': str(results['actual_results'][i]['blue_balls']),
                        '总命中率': results['hit_rates'][i],
                        '红球命中率': results['red_hit_rates'][i],
                        '蓝球命中率': results['blue_hit_rates'][i],
                        '预测时间': results['prediction_times'][i]
                    })
                
                detail_df = pd.DataFrame(detail_data)
                detail_df.to_excel(writer, sheet_name=f'{system_name}_详细结果', index=False)
        
        logger.info(f"对比报告已保存: {report_path}")
        
        # 打印汇总结果
        print("\n" + "="*80)
        print("选号系统性能对比汇总")
        print("="*80)
        print(summary_df.to_string(index=False, float_format='%.3f'))
        
        # 找出最佳系统
        best_system = summary_df.iloc[0]
        print(f"\n最佳系统: {best_system['系统名称']}")
        print(f"   平均总命中率: {best_system['平均总命中率']:.3f}")
        print(f"   平均红球命中率: {best_system['平均红球命中率']:.3f}")
        print(f"   平均蓝球命中率: {best_system['平均蓝球命中率']:.3f}")
        print(f"   稳定性指数: {best_system['稳定性指数']:.3f}")
        
        return summary_df


# 各种选号系统的实现
class RandomSelector:
    """随机选号器（基准）"""
    
    def predict(self, train_data, period):
        """随机预测"""
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        return red_balls, blue_balls


class FrequencySelector:
    """频率分析选号器"""
    
    def predict(self, train_data, period):
        """基于历史频率预测"""
        # 统计红球频率
        red_freq = {}
        for i in range(1, 6):
            for ball in train_data[f'红球{i}']:
                red_freq[ball] = red_freq.get(ball, 0) + 1
        
        # 统计蓝球频率
        blue_freq = {}
        for i in range(1, 3):
            for ball in train_data[f'蓝球{i}']:
                blue_freq[ball] = blue_freq.get(ball, 0) + 1
        
        # 选择频率最高的号码
        red_sorted = sorted(red_freq.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_freq.items(), key=lambda x: x[1], reverse=True)
        
        red_balls = sorted([ball for ball, _ in red_sorted[:5]])
        blue_balls = sorted([ball for ball, _ in blue_sorted[:2]])
        
        # 如果数量不够，随机补充
        while len(red_balls) < 5:
            candidate = np.random.randint(1, 36)
            if candidate not in red_balls:
                red_balls.append(candidate)
        
        while len(blue_balls) < 2:
            candidate = np.random.randint(1, 13)
            if candidate not in blue_balls:
                blue_balls.append(candidate)
        
        return sorted(red_balls), sorted(blue_balls)


class MarkovSelector:
    """马尔可夫链选号器"""
    
    def predict(self, train_data, period):
        """基于马尔可夫链预测"""
        # 简化的马尔可夫链实现
        # 分析号码转移概率
        
        if len(train_data) < 2:
            # 数据不足，使用随机选号
            return RandomSelector().predict(train_data, period)
        
        # 获取最近的号码作为状态
        last_red = [train_data.iloc[-1][f'红球{i}'] for i in range(1, 6)]
        last_blue = [train_data.iloc[-1][f'蓝球{i}'] for i in range(1, 3)]
        
        # 基于最近号码进行调整
        red_balls = []
        for ball in last_red:
            # 随机调整±1-3
            adjustment = np.random.randint(-3, 4)
            new_ball = max(1, min(35, ball + adjustment))
            if new_ball not in red_balls:
                red_balls.append(new_ball)
        
        # 补充到5个
        while len(red_balls) < 5:
            candidate = np.random.randint(1, 36)
            if candidate not in red_balls:
                red_balls.append(candidate)
        
        blue_balls = []
        for ball in last_blue:
            adjustment = np.random.randint(-2, 3)
            new_ball = max(1, min(12, ball + adjustment))
            if new_ball not in blue_balls:
                blue_balls.append(new_ball)
        
        while len(blue_balls) < 2:
            candidate = np.random.randint(1, 13)
            if candidate not in blue_balls:
                blue_balls.append(candidate)
        
        return sorted(red_balls), sorted(blue_balls)


class MLSelector:
    """机器学习选号器"""
    
    def __init__(self):
        self.model = None
    
    def predict(self, train_data, period):
        """基于机器学习预测"""
        try:
            from sklearn.ensemble import RandomForestRegressor
            
            if len(train_data) < 10:
                return RandomSelector().predict(train_data, period)
            
            # 准备特征
            features = []
            targets_red = []
            targets_blue = []
            
            for i in range(len(train_data) - 1):
                # 使用当期号码作为特征预测下期
                feature = []
                for j in range(1, 6):
                    feature.append(train_data.iloc[i][f'红球{j}'])
                for j in range(1, 3):
                    feature.append(train_data.iloc[i][f'蓝球{j}'])
                
                features.append(feature)
                
                # 下期号码作为目标
                next_red = [train_data.iloc[i+1][f'红球{j}'] for j in range(1, 6)]
                next_blue = [train_data.iloc[i+1][f'蓝球{j}'] for j in range(1, 3)]
                
                targets_red.append(next_red)
                targets_blue.append(next_blue)
            
            if len(features) < 5:
                return RandomSelector().predict(train_data, period)
            
            # 训练模型
            X = np.array(features)
            y_red = np.array(targets_red)
            y_blue = np.array(targets_blue)
            
            # 红球预测
            rf_red = RandomForestRegressor(n_estimators=10, random_state=42)
            rf_red.fit(X, y_red)
            
            # 蓝球预测
            rf_blue = RandomForestRegressor(n_estimators=10, random_state=42)
            rf_blue.fit(X, y_blue)
            
            # 使用最后一期数据进行预测
            last_feature = []
            for j in range(1, 6):
                last_feature.append(train_data.iloc[-1][f'红球{j}'])
            for j in range(1, 3):
                last_feature.append(train_data.iloc[-1][f'蓝球{j}'])
            
            pred_red = rf_red.predict([last_feature])[0]
            pred_blue = rf_blue.predict([last_feature])[0]
            
            # 转换为整数并确保在有效范围内
            red_balls = []
            for val in pred_red:
                ball = max(1, min(35, int(round(val))))
                if ball not in red_balls:
                    red_balls.append(ball)
            
            blue_balls = []
            for val in pred_blue:
                ball = max(1, min(12, int(round(val))))
                if ball not in blue_balls:
                    blue_balls.append(ball)
            
            # 补充到所需数量
            while len(red_balls) < 5:
                candidate = np.random.randint(1, 36)
                if candidate not in red_balls:
                    red_balls.append(candidate)
            
            while len(blue_balls) < 2:
                candidate = np.random.randint(1, 13)
                if candidate not in blue_balls:
                    blue_balls.append(candidate)
            
            return sorted(red_balls[:5]), sorted(blue_balls[:2])
            
        except Exception as e:
            logger.warning(f"ML预测失败，使用随机选号: {e}")
            return RandomSelector().predict(train_data, period)


class EnsembleSelector:
    """集成选号器"""
    
    def __init__(self):
        self.selectors = [
            FrequencySelector(),
            MarkovSelector(),
            MLSelector()
        ]
    
    def predict(self, train_data, period):
        """集成预测"""
        all_red_votes = {}
        all_blue_votes = {}
        
        # 收集所有选号器的投票
        for selector in self.selectors:
            try:
                red_balls, blue_balls = selector.predict(train_data, period)
                
                # 红球投票
                for ball in red_balls:
                    all_red_votes[ball] = all_red_votes.get(ball, 0) + 1
                
                # 蓝球投票
                for ball in blue_balls:
                    all_blue_votes[ball] = all_blue_votes.get(ball, 0) + 1
                    
            except Exception as e:
                logger.warning(f"集成选号器中某个子选号器失败: {e}")
                continue
        
        # 选择得票最高的号码
        red_sorted = sorted(all_red_votes.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(all_blue_votes.items(), key=lambda x: x[1], reverse=True)
        
        red_balls = [ball for ball, _ in red_sorted[:5]]
        blue_balls = [ball for ball, _ in blue_sorted[:2]]
        
        # 补充不足的号码
        while len(red_balls) < 5:
            candidate = np.random.randint(1, 36)
            if candidate not in red_balls:
                red_balls.append(candidate)
        
        while len(blue_balls) < 2:
            candidate = np.random.randint(1, 13)
            if candidate not in blue_balls:
                blue_balls.append(candidate)
        
        return sorted(red_balls), sorted(blue_balls)


def main():
    """主函数"""
    print("="*80)
    print("选号系统性能分析对比")
    print("="*80)
    
    # 创建分析器
    analyzer = SelectionSystemAnalyzer()
    
    # 创建测试数据
    analyzer.create_test_data(150)  # 创建150期数据
    
    # 初始化系统
    analyzer.initialize_systems()
    
    # 运行回测（测试最后30期）
    analyzer.run_backtest(test_periods=30)
    
    # 生成对比报告
    summary_df = analyzer.generate_comparison_report()
    
    print("\n分析完成！")
    print("详细报告已保存到 results/selection_analysis/ 目录")
    
    return summary_df


if __name__ == "__main__":
    try:
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        # 运行分析
        result = main()
        
    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()