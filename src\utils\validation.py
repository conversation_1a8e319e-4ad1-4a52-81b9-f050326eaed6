"""
数据验证工具函数
提供数据验证和检查功能
"""

from typing import List, Tuple, Dict, Any
import pandas as pd


def validate_numbers(red_balls: List[int], blue_balls: List[int]) -> bool:
    """
    验证号码的有效性

    Args:
        red_balls: 红球号码列表
        blue_balls: 蓝球号码列表

    Returns:
        bool: 是否有效
    """
    # 检查红球
    if len(red_balls) != 5:
        return False

    if not all(1 <= num <= 35 for num in red_balls):
        return False

    if len(set(red_balls)) != 5:  # 检查重复
        return False

    # 检查蓝球 - 大乐透格式：2个蓝球
    if len(blue_balls) != 2:
        return False

    if not all(1 <= num <= 12 for num in blue_balls):
        return False

    if len(set(blue_balls)) != 2:  # 检查重复
        return False

    return True


def validate_predictions(predictions: Dict[str, Any]) -> bool:
    """
    验证预测结果的有效性

    Args:
        predictions: 预测结果字典

    Returns:
        bool: 是否有效
    """
    required_keys = ["red_odd_even", "red_size", "blue_size"]

    for key in required_keys:
        if key not in predictions:
            return False

    # 验证预测值格式
    valid_red_states = ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]
    valid_blue_states = ["0:2", "1:1", "2:0"]

    if predictions["red_odd_even"] not in valid_red_states:
        return False

    if predictions["red_size"] not in valid_red_states:
        return False

    if predictions["blue_size"] not in valid_blue_states:
        return False

    return True


def validate_config(config: Dict[str, Any]) -> bool:
    """
    验证配置的有效性

    Args:
        config: 配置字典

    Returns:
        bool: 是否有效
    """
    # 基本配置验证
    if not isinstance(config, dict):
        return False

    # 可以根据需要添加更多验证逻辑
    return True


def validate_data_frame(df: pd.DataFrame) -> Tuple[bool, List[str]]:
    """
    验证DataFrame的有效性

    Args:
        df: 要验证的DataFrame

    Returns:
        Tuple[bool, List[str]]: (是否有效, 错误信息列表)
    """
    errors = []

    # 检查必要列
    required_columns = [
        "期号",
        "红球1",
        "红球2",
        "红球3",
        "红球4",
        "红球5",
        "蓝球1",
        "蓝球2",
    ]
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        errors.append(f"缺少必要列: {missing_columns}")

    # 检查数据类型
    for i in range(1, 6):
        col = f"红球{i}"
        if col in df.columns and not pd.api.types.is_numeric_dtype(df[col]):
            errors.append(f"列 {col} 不是数值类型")

    for i in range(1, 3):
        col = f"蓝球{i}"
        if col in df.columns and not pd.api.types.is_numeric_dtype(df[col]):
            errors.append(f"列 {col} 不是数值类型")

    # 检查数据范围
    if len(errors) == 0:  # 只有在基本结构正确时才检查数据范围
        for _, row in df.iterrows():
            try:
                red_balls = [int(row[f"红球{i}"]) for i in range(1, 6)]
                blue_balls = [int(row[f"蓝球{i}"]) for i in range(1, 3)]

                if not validate_numbers(red_balls, blue_balls):
                    errors.append(f"期号 {row['期号']}: 号码无效")
                    break  # 只报告第一个错误

            except (ValueError, KeyError) as e:
                errors.append(f"期号 {row['期号']}: 数据格式错误 - {e}")
                break

    return len(errors) == 0, errors
