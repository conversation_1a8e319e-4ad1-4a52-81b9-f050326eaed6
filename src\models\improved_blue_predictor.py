class ImprovedBlueBallPredictor:
    """改进的蓝球大小比预测器"""

    def __init__(self):
        self.name = "改进蓝球预测器"
        self.transitions = {}
        self.frequency_weights = {}

    def train(self, data):
        """训练预测器"""
        blue_ratios = []

        for _, row in data.iterrows():
            blue1, blue2 = int(row["蓝球1"]), int(row["蓝球2"])
            large_count = sum(1 for b in [blue1, blue2] if b >= 8)
            small_count = 2 - large_count
            ratio = f"{large_count}:{small_count}"
            blue_ratios.append(ratio)

        # 构建转移矩阵
        for i in range(len(blue_ratios) - 1):
            current = blue_ratios[i]
            next_state = blue_ratios[i + 1]

            if current not in self.transitions:
                self.transitions[current] = {}
            if next_state not in self.transitions[current]:
                self.transitions[current][next_state] = 0
            self.transitions[current][next_state] += 1

        # 计算频率权重
        from collections import Counter

        ratio_counts = Counter(blue_ratios)
        total = len(blue_ratios)

        for ratio, count in ratio_counts.items():
            self.frequency_weights[ratio] = count / total

    def predict(self, recent_history):
        """预测下一期蓝球大小比"""
        if not recent_history:
            return "1:1", 0.5

        last_ratio = recent_history[-1]

        # 马尔科夫预测
        if last_ratio in self.transitions:
            transitions = self.transitions[last_ratio]
            if transitions:
                total_transitions = sum(transitions.values())
                best_ratio = max(transitions.keys(), key=lambda x: transitions[x])
                confidence = transitions[best_ratio] / total_transitions
                return best_ratio, confidence

        # 回退到频率预测
        if self.frequency_weights:
            best_ratio = max(
                self.frequency_weights.keys(), key=lambda x: self.frequency_weights[x]
            )
            confidence = self.frequency_weights[best_ratio]
            return best_ratio, confidence

        return "1:1", 0.5
