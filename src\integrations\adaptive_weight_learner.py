#!/usr/bin/env python3
"""
自适应权重学习器 - Phase 3: 智能权重学习系统
使用强化学习和自适应算法动态调整权重
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import deque, defaultdict
import time
import json

@dataclass
class LearningState:
    """学习状态类"""
    weights: Dict[str, float]
    performance: float
    timestamp: float
    action_taken: str
    reward: float = 0.0

@dataclass
class LearningConfig:
    """学习配置类"""
    learning_rate: float = 0.01
    exploration_rate: float = 0.1
    exploration_decay: float = 0.995
    min_exploration_rate: float = 0.01
    memory_size: int = 1000
    batch_size: int = 32
    target_update_frequency: int = 10
    reward_discount: float = 0.95

class AdaptiveWeightLearner:
    """自适应权重学习器"""
    
    def __init__(self, config: Optional[LearningConfig] = None):
        self.config = config or LearningConfig()
        
        # 学习状态
        self.current_state = None
        self.learning_history = deque(maxlen=self.config.memory_size)
        self.performance_history = deque(maxlen=100)
        
        # Q-Learning相关
        self.q_table = defaultdict(lambda: defaultdict(float))
        self.state_action_counts = defaultdict(lambda: defaultdict(int))
        
        # 权重学习
        self.weight_performance_map = defaultdict(list)
        self.weight_trends = defaultdict(deque)
        
        # 自适应参数
        self.adaptive_lr = self.config.learning_rate
        self.exploration_rate = self.config.exploration_rate
        
        # 动作空间定义
        self.actions = [
            'increase_small',    # 小幅增加 (+0.05)
            'increase_medium',   # 中幅增加 (+0.1)
            'increase_large',    # 大幅增加 (+0.2)
            'decrease_small',    # 小幅减少 (-0.05)
            'decrease_medium',   # 中幅减少 (-0.1)
            'decrease_large',    # 大幅减少 (-0.2)
            'maintain'           # 保持不变
        ]
        
        # 状态特征
        self.state_features = [
            'performance_level',     # 性能水平 (low/medium/high)
            'performance_trend',     # 性能趋势 (declining/stable/improving)
            'weight_level',         # 权重水平 (low/medium/high)
            'weight_balance'        # 权重平衡性 (unbalanced/balanced)
        ]
    
    def learn_from_experience(self, weights: Dict[str, float], 
                            performance: float,
                            target_metric: str = 'hit_rate_2_plus_1') -> Dict[str, float]:
        """从经验中学习并返回调整后的权重"""
        
        # 创建当前状态
        current_state = LearningState(
            weights=weights.copy(),
            performance=performance,
            timestamp=time.time(),
            action_taken='initial'
        )
        
        # 如果有历史状态，计算奖励并更新Q表
        if self.current_state is not None:
            reward = self._calculate_reward(self.current_state.performance, performance)
            self.current_state.reward = reward
            self._update_q_table(self.current_state, current_state, reward)
            self.learning_history.append(self.current_state)
        
        # 更新性能历史
        self.performance_history.append(performance)
        
        # 选择动作并调整权重
        adjusted_weights = {}
        for weight_name, weight_value in weights.items():
            state_key = self._encode_state(weight_name, weight_value, performance)
            action = self._select_action(state_key, weight_name)
            new_weight = self._apply_action(weight_value, action, weight_name)
            adjusted_weights[weight_name] = new_weight
            
            # 记录权重趋势
            self.weight_trends[weight_name].append(new_weight)
            if len(self.weight_trends[weight_name]) > 20:
                self.weight_trends[weight_name].popleft()
        
        # 更新当前状态
        current_state.weights = adjusted_weights
        current_state.action_taken = 'adaptive_adjustment'
        self.current_state = current_state
        
        # 自适应参数更新
        self._update_adaptive_parameters()
        
        return adjusted_weights
    
    def _calculate_reward(self, prev_performance: float, current_performance: float) -> float:
        """计算奖励"""
        # 基础奖励：性能改进
        performance_improvement = current_performance - prev_performance
        base_reward = performance_improvement * 10  # 放大奖励信号
        
        # 稳定性奖励：避免剧烈波动
        if len(self.performance_history) >= 3:
            recent_performances = list(self.performance_history)[-3:]
            stability = 1.0 - np.std(recent_performances)
            stability_reward = stability * 2
        else:
            stability_reward = 0
        
        # 目标奖励：接近目标性能
        target_performance = 0.4  # 目标2+1命中率
        distance_to_target = abs(current_performance - target_performance)
        target_reward = max(0, 2 - distance_to_target * 5)
        
        # 综合奖励
        total_reward = base_reward + stability_reward + target_reward
        
        return total_reward
    
    def _encode_state(self, weight_name: str, weight_value: float, performance: float) -> str:
        """编码状态为字符串键"""
        # 性能水平
        if performance < 0.2:
            perf_level = 'low'
        elif performance < 0.35:
            perf_level = 'medium'
        else:
            perf_level = 'high'
        
        # 性能趋势
        if len(self.performance_history) >= 3:
            recent_trend = np.mean(list(self.performance_history)[-3:]) - np.mean(list(self.performance_history)[-6:-3])
            if recent_trend < -0.02:
                perf_trend = 'declining'
            elif recent_trend > 0.02:
                perf_trend = 'improving'
            else:
                perf_trend = 'stable'
        else:
            perf_trend = 'stable'
        
        # 权重水平
        if weight_value < 0.2:
            weight_level = 'low'
        elif weight_value < 0.6:
            weight_level = 'medium'
        else:
            weight_level = 'high'
        
        # 权重平衡性（基于当前权重分布）
        if hasattr(self, 'current_state') and self.current_state:
            weight_values = list(self.current_state.weights.values())
            weight_std = np.std(weight_values)
            weight_balance = 'balanced' if weight_std < 0.2 else 'unbalanced'
        else:
            weight_balance = 'balanced'
        
        state_key = f"{weight_name}_{perf_level}_{perf_trend}_{weight_level}_{weight_balance}"
        return state_key
    
    def _select_action(self, state_key: str, weight_name: str) -> str:
        """选择动作（ε-贪婪策略）"""
        # 探索 vs 利用
        if np.random.random() < self.exploration_rate:
            # 探索：随机选择动作
            action = np.random.choice(self.actions)
        else:
            # 利用：选择Q值最高的动作
            q_values = self.q_table[state_key]
            if q_values:
                action = max(q_values.items(), key=lambda x: x[1])[0]
            else:
                # 如果没有Q值，使用启发式选择
                action = self._heuristic_action_selection(weight_name)
        
        return action
    
    def _heuristic_action_selection(self, weight_name: str) -> str:
        """启发式动作选择"""
        # 基于权重名称的启发式规则
        priority_weights = ['red_odd_even', 'red_size', 'blue_size', 'bayesian', 'frequency']
        
        if weight_name in priority_weights:
            # 高优先级权重倾向于增加
            return np.random.choice(['increase_small', 'increase_medium', 'maintain'], p=[0.4, 0.3, 0.3])
        else:
            # 其他权重保持平衡
            return np.random.choice(['increase_small', 'decrease_small', 'maintain'], p=[0.3, 0.3, 0.4])
    
    def _apply_action(self, current_weight: float, action: str, weight_name: str) -> float:
        """应用动作到权重"""
        action_map = {
            'increase_small': 0.05,
            'increase_medium': 0.1,
            'increase_large': 0.2,
            'decrease_small': -0.05,
            'decrease_medium': -0.1,
            'decrease_large': -0.2,
            'maintain': 0.0
        }
        
        adjustment = action_map.get(action, 0.0)
        new_weight = current_weight + adjustment
        
        # 应用约束
        if weight_name in ['red_odd_even', 'red_size', 'blue_size']:
            # 特征权重约束
            new_weight = max(0.1, min(1.0, new_weight))
        elif 'loss' in weight_name:
            # 损失权重约束
            new_weight = max(0.01, min(5.0, new_weight))
        else:
            # 其他权重约束
            new_weight = max(0.01, min(1.0, new_weight))
        
        return new_weight
    
    def _update_q_table(self, prev_state: LearningState, current_state: LearningState, reward: float):
        """更新Q表"""
        for weight_name in prev_state.weights:
            prev_state_key = self._encode_state(weight_name, prev_state.weights[weight_name], prev_state.performance)
            current_state_key = self._encode_state(weight_name, current_state.weights[weight_name], current_state.performance)
            
            # Q-Learning更新
            current_q = self.q_table[prev_state_key][prev_state.action_taken]
            
            # 找到当前状态的最大Q值
            max_future_q = 0
            if self.q_table[current_state_key]:
                max_future_q = max(self.q_table[current_state_key].values())
            
            # Q值更新
            new_q = current_q + self.adaptive_lr * (reward + self.config.reward_discount * max_future_q - current_q)
            self.q_table[prev_state_key][prev_state.action_taken] = new_q
            
            # 更新访问计数
            self.state_action_counts[prev_state_key][prev_state.action_taken] += 1
    
    def _update_adaptive_parameters(self):
        """更新自适应参数"""
        # 探索率衰减
        self.exploration_rate = max(
            self.config.min_exploration_rate,
            self.exploration_rate * self.config.exploration_decay
        )
        
        # 自适应学习率
        if len(self.performance_history) >= 10:
            recent_variance = np.var(list(self.performance_history)[-10:])
            # 如果性能波动大，增加学习率；如果稳定，减少学习率
            if recent_variance > 0.01:
                self.adaptive_lr = min(0.05, self.adaptive_lr * 1.1)
            else:
                self.adaptive_lr = max(0.001, self.adaptive_lr * 0.95)
    
    def get_learning_insights(self) -> Dict[str, Any]:
        """获取学习洞察"""
        insights = {
            'learning_progress': {
                'total_experiences': len(self.learning_history),
                'exploration_rate': self.exploration_rate,
                'adaptive_learning_rate': self.adaptive_lr,
                'q_table_size': len(self.q_table)
            },
            'performance_analysis': {},
            'weight_analysis': {},
            'recommendations': []
        }
        
        # 性能分析
        if self.performance_history:
            performances = list(self.performance_history)
            insights['performance_analysis'] = {
                'current_performance': performances[-1] if performances else 0,
                'average_performance': np.mean(performances),
                'performance_trend': self._calculate_trend(performances),
                'performance_stability': 1.0 / (1.0 + np.std(performances))
            }
        
        # 权重分析
        for weight_name, trend_data in self.weight_trends.items():
            if trend_data:
                trend_values = list(trend_data)
                insights['weight_analysis'][weight_name] = {
                    'current_value': trend_values[-1],
                    'average_value': np.mean(trend_values),
                    'trend': self._calculate_trend(trend_values),
                    'stability': 1.0 / (1.0 + np.std(trend_values))
                }
        
        # 生成建议
        insights['recommendations'] = self._generate_learning_recommendations(insights)
        
        return insights
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势"""
        if len(values) < 3:
            return 'insufficient_data'
        
        # 简单线性趋势
        x = np.arange(len(values))
        slope = np.polyfit(x, values, 1)[0]
        
        if slope > 0.01:
            return 'increasing'
        elif slope < -0.01:
            return 'decreasing'
        else:
            return 'stable'
    
    def _generate_learning_recommendations(self, insights: Dict[str, Any]) -> List[str]:
        """生成学习建议"""
        recommendations = []
        
        # 基于探索率的建议
        if self.exploration_rate < 0.05:
            recommendations.append("探索率较低，可能陷入局部最优，建议增加探索")
        
        # 基于性能的建议
        perf_analysis = insights.get('performance_analysis', {})
        if perf_analysis.get('performance_trend') == 'decreasing':
            recommendations.append("性能呈下降趋势，建议调整学习策略")
        
        if perf_analysis.get('performance_stability', 0) < 0.5:
            recommendations.append("性能不稳定，建议降低学习率或增加正则化")
        
        # 基于权重的建议
        weight_analysis = insights.get('weight_analysis', {})
        unstable_weights = [name for name, data in weight_analysis.items() 
                          if data.get('stability', 1) < 0.6]
        if unstable_weights:
            recommendations.append(f"权重 {unstable_weights} 不稳定，建议调整约束或学习率")
        
        return recommendations
    
    def save_learning_state(self, filepath: str):
        """保存学习状态"""
        state_data = {
            'q_table': dict(self.q_table),
            'state_action_counts': dict(self.state_action_counts),
            'exploration_rate': self.exploration_rate,
            'adaptive_lr': self.adaptive_lr,
            'performance_history': list(self.performance_history),
            'weight_trends': {k: list(v) for k, v in self.weight_trends.items()}
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, indent=2, ensure_ascii=False)
    
    def load_learning_state(self, filepath: str):
        """加载学习状态"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 恢复Q表
            self.q_table = defaultdict(lambda: defaultdict(float))
            for state_key, actions in state_data.get('q_table', {}).items():
                for action, q_value in actions.items():
                    self.q_table[state_key][action] = q_value
            
            # 恢复其他状态
            self.state_action_counts = defaultdict(lambda: defaultdict(int), state_data.get('state_action_counts', {}))
            self.exploration_rate = state_data.get('exploration_rate', self.config.exploration_rate)
            self.adaptive_lr = state_data.get('adaptive_lr', self.config.learning_rate)
            
            # 恢复历史数据
            perf_history = state_data.get('performance_history', [])
            self.performance_history = deque(perf_history, maxlen=100)
            
            weight_trends = state_data.get('weight_trends', {})
            for weight_name, trend_data in weight_trends.items():
                self.weight_trends[weight_name] = deque(trend_data, maxlen=20)
            
            print(f"学习状态已从 {filepath} 加载")
            
        except Exception as e:
            print(f"加载学习状态失败: {e}")

def create_adaptive_weight_learner(config: Optional[LearningConfig] = None) -> AdaptiveWeightLearner:
    """创建自适应权重学习器实例"""
    return AdaptiveWeightLearner(config)

if __name__ == "__main__":
    # 测试自适应权重学习器
    learner = create_adaptive_weight_learner()
    
    print("自适应权重学习器测试")
    print("=" * 50)
    
    # 模拟学习过程
    test_weights = {
        'red_odd_even': 0.3,
        'red_size': 0.4,
        'blue_size': 0.2,
        'bayesian': 0.15,
        'frequency': 0.25
    }
    
    print("模拟学习过程...")
    for i in range(10):
        # 模拟性能变化
        performance = 0.2 + i * 0.02 + np.random.normal(0, 0.01)
        performance = max(0, min(1, performance))
        
        # 学习并调整权重
        adjusted_weights = learner.learn_from_experience(test_weights, performance)
        test_weights = adjusted_weights
        
        print(f"  第{i+1}轮: 性能={performance:.3f}, 探索率={learner.exploration_rate:.3f}")
    
    # 获取学习洞察
    insights = learner.get_learning_insights()
    print(f"\n学习洞察:")
    print(f"  总经验数: {insights['learning_progress']['total_experiences']}")
    print(f"  Q表大小: {insights['learning_progress']['q_table_size']}")
    print(f"  当前性能: {insights['performance_analysis'].get('current_performance', 0):.3f}")
    print(f"  性能趋势: {insights['performance_analysis'].get('performance_trend', 'unknown')}")
    
    if insights['recommendations']:
        print(f"\n建议:")
        for rec in insights['recommendations']:
            print(f"  - {rec}")
    
    print("\n自适应权重学习器测试完成！")