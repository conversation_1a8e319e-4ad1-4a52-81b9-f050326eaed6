"""奇偶比决策树预测器

专门用于预测红球奇偶比的决策树模型。
"""

from typing import List, Tuple, Optional, Dict, Any
import random
from .decision_tree_predictor import DecisionTreePredictor


class OddEvenTreePredictor(DecisionTreePredictor):
    """奇偶比决策树预测器"""
    
    def __init__(self, **kwargs: Any) -> None:
        """初始化奇偶比决策树预测器
        
        Args:
            **kwargs: 传递给父类的参数
        """
        super().__init__(**kwargs)
    
    def _generate_numbers(self, odd_even_pred: List[Tuple[str, float]], 
                         red_size_pred: List[Tuple[str, float]], 
                         blue_size_pred: List[Tuple[str, float]]) -> Tuple[List[int], List[int]]:
        """
        根据奇偶比预测生成号码
        
        Args:
            odd_even_pred: 奇偶比预测结果
            red_size_pred: 红球大小比预测结果
            blue_size_pred: 蓝球大小预测结果
            
        Returns:
            (红球列表, 蓝球列表)
        """
        # 获取最可能的奇偶比
        target_odd_even = odd_even_pred[0][0] if odd_even_pred else '3:3'
        target_red_size = red_size_pred[0][0] if red_size_pred else '3:3'
        target_blue_size = blue_size_pred[0][0] if blue_size_pred else '大'
        
        # 解析奇偶比
        if ':' in target_odd_even:
            odd_count, even_count = map(int, target_odd_even.split(':'))
        else:
            odd_count, even_count = 3, 3
        
        # 解析红球大小比
        if ':' in target_red_size:
            small_count, big_count = map(int, target_red_size.split(':'))
        else:
            small_count, big_count = 3, 3
        
        # 生成红球
        red_balls = self._generate_red_balls(odd_count, even_count, small_count, big_count)
        
        # 生成蓝球
        blue_balls = self._generate_blue_balls(target_blue_size)
        
        return red_balls, blue_balls
    
    def _generate_red_balls(self, odd_count: int, even_count: int, 
                           small_count: int, big_count: int) -> List[int]:
        """
        生成满足奇偶比和大小比要求的红球
        
        Args:
            odd_count: 奇数个数
            even_count: 偶数个数
            small_count: 小数个数（1-18）- 使用统一标准
            big_count: 大数个数（19-35）- 使用统一标准
            
        Returns:
            红球列表
        """
        # 定义号码池 - 使用统一标准
        odd_small = [i for i in range(1, 19) if i % 2 == 1]  # 1-18中的奇数
        odd_big = [i for i in range(19, 36) if i % 2 == 1]   # 19-35中的奇数
        even_small = [i for i in range(1, 19) if i % 2 == 0] # 1-18中的偶数
        even_big = [i for i in range(19, 36) if i % 2 == 0]  # 19-35中的偶数
        
        red_balls = []
        
        # 计算各类别需要的数量
        # 使用贪心算法分配
        odd_small_need = min(odd_count, small_count, len(odd_small))
        odd_big_need = min(odd_count - odd_small_need, big_count, len(odd_big))
        even_small_need = min(even_count, small_count - odd_small_need, len(even_small))
        even_big_need = min(even_count - even_small_need, big_count - odd_big_need, len(even_big))
        
        # 调整以确保总数为6
        total_selected = odd_small_need + odd_big_need + even_small_need + even_big_need
        if total_selected < 6:
            # 需要补充号码
            remaining = 6 - total_selected
            all_remaining = []
            
            # 收集剩余可选号码
            used_odd_small = set()
            used_odd_big = set()
            used_even_small = set()
            used_even_big = set()
            
            if odd_small_need < len(odd_small):
                all_remaining.extend([n for n in odd_small if n not in used_odd_small])
            if odd_big_need < len(odd_big):
                all_remaining.extend([n for n in odd_big if n not in used_odd_big])
            if even_small_need < len(even_small):
                all_remaining.extend([n for n in even_small if n not in used_even_small])
            if even_big_need < len(even_big):
                all_remaining.extend([n for n in even_big if n not in used_even_big])
            
            # 随机补充
            if len(all_remaining) >= remaining:
                additional = random.sample(all_remaining, remaining)
                # 根据补充的号码类型调整计数
                for num in additional:
                    if num <= 16 and num % 2 == 1:
                        odd_small_need += 1
                    elif num > 16 and num % 2 == 1:
                        odd_big_need += 1
                    elif num <= 16 and num % 2 == 0:
                        even_small_need += 1
                    else:
                        even_big_need += 1
        
        # 生成号码
        try:
            if odd_small_need > 0:
                red_balls.extend(random.sample(odd_small, min(odd_small_need, len(odd_small))))
            if odd_big_need > 0:
                red_balls.extend(random.sample(odd_big, min(odd_big_need, len(odd_big))))
            if even_small_need > 0:
                red_balls.extend(random.sample(even_small, min(even_small_need, len(even_small))))
            if even_big_need > 0:
                red_balls.extend(random.sample(even_big, min(even_big_need, len(even_big))))
            
            # 如果还不够6个，随机补充
            if len(red_balls) < 6:
                all_numbers = list(range(1, 34))
                remaining_numbers = [n for n in all_numbers if n not in red_balls]
                needed = 6 - len(red_balls)
                if len(remaining_numbers) >= needed:
                    red_balls.extend(random.sample(remaining_numbers, needed))
                else:
                    red_balls.extend(remaining_numbers)
            
            # 确保只有6个号码
            red_balls = red_balls[:6]
            
        except ValueError as e:
            self.logger.warning(f"生成红球时出错: {e}，使用随机生成")
            red_balls = random.sample(range(1, 34), 6)
        
        return sorted(red_balls)
    
    def _generate_blue_balls(self, target_size: str) -> List[int]:
        """
        生成蓝球
        
        Args:
            target_size: 目标大小（'大' 或 '小'）
            
        Returns:
            蓝球列表
        """
        if target_size == '大':
            # 蓝球大号：7-12
            blue_pool = list(range(7, 13))
        else:
            # 蓝球小号：1-6
            blue_pool = list(range(1, 7))
        
        return [random.choice(blue_pool)]
    
    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return "OddEvenTreePredictor"
    
    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return "1.0.0"