"""
GPU配置和优化工具 - PyTorch版本
支持RTX 3060 12GB的GPU优化配置
"""

import torch
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class GPUConfig:
    """GPU配置管理器"""

    def __init__(self):
        self.gpu_available = False
        self.device = torch.device("cpu")
        self.device_name = "cpu"
        self.gpu_count = 0
        self.gpu_name = None
        self.memory_limit = None
        self.mixed_precision_enabled = False
        self.setup_gpu()

    def setup_gpu(self):
        """设置GPU配置"""
        try:
            # 检查GPU可用性
            self.gpu_available = torch.cuda.is_available()

            if self.gpu_available:
                self.gpu_count = torch.cuda.device_count()
                self.device = torch.device("cuda:0")
                self.device_name = "cuda:0"
                self.gpu_name = torch.cuda.get_device_name(0)

                logger.info(f"检测到 {self.gpu_count} 个GPU设备")
                logger.info(f"GPU名称: {self.gpu_name}")
                logger.info(f"CUDA版本: {torch.version.cuda}")

                # 设置内存限制（为3060 12G预留一些内存）
                self.set_memory_limit(10240)  # 10GB，预留2GB给系统

                # 启用混合精度训练
                self.enable_mixed_precision()

            else:
                logger.warning("未检测到GPU设备，将使用CPU训练")

        except Exception as e:
            logger.error(f"GPU配置失败: {e}")
            self.gpu_available = False
            self.device = torch.device("cpu")
            self.device_name = "cpu"

    def set_memory_limit(self, memory_mb: int):
        """设置GPU内存限制"""
        if not self.gpu_available:
            return

        try:
            # PyTorch中通过设置环境变量或使用torch.cuda.set_per_process_memory_fraction
            memory_fraction = memory_mb / (12 * 1024)  # 3060有12GB显存
            if memory_fraction <= 1.0:
                torch.cuda.set_per_process_memory_fraction(memory_fraction, device=0)
                self.memory_limit = memory_mb
                logger.info(f"GPU内存限制设置为 {memory_mb}MB ({memory_fraction:.2f})")
            else:
                logger.warning(f"内存限制 {memory_mb}MB 超过GPU总内存")
        except Exception as e:
            logger.warning(f"无法设置GPU内存限制: {e}")

    def get_device(self):
        """获取PyTorch设备"""
        return self.device

    def get_optimized_config(self) -> Dict[str, Any]:
        """获取针对GPU优化的训练配置"""
        if self.gpu_available:
            return {
                "batch_size": 64,  # GPU可以处理更大的batch size
                "epochs": 50,  # GPU训练更快，可以增加epochs
                "learning_rate": 0.001,
                "use_mixed_precision": True,  # 启用混合精度训练
                "device": self.device,
                "num_workers": 4,  # 数据加载并行度
                "pin_memory": True,  # 加速GPU数据传输
            }
        else:
            return {
                "batch_size": 32,  # CPU使用较小的batch size
                "epochs": 20,  # CPU训练较慢，减少epochs
                "learning_rate": 0.0005,
                "use_mixed_precision": False,
                "device": self.device,
                "num_workers": 0,  # CPU不使用多进程
                "pin_memory": False,
            }

    def enable_mixed_precision(self):
        """启用混合精度训练（仅GPU）"""
        if self.gpu_available:
            try:
                # PyTorch的混合精度通过torch.cuda.amp实现
                self.mixed_precision_enabled = True
                logger.info("混合精度训练已启用 (torch.cuda.amp)")
                return True
            except Exception as e:
                logger.warning(f"无法启用混合精度训练: {e}")
        return False

    def test_gpu_performance(self):
        """测试GPU性能"""
        if not self.gpu_available:
            logger.info("GPU不可用，跳过性能测试")
            return None

        try:
            import time

            # GPU测试
            device_gpu = torch.device("cuda:0")
            start_time = time.time()
            a = torch.randn(1000, 1000, device=device_gpu)
            b = torch.randn(1000, 1000, device=device_gpu)
            c = torch.matmul(a, b)
            torch.sum(c)  # 确保计算完成
            torch.cuda.synchronize()  # 等待GPU计算完成
            gpu_time = time.time() - start_time

            # CPU对比测试
            device_cpu = torch.device("cpu")
            start_time = time.time()
            a = torch.randn(1000, 1000, device=device_cpu)
            b = torch.randn(1000, 1000, device=device_cpu)
            c = torch.matmul(a, b)
            torch.sum(c)
            cpu_time = time.time() - start_time

            speedup = cpu_time / gpu_time
            logger.info(
                f"GPU性能测试完成 - GPU: {gpu_time:.3f}s, CPU: {cpu_time:.3f}s, 加速比: {speedup:.2f}x"
            )

            return {"gpu_time": gpu_time, "cpu_time": cpu_time, "speedup": speedup}

        except Exception as e:
            logger.error(f"GPU性能测试失败: {e}")
            return None


# 全局GPU配置实例
gpu_config = GPUConfig()


def get_gpu_config() -> GPUConfig:
    """获取全局GPU配置"""
    return gpu_config


def is_gpu_available() -> bool:
    """检查GPU是否可用"""
    return gpu_config.gpu_available


def get_device():
    """获取当前使用的PyTorch设备"""
    return gpu_config.device


def get_device_name() -> str:
    """获取当前使用的设备名称"""
    return gpu_config.device_name


def get_optimized_training_config() -> Dict[str, Any]:
    """获取优化的训练配置"""
    return gpu_config.get_optimized_config()


def to_device(tensor_or_model, device=None):
    """将tensor或模型移动到指定设备"""
    if device is None:
        device = gpu_config.device
    return tensor_or_model.to(device)
