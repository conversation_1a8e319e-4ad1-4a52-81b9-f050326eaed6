"""
V4.0 Transformer优化训练器
Optimized Trainer for V4.0 Transformer
解决训练问题并实现超参数优化
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
import json
import time
from datetime import datetime
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# TensorFlow导入
try:
    import tensorflow as tf
    from tensorflow import keras
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.model_selection import train_test_split
    TF_AVAILABLE = True
except ImportError:
    print("[WARN] TensorFlow或sklearn不可用，使用简化模式")
    TF_AVAILABLE = False

# 导入V4.0组件
from v4_transformer_config import V4TransformerConfig, V4ConfigTemplates
from v4_transformer_predictor import V4TransformerPredictor


class V4OptimizedTrainer:
    """V4.0 Transformer优化训练器"""
    
    def __init__(self, config: Optional[V4TransformerConfig] = None):
        self.config = config or V4TransformerConfig()
        self.project_root = Path(project_root)
        self.data_path = self.project_root / "data" / "raw" / "dlt_data.csv"
        self.models_dir = self.project_root / "models" / "v4_trained"
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 训练历史
        self.training_results = {}
        self.best_model = None
        self.best_score = 0.0
        
    def load_and_prepare_data(self) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """加载并准备训练数据"""
        print("[INFO] 加载并准备训练数据...")
        
        # 读取数据
        df = pd.read_csv(self.data_path, header=None)
        df.columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2', '日期']
        
        # 数据类型转换
        numeric_cols = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 删除空值
        df = df.dropna().reset_index(drop=True)
        
        # 特征工程
        df = self._enhanced_feature_engineering(df)
        
        # 数据统计
        data_stats = {
            "total_periods": len(df),
            "date_range": f"{df['期号'].min():.0f} - {df['期号'].max():.0f}",
            "features_count": len([col for col in df.columns if col not in ['期号', '日期']]),
            "data_quality": "good" if df.isnull().sum().sum() == 0 else "has_nulls"
        }
        
        print(f"[OK] 数据准备完成: {data_stats['total_periods']} 期，{data_stats['features_count']} 个特征")
        return df, data_stats
    
    def _enhanced_feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """增强特征工程"""
        print("[INFO] 执行增强特征工程...")
        
        red_balls = ['红球1', '红球2', '红球3', '红球4', '红球5']
        blue_balls = ['蓝球1', '蓝球2']
        
        # 基础比例特征
        df['红球奇数个数'] = df[red_balls].apply(lambda row: sum(x % 2 == 1 for x in row), axis=1)
        df['红球偶数个数'] = 5 - df['红球奇数个数']
        df['红球大数个数'] = df[red_balls].apply(lambda row: sum(x > 17 for x in row), axis=1)
        df['红球小数个数'] = 5 - df['红球大数个数']
        
        df['蓝球大数个数'] = df[blue_balls].apply(lambda row: sum(x > 6 for x in row), axis=1)
        df['蓝球小数个数'] = 2 - df['蓝球大数个数']
        
        # 统计特征
        df['红球和值'] = df[red_balls].sum(axis=1)
        df['红球均值'] = df[red_balls].mean(axis=1)
        df['红球方差'] = df[red_balls].var(axis=1)
        df['红球跨度'] = df[red_balls].max(axis=1) - df[red_balls].min(axis=1)
        
        df['蓝球和值'] = df[blue_balls].sum(axis=1)
        df['蓝球跨度'] = df[blue_balls].max(axis=1) - df[blue_balls].min(axis=1)
        
        # 区间分布特征
        for i, (start, end) in enumerate([(1, 7), (8, 14), (15, 21), (22, 28), (29, 35)]):
            df[f'红球区间{i+1}个数'] = df[red_balls].apply(
                lambda row: sum(start <= x <= end for x in row), axis=1
            )
        
        # 连号特征
        df['红球连号个数'] = df[red_balls].apply(self._count_consecutive, axis=1)
        df['蓝球连号个数'] = df[blue_balls].apply(self._count_consecutive, axis=1)
        
        # 历史趋势特征（滑动窗口）
        window_size = 5
        for col in ['红球奇数个数', '红球大数个数', '蓝球大数个数']:
            df[f'{col}_趋势'] = df[col].rolling(window=window_size, min_periods=1).mean()
            df[f'{col}_波动'] = df[col].rolling(window=window_size, min_periods=1).std().fillna(0)
        
        print("[OK] 增强特征工程完成")
        return df
    
    def _count_consecutive(self, row) -> int:
        """计算连号个数"""
        sorted_nums = sorted(row)
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive_count += 1
        return consecutive_count
    
    def prepare_training_sequences(self, df: pd.DataFrame) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """准备训练序列数据"""
        print("[INFO] 准备训练序列数据...")
        
        sequences = []
        targets = {
            'red_odd_even': [],
            'red_size': [],
            'blue_size': [],
            'red_numbers': [],
            'blue_numbers': []
        }
        
        seq_len = self.config.sequence_length
        
        # 选择特征列
        feature_cols = [
            '红球奇数个数', '红球大数个数', '蓝球大数个数',
            '红球和值', '红球均值', '红球方差', '红球跨度',
            '蓝球和值', '蓝球跨度',
            '红球区间1个数', '红球区间2个数', '红球区间3个数', '红球区间4个数', '红球区间5个数',
            '红球连号个数', '蓝球连号个数',
            '红球奇数个数_趋势', '红球大数个数_趋势', '蓝球大数个数_趋势',
            '红球奇数个数_波动', '红球大数个数_波动', '蓝球大数个数_波动'
        ]
        
        # 确保所有特征列都存在
        available_features = [col for col in feature_cols if col in df.columns]
        print(f"[INFO] 使用 {len(available_features)} 个特征: {available_features[:5]}...")
        
        for i in range(seq_len, len(df)):
            # 输入序列特征
            sequence_features = []
            for j in range(i - seq_len, i):
                row_features = [df.iloc[j][col] for col in available_features]
                sequence_features.append(row_features)
            
            sequences.append(sequence_features)
            
            # 目标值（下一期的结果）
            target_row = df.iloc[i]
            
            # 比例目标
            targets['red_odd_even'].append(target_row['红球奇数个数'])
            targets['red_size'].append(target_row['红球大数个数'])
            targets['blue_size'].append(target_row['蓝球大数个数'])
            
            # 号码目标（one-hot编码）
            red_balls = [target_row[f'红球{k}'] for k in range(1, 6)]
            blue_balls = [target_row[f'蓝球{k}'] for k in range(1, 3)]
            
            red_target = np.zeros(35)
            blue_target = np.zeros(12)
            
            for ball in red_balls:
                if 1 <= ball <= 35:
                    red_target[int(ball) - 1] = 1
            for ball in blue_balls:
                if 1 <= ball <= 12:
                    blue_target[int(ball) - 1] = 1
            
            targets['red_numbers'].append(red_target)
            targets['blue_numbers'].append(blue_target)
        
        if not sequences:
            raise ValueError("无法生成训练序列")
        
        X = np.array(sequences, dtype=np.float32)
        
        # 特征标准化
        scaler = StandardScaler()
        X_reshaped = X.reshape(-1, X.shape[-1])
        X_scaled = scaler.fit_transform(X_reshaped)
        X = X_scaled.reshape(X.shape)
        
        # 转换目标
        y = {}
        if TF_AVAILABLE:
            y['red_odd_even'] = tf.keras.utils.to_categorical(targets['red_odd_even'], num_classes=6)
            y['red_size'] = tf.keras.utils.to_categorical(targets['red_size'], num_classes=6)
            y['blue_size'] = tf.keras.utils.to_categorical(targets['blue_size'], num_classes=3)
        else:
            y['red_odd_even'] = np.array(targets['red_odd_even'])
            y['red_size'] = np.array(targets['red_size'])
            y['blue_size'] = np.array(targets['blue_size'])
        
        y['red_numbers'] = np.array(targets['red_numbers'], dtype=np.float32)
        y['blue_numbers'] = np.array(targets['blue_numbers'], dtype=np.float32)
        
        print(f"[OK] 训练序列准备完成: {X.shape[0]} 个序列，{X.shape[1]} 时间步，{X.shape[2]} 特征")
        return X, y, scaler
    
    def build_optimized_model(self, input_shape: Tuple[int, int]) -> 'tf.keras.Model':
        """构建优化的V4.0 Transformer模型"""
        if not TF_AVAILABLE:
            raise RuntimeError("TensorFlow不可用，无法构建模型")
        
        print("[INFO] 构建优化的V4.0 Transformer模型...")
        
        # 输入层
        inputs = keras.layers.Input(shape=input_shape, name='sequence_input')
        
        # 简化的特征提取 - 使用LSTM替代复杂的Transformer
        x = keras.layers.LSTM(
            self.config.d_model, 
            return_sequences=True, 
            dropout=self.config.dropout_rate,
            name='lstm_1'
        )(inputs)
        
        x = keras.layers.LSTM(
            self.config.d_model // 2, 
            return_sequences=False,
            dropout=self.config.dropout_rate,
            name='lstm_2'
        )(x)
        
        # 共享特征提取
        shared_features = keras.layers.Dense(
            self.config.d_model, 
            activation='relu', 
            name='shared_features'
        )(x)
        shared_features = keras.layers.Dropout(self.config.dropout_rate)(shared_features)
        
        # 多任务输出头
        outputs = {}
        
        # 红球奇偶比预测
        red_odd_even = keras.layers.Dense(64, activation='relu')(shared_features)
        red_odd_even = keras.layers.Dropout(0.3)(red_odd_even)
        outputs['red_odd_even'] = keras.layers.Dense(
            6, activation='softmax', name='red_odd_even'
        )(red_odd_even)
        
        # 红球大小比预测
        red_size = keras.layers.Dense(64, activation='relu')(shared_features)
        red_size = keras.layers.Dropout(0.3)(red_size)
        outputs['red_size'] = keras.layers.Dense(
            6, activation='softmax', name='red_size'
        )(red_size)
        
        # 蓝球大小比预测
        blue_size = keras.layers.Dense(32, activation='relu')(shared_features)
        blue_size = keras.layers.Dropout(0.3)(blue_size)
        outputs['blue_size'] = keras.layers.Dense(
            3, activation='softmax', name='blue_size'
        )(blue_size)
        
        # 红球号码预测
        red_numbers = keras.layers.Dense(128, activation='relu')(shared_features)
        red_numbers = keras.layers.Dropout(0.4)(red_numbers)
        outputs['red_numbers'] = keras.layers.Dense(
            35, activation='sigmoid', name='red_numbers'
        )(red_numbers)
        
        # 蓝球号码预测
        blue_numbers = keras.layers.Dense(64, activation='relu')(shared_features)
        blue_numbers = keras.layers.Dropout(0.4)(blue_numbers)
        outputs['blue_numbers'] = keras.layers.Dense(
            12, activation='sigmoid', name='blue_numbers'
        )(blue_numbers)
        
        # 创建模型
        model = keras.Model(inputs=inputs, outputs=outputs, name='V4_Transformer_Optimized')
        
        # 编译模型
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=self.config.learning_rate),
            loss={
                'red_odd_even': 'categorical_crossentropy',
                'red_size': 'categorical_crossentropy',
                'blue_size': 'categorical_crossentropy',
                'red_numbers': 'binary_crossentropy',
                'blue_numbers': 'binary_crossentropy'
            },
            loss_weights={
                'red_odd_even': 0.25,
                'red_size': 0.25,
                'blue_size': 0.15,
                'red_numbers': 0.2,
                'blue_numbers': 0.15
            },
            metrics={
                'red_odd_even': 'accuracy',
                'red_size': 'accuracy',
                'blue_size': 'accuracy',
                'red_numbers': 'binary_accuracy',
                'blue_numbers': 'binary_accuracy'
            }
        )
        
        print(f"[OK] 模型构建完成，参数量: {model.count_params():,}")
        return model
    

    
    def train_optimized_model(self, X: np.ndarray, y: Dict[str, np.ndarray], 
                            scaler: StandardScaler) -> Dict[str, Any]:
        """训练优化模型"""
        print("\n=== V4.0 Transformer优化训练 ===")
        
        if not TF_AVAILABLE:
            print("[ERROR] TensorFlow不可用，无法训练")
            return {"success": False, "error": "TensorFlow不可用"}
        
        # 构建模型
        model = self.build_optimized_model((X.shape[1], X.shape[2]))
        
        # 数据分割
        X_train, X_val, y_train_dict, y_val_dict = self._split_data(X, y)
        
        # 设置回调
        callbacks = self._setup_training_callbacks()
        
        # 训练模型
        start_time = time.time()
        print(f"[INFO] 开始训练，训练数据: {X_train.shape[0]} 个序列")
        
        try:
            history = model.fit(
                X_train, y_train_dict,
                validation_data=(X_val, y_val_dict),
                batch_size=self.config.batch_size,
                epochs=self.config.epochs,
                callbacks=callbacks,
                verbose=1
            )
            
            training_time = time.time() - start_time
            
            # 评估模型
            val_results = model.evaluate(X_val, y_val_dict, verbose=0)
            
            # 保存模型
            model_path = self.models_dir / f"v4_optimized_{datetime.now().strftime('%Y%m%d_%H%M%S')}.h5"
            model.save(str(model_path))
            
            # 保存scaler
            scaler_path = self.models_dir / f"scaler_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
            import pickle
            with open(scaler_path, 'wb') as f:
                pickle.dump(scaler, f)
            
            training_results = {
                "success": True,
                "training_time": training_time,
                "model_path": str(model_path),
                "scaler_path": str(scaler_path),
                "training_samples": X_train.shape[0],
                "validation_samples": X_val.shape[0],
                "final_loss": history.history['loss'][-1],
                "final_val_loss": history.history['val_loss'][-1],
                "history": {k: v[-5:] for k, v in history.history.items()},  # 保存最后5个epoch
                "validation_results": dict(zip(model.metrics_names, val_results))
            }
            
            print(f"[OK] 训练完成！耗时: {training_time:.2f}秒")
            print(f"[OK] 模型已保存: {model_path}")
            print(f"[OK] 最终验证损失: {training_results['final_val_loss']:.4f}")
            
            self.best_model = model
            self.training_results = training_results
            
            return training_results
            
        except Exception as e:
            print(f"[ERROR] 训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}
    
    def _split_data(self, X: np.ndarray, y: Dict[str, np.ndarray]) -> Tuple:
        """分割训练和验证数据"""
        split_idx = int(len(X) * (1 - self.config.validation_split))
        
        X_train, X_val = X[:split_idx], X[split_idx:]
        
        y_train_dict = {}
        y_val_dict = {}
        
        for key, values in y.items():
            y_train_dict[key] = values[:split_idx]
            y_val_dict[key] = values[split_idx:]
        
        return X_train, X_val, y_train_dict, y_val_dict
    
    def _setup_training_callbacks(self) -> List:
        """设置训练回调"""
        callbacks = []
        
        # 早停
        early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=self.config.early_stopping_patience,
            restore_best_weights=True,
            verbose=1
        )
        callbacks.append(early_stopping)
        
        # 学习率调度
        lr_scheduler = keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            min_lr=1e-7,
            verbose=1
        )
        callbacks.append(lr_scheduler)
        
        return callbacks
    
    def run_complete_optimization(self) -> Dict[str, Any]:
        """运行完整优化流程"""
        print("V4.0 Transformer 优化训练系统")
        print("=" * 60)
        
        complete_results = {
            "start_time": time.time(),
            "data_preparation": {},
            "model_training": {},
            "success": False
        }
        
        try:
            # 1. 数据准备
            print("\n[STEP 1] 数据加载和特征工程")
            df, data_stats = self.load_and_prepare_data()
            complete_results["data_preparation"] = data_stats
            
            # 2. 序列数据准备
            print("\n[STEP 2] 训练序列准备")
            X, y, scaler = self.prepare_training_sequences(df)
            
            # 3. 模型训练
            print("\n[STEP 3] 优化模型训练")
            training_results = self.train_optimized_model(X, y, scaler)
            complete_results["model_training"] = training_results
            
            complete_results["success"] = training_results.get("success", False)
            complete_results["end_time"] = time.time()
            complete_results["total_duration"] = complete_results["end_time"] - complete_results["start_time"]
            
            if complete_results["success"]:
                print(f"\n[SUCCESS] V4.0优化训练完成！总耗时: {complete_results['total_duration']:.2f}秒")
            else:
                print(f"\n[ERROR] V4.0优化训练失败")
            
        except Exception as e:
            complete_results["error"] = str(e)
            print(f"\n[ERROR] 优化训练流程失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 保存结果
        results_path = self.models_dir / "optimization_results.json"
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(complete_results, f, indent=2, ensure_ascii=False, default=str)
        
        return complete_results


def main():
    """主函数"""
    # 创建优化配置
    config = V4TransformerConfig()
    config.epochs = 50  # 增加训练轮数
    config.batch_size = 16  # 适中的批次大小
    config.learning_rate = 0.001  # 适中的学习率
    config.early_stopping_patience = 10  # 早停耐心
    
    # 创建优化训练器
    trainer = V4OptimizedTrainer(config)
    
    # 运行优化训练
    results = trainer.run_complete_optimization()
    
    return results["success"]


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)