"""
主配置文件
管理所有系统配置参数
"""

import os
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class DataConfig:
    """数据相关配置"""
    data_file: str = "data/raw/dlt_data.csv"
    processed_data_dir: str = "data/processed"
    external_data_dir: str = "data/external"
    backup_enabled: bool = True
    validation_enabled: bool = True


@dataclass
class ModelConfig:
    """模型相关配置"""
    # 马尔科夫模型
    markov_order: int = 2
    markov_smoothing: float = 0.1
    
    # 贝叶斯模型
    bayes_prior_weight: float = 0.4
    bayes_recent_window: int = 20
    
    # 神经网络
    neural_hidden_layers: List[int] = field(default_factory=lambda: [64, 32, 16])
    neural_dropout_rate: float = 0.2
    neural_learning_rate: float = 0.001
    neural_epochs: int = 100
    neural_batch_size: int = 32
    
    # 集成学习
    ensemble_weights: Dict[str, float] = field(default_factory=lambda: {
        'markov': 0.3,
        'bayes': 0.3,
        'neural': 0.4
    })


@dataclass
class GeneratorConfig:
    """号码生成器配置"""
    max_attempts: int = 1000
    diversity_threshold: float = 0.7
    precision_weight: float = 0.8
    dynamic_seed_enabled: bool = True
    
    # 杀号配置
    kill_numbers_per_position: int = 2
    kill_success_rate_threshold: float = 0.9
    
    # 验证配置
    sum_range_tolerance: int = 20
    consecutive_limit: int = 3


@dataclass
class BacktestConfig:
    """回测配置"""
    default_periods: int = 10  # 统一为10期，与主程序和优化器一致
    display_periods: int = 10  # 统一为10期显示
    min_training_periods: int = 0  # 统一为0，使用所有可用训练数据
    validation_split: float = 0.2
    
    # 性能指标阈值
    feature_hit_rate_threshold: float = 0.8
    hit_2_plus_1_threshold: float = 0.6
    kill_success_threshold: float = 0.9


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_enabled: bool = True
    file_path: str = "logs/lottery_predictor.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class Settings:
    """主配置类"""
    # 基础配置
    project_name: str = "大乐透预测系统"
    version: str = "2.0.0"
    debug: bool = False
    
    # 子配置
    data: DataConfig = field(default_factory=DataConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    generator: GeneratorConfig = field(default_factory=GeneratorConfig)
    backtest: BacktestConfig = field(default_factory=BacktestConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # 环境变量覆盖
    def __post_init__(self) -> None:
        """从环境变量加载配置"""
        # 数据配置
        data_file = os.getenv('DATA_FILE')
        if data_file:
            self.data.data_file = data_file
        
        # 模型配置
        markov_order = os.getenv('MARKOV_ORDER')
        if markov_order:
            self.model.markov_order = int(markov_order)

        neural_epochs = os.getenv('NEURAL_EPOCHS')
        if neural_epochs:
            self.model.neural_epochs = int(neural_epochs)
        
        # 回测配置
        backtest_periods = os.getenv('BACKTEST_PERIODS')
        if backtest_periods:
            self.backtest.default_periods = int(backtest_periods)

        # 日志配置
        log_level = os.getenv('LOG_LEVEL')
        if log_level:
            self.logging.level = log_level

        debug_env = os.getenv('DEBUG')
        if debug_env:
            self.debug = debug_env.lower() == 'true'
    
    def get_data_path(self, relative_path: str) -> Path:
        """获取数据文件的绝对路径"""
        return Path(relative_path)
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        try:
            # 验证数据文件存在
            data_path = self.get_data_path(self.data.data_file)
            if not data_path.exists():
                raise FileNotFoundError(f"数据文件不存在: {data_path}")
            
            # 验证模型参数
            if self.model.markov_order < 1:
                raise ValueError("马尔科夫阶数必须大于0")
            
            if not 0 < self.model.bayes_prior_weight < 1:
                raise ValueError("贝叶斯先验权重必须在0-1之间")
            
            # 验证回测参数
            if self.backtest.default_periods < self.backtest.min_training_periods:
                raise ValueError("回测期数不能小于最小训练期数")
            
            return True
            
        except Exception as e:
            print(f"配置验证失败: {e}")
            return False


# 全局配置实例
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """获取全局配置实例（单例模式）"""
    global _settings
    if _settings is None:
        _settings = Settings()
        if not _settings.validate():
            raise RuntimeError("配置验证失败")
    return _settings


def reload_settings() -> Settings:
    """重新加载配置"""
    global _settings
    _settings = None
    return get_settings()


def update_settings(**kwargs: Any) -> Settings:
    """更新配置参数"""
    settings = get_settings()
    for key, value in kwargs.items():
        if hasattr(settings, key):
            setattr(settings, key, value)
    return settings
