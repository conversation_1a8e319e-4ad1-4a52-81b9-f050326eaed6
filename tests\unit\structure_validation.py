#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构验证脚本 - 验证重构后的项目结构
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_directory_structure():
    """测试目录结构"""
    print("测试目录结构...")
    
    try:
        # 检查关键目录是否存在
        directories = [
            "src",
            "src/core",
            "src/models", 
            "src/algorithms",
            "src/decision_tree",
            "src/systems",
            "src/utils",
            "tests",
            "tests/unit",
            "docs",
            "scripts",
            "config"
        ]
        
        for dir_path in directories:
            full_path = project_root / dir_path
            if full_path.exists():
                print(f"[OK] 目录存在: {dir_path}")
            else:
                print(f"[WARN] 目录缺失: {dir_path}")
        
        return True
    except Exception as e:
        print(f"[FAIL] 目录结构测试失败: {e}")
        return False

def test_file_cleanup():
    """测试文件清理结果"""
    print("\n测试文件清理结果...")
    
    try:
        # 检查已删除的重复文件
        deleted_files = [
            "src/config/config_manager.py",
            "src/core/configuration_manager.py", 
            "src/decision_tree/odd_even_tree.py",
            "src/decision_tree/size_ratio_tree.py",
            "src/models/neural/neural_predictor.py",
            "src/models/unified_predictor.py",
            "src/models/deep_learning/v4_integration_test.py",
            "src/models/deep_learning/v4_transformer_test.py",
            "src/models/deep_learning/v4_validation.py",
            "src/models/deep_learning/v4_quick_optimizer.py"
        ]
        
        deleted_count = 0
        for file_path in deleted_files:
            full_path = project_root / file_path
            if not full_path.exists():
                deleted_count += 1
                print(f"[OK] 文件已删除: {file_path}")
            else:
                print(f"[WARN] 文件仍存在: {file_path}")
        
        print(f"[INFO] 已删除文件数量: {deleted_count}/{len(deleted_files)}")
        
        # 检查保留的核心文件
        preserved_files = [
            "src/core/config_manager.py",
            "src/core/exceptions.py",
            "src/decision_tree/improved_odd_even_tree.py", 
            "src/models/enhanced_size_ratio_predictor.py",
            "src/models/unified_odd_even_predictor.py",
            "src/models/deep_learning/v4_transformer_predictor.py",
            "src/models/deep_learning/v4_transformer_config.py",
            "src/models/deep_learning/v4_training_evaluation.py"
        ]
        
        preserved_count = 0
        for file_path in preserved_files:
            full_path = project_root / file_path
            if full_path.exists():
                preserved_count += 1
                print(f"[OK] 核心文件保留: {file_path}")
            else:
                print(f"[WARN] 核心文件缺失: {file_path}")
        
        print(f"[INFO] 保留核心文件数量: {preserved_count}/{len(preserved_files)}")
        
        return True
    except Exception as e:
        print(f"[FAIL] 文件清理测试失败: {e}")
        return False

def test_project_files():
    """测试项目配置文件"""
    print("\n测试项目配置文件...")
    
    try:
        # 检查项目配置文件
        config_files = [
            "README.md",
            "pyproject.toml", 
            "main.py"
        ]
        
        for file_path in config_files:
            full_path = project_root / file_path
            if full_path.exists():
                print(f"[OK] 配置文件存在: {file_path}")
            else:
                print(f"[WARN] 配置文件缺失: {file_path}")
        
        # 检查README是否包含重构说明
        readme_path = project_root / "README.md"
        if readme_path.exists():
            content = readme_path.read_text(encoding='utf-8')
            if "项目重构说明" in content:
                print("[OK] README包含重构说明")
            else:
                print("[WARN] README缺少重构说明")
        
        return True
    except Exception as e:
        print(f"[FAIL] 项目文件测试失败: {e}")
        return False

def test_documentation():
    """测试文档组织"""
    print("\n测试文档组织...")
    
    try:
        docs_dir = project_root / "docs"
        if docs_dir.exists():
            doc_files = list(docs_dir.glob("*.md"))
            print(f"[OK] docs目录存在，包含{len(doc_files)}个markdown文件")
            
            for doc_file in doc_files:
                print(f"[INFO] 文档文件: {doc_file.name}")
        else:
            print("[WARN] docs目录不存在")
        
        return True
    except Exception as e:
        print(f"[FAIL] 文档测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("项目结构验证测试")
    print("=" * 60)
    
    tests = [
        test_directory_structure,
        test_file_cleanup,
        test_project_files,
        test_documentation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("[SUCCESS] 项目结构验证完全通过！")
        print("[SUCCESS] 重构任务成功完成")
        return True
    else:
        print("[WARNING] 部分验证失败，需要检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)