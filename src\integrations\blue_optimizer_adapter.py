"""
蓝球优化器集成适配器
将增强版蓝球优化器集成到主系统中
"""

import logging
from typing import Dict, List, Tuple, Optional
import pandas as pd

from src.optimizers.enhanced_blue_optimizer import EnhancedBlueBallOptimizer
from src.utils.utils import parse_numbers, calculate_size_ratio_blue


class BlueBallOptimizerAdapter:
    """蓝球优化器适配器 - 桥接增强版优化器和主系统"""
    
    def __init__(self, data: pd.DataFrame = None):
        """
        初始化适配器
        
        Args:
            data: 历史数据，用于训练优化器
        """
        self.logger = logging.getLogger(__name__)
        self.optimizer = EnhancedBlueBallOptimizer(seed=42)
        self.is_trained = False
        
        if data is not None:
            self.train(data)
    
    def train(self, data: pd.DataFrame) -> None:
        """训练蓝球优化器"""
        try:
            self.optimizer.train(data)
            self.is_trained = True
            self.logger.info("蓝球优化器适配器训练完成")
        except Exception as e:
            self.logger.error(f"蓝球优化器适配器训练失败: {e}")
            raise
    
    def predict_blue_kills(self, period_data: Dict, target_count: int = 2) -> List[int]:
        """
        预测蓝球杀号 - 替换原有算法
        
        Args:
            period_data: 期数据字典
            target_count: 杀号数量
            
        Returns:
            List[int]: 杀号列表
        """
        if not self.is_trained:
            self.logger.warning("蓝球优化器未训练，使用默认杀号")
            return [3, 6]  # 默认杀号
        
        try:
            # 使用增强版优化器获取杀号建议
            kill_suggestions = self.optimizer.get_kill_suggestions(
                period_data, kill_count=target_count
            )
            
            self.logger.info(f"🔵 增强版蓝球杀号: {sorted(kill_suggestions)}")
            return kill_suggestions
            
        except Exception as e:
            self.logger.error(f"蓝球杀号预测失败: {e}")
            # 备用策略
            return [3, 6]
    
    def predict_blue_numbers(self, period_data: Dict, 
                           target_size_ratio: str = None,
                           kill_numbers: List[int] = None) -> List[int]:
        """
        预测蓝球号码 - 新增功能
        
        Args:
            period_data: 期数据字典
            target_size_ratio: 目标大小比 (如 "1:1")
            kill_numbers: 杀号列表
            
        Returns:
            List[int]: 预测的蓝球号码
        """
        if not self.is_trained:
            self.logger.warning("蓝球优化器未训练，使用随机预测")
            import random
            return random.sample(range(1, 13), 2)
        
        try:
            # 使用增强版优化器预测蓝球
            predicted_blues = self.optimizer.predict_optimal_blues(
                target_size_ratio=target_size_ratio,
                kill_numbers=kill_numbers,
                period_context=period_data
            )
            
            self.logger.info(f"🔵 增强版蓝球预测: {sorted(predicted_blues)}")
            return predicted_blues
            
        except Exception as e:
            self.logger.error(f"蓝球号码预测失败: {e}")
            # 备用策略
            import random
            return random.sample(range(1, 13), 2)
    
    def predict_blue_size_ratio(self, period_data: Dict) -> Tuple[str, float]:
        """
        预测蓝球大小比
        
        Args:
            period_data: 期数据字典
            
        Returns:
            Tuple[str, float]: (大小比预测, 置信度)
        """
        if not self.is_trained:
            return "1:1", 0.5
        
        try:
            # 使用优化器预测蓝球号码，然后计算大小比
            predicted_blues = self.predict_blue_numbers(period_data)
            big_count, small_count = calculate_size_ratio_blue(predicted_blues)
            ratio = f"{big_count}:{small_count}"
            
            # 基于历史数据计算置信度
            confidence = self._calculate_ratio_confidence(ratio, period_data)
            
            self.logger.info(f"🔵 蓝球大小比预测: {ratio} (置信度: {confidence:.3f})")
            return ratio, confidence
            
        except Exception as e:
            self.logger.error(f"蓝球大小比预测失败: {e}")
            return "1:1", 0.5
    
    def _calculate_ratio_confidence(self, predicted_ratio: str, 
                                  period_data: Dict) -> float:
        """计算大小比预测的置信度"""
        try:
            # 基于历史模式计算置信度
            if not hasattr(self.optimizer, 'training_data') or self.optimizer.training_data is None:
                return 0.6  # 默认置信度
            
            # 分析最近期数的大小比分布
            recent_data = self.optimizer.training_data.head(10)
            ratio_counts = {}
            
            for _, row in recent_data.iterrows():
                _, blue_balls = parse_numbers(row)
                big_count, small_count = calculate_size_ratio_blue(blue_balls)
                ratio = f"{big_count}:{small_count}"
                ratio_counts[ratio] = ratio_counts.get(ratio, 0) + 1
            
            # 计算预测比例的历史频率
            total_periods = len(recent_data)
            predicted_freq = ratio_counts.get(predicted_ratio, 0) / total_periods
            
            # 置信度基于频率和多样性
            base_confidence = 0.5
            frequency_bonus = predicted_freq * 0.3
            diversity_bonus = 0.2 if len(ratio_counts) > 1 else 0.1
            
            confidence = min(0.9, base_confidence + frequency_bonus + diversity_bonus)
            return confidence
            
        except Exception as e:
            self.logger.error(f"置信度计算失败: {e}")
            return 0.6
    
    def get_performance_metrics(self, test_data: pd.DataFrame) -> Dict:
        """获取性能指标"""
        if not self.is_trained:
            return {"error": "优化器未训练"}
        
        try:
            # 使用优化器的分析功能
            return self.optimizer.analyze_performance(test_data)
        except Exception as e:
            self.logger.error(f"性能分析失败: {e}")
            return {"error": str(e)}
    
    def update_training_data(self, new_data: pd.DataFrame) -> None:
        """更新训练数据"""
        try:
            self.optimizer.train(new_data)
            self.is_trained = True
            self.logger.info("蓝球优化器训练数据已更新")
        except Exception as e:
            self.logger.error(f"训练数据更新失败: {e}")
    
    def get_optimizer_status(self) -> Dict:
        """获取优化器状态"""
        return {
            "is_trained": self.is_trained,
            "optimizer_type": "EnhancedBlueBallOptimizer",
            "prediction_history_length": len(self.optimizer.prediction_history) if self.is_trained else 0,
            "recent_window": self.optimizer.recent_window if self.is_trained else 0,
            "weights": {
                "trend_weight": self.optimizer.trend_weight if self.is_trained else 0,
                "frequency_weight": self.optimizer.frequency_weight if self.is_trained else 0,
                "diversity_weight": self.optimizer.diversity_weight if self.is_trained else 0,
                "random_weight": self.optimizer.random_weight if self.is_trained else 0
            }
        }


class LegacyBluePredictorWrapper:
    """传统蓝球预测器包装器 - 用于向后兼容"""
    
    def __init__(self, adapter: BlueBallOptimizerAdapter):
        self.adapter = adapter
        self.logger = logging.getLogger(__name__)
    
    def _calculate_enhanced_blue_probabilities(self, period_data: Dict, 
                                             period_num: int) -> Dict[int, float]:
        """兼容原有接口 - 计算蓝球概率"""
        try:
            # 使用适配器获取杀号建议，反向推导概率
            kill_suggestions = self.adapter.predict_blue_kills(period_data, target_count=2)
            
            # 构建概率字典
            probabilities = {}
            for ball in range(1, 13):
                if ball in kill_suggestions:
                    probabilities[ball] = 0.1  # 杀号的概率很低
                else:
                    probabilities[ball] = 0.8  # 非杀号的概率较高
            
            return probabilities
            
        except Exception as e:
            self.logger.error(f"蓝球概率计算失败: {e}")
            # 返回均匀分布
            return {ball: 0.5 for ball in range(1, 13)}
    
    def _apply_blue_diversity_constraints(self, ball_probabilities: Dict[int, float],
                                        period_data: Dict, period_num: int) -> Dict[int, float]:
        """兼容原有接口 - 应用多样性约束"""
        # 直接返回，因为增强版优化器已经处理了多样性
        return ball_probabilities
    
    def _apply_enhanced_blue_safety_filters(self, kill_candidates: List[int],
                                          period_data: Dict, target_count: int,
                                          period_num: int) -> List[int]:
        """兼容原有接口 - 应用安全过滤"""
        # 直接返回前target_count个候选
        return kill_candidates[:target_count]
