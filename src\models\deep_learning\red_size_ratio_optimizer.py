#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红球大小比专项优化器
基于分析结果，专门优化红球大小比预测准确率
"""

import pandas as pd
import numpy as np
from pathlib import Path
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
import joblib
from datetime import datetime
import warnings

warnings.filterwarnings("ignore")

# TensorFlow导入
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers

    TF_AVAILABLE = True
except ImportError:
    print("[WARN] TensorFlow不可用，使用传统机器学习方法")
    TF_AVAILABLE = False


class RedSizeRatioOptimizer:
    """红球大小比专项优化器"""

    def __init__(self, data_path: str = "data/raw/dlt_data.csv"):
        self.data_path = Path(data_path)
        self.df = None
        self.models = {}
        self.scaler = StandardScaler()
        self.feature_cols = []
        self.results = {}

    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("[INFO] 加载彩票数据...")

        # 读取数据
        self.df = pd.read_csv(self.data_path, header=None)
        self.df.columns = [
            "期号",
            "红球1",
            "红球2",
            "红球3",
            "红球4",
            "红球5",
            "蓝球1",
            "蓝球2",
            "日期",
        ]

        # 数据类型转换
        numeric_cols = [
            "期号",
            "红球1",
            "红球2",
            "红球3",
            "红球4",
            "红球5",
            "蓝球1",
            "蓝球2",
        ]
        for col in numeric_cols:
            self.df[col] = pd.to_numeric(self.df[col], errors="coerce")

        # 删除空值
        self.df = self.df.dropna().reset_index(drop=True)

        # 特征工程
        self._enhanced_feature_engineering()

        print(f"[OK] 数据准备完成: {len(self.df)} 期")

    def _enhanced_feature_engineering(self):
        """基于分析结果的增强特征工程"""
        red_balls = ["红球1", "红球2", "红球3", "红球4", "红球5"]
        blue_balls = ["蓝球1", "蓝球2"]

        # 目标变量
        self.df["红球大数个数"] = self.df[red_balls].apply(
            lambda row: sum(x > 17 for x in row), axis=1
        )

        # 核心特征（基于分析结果的高重要性特征）
        self.df["红球和值"] = self.df[red_balls].sum(axis=1)
        self.df["红球均值"] = self.df[red_balls].mean(axis=1)

        # 增强的统计特征
        self.df["红球方差"] = self.df[red_balls].var(axis=1)
        self.df["红球跨度"] = self.df[red_balls].max(axis=1) - self.df[red_balls].min(
            axis=1
        )
        self.df["红球标准差"] = self.df[red_balls].std(axis=1)
        self.df["红球中位数"] = self.df[red_balls].median(axis=1)

        # 基于红球和值的分段特征（关键创新）
        self.df["红球和值分段"] = pd.cut(
            self.df["红球和值"], bins=[0, 70, 85, 100, 115, 200], labels=[0, 1, 2, 3, 4]
        )

        # 基于红球均值的分段特征
        self.df["红球均值分段"] = pd.cut(
            self.df["红球均值"], bins=[0, 14, 17, 20, 23, 40], labels=[0, 1, 2, 3, 4]
        )

        # 历史趋势特征（重点关注短期趋势）
        for window in [3, 5, 7]:
            self.df[f"红球和值_趋势{window}"] = (
                self.df["红球和值"].rolling(window=window, min_periods=1).mean()
            )
            self.df[f"红球均值_趋势{window}"] = (
                self.df["红球均值"].rolling(window=window, min_periods=1).mean()
            )
            self.df[f"红球大数个数_趋势{window}"] = (
                self.df["红球大数个数"].rolling(window=window, min_periods=1).mean()
            )

        # 波动性特征
        for window in [3, 5]:
            self.df[f"红球和值_波动{window}"] = (
                self.df["红球和值"]
                .rolling(window=window, min_periods=1)
                .std()
                .fillna(0)
            )
            self.df[f"红球均值_波动{window}"] = (
                self.df["红球均值"]
                .rolling(window=window, min_periods=1)
                .std()
                .fillna(0)
            )

        # 区间分布特征
        for i, (start, end) in enumerate(
            [(1, 7), (8, 14), (15, 21), (22, 28), (29, 35)]
        ):
            self.df[f"红球区间{i+1}个数"] = self.df[red_balls].apply(
                lambda row: sum(start <= x <= end for x in row), axis=1
            )

        # 选择最终特征
        self.feature_cols = [
            "红球和值",
            "红球均值",
            "红球方差",
            "红球跨度",
            "红球标准差",
            "红球中位数",
            "红球和值分段",
            "红球均值分段",
            "红球和值_趋势3",
            "红球均值_趋势3",
            "红球大数个数_趋势3",
            "红球和值_趋势5",
            "红球均值_趋势5",
            "红球大数个数_趋势5",
            "红球和值_趋势7",
            "红球均值_趋势7",
            "红球大数个数_趋势7",
            "红球和值_波动3",
            "红球均值_波动3",
            "红球和值_波动5",
            "红球均值_波动5",
            "红球区间1个数",
            "红球区间2个数",
            "红球区间3个数",
            "红球区间4个数",
            "红球区间5个数",
        ]

        # 确保所有特征列都存在且无缺失值
        for col in self.feature_cols:
            if col in self.df.columns:
                self.df[col] = self.df[col].fillna(0)

    def train_ensemble_models(self):
        """训练集成模型"""
        print("\n[STEP 1] 训练集成模型")
        print("=" * 50)

        # 准备数据
        X = self.df[self.feature_cols].fillna(0)
        y = self.df["红球大数个数"]

        # 数据标准化
        X_scaled = self.scaler.fit_transform(X)

        # 定义模型
        models = {
            "RandomForest": RandomForestClassifier(
                n_estimators=200,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                class_weight="balanced",
            ),
            "GradientBoosting": GradientBoostingClassifier(
                n_estimators=150, max_depth=6, learning_rate=0.1, random_state=42
            ),
            "LogisticRegression": LogisticRegression(
                random_state=42, class_weight="balanced", max_iter=1000
            ),
        }

        # 训练和评估每个模型
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

        for name, model in models.items():
            print(f"\n训练 {name}...")

            # 交叉验证
            cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring="accuracy")

            # 训练完整模型
            model.fit(X_scaled, y)

            # 保存模型
            self.models[name] = model

            print(
                f"  交叉验证准确率: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})"
            )

            # 特征重要性（如果支持）
            if hasattr(model, "feature_importances_"):
                feature_importance = list(
                    zip(self.feature_cols, model.feature_importances_)
                )
                feature_importance.sort(key=lambda x: x[1], reverse=True)
                print(f"  前5个重要特征:")
                for feat, imp in feature_importance[:5]:
                    print(f"    {feat}: {imp:.4f}")

        return self.models

    def create_neural_network(self):
        """创建专门的神经网络"""
        if not TF_AVAILABLE:
            print("[SKIP] TensorFlow不可用，跳过神经网络训练")
            return None

        print("\n[STEP 2] 训练专门神经网络")
        print("=" * 50)

        # 准备数据
        X = self.df[self.feature_cols].fillna(0)
        y = self.df["红球大数个数"]

        # 标准化
        X_scaled = self.scaler.transform(X)

        # 转换为分类标签
        y_categorical = keras.utils.to_categorical(y, num_classes=6)

        # 构建模型
        model = keras.Sequential(
            [
                layers.Dense(
                    128, activation="relu", input_shape=(len(self.feature_cols),)
                ),
                layers.BatchNormalization(),
                layers.Dropout(0.3),
                layers.Dense(64, activation="relu"),
                layers.BatchNormalization(),
                layers.Dropout(0.2),
                layers.Dense(32, activation="relu"),
                layers.Dropout(0.1),
                layers.Dense(6, activation="softmax"),
            ]
        )

        # 编译模型
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss="categorical_crossentropy",
            metrics=["accuracy"],
        )

        # 训练模型
        history = model.fit(
            X_scaled,
            y_categorical,
            epochs=100,
            batch_size=32,
            validation_split=0.2,
            verbose=0,
            callbacks=[
                keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
                keras.callbacks.ReduceLROnPlateau(patience=5, factor=0.5),
            ],
        )

        # 评估
        val_accuracy = max(history.history["val_accuracy"])
        print(f"  最佳验证准确率: {val_accuracy:.4f}")

        self.models["NeuralNetwork"] = model
        return model

    def create_ensemble_predictor(self):
        """创建集成预测器"""
        print("\n[STEP 3] 创建集成预测器")
        print("=" * 50)

        # 准备测试数据（最后100期）
        test_size = 100
        X_test = self.df[self.feature_cols].iloc[-test_size:].fillna(0)
        y_test = self.df["红球大数个数"].iloc[-test_size:]
        X_test_scaled = self.scaler.transform(X_test)

        # 获取每个模型的预测
        predictions = {}

        for name, model in self.models.items():
            if name == "NeuralNetwork" and TF_AVAILABLE:
                pred_proba = model.predict(X_test_scaled, verbose=0)
                pred = np.argmax(pred_proba, axis=1)
            else:
                pred = model.predict(X_test_scaled)

            predictions[name] = pred
            accuracy = np.mean(pred == y_test)
            print(f"  {name} 准确率: {accuracy:.4f}")

        # 集成预测（投票）
        if len(predictions) > 1:
            ensemble_pred = np.array(
                [
                    np.bincount(
                        [predictions[name][i] for name in predictions.keys()]
                    ).argmax()
                    for i in range(len(y_test))
                ]
            )

            ensemble_accuracy = np.mean(ensemble_pred == y_test)
            print(f"  集成模型准确率: {ensemble_accuracy:.4f}")

            self.results["ensemble_accuracy"] = ensemble_accuracy
            self.results["individual_accuracies"] = {
                name: np.mean(pred == y_test) for name, pred in predictions.items()
            }

        return predictions

    def save_optimized_models(self):
        """保存优化后的模型"""
        print("\n[STEP 4] 保存模型")
        print("=" * 50)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_dir = Path(f"models/red_size_ratio_optimized/{timestamp}")
        save_dir.mkdir(parents=True, exist_ok=True)

        # 保存传统机器学习模型
        for name, model in self.models.items():
            if name != "NeuralNetwork":
                model_path = save_dir / f"{name.lower()}_model.pkl"
                joblib.dump(model, model_path)
                print(f"  已保存: {model_path}")

        # 保存神经网络
        if "NeuralNetwork" in self.models and TF_AVAILABLE:
            nn_path = save_dir / "neural_network_model.h5"
            self.models["NeuralNetwork"].save(nn_path)
            print(f"  已保存: {nn_path}")

        # 保存标准化器
        scaler_path = save_dir / "scaler.pkl"
        joblib.dump(self.scaler, scaler_path)
        print(f"  已保存: {scaler_path}")

        # 保存特征列表
        features_path = save_dir / "feature_columns.txt"
        with open(features_path, "w", encoding="utf-8") as f:
            for col in self.feature_cols:
                f.write(f"{col}\n")
        print(f"  已保存: {features_path}")

        return save_dir

    def run_optimization(self):
        """运行完整优化流程"""
        print("红球大小比专项优化器")
        print("=" * 60)

        # 加载数据
        self.load_and_prepare_data()

        # 训练模型
        self.train_ensemble_models()

        if TF_AVAILABLE:
            self.create_neural_network()

        # 集成预测
        self.create_ensemble_predictor()

        # 保存模型
        save_dir = self.save_optimized_models()

        print(f"\n[SUCCESS] 红球大小比优化完成！")
        print(f"模型保存路径: {save_dir}")

        # 显示结果摘要
        if "ensemble_accuracy" in self.results:
            print(f"集成模型准确率: {self.results['ensemble_accuracy']:.4f}")
            print("各模型准确率:")
            for name, acc in self.results["individual_accuracies"].items():
                print(f"  {name}: {acc:.4f}")

        return self.results


if __name__ == "__main__":
    optimizer = RedSizeRatioOptimizer()
    results = optimizer.run_optimization()
