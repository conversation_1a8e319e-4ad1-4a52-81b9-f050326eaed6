"""
自动优化的训练配置
由AI自动调优系统生成
生成时间: 2025-06-28 19:32:14
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List


@dataclass
class AutoOptimizedConfig:
    """自动优化的训练配置"""

    # 基础训练参数
    sequence_length: int = 30
    epochs: int = 200
    batch_size: int = 16
    initial_learning_rate: float = 0.005

    # 学习率调度
    use_lr_scheduler: bool = True
    lr_scheduler_type: str = "ReduceLROnPlateau"
    lr_scheduler_factor: float = 0.7
    lr_scheduler_patience: int = 15
    lr_scheduler_min_lr: float = 5e-06

    # 早停配置
    early_stopping_patience: int = 30
    early_stopping_min_delta: float = 0.0001
    early_stopping_restore_best: bool = True

    # 正则化
    weight_decay: float = 0.005
    gradient_clip_norm: float = 1.0
    dropout_rate: float = 0.2

    # 数据分割
    train_ratio: float = 0.85
    validation_ratio: float = 0.1
    test_ratio: float = 0.05

    # LSTM配置
    lstm_config: Dict[str, Any] = field(default_factory=dict)

    # Transformer配置
    transformer_config: Dict[str, Any] = field(default_factory=dict)

    # 多任务配置
    multi_task_config: Dict[str, Any] = field(default_factory=dict)

    # 优化器配置
    use_mixed_precision: bool = True
    use_data_augmentation: bool = False
    augmentation_noise_std: float = 0.01
    optimizer_type: str = "AdamW"
    optimizer_betas: List[float] = field(default_factory=lambda: [0.9, 0.999])
    optimizer_eps: float = 1e-08
    
    def __post_init__(self):
        """初始化嵌套配置"""
        if not self.lstm_config:
            self.lstm_config = {
                "hidden_size": 320,
                "num_layers": 4,
                "dropout": 0.2,
                "bidirectional": False,
                "batch_first": True
            }

        if not self.transformer_config:
            self.transformer_config = {
                "d_model": 448,
                "num_heads": 14,
                "num_layers": 8,
                "dim_feedforward": 1536,
                "dropout": 0.1,
                "activation": "gelu"
            }

        if not self.multi_task_config:
            self.multi_task_config = {
                "shared_hidden_size": 384,
                "task_hidden_size": 128,
                "num_shared_layers": 4,
                "num_task_layers": 2,
                "dropout": 0.24
            }


# 创建默认配置实例
auto_optimized_config = AutoOptimizedConfig()


# 配置说明
CONFIG_DESCRIPTION = """
🎯 自动优化配置说明:

📊 性能提升: 相比基准配置提升约4.1%
🔧 优化策略: 激进学习配置 (aggressive_learning)

🎛️ 关键参数调整:
- 学习率: 0.002 → 0.005 (提升150.0%)
- LSTM隐藏层: 256 → 320 (提升25.0%)
- Transformer维度: 384 → 448 (提升16.7%)
- 早停耐心: 20 → 30 (提升50.0%)
- 训练轮数: 150 → 200 (提升33.3%)

💡 使用建议:
1. 直接导入: from src.config.auto_optimized_config import auto_optimized_config
2. 替换现有配置: 将此配置传递给训练系统
3. 监控训练: 观察是否达到预期的性能提升
4. 微调调整: 根据实际效果进行小幅调整
"""
