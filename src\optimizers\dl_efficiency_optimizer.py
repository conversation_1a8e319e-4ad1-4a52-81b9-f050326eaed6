#!/usr/bin/env python3
"""
深度学习效率优化器
解决当前深度学习系统每次都重新训练的效率问题

核心功能：
1. 智能模型缓存系统
2. 增量学习管理
3. 训练效率优化
4. 统一深度学习接口
"""

import os
import pickle
import hashlib
import time
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
import pandas as pd
import numpy as np

class ModelCacheManager:
    """智能模型缓存管理器"""
    
    def __init__(self, cache_dir: str = "models/cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.cache_index_file = self.cache_dir / "cache_index.json"
        self.cache_index = self._load_cache_index()
        self.logger = logging.getLogger(__name__)
        
    def _load_cache_index(self) -> Dict:
        """加载缓存索引"""
        if self.cache_index_file.exists():
            try:
                with open(self.cache_index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"加载缓存索引失败: {e}")
        return {}
    
    def _save_cache_index(self):
        """保存缓存索引"""
        try:
            with open(self.cache_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_index, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存缓存索引失败: {e}")
    
    def _calculate_data_hash(self, data: pd.DataFrame) -> str:
        """计算数据哈希值"""
        try:
            # 使用数据的形状、列名和最后几行来计算哈希
            data_info = {
                'shape': data.shape,
                'columns': list(data.columns),
                'last_5_rows': data.tail(5).to_dict() if len(data) >= 5 else data.to_dict(),
                'dtypes': str(data.dtypes.to_dict())
            }
            data_str = json.dumps(data_info, sort_keys=True)
            return hashlib.md5(data_str.encode()).hexdigest()
        except Exception as e:
            self.logger.error(f"计算数据哈希失败: {e}")
            return str(time.time())  # 回退到时间戳
    
    def should_retrain(self, model_name: str, data: pd.DataFrame, 
                      max_cache_hours: int = 24) -> bool:
        """判断是否需要重新训练模型"""
        try:
            cache_key = f"{model_name}_cache"
            
            if cache_key not in self.cache_index:
                self.logger.info(f"模型 {model_name} 无缓存，需要训练")
                return True
            
            cache_info = self.cache_index[cache_key]
            
            # 检查时间是否过期
            cache_time = datetime.fromisoformat(cache_info['timestamp'])
            if datetime.now() - cache_time > timedelta(hours=max_cache_hours):
                self.logger.info(f"模型 {model_name} 缓存过期，需要重新训练")
                return True
            
            # 检查数据是否变化
            current_hash = self._calculate_data_hash(data)
            if cache_info['data_hash'] != current_hash:
                self.logger.info(f"模型 {model_name} 数据已变化，需要重新训练")
                return True
            
            # 检查模型文件是否存在
            model_path = self.cache_dir / cache_info['model_file']
            if not model_path.exists():
                self.logger.info(f"模型 {model_name} 文件不存在，需要重新训练")
                return True
            
            self.logger.info(f"模型 {model_name} 使用缓存")
            return False
            
        except Exception as e:
            self.logger.error(f"检查重训练状态失败: {e}")
            return True  # 出错时选择重新训练
    
    def cache_model(self, model_name: str, model: Any, data: pd.DataFrame, 
                   metadata: Dict = None) -> str:
        """缓存模型"""
        try:
            timestamp = datetime.now().isoformat()
            model_filename = f"{model_name}_{timestamp.replace(':', '-')}.pkl"
            model_path = self.cache_dir / model_filename
            
            # 保存模型
            with open(model_path, 'wb') as f:
                pickle.dump(model, f)
            
            # 更新缓存索引
            cache_info = {
                'model_file': model_filename,
                'timestamp': timestamp,
                'data_hash': self._calculate_data_hash(data),
                'data_shape': data.shape,
                'metadata': metadata or {}
            }
            
            self.cache_index[f"{model_name}_cache"] = cache_info
            self._save_cache_index()
            
            self.logger.info(f"模型 {model_name} 已缓存到 {model_filename}")
            return str(model_path)
            
        except Exception as e:
            self.logger.error(f"缓存模型失败: {e}")
            return ""
    
    def load_cached_model(self, model_name: str) -> Optional[Any]:
        """加载缓存的模型"""
        try:
            cache_key = f"{model_name}_cache"
            if cache_key not in self.cache_index:
                return None
            
            cache_info = self.cache_index[cache_key]
            model_path = self.cache_dir / cache_info['model_file']
            
            if not model_path.exists():
                return None
            
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            
            self.logger.info(f"成功加载缓存模型 {model_name}")
            return model
            
        except Exception as e:
            self.logger.error(f"加载缓存模型失败: {e}")
            return None
    
    def get_cache_info(self, model_name: str) -> Optional[Dict]:
        """获取缓存信息"""
        cache_key = f"{model_name}_cache"
        return self.cache_index.get(cache_key)
    
    def clear_cache(self, model_name: str = None):
        """清理缓存"""
        try:
            if model_name:
                # 清理特定模型缓存
                cache_key = f"{model_name}_cache"
                if cache_key in self.cache_index:
                    cache_info = self.cache_index[cache_key]
                    model_path = self.cache_dir / cache_info['model_file']
                    if model_path.exists():
                        model_path.unlink()
                    del self.cache_index[cache_key]
                    self.logger.info(f"已清理模型 {model_name} 的缓存")
            else:
                # 清理所有缓存
                for cache_file in self.cache_dir.glob("*.pkl"):
                    cache_file.unlink()
                self.cache_index.clear()
                self.logger.info("已清理所有模型缓存")
            
            self._save_cache_index()
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")


class IncrementalLearningManager:
    """增量学习管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.last_training_data_size = {}
        self.incremental_threshold = 50  # 新增数据达到50条时触发增量学习
    
    def should_incremental_update(self, model_name: str, current_data_size: int) -> bool:
        """判断是否需要增量更新"""
        try:
            if model_name not in self.last_training_data_size:
                self.last_training_data_size[model_name] = current_data_size
                return False
            
            last_size = self.last_training_data_size[model_name]
            new_data_count = current_data_size - last_size
            
            if new_data_count >= self.incremental_threshold:
                self.logger.info(f"模型 {model_name} 新增 {new_data_count} 条数据，触发增量学习")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查增量更新状态失败: {e}")
            return False
    
    def update_training_size(self, model_name: str, data_size: int):
        """更新训练数据大小记录"""
        self.last_training_data_size[model_name] = data_size
    
    def get_incremental_data(self, data: pd.DataFrame, model_name: str) -> pd.DataFrame:
        """获取增量数据"""
        try:
            if model_name not in self.last_training_data_size:
                return data
            
            last_size = self.last_training_data_size[model_name]
            if len(data) > last_size:
                incremental_data = data.iloc[last_size:]
                self.logger.info(f"获取模型 {model_name} 的增量数据: {len(incremental_data)} 条")
                return incremental_data
            
            return pd.DataFrame()  # 返回空DataFrame
            
        except Exception as e:
            self.logger.error(f"获取增量数据失败: {e}")
            return pd.DataFrame()


class TrainingEfficiencyOptimizer:
    """训练效率优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.training_history = {}
        self.optimal_params = {}
    
    def optimize_training_params(self, model_name: str, data_size: int) -> Dict[str, Any]:
        """根据数据大小优化训练参数"""
        try:
            # 基于数据大小动态调整参数
            if data_size < 500:
                # 小数据集
                params = {
                    'epochs': 20,
                    'batch_size': 8,
                    'learning_rate': 0.001,
                    'patience': 5
                }
            elif data_size < 1000:
                # 中等数据集
                params = {
                    'epochs': 30,
                    'batch_size': 16,
                    'learning_rate': 0.0005,
                    'patience': 8
                }
            else:
                # 大数据集
                params = {
                    'epochs': 50,
                    'batch_size': 32,
                    'learning_rate': 0.0001,
                    'patience': 10
                }
            
            # 如果有历史最优参数，使用历史参数
            if model_name in self.optimal_params:
                historical_params = self.optimal_params[model_name]
                params.update(historical_params)
                self.logger.info(f"使用模型 {model_name} 的历史最优参数")
            
            self.logger.info(f"模型 {model_name} 优化参数: {params}")
            return params
            
        except Exception as e:
            self.logger.error(f"优化训练参数失败: {e}")
            return {'epochs': 20, 'batch_size': 16, 'learning_rate': 0.001}
    
    def record_training_result(self, model_name: str, params: Dict, 
                             training_time: float, performance: float):
        """记录训练结果"""
        try:
            if model_name not in self.training_history:
                self.training_history[model_name] = []
            
            result = {
                'timestamp': datetime.now().isoformat(),
                'params': params,
                'training_time': training_time,
                'performance': performance,
                'efficiency_score': performance / training_time  # 效率分数
            }
            
            self.training_history[model_name].append(result)
            
            # 更新最优参数
            if (model_name not in self.optimal_params or 
                result['efficiency_score'] > self._get_best_efficiency_score(model_name)):
                self.optimal_params[model_name] = params.copy()
                self.logger.info(f"更新模型 {model_name} 的最优参数")
            
        except Exception as e:
            self.logger.error(f"记录训练结果失败: {e}")
    
    def _get_best_efficiency_score(self, model_name: str) -> float:
        """获取最佳效率分数"""
        if model_name not in self.training_history:
            return 0.0
        
        history = self.training_history[model_name]
        if not history:
            return 0.0
        
        return max(result['efficiency_score'] for result in history)


class UnifiedDeepLearningInterface:
    """统一深度学习接口"""
    
    def __init__(self):
        self.cache_manager = ModelCacheManager()
        self.incremental_manager = IncrementalLearningManager()
        self.efficiency_optimizer = TrainingEfficiencyOptimizer()
        self.logger = logging.getLogger(__name__)
        self.available_models = {}
        self.model_performance = {}
    
    def register_model(self, model_name: str, model_class: Any, config: Dict = None):
        """注册深度学习模型"""
        self.available_models[model_name] = {
            'class': model_class,
            'config': config or {},
            'instance': None
        }
        self.logger.info(f"注册深度学习模型: {model_name}")
    
    def get_or_train_model(self, model_name: str, data: pd.DataFrame, 
                          force_retrain: bool = False) -> Optional[Any]:
        """获取或训练模型（智能缓存）"""
        try:
            # 检查是否需要重新训练
            if not force_retrain and not self.cache_manager.should_retrain(model_name, data):
                # 尝试加载缓存模型
                cached_model = self.cache_manager.load_cached_model(model_name)
                if cached_model is not None:
                    self.logger.info(f"使用缓存模型: {model_name}")
                    return cached_model
            
            # 需要训练新模型
            self.logger.info(f"开始训练模型: {model_name}")
            
            if model_name not in self.available_models:
                self.logger.error(f"未注册的模型: {model_name}")
                return None
            
            model_info = self.available_models[model_name]
            
            # 优化训练参数
            optimized_params = self.efficiency_optimizer.optimize_training_params(
                model_name, len(data)
            )
            
            # 创建模型实例
            config = model_info['config'].copy()
            config.update(optimized_params)
            
            model = model_info['class'](config)
            
            # 训练模型
            start_time = time.time()
            success = model.train(data)
            training_time = time.time() - start_time
            
            if success:
                # 缓存模型
                self.cache_manager.cache_model(
                    model_name, model, data, 
                    {'training_time': training_time, 'config': config}
                )
                
                # 记录训练结果
                performance = self._evaluate_model_performance(model, data)
                self.efficiency_optimizer.record_training_result(
                    model_name, config, training_time, performance
                )
                
                # 更新增量学习记录
                self.incremental_manager.update_training_size(model_name, len(data))
                
                self.logger.info(f"模型 {model_name} 训练完成，耗时 {training_time:.2f}s")
                return model
            else:
                self.logger.error(f"模型 {model_name} 训练失败")
                return None
                
        except Exception as e:
            self.logger.error(f"获取或训练模型失败: {e}")
            return None
    
    def _evaluate_model_performance(self, model: Any, data: pd.DataFrame) -> float:
        """评估模型性能"""
        try:
            # 简单的性能评估，可以根据具体模型类型扩展
            if hasattr(model, 'evaluate'):
                return model.evaluate(data)
            elif hasattr(model, 'score'):
                return model.score(data)
            else:
                return 0.5  # 默认分数
        except Exception as e:
            self.logger.error(f"评估模型性能失败: {e}")
            return 0.0
    
    def get_model_status(self) -> Dict[str, Any]:
        """获取所有模型状态"""
        status = {
            'registered_models': list(self.available_models.keys()),
            'cached_models': [],
            'cache_info': {}
        }
        
        for model_name in self.available_models:
            cache_info = self.cache_manager.get_cache_info(model_name)
            if cache_info:
                status['cached_models'].append(model_name)
                status['cache_info'][model_name] = cache_info
        
        return status
    
    def clear_all_cache(self):
        """清理所有缓存"""
        self.cache_manager.clear_cache()
        self.logger.info("已清理所有深度学习模型缓存")


# 使用示例和测试函数
def create_dl_efficiency_optimizer():
    """创建深度学习效率优化器实例"""
    return UnifiedDeepLearningInterface()


if __name__ == "__main__":
    # 测试代码
    print("[测试] 深度学习效率优化器")
    
    # 创建优化器
    dl_optimizer = create_dl_efficiency_optimizer()
    
    # 显示状态
    status = dl_optimizer.get_model_status()
    print(f"[状态] {status}")
    
    print("[完成] 深度学习效率优化器创建成功")