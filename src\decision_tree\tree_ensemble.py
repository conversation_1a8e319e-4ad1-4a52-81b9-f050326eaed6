"""决策树集成预测器

结合多个决策树模型的预测结果，提供更稳定的预测。
"""

from typing import List, Tuple, Dict, Any
import pandas as pd
import numpy as np
import pickle
import os
from collections import Counter
import random

from ..framework.interfaces import PredictorInterface
from ..framework.data_models import PredictionResult
from ..utils.logger import get_logger
from .odd_even_tree import OddEvenTreePredictor
from .size_ratio_tree import SizeRatioTreePredictor


class TreeEnsemblePredictor(PredictorInterface):
    """决策树集成预测器"""

    def __init__(
        self,
        ensemble_size: int = 5,
        voting_strategy: str = "weighted",
        random_state: int = 42,
    ):
        """
        初始化集成预测器

        Args:
            ensemble_size: 集成模型数量
            voting_strategy: 投票策略 ('majority', 'weighted', 'average')
            random_state: 随机种子
        """
        self.ensemble_size = ensemble_size
        self.voting_strategy = voting_strategy
        self.random_state = random_state

        # 创建多个预测器
        self.predictors = []
        random.seed(random_state)

        for i in range(ensemble_size):
            # 交替使用不同类型的预测器
            if i % 2 == 0:
                predictor = OddEvenTreePredictor(
                    max_depth=random.randint(8, 15),
                    min_samples_split=random.randint(3, 8),
                    min_samples_leaf=random.randint(1, 4),
                    random_state=random_state + i,
                )
            else:
                predictor = SizeRatioTreePredictor(
                    max_depth=random.randint(8, 15),
                    min_samples_split=random.randint(3, 8),
                    min_samples_leaf=random.randint(1, 4),
                    random_state=random_state + i,
                )
            self.predictors.append(predictor)

        self.logger = get_logger(self.__class__.__name__)

    def _ensemble_ratio_predictions(
        self, predictions: List[List[Tuple[str, float]]]
    ) -> List[Tuple[str, float]]:
        """
        集成比例预测结果

        Args:
            predictions: 各个预测器的比例预测结果

        Returns:
            集成后的比例预测
        """
        if not predictions:
            return [("3:3", 0.4), ("4:2", 0.3), ("2:4", 0.3)]

        # 收集所有可能的比例值
        all_ratios = set()
        for pred_list in predictions:
            for ratio, _ in pred_list:
                all_ratios.add(ratio)

        # 计算每个比例的集成概率
        ensemble_probs = {}

        for ratio in all_ratios:
            if self.voting_strategy == "majority":
                # 多数投票：计算预测该比例的模型数量
                votes = sum(
                    1
                    for pred_list in predictions
                    if any(r == ratio for r, _ in pred_list)
                )
                ensemble_probs[ratio] = votes / len(predictions)

            elif self.voting_strategy == "weighted":
                # 加权投票：使用概率加权
                total_prob = 0
                count = 0
                for pred_list in predictions:
                    for r, prob in pred_list:
                        if r == ratio:
                            total_prob += prob
                            count += 1
                ensemble_probs[ratio] = (
                    total_prob / len(predictions) if count > 0 else 0
                )

            elif self.voting_strategy == "average":
                # 平均投票：所有模型的平均概率
                probs = []
                for pred_list in predictions:
                    prob = next((p for r, p in pred_list if r == ratio), 0)
                    probs.append(prob)
                ensemble_probs[ratio] = np.mean(probs)

        # 归一化概率
        total_prob = sum(ensemble_probs.values())
        if total_prob > 0:
            ensemble_probs = {k: v / total_prob for k, v in ensemble_probs.items()}
        else:
            # 如果所有概率都是0，使用均匀分布
            ensemble_probs = {k: 1 / len(all_ratios) for k in all_ratios}

        # 按概率排序
        result = sorted(ensemble_probs.items(), key=lambda x: x[1], reverse=True)

        return result

    def _ensemble_numbers(
        self, all_numbers: List[Tuple[List[int], List[int]]]
    ) -> Tuple[List[int], List[int]]:
        """
        集成号码预测结果

        Args:
            all_numbers: 各个预测器的号码预测结果

        Returns:
            集成后的号码
        """
        if not all_numbers:
            return ([1, 2, 3, 4, 5, 6], [1])

        # 统计红球出现频次
        red_counter = Counter()
        for red_balls, _ in all_numbers:
            red_counter.update(red_balls)

        # 统计蓝球出现频次
        blue_counter = Counter()
        for _, blue_balls in all_numbers:
            blue_counter.update(blue_balls)

        # 选择出现频次最高的红球
        most_common_red = red_counter.most_common()

        if len(most_common_red) >= 6:
            # 如果有足够的高频红球，选择前6个
            ensemble_red = [num for num, _ in most_common_red[:6]]
        else:
            # 否则补充一些号码
            ensemble_red = [num for num, _ in most_common_red]
            remaining_count = 6 - len(ensemble_red)

            # 从所有预测的红球中随机选择补充
            all_red_set = set()
            for red_balls, _ in all_numbers:
                all_red_set.update(red_balls)

            available_red = [num for num in all_red_set if num not in ensemble_red]
            if len(available_red) >= remaining_count:
                ensemble_red.extend(random.sample(available_red, remaining_count))
            else:
                ensemble_red.extend(available_red)
                # 如果还不够，从1-33中随机选择
                still_needed = 6 - len(ensemble_red)
                if still_needed > 0:
                    all_possible = [i for i in range(1, 34) if i not in ensemble_red]
                    if len(all_possible) >= still_needed:
                        ensemble_red.extend(random.sample(all_possible, still_needed))

        # 选择出现频次最高的蓝球
        if blue_counter:
            ensemble_blue = [blue_counter.most_common(1)[0][0]]
        else:
            ensemble_blue = [random.randint(1, 16)]

        return sorted(ensemble_red[:6]), ensemble_blue

    def predict_for_period(
        self, data_index: int, data: pd.DataFrame
    ) -> PredictionResult:
        """
        为指定数据索引预测下一期

        Args:
            data_index: 数据索引
            data: 完整的历史数据

        Returns:
            PredictionResult: 集成预测结果
        """
        try:
            # 收集所有预测器的结果
            all_predictions = []
            all_red_odd_even = []
            all_red_size = []
            all_blue_size = []
            all_numbers = []

            for i, predictor in enumerate(self.predictors):
                try:
                    pred_result = predictor.predict_for_period(data_index, data)
                    all_predictions.append(pred_result)
                    all_red_odd_even.append(pred_result.red_odd_even_predictions)
                    all_red_size.append(pred_result.red_size_predictions)
                    all_blue_size.append(pred_result.blue_size_predictions)
                    all_numbers.append(pred_result.generated_numbers)
                except Exception as e:
                    self.logger.warning(f"预测器 {i} 预测失败: {e}")
                    continue

            if not all_predictions:
                raise Exception("所有预测器都失败了")

            # 集成比例预测
            ensemble_red_odd_even = self._ensemble_ratio_predictions(all_red_odd_even)
            ensemble_red_size = self._ensemble_ratio_predictions(all_red_size)
            ensemble_blue_size = self._ensemble_ratio_predictions(all_blue_size)

            # 集成号码预测
            ensemble_numbers = self._ensemble_numbers(all_numbers)

            # 构建集成预测结果
            period_number = str(data.iloc[data_index]["期号"])

            return PredictionResult(
                period_number=period_number,
                data_index=data_index,
                red_odd_even_predictions=ensemble_red_odd_even,
                red_size_predictions=ensemble_red_size,
                blue_size_predictions=ensemble_blue_size,
                generated_numbers=ensemble_numbers,
                kill_numbers={},
                predictor_name=self.get_predictor_name(),
                training_data_size=sum(p.training_data_size for p in all_predictions)
                // len(all_predictions),
            )

        except Exception as e:
            self.logger.error(f"集成预测过程中出错: {e}")
            # 返回默认预测结果
            period_number = str(data.iloc[data_index]["期号"])
            return PredictionResult(
                period_number=period_number,
                data_index=data_index,
                red_odd_even_predictions=[("3:3", 0.4), ("4:2", 0.3), ("2:4", 0.3)],
                red_size_predictions=[("3:3", 0.4), ("4:2", 0.3), ("2:4", 0.3)],
                blue_size_predictions=[("大", 0.5), ("小", 0.5)],
                generated_numbers=([1, 2, 3, 4, 5, 6], [1]),
                kill_numbers={},
                predictor_name=self.get_predictor_name(),
                training_data_size=0,
            )

    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return f"TreeEnsemblePredictor_{self.voting_strategy}"

    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return "1.0.0"

    def get_ensemble_info(self) -> Dict[str, Any]:
        """
        获取集成信息

        Returns:
            集成配置信息
        """
        return {
            "ensemble_size": self.ensemble_size,
            "voting_strategy": self.voting_strategy,
            "predictor_types": [type(p).__name__ for p in self.predictors],
            "random_state": self.random_state,
        }

    def save_models(self, filepath: str):
        """保存集成决策树模型"""
        model_data = {
            "ensemble_size": self.ensemble_size,
            "voting_strategy": self.voting_strategy,
            "random_state": self.random_state,
            "predictors": [],
            "predictor_name": self.get_predictor_name(),
            "predictor_version": self.get_predictor_version(),
        }

        # 保存每个预测器的数据
        for i, predictor in enumerate(self.predictors):
            predictor_data = {
                "type": type(predictor).__name__,
                "odd_even_tree": predictor.odd_even_tree,
                "red_size_tree": predictor.red_size_tree,
                "blue_size_tree": predictor.blue_size_tree,
                "odd_even_encoder": predictor.odd_even_encoder,
                "red_size_encoder": predictor.red_size_encoder,
                "blue_size_encoder": predictor.blue_size_encoder,
                "max_depth": predictor.max_depth,
                "min_samples_split": predictor.min_samples_split,
                "min_samples_leaf": predictor.min_samples_leaf,
                "random_state": predictor.random_state,
            }
            model_data["predictors"].append(predictor_data)

        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        with open(filepath, "wb") as f:
            pickle.dump(model_data, f)

        self.logger.info(f"集成决策树模型已保存到 {filepath}")

    def load_models(self, filepath: str) -> bool:
        """加载集成决策树模型"""
        if not os.path.exists(filepath):
            self.logger.error(f"模型文件 {filepath} 不存在")
            return False

        try:
            with open(filepath, "rb") as f:
                model_data = pickle.load(f)

            # 恢复集成参数
            self.ensemble_size = model_data.get("ensemble_size", self.ensemble_size)
            self.voting_strategy = model_data.get(
                "voting_strategy", self.voting_strategy
            )
            self.random_state = model_data.get("random_state", self.random_state)

            # 重建预测器
            self.predictors = []
            for predictor_data in model_data.get("predictors", []):
                predictor_type = predictor_data["type"]

                # 根据类型创建预测器
                if predictor_type == "OddEvenTreePredictor":
                    predictor = OddEvenTreePredictor(
                        max_depth=predictor_data["max_depth"],
                        min_samples_split=predictor_data["min_samples_split"],
                        min_samples_leaf=predictor_data["min_samples_leaf"],
                        random_state=predictor_data["random_state"],
                    )
                elif predictor_type == "SizeRatioTreePredictor":
                    predictor = SizeRatioTreePredictor(
                        max_depth=predictor_data["max_depth"],
                        min_samples_split=predictor_data["min_samples_split"],
                        min_samples_leaf=predictor_data["min_samples_leaf"],
                        random_state=predictor_data["random_state"],
                    )
                else:
                    self.logger.warning(f"未知的预测器类型: {predictor_type}")
                    continue

                # 恢复模型状态
                predictor.odd_even_tree = predictor_data.get("odd_even_tree")
                predictor.red_size_tree = predictor_data.get("red_size_tree")
                predictor.blue_size_tree = predictor_data.get("blue_size_tree")
                predictor.odd_even_encoder = predictor_data.get("odd_even_encoder")
                predictor.red_size_encoder = predictor_data.get("red_size_encoder")
                predictor.blue_size_encoder = predictor_data.get("blue_size_encoder")

                self.predictors.append(predictor)

            self.logger.info(f"集成决策树模型已从 {filepath} 加载")
            self.logger.info(
                f"模型版本: {model_data.get('predictor_version', 'Unknown')}"
            )
            self.logger.info(f"加载了 {len(self.predictors)} 个预测器")
            return True

        except Exception as e:
            self.logger.error(f"加载集成决策树模型失败: {e}")
            return False

    def is_trained(self) -> bool:
        """检查集成模型是否已训练"""
        return len(self.predictors) > 0 and all(p.is_trained() for p in self.predictors)
