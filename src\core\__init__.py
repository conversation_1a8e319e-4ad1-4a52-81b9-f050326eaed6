"""核心功能模块 - 简化版
提供核心架构组件
"""

# 导入核心架构组件
from .dependency_container import DependencyContainer
from .config_manager import ConfigManager as ConfigurationManager
from .predictor_factory import ConfigurablePredictorFactory, PredictorRegistry

# from .system_facade import LotteryPredictionFacade

# 导入接口和枚举
from .interfaces import (
    PredictionType,
    BallType as InterfaceBallType,
    PredictionResult as InterfacePredictionResult,
    ValidationResult,
    IPredictor,
    IStandardPredictor,
)

# 导入基础类和接口
from .base import (
    BallType,
    FeatureType,
    PredictionResult,
    GenerationResult,
    AnalysisResult,
    BaseAnalyzer,
    BasePredictor,
    BaseGenerator,
    BaseSystem,
    StandardBasePredictor,
)

# 导入异常处理
from .exceptions import (
    ErrorCode,
    LotteryPredictorException,
    DataException,
    ModelException,
    PredictionException,
    ConfigurationException,
    ValidationException,
    AlgorithmException,
    ResourceException,
)

# 导出列表
__all__ = [
    # 核心架构组件
    "DependencyContainer",
    "ConfigurationManager",
    "ConfigurablePredictorFactory",
    "PredictorRegistry",
    # 'LotteryPredictionFacade',
    # 接口和枚举
    "PredictionType",
    "InterfaceBallType",
    "InterfacePredictionResult",
    "ValidationResult",
    "IPredictor",
    "IStandardPredictor",
    # 基础类和接口
    "BallType",
    "FeatureType",
    "PredictionResult",
    "GenerationResult",
    "AnalysisResult",
    "BaseAnalyzer",
    "BasePredictor",
    "BaseGenerator",
    "BaseSystem",
    "StandardBasePredictor",
    # 异常处理
    "ErrorCode",
    "LotteryPredictorException",
    "DataException",
    "ModelException",
    "PredictionException",
    "ConfigurationException",
    "ValidationException",
    "AlgorithmException",
    "ResourceException",
]
