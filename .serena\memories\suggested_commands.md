# 建议的开发命令

## Windows系统命令
- **目录列表**: `powershell -Command "Get-ChildItem"`
- **文件搜索**: `powershell -Command "Get-ChildItem -Recurse -Name '*.py'"`
- **文本搜索**: `powershell -Command "Select-String -Path '*.py' -Pattern 'pattern'"`
- **创建目录**: `powershell -Command "New-Item -ItemType Directory -Path 'dirname'"`
- **删除文件**: `powershell -Command "Remove-Item 'filename'"`

## Python开发命令
- **运行主程序**: `python main.py`
- **运行重构版本**: `python -m src.systems.refactored_main`
- **运行测试**: `python -m pytest tests/`
- **运行特定测试**: `python -m pytest tests/unit/`
- **代码覆盖率**: `python -m pytest --cov=src tests/`

## 代码质量检查
- **代码格式化**: `black src/ tests/`
- **代码检查**: `flake8 src/ tests/`
- **类型检查**: `mypy src/`
- **安全检查**: `bandit -r src/`

## 包管理
- **安装依赖**: `pip install -r requirements.txt`
- **安装开发依赖**: `pip install -e .[dev]`
- **更新依赖**: `pip install --upgrade -r requirements.txt`
- **生成依赖**: `pip freeze > requirements.txt`

## 项目管理
- **清理缓存**: `powershell -Command "Remove-Item -Recurse -Force __pycache__, .pytest_cache"`
- **构建包**: `python setup.py sdist bdist_wheel`
- **安装本地包**: `pip install -e .`

## Git命令
- **状态检查**: `git status`
- **添加文件**: `git add .`
- **提交更改**: `git commit -m "message"`
- **推送更改**: `git push origin main`
- **拉取更新**: `git pull origin main`

## 性能分析
- **内存分析**: `python -m memory_profiler script.py`
- **性能分析**: `python -m cProfile script.py`
- **行级分析**: `kernprof -l -v script.py`