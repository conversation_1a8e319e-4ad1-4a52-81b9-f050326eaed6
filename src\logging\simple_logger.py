"""
简洁日志系统
提供清晰、简洁的预测结果输出，符合用户要求的格式
"""

from typing import Dict, List, Any, Optional
import pandas as pd
from datetime import datetime
from ..core.interfaces import ILogger, PredictionResult


class SimplePredictionLogger(ILogger):
    """简洁预测日志记录器"""

    def __init__(self, enable_console: bool = True, log_file: Optional[str] = None):
        self.enable_console = enable_console
        self.log_file = log_file

        # 比值类型映射
        self.ratio_type_names = {
            "odd_even": "奇偶比",
            "size": "大小比",
            "prime_composite": "质合比",
        }

    def log_prediction(self, prediction: PredictionResult) -> None:
        """记录预测（简洁日志不需要单独记录每个预测）"""
        pass

    def log_performance(
        self, operation: str, duration: float, metadata: Dict[str, Any] = None
    ) -> None:
        """记录性能（简洁日志不记录性能信息）"""
        pass

    def log_error(self, error: Exception, context: Dict[str, Any] = None) -> None:
        """记录错误"""
        error_msg = f"❌ 错误: {str(error)}"
        self._output(error_msg)

    def log_prediction_results(
        self,
        base_period: str,
        target_period: str,
        ratio_predictions: Dict[str, PredictionResult],
        actual_data: Optional[pd.Series] = None,
    ) -> None:
        """记录预测结果（按用户要求的格式）"""

        lines = []
        lines.append(f"基于{base_period}期预测第{target_period}期:")

        # 红球比数
        lines.append("  红球比数:")
        red_types = ["odd_even", "size", "prime_composite"]
        for ratio_type in red_types:
            key = f"red_{ratio_type}"
            if key in ratio_predictions:
                line = self._format_ratio_prediction(
                    ratio_predictions[key],
                    self.ratio_type_names[ratio_type],
                    actual_data,
                    "red",
                )
                lines.append(f"     红球{line}")

        # 蓝球比数
        lines.append("  蓝球比数:")
        blue_types = ["odd_even", "size", "prime_composite"]
        for ratio_type in blue_types:
            key = f"blue_{ratio_type}"
            if key in ratio_predictions:
                line = self._format_ratio_prediction(
                    ratio_predictions[key],
                    self.ratio_type_names[ratio_type],
                    actual_data,
                    "blue",
                )
                lines.append(f"     蓝球{line}")

        # 输出结果
        output = "\n".join(lines)
        self._output(output)

    def log_ratio_state_mapping(self) -> None:
        """记录比值状态对照表"""
        lines = []
        lines.append("\n比值状态：")
        lines.append("红球比数状态对照:")
        lines.append(
            "  奇偶比: 状态0=0:5, 状态1=1:4, 状态2=2:3, 状态3=3:2, 状态4=4:1, 状态5=5:0"
        )
        lines.append(
            "  大小比: 状态0=0:5, 状态1=1:4, 状态2=2:3, 状态3=3:2, 状态4=4:1, 状态5=5:0"
        )
        lines.append(
            "  质合比: 状态0=0:5, 状态1=1:4, 状态2=2:3, 状态3=3:2, 状态4=4:1, 状态5=5:0"
        )
        lines.append("")
        lines.append("蓝球比数状态对照:")
        lines.append("  奇偶比: 状态0=0:2, 状态1=1:1, 状态2=2:0")
        lines.append("  大小比: 状态0=0:2, 状态1=1:1, 状态2=2:0")
        lines.append("  质合比: 状态0=0:2, 状态1=1:1, 状态2=2:0")

        output = "\n".join(lines)
        self._output(output)

    def _format_ratio_prediction(
        self,
        prediction: PredictionResult,
        ratio_name: str,
        actual_data: Optional[pd.Series],
        ball_type: str,
    ) -> str:
        """格式化比值预测结果"""

        if not prediction.value or "predictions" not in prediction.value:
            return f"{ratio_name}: 预测失败"

        predictions = prediction.value["predictions"]

        # 格式化预测部分
        pred_parts = []
        for pred in predictions:
            pred_parts.append(f"{pred.ratio}({pred.confidence:.3f})")

        pred_str = f"预测[{', '.join(pred_parts)}]"

        # 如果有实际数据，计算实际比值和命中情况
        if actual_data is not None:
            actual_ratio, hit_status = self._calculate_actual_ratio(
                actual_data, prediction.value["ratio_type"], ball_type, predictions
            )
            result_str = (
                f" -> 实际[{actual_ratio}] ({'命中' if hit_status else '未命中'})"
            )
        else:
            result_str = ""

        return f"{ratio_name}: {pred_str}{result_str}"

    def _calculate_actual_ratio(
        self,
        actual_data: pd.Series,
        ratio_type: str,
        ball_type: str,
        predictions: List[Any],
    ) -> tuple:
        """计算实际比值和命中状态"""

        if ball_type == "red":
            balls = [
                actual_data["红球1"],
                actual_data["红球2"],
                actual_data["红球3"],
                actual_data["红球4"],
                actual_data["红球5"],
            ]
            actual_ratio = self._get_red_ratio_string(balls, ratio_type)
        else:
            balls = [actual_data["蓝球1"], actual_data["蓝球2"]]
            actual_ratio = self._get_blue_ratio_string(balls, ratio_type)

        # 检查是否命中
        hit = any(pred.ratio == actual_ratio for pred in predictions)

        return actual_ratio, hit

    def _get_red_ratio_string(self, balls: List[int], ratio_type: str) -> str:
        """获取红球比值字符串"""
        if ratio_type == "odd_even":
            odd_count = sum(1 for ball in balls if ball % 2 == 1)
            even_count = 5 - odd_count
            return f"{odd_count}:{even_count}"
        elif ratio_type == "size":
            large_count = sum(1 for ball in balls if ball >= 19)
            small_count = 5 - large_count
            return f"{large_count}:{small_count}"  # 大:小
        elif ratio_type == "prime_composite":
            primes = {2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31}
            prime_count = sum(1 for ball in balls if ball in primes)
            composite_count = 5 - prime_count
            return f"{prime_count}:{composite_count}"
        else:
            return "2:3"

    def _get_blue_ratio_string(self, balls: List[int], ratio_type: str) -> str:
        """获取蓝球比值字符串"""
        if ratio_type == "odd_even":
            odd_count = sum(1 for ball in balls if ball % 2 == 1)
            even_count = 2 - odd_count
            return f"{odd_count}:{even_count}"
        elif ratio_type == "size":
            large_count = sum(1 for ball in balls if ball >= 7)
            small_count = 2 - large_count
            return f"{large_count}:{small_count}"  # 大:小
        elif ratio_type == "prime_composite":
            primes = {2, 3, 5, 7, 11}
            prime_count = sum(1 for ball in balls if ball in primes)
            composite_count = 2 - prime_count
            return f"{prime_count}:{composite_count}"
        else:
            return "1:1"

    def _output(self, message: str) -> None:
        """输出消息"""
        if self.enable_console:
            print(message)

        if self.log_file:
            try:
                with open(self.log_file, "a", encoding="utf-8") as f:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    f.write(f"[{timestamp}] {message}\n")
            except Exception:
                pass  # 简洁日志不报告文件写入错误

    def log_backtest_summary(
        self, total_periods: int, hit_rates: Dict[str, float]
    ) -> None:
        """记录回测摘要"""
        lines = []
        lines.append(f"\n📊 回测摘要 (共{total_periods}期):")
        lines.append("=" * 50)

        # 红球命中率
        lines.append("红球比数命中率:")
        for ratio_type in ["odd_even", "size", "prime_composite"]:
            key = f"red_{ratio_type}"
            if key in hit_rates:
                rate = hit_rates[key] * 100
                lines.append(f"  红球{self.ratio_type_names[ratio_type]}: {rate:.1f}%")

        # 蓝球命中率
        lines.append("蓝球比数命中率:")
        for ratio_type in ["odd_even", "size", "prime_composite"]:
            key = f"blue_{ratio_type}"
            if key in hit_rates:
                rate = hit_rates[key] * 100
                lines.append(f"  蓝球{self.ratio_type_names[ratio_type]}: {rate:.1f}%")

        # 总体命中率
        if hit_rates:
            overall_rate = sum(hit_rates.values()) / len(hit_rates) * 100
            lines.append(f"\n总体命中率: {overall_rate:.1f}%")

        output = "\n".join(lines)
        self._output(output)


class BacktestLogger:
    """回测专用日志记录器"""

    def __init__(self, logger: SimplePredictionLogger):
        self.logger = logger
        self.results = []

    def log_period_result(
        self,
        base_period: str,
        target_period: str,
        predictions: Dict[str, PredictionResult],
        actual_data: pd.Series,
    ) -> None:
        """记录单期回测结果"""

        # 记录预测结果
        self.logger.log_prediction_results(
            base_period, target_period, predictions, actual_data
        )

        # 计算命中情况
        period_hits = {}
        for key, prediction in predictions.items():
            if prediction.ball_type.value == "red":
                balls = [
                    actual_data["红球1"],
                    actual_data["红球2"],
                    actual_data["红球3"],
                    actual_data["红球4"],
                    actual_data["红球5"],
                ]
            else:
                balls = [actual_data["蓝球1"], actual_data["蓝球2"]]

            # 使用比值预测器的评估方法
            from ..predictors.ratio_predictor import RatioPredictor

            evaluator = RatioPredictor()
            eval_result = evaluator.evaluate_prediction(prediction, balls)
            period_hits[key] = eval_result["hit"]

        self.results.append(
            {
                "base_period": base_period,
                "target_period": target_period,
                "hits": period_hits,
            }
        )

    def get_hit_rates(self) -> Dict[str, float]:
        """计算命中率"""
        if not self.results:
            return {}

        hit_counts = {}
        total_count = len(self.results)

        # 统计各类型命中次数
        for result in self.results:
            for key, hit in result["hits"].items():
                if key not in hit_counts:
                    hit_counts[key] = 0
                if hit:
                    hit_counts[key] += 1

        # 计算命中率
        hit_rates = {}
        for key, count in hit_counts.items():
            hit_rates[key] = count / total_count

        return hit_rates

    def log_summary(self) -> None:
        """记录回测摘要"""
        hit_rates = self.get_hit_rates()
        self.logger.log_backtest_summary(len(self.results), hit_rates)
