#!/usr/bin/env python3
"""
权重优化器 - Phase 3: 专门针对2+1命中率优化
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import time
from collections import defaultdict

@dataclass
class OptimizationResult:
    """优化结果类"""
    original_weights: Dict[str, float]
    optimized_weights: Dict[str, float]
    improvement: float
    iterations: int
    convergence_time: float
    success: bool
    details: Dict[str, Any]

class WeightOptimizer:
    """权重优化器 - 专门针对2+1命中率优化"""
    
    def __init__(self, target_hit_rate: float = 0.4):
        self.target_hit_rate = target_hit_rate
        self.optimization_history = []
        self.best_weights = {}
        self.best_performance = 0.0
        
        # 优化参数
        self.learning_rate = 0.01
        self.momentum = 0.9
        self.max_iterations = 100
        self.convergence_threshold = 0.001
        
        # 权重约束
        self.weight_constraints = {
            'ensemble': {'min': 0.01, 'max': 1.0},
            'model': {'min': 0.01, 'max': 1.0},
            'feature': {'min': 0.1, 'max': 1.0},  # 特征权重更重要
            'loss': {'min': 0.01, 'max': 5.0}
        }
        
        # 2+1命中率相关权重优先级
        self.priority_weights = {
            'red_odd_even': 1.5,  # 红球奇偶比最重要
            'red_size': 1.4,      # 红球大小比次重要
            'blue_size': 1.2,     # 蓝球大小比重要
            'bayesian': 1.1,      # 贝叶斯方法重要
            'frequency': 1.3      # 频率分析重要
        }
    
    def optimize_for_2_plus_1(self, current_weights: Dict[str, float], 
                             weight_categories: Dict[str, str],
                             performance_history: List[float]) -> OptimizationResult:
        """针对2+1命中率优化权重"""
        start_time = time.time()
        original_weights = current_weights.copy()
        
        # 初始化优化变量
        weights = current_weights.copy()
        velocity = {name: 0.0 for name in weights}
        
        best_weights = weights.copy()
        best_score = self._evaluate_weights(weights, performance_history)
        
        optimization_details = {
            'iterations_data': [],
            'convergence_curve': [],
            'weight_evolution': defaultdict(list)
        }
        
        # 梯度下降优化
        for iteration in range(self.max_iterations):
            # 计算梯度
            gradients = self._compute_gradients(weights, weight_categories, performance_history)
            
            # 更新权重
            prev_weights = weights.copy()
            for name in weights:
                # 动量更新
                velocity[name] = self.momentum * velocity[name] + self.learning_rate * gradients[name]
                weights[name] += velocity[name]
                
                # 应用约束
                category = weight_categories.get(name, 'ensemble')
                constraints = self.weight_constraints.get(category, {'min': 0.01, 'max': 1.0})
                weights[name] = max(constraints['min'], min(constraints['max'], weights[name]))
            
            # 评估当前权重
            current_score = self._evaluate_weights(weights, performance_history)
            
            # 更新最佳权重
            if current_score > best_score:
                best_score = current_score
                best_weights = weights.copy()
            
            # 记录优化过程
            optimization_details['iterations_data'].append({
                'iteration': iteration,
                'score': current_score,
                'weights': weights.copy()
            })
            optimization_details['convergence_curve'].append(current_score)
            
            for name, value in weights.items():
                optimization_details['weight_evolution'][name].append(value)
            
            # 检查收敛
            weight_change = sum(abs(weights[name] - prev_weights[name]) for name in weights)
            if weight_change < self.convergence_threshold:
                break
        
        # 计算改进
        original_score = self._evaluate_weights(original_weights, performance_history)
        improvement = best_score - original_score
        
        optimization_time = time.time() - start_time
        
        result = OptimizationResult(
            original_weights=original_weights,
            optimized_weights=best_weights,
            improvement=improvement,
            iterations=iteration + 1,
            convergence_time=optimization_time,
            success=improvement > 0,
            details=optimization_details
        )
        
        # 更新历史记录
        self.optimization_history.append(result)
        if improvement > 0:
            self.best_weights = best_weights.copy()
            self.best_performance = best_score
        
        return result
    
    def _compute_gradients(self, weights: Dict[str, float], 
                          weight_categories: Dict[str, str],
                          performance_history: List[float]) -> Dict[str, float]:
        """计算权重梯度"""
        gradients = {}
        epsilon = 1e-6
        
        base_score = self._evaluate_weights(weights, performance_history)
        
        for name in weights:
            # 数值梯度计算
            weights_plus = weights.copy()
            weights_plus[name] += epsilon
            score_plus = self._evaluate_weights(weights_plus, performance_history)
            
            weights_minus = weights.copy()
            weights_minus[name] -= epsilon
            score_minus = self._evaluate_weights(weights_minus, performance_history)
            
            gradient = (score_plus - score_minus) / (2 * epsilon)
            
            # 应用优先级权重
            priority = self.priority_weights.get(name, 1.0)
            gradient *= priority
            
            gradients[name] = gradient
        
        return gradients
    
    def _evaluate_weights(self, weights: Dict[str, float], 
                         performance_history: List[float]) -> float:
        """评估权重配置的性能"""
        if not performance_history:
            return 0.0
        
        # 基础性能分数
        base_score = np.mean(performance_history[-10:]) if len(performance_history) >= 10 else np.mean(performance_history)
        
        # 权重平衡性评分
        balance_score = self._calculate_balance_score(weights)
        
        # 2+1命中率相关权重评分
        priority_score = self._calculate_priority_score(weights)
        
        # 综合评分
        total_score = 0.6 * base_score + 0.2 * balance_score + 0.2 * priority_score
        
        return total_score
    
    def _calculate_balance_score(self, weights: Dict[str, float]) -> float:
        """计算权重平衡性评分"""
        if not weights:
            return 0.0
        
        # 计算权重方差（越小越好）
        weight_values = list(weights.values())
        variance = np.var(weight_values)
        
        # 转换为评分（方差越小评分越高）
        balance_score = 1.0 / (1.0 + variance)
        
        return balance_score
    
    def _calculate_priority_score(self, weights: Dict[str, float]) -> float:
        """计算优先级权重评分"""
        priority_score = 0.0
        total_priority = 0.0
        
        for name, priority in self.priority_weights.items():
            if name in weights:
                priority_score += weights[name] * priority
                total_priority += priority
        
        if total_priority > 0:
            priority_score /= total_priority
        
        return priority_score
    
    def suggest_weight_adjustments(self, current_weights: Dict[str, float],
                                 weight_categories: Dict[str, str],
                                 current_performance: float) -> Dict[str, Dict[str, Any]]:
        """建议权重调整"""
        suggestions = {}
        
        for name, current_weight in current_weights.items():
            category = weight_categories.get(name, 'ensemble')
            priority = self.priority_weights.get(name, 1.0)
            
            suggestion = {
                'current_weight': current_weight,
                'category': category,
                'priority': priority,
                'recommendations': []
            }
            
            # 基于优先级的建议
            if priority > 1.2 and current_weight < 0.5:
                suggestion['recommendations'].append({
                    'action': 'increase',
                    'reason': '高优先级权重偏低，建议增加',
                    'suggested_range': (current_weight * 1.2, current_weight * 1.5)
                })
            
            # 基于性能的建议
            if current_performance < self.target_hit_rate:
                if name in ['red_odd_even', 'red_size', 'blue_size']:
                    suggestion['recommendations'].append({
                        'action': 'optimize',
                        'reason': '2+1命中率未达标，需优化特征权重',
                        'suggested_range': (0.5, 0.8)
                    })
            
            # 基于约束的建议
            constraints = self.weight_constraints.get(category, {'min': 0.01, 'max': 1.0})
            if current_weight < constraints['min'] * 1.1:
                suggestion['recommendations'].append({
                    'action': 'increase',
                    'reason': '权重接近下限，建议适当增加',
                    'suggested_range': (constraints['min'] * 1.2, constraints['min'] * 2.0)
                })
            elif current_weight > constraints['max'] * 0.9:
                suggestion['recommendations'].append({
                    'action': 'decrease',
                    'reason': '权重接近上限，建议适当减少',
                    'suggested_range': (constraints['max'] * 0.7, constraints['max'] * 0.9)
                })
            
            suggestions[name] = suggestion
        
        return suggestions
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        if not self.optimization_history:
            return {'status': 'no_optimization_performed'}
        
        latest_result = self.optimization_history[-1]
        
        report = {
            'total_optimizations': len(self.optimization_history),
            'latest_optimization': {
                'improvement': latest_result.improvement,
                'iterations': latest_result.iterations,
                'convergence_time': latest_result.convergence_time,
                'success': latest_result.success
            },
            'best_performance': self.best_performance,
            'best_weights': self.best_weights,
            'optimization_trends': self._analyze_optimization_trends(),
            'recommendations': self._generate_optimization_recommendations()
        }
        
        return report
    
    def _analyze_optimization_trends(self) -> Dict[str, Any]:
        """分析优化趋势"""
        if len(self.optimization_history) < 2:
            return {'status': 'insufficient_data'}
        
        improvements = [result.improvement for result in self.optimization_history]
        convergence_times = [result.convergence_time for result in self.optimization_history]
        
        trends = {
            'improvement_trend': 'improving' if improvements[-1] > improvements[-2] else 'declining',
            'average_improvement': np.mean(improvements),
            'improvement_stability': 1.0 / (1.0 + np.std(improvements)),
            'average_convergence_time': np.mean(convergence_times),
            'convergence_efficiency': 'improving' if convergence_times[-1] < convergence_times[-2] else 'declining'
        }
        
        return trends
    
    def _generate_optimization_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if not self.optimization_history:
            return ['建议进行首次权重优化']
        
        latest_result = self.optimization_history[-1]
        
        if latest_result.improvement <= 0:
            recommendations.append('最近优化未见改进，建议调整优化策略')
        
        if latest_result.iterations >= self.max_iterations * 0.9:
            recommendations.append('优化接近最大迭代次数，建议增加迭代限制或调整学习率')
        
        if self.best_performance < self.target_hit_rate:
            recommendations.append(f'当前最佳性能({self.best_performance:.3f})未达目标({self.target_hit_rate})，建议继续优化')
        
        # 分析权重分布
        if self.best_weights:
            feature_weights = {k: v for k, v in self.best_weights.items() 
                             if k in ['red_odd_even', 'red_size', 'blue_size']}
            if feature_weights and max(feature_weights.values()) < 0.5:
                recommendations.append('特征权重偏低，建议增强特征权重以提升2+1命中率')
        
        return recommendations

def create_weight_optimizer(target_hit_rate: float = 0.4) -> WeightOptimizer:
    """创建权重优化器实例"""
    return WeightOptimizer(target_hit_rate)

if __name__ == "__main__":
    # 测试权重优化器
    optimizer = create_weight_optimizer()
    
    print("权重优化器测试")
    print("=" * 50)
    
    # 模拟权重和性能数据
    test_weights = {
        'red_odd_even': 0.3,
        'red_size': 0.4,
        'blue_size': 0.2,
        'bayesian': 0.15,
        'frequency': 0.25
    }
    
    test_categories = {
        'red_odd_even': 'feature',
        'red_size': 'feature',
        'blue_size': 'feature',
        'bayesian': 'ensemble',
        'frequency': 'ensemble'
    }
    
    test_performance = [0.2, 0.25, 0.3, 0.28, 0.32, 0.35, 0.33, 0.37, 0.36, 0.38]
    
    # 执行优化
    result = optimizer.optimize_for_2_plus_1(test_weights, test_categories, test_performance)
    
    print(f"优化结果:")
    print(f"  改进: {result.improvement:.4f}")
    print(f"  迭代次数: {result.iterations}")
    print(f"  收敛时间: {result.convergence_time:.2f}秒")
    print(f"  成功: {result.success}")
    
    print(f"\n原始权重: {result.original_weights}")
    print(f"优化权重: {result.optimized_weights}")
    
    # 获取建议
    suggestions = optimizer.suggest_weight_adjustments(test_weights, test_categories, 0.3)
    print(f"\n权重调整建议:")
    for name, suggestion in suggestions.items():
        if suggestion['recommendations']:
            print(f"  {name}: {suggestion['recommendations'][0]['reason']}")
    
    print("\n权重优化器测试完成！")