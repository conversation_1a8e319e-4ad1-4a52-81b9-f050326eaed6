#!/usr/bin/env python3
"""
蓝球优化器集成脚本
将增强版蓝球优化器集成到主系统中
"""

import sys
import os
import pandas as pd
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.integrations.blue_optimizer_adapter import BlueBallOptimizerAdapter, LegacyBluePredictorWrapper
from src.utils.data_loader import DataLoader
from src.utils.utils import parse_numbers, calculate_size_ratio_blue


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/blue_optimizer_integration.log'),
            logging.StreamHandler()
        ]
    )


def load_training_data():
    """加载训练数据"""
    try:
        data_loader = DataLoader()
        data = data_loader.load_data()
        print(f"✅ 成功加载训练数据: {len(data)} 期")
        return data
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None


def create_blue_optimizer_adapter(data):
    """创建并训练蓝球优化器适配器"""
    try:
        print("🔧 创建蓝球优化器适配器...")
        adapter = BlueBallOptimizerAdapter(data)
        
        print("✅ 蓝球优化器适配器创建成功")
        print(f"📊 适配器状态: {adapter.get_optimizer_status()}")
        
        return adapter
    except Exception as e:
        print(f"❌ 适配器创建失败: {e}")
        return None


def test_adapter_functionality(adapter, test_data):
    """测试适配器功能"""
    print("\n🧪 测试适配器功能...")
    
    # 测试数据准备
    test_periods = test_data.head(5)
    
    for idx, row in test_periods.iterrows():
        period_num = row['期号']
        print(f"\n📅 测试期号: {period_num}")
        
        # 构建期数据
        period_data = {
            'current': row.to_dict(),
            'recent_data': test_data.head(10)
        }
        
        try:
            # 测试杀号预测
            kill_numbers = adapter.predict_blue_kills(period_data, target_count=2)
            print(f"  🔵 杀号预测: {sorted(kill_numbers)}")
            
            # 测试号码预测
            predicted_blues = adapter.predict_blue_numbers(
                period_data, 
                target_size_ratio="1:1",
                kill_numbers=kill_numbers
            )
            print(f"  🔵 号码预测: {sorted(predicted_blues)}")
            
            # 测试大小比预测
            size_ratio, confidence = adapter.predict_blue_size_ratio(period_data)
            print(f"  🔵 大小比预测: {size_ratio} (置信度: {confidence:.3f})")
            
            # 验证预测结果
            actual_red, actual_blue = parse_numbers(row)
            actual_big, actual_small = calculate_size_ratio_blue(actual_blue)
            actual_ratio = f"{actual_big}:{actual_small}"
            
            print(f"  ✅ 实际蓝球: {sorted(actual_blue)}")
            print(f"  ✅ 实际大小比: {actual_ratio}")
            
            # 检查杀号成功率
            kill_success = not any(num in actual_blue for num in kill_numbers)
            print(f"  {'✅' if kill_success else '❌'} 杀号成功: {kill_success}")
            
            # 检查大小比预测
            ratio_success = size_ratio == actual_ratio
            print(f"  {'✅' if ratio_success else '❌'} 大小比预测: {ratio_success}")
            
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")


def integrate_to_main_system(adapter):
    """集成到主系统"""
    print("\n🔗 开始集成到主系统...")
    
    try:
        # 1. 修改 advanced_probabilistic_system.py
        print("📝 修改 advanced_probabilistic_system.py...")
        
        # 读取原文件
        main_system_path = "src/apps/advanced_probabilistic_system.py"
        with open(main_system_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经集成
        if "BlueBallOptimizerAdapter" in content:
            print("⚠️  蓝球优化器已经集成，跳过修改")
            return True
        
        # 添加导入语句
        import_line = "from src.integrations.blue_optimizer_adapter import BlueBallOptimizerAdapter, LegacyBluePredictorWrapper\n"
        
        # 找到合适的位置插入导入
        lines = content.split('\n')
        insert_pos = 0
        for i, line in enumerate(lines):
            if line.startswith('from src.') or line.startswith('import '):
                insert_pos = i + 1
        
        lines.insert(insert_pos, import_line.strip())
        
        # 在类初始化中添加适配器
        init_addition = """
        # 蓝球优化器适配器 - 增强版蓝球预测
        try:
            self.blue_optimizer_adapter = BlueBallOptimizerAdapter()
            self.legacy_blue_wrapper = LegacyBluePredictorWrapper(self.blue_optimizer_adapter)
            print("✅ 蓝球优化器适配器初始化成功")
        except Exception as e:
            print(f"⚠️  蓝球优化器适配器初始化失败: {e}")
            self.blue_optimizer_adapter = None
            self.legacy_blue_wrapper = None"""
        
        # 找到 __init__ 方法的结尾
        for i, line in enumerate(lines):
            if "def __init__" in line and "AdvancedProbabilisticSystem" in lines[max(0, i-5):i+1]:
                # 找到这个方法的结尾
                indent_level = len(line) - len(line.lstrip())
                for j in range(i+1, len(lines)):
                    if lines[j].strip() == "" or lines[j].startswith(' ' * (indent_level + 4)):
                        continue
                    elif lines[j].startswith(' ' * indent_level) and lines[j].strip():
                        # 找到下一个方法或类的开始
                        lines.insert(j, init_addition)
                        break
                break
        
        # 写回文件
        with open(main_system_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ advanced_probabilistic_system.py 修改完成")
        
        # 2. 创建训练脚本
        print("📝 创建训练脚本...")
        training_script = """
# 在 AdvancedProbabilisticSystem 类中添加训练方法
def train_blue_optimizer(self, data: pd.DataFrame):
    \"\"\"训练蓝球优化器\"\"\"
    if self.blue_optimizer_adapter:
        try:
            self.blue_optimizer_adapter.train(data)
            print("✅ 蓝球优化器训练完成")
        except Exception as e:
            print(f"❌ 蓝球优化器训练失败: {e}")
    else:
        print("⚠️  蓝球优化器适配器未初始化")

def get_enhanced_blue_predictions(self, period_data: Dict):
    \"\"\"获取增强版蓝球预测\"\"\"
    if self.blue_optimizer_adapter and self.blue_optimizer_adapter.is_trained:
        try:
            # 使用增强版优化器
            kill_numbers = self.blue_optimizer_adapter.predict_blue_kills(period_data, target_count=2)
            predicted_blues = self.blue_optimizer_adapter.predict_blue_numbers(period_data, kill_numbers=kill_numbers)
            size_ratio, confidence = self.blue_optimizer_adapter.predict_blue_size_ratio(period_data)
            
            return {
                'kill_numbers': kill_numbers,
                'predicted_blues': predicted_blues,
                'size_ratio': size_ratio,
                'confidence': confidence
            }
        except Exception as e:
            print(f"❌ 增强版蓝球预测失败: {e}")
            return None
    else:
        print("⚠️  蓝球优化器未训练，使用传统方法")
        return None
"""
        
        with open("blue_optimizer_training_methods.py", 'w', encoding='utf-8') as f:
            f.write(training_script)
        
        print("✅ 训练脚本创建完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始蓝球优化器集成流程...")
    
    # 设置日志
    setup_logging()
    
    # 1. 加载数据
    data = load_training_data()
    if data is None:
        print("❌ 数据加载失败，终止集成")
        return False
    
    # 2. 创建适配器
    adapter = create_blue_optimizer_adapter(data)
    if adapter is None:
        print("❌ 适配器创建失败，终止集成")
        return False
    
    # 3. 测试功能
    test_adapter_functionality(adapter, data)
    
    # 4. 集成到主系统
    success = integrate_to_main_system(adapter)
    
    if success:
        print("\n🎉 蓝球优化器集成完成！")
        print("\n📋 下一步操作:")
        print("1. 运行主系统测试，验证集成效果")
        print("2. 检查 2+1 命中率是否有提升")
        print("3. 监控蓝球预测多样性和准确率")
        print("4. 根据测试结果调整参数")
        
        # 保存适配器状态
        status = adapter.get_optimizer_status()
        print(f"\n📊 最终适配器状态:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        return True
    else:
        print("❌ 集成失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
