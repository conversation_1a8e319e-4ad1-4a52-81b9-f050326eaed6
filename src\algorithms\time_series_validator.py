"""
时间序列验证器
确保机器学习模型的时间序列验证正确性，避免数据泄露
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime


class TimeSeriesValidator:
    """时间序列验证器"""

    def __init__(self):
        """初始化验证器"""
        self.validation_results = {}

    def validate_time_series_split(
        self,
        data: pd.DataFrame,
        predictor,
        validation_periods: int = 20,
        min_train_periods: int = 50,
    ) -> Dict[str, Any]:
        """
        时间序列交叉验证

        Args:
            data: 完整数据集
            predictor: 预测器实例
            validation_periods: 验证期数
            min_train_periods: 最小训练期数

        Returns:
            验证结果
        """
        print(f"🔍 开始时间序列验证 (验证期数: {validation_periods})")

        if len(data) < min_train_periods + validation_periods:
            raise ValueError(
                f"数据不足：需要至少 {min_train_periods + validation_periods} 期数据"
            )

        results = {
            "period_results": [],
            "accuracies": {"red_odd_even": [], "red_size": [], "blue_size": []},
            "predictions": [],
            "actuals": [],
            "training_sizes": [],
            "validation_timeline": [],
        }

        # 滑动窗口验证
        for i in range(validation_periods):
            # 计算训练和验证数据的分割点
            train_end = len(data) - validation_periods + i
            validation_index = train_end

            # 确保有足够的训练数据
            if train_end < min_train_periods:
                continue

            # 分割数据：使用1到train_end期训练，预测train_end+1期
            train_data = data.iloc[:train_end]
            validation_row = data.iloc[validation_index]

            print(
                f"📊 验证 {i+1}/{validation_periods}: 训练期数 {len(train_data)}, 预测期号 {validation_row['期号']}"
            )

            # 训练模型（使用当前的训练数据）
            try:
                predictor.train(train_data)

                # 预测
                pred_result = predictor.predict_ratios(train_data, len(train_data))

                # 获取实际值
                actual_values = self._get_actual_ratios(validation_row)

                # 计算准确率
                period_accuracy = {}
                for target_name in ["red_odd_even", "red_size", "blue_size"]:
                    is_correct = (
                        pred_result["predictions"][target_name]
                        == actual_values[target_name]
                    )
                    period_accuracy[target_name] = is_correct
                    results["accuracies"][target_name].append(is_correct)

                # 记录详细结果
                period_result = {
                    "period": validation_row["期号"],
                    "train_size": len(train_data),
                    "predictions": pred_result["predictions"],
                    "actuals": actual_values,
                    "accuracies": period_accuracy,
                    "confidences": pred_result["confidences"],
                }

                results["period_results"].append(period_result)
                results["predictions"].append(pred_result["predictions"])
                results["actuals"].append(actual_values)
                results["training_sizes"].append(len(train_data))
                results["validation_timeline"].append(validation_row["期号"])

            except Exception as e:
                print(f"❌ 验证期 {i+1} 失败: {str(e)}")
                continue

        # 计算总体统计
        overall_stats = self._calculate_overall_statistics(results)
        results.update(overall_stats)

        self.validation_results = results
        return results

    def _get_actual_ratios(self, row: pd.Series) -> Dict[str, str]:
        """获取实际比值"""
        red_nums = [int(row[f"红球{i}"]) for i in range(1, 6)]
        blue_nums = [int(row[f"蓝球{i}"]) for i in range(1, 3)]

        # 计算实际比值
        red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
        red_even_count = len(red_nums) - red_odd_count
        actual_red_odd_even = f"{red_odd_count}:{red_even_count}"

        red_big_count = sum(1 for x in red_nums if x > 17)
        red_small_count = len(red_nums) - red_big_count
        actual_red_size = f"{red_big_count}:{red_small_count}"

        blue_big_count = sum(1 for x in blue_nums if x > 6)
        blue_small_count = len(blue_nums) - blue_big_count
        actual_blue_size = f"{blue_big_count}:{blue_small_count}"

        return {
            "red_odd_even": actual_red_odd_even,
            "red_size": actual_red_size,
            "blue_size": actual_blue_size,
        }

    def _calculate_overall_statistics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """计算总体统计指标"""
        stats = {}

        # 计算每个目标的准确率
        for target_name in ["red_odd_even", "red_size", "blue_size"]:
            if results["accuracies"][target_name]:
                accuracy = sum(results["accuracies"][target_name]) / len(
                    results["accuracies"][target_name]
                )
                stats[f"{target_name}_accuracy"] = accuracy
            else:
                stats[f"{target_name}_accuracy"] = 0.0

        # 计算平均准确率
        target_accuracies = [
            stats[f"{target}_accuracy"]
            for target in ["red_odd_even", "red_size", "blue_size"]
        ]
        stats["average_accuracy"] = sum(target_accuracies) / len(target_accuracies)

        # 计算置信度统计
        if results["period_results"]:
            confidences = {"red_odd_even": [], "red_size": [], "blue_size": []}
            for period_result in results["period_results"]:
                for target_name in ["red_odd_even", "red_size", "blue_size"]:
                    confidences[target_name].append(
                        period_result["confidences"][target_name]
                    )

            for target_name in ["red_odd_even", "red_size", "blue_size"]:
                if confidences[target_name]:
                    stats[f"{target_name}_avg_confidence"] = np.mean(
                        confidences[target_name]
                    )
                    stats[f"{target_name}_std_confidence"] = np.std(
                        confidences[target_name]
                    )

        # 训练数据大小统计
        if results["training_sizes"]:
            stats["avg_training_size"] = np.mean(results["training_sizes"])
            stats["min_training_size"] = np.min(results["training_sizes"])
            stats["max_training_size"] = np.max(results["training_sizes"])

        return stats

    def print_validation_report(self):
        """打印验证报告"""
        if not self.validation_results:
            print("❌ 没有验证结果可显示")
            return

        results = self.validation_results

        print("\n" + "=" * 60)
        print("🎯 时间序列验证报告")
        print("=" * 60)

        # 总体准确率
        print(f"\n📊 总体准确率:")
        print(f"  红球奇偶比: {results.get('red_odd_even_accuracy', 0):.1%}")
        print(f"  红球大小比: {results.get('red_size_accuracy', 0):.1%}")
        print(f"  蓝球大小比: {results.get('blue_size_accuracy', 0):.1%}")
        print(f"  平均准确率: {results.get('average_accuracy', 0):.1%}")

        # 置信度统计
        print(f"\n🎯 平均置信度:")
        for target in ["red_odd_even", "red_size", "blue_size"]:
            avg_conf = results.get(f"{target}_avg_confidence", 0)
            std_conf = results.get(f"{target}_std_confidence", 0)
            print(f"  {target}: {avg_conf:.3f} (±{std_conf:.3f})")

        # 训练数据统计
        print(f"\n📈 训练数据统计:")
        print(f"  平均训练期数: {results.get('avg_training_size', 0):.0f}")
        print(f"  最小训练期数: {results.get('min_training_size', 0):.0f}")
        print(f"  最大训练期数: {results.get('max_training_size', 0):.0f}")

        # 详细期数结果
        print(f"\n📋 详细验证结果:")
        print("-" * 80)
        print(f"{'期号':<8} {'红奇偶':<8} {'红大小':<8} {'蓝大小':<8} {'训练期数':<8}")
        print("-" * 80)

        for period_result in results["period_results"][-10:]:  # 显示最后10期
            period = period_result["period"]
            red_oe = "✅" if period_result["accuracies"]["red_odd_even"] else "❌"
            red_size = "✅" if period_result["accuracies"]["red_size"] else "❌"
            blue_size = "✅" if period_result["accuracies"]["blue_size"] else "❌"
            train_size = period_result["train_size"]

            print(
                f"{period:<8} {red_oe:<8} {red_size:<8} {blue_size:<8} {train_size:<8}"
            )

        print("=" * 60)

    def save_validation_results(self, filepath: str):
        """保存验证结果到文件"""
        if not self.validation_results:
            print("❌ 没有验证结果可保存")
            return

        # 创建详细报告
        report_lines = []
        report_lines.append("时间序列验证报告")
        report_lines.append("=" * 50)
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        results = self.validation_results

        # 总体统计
        report_lines.append("总体准确率:")
        report_lines.append(
            f"  红球奇偶比: {results.get('red_odd_even_accuracy', 0):.1%}"
        )
        report_lines.append(f"  红球大小比: {results.get('red_size_accuracy', 0):.1%}")
        report_lines.append(f"  蓝球大小比: {results.get('blue_size_accuracy', 0):.1%}")
        report_lines.append(f"  平均准确率: {results.get('average_accuracy', 0):.1%}")
        report_lines.append("")

        # 详细结果
        report_lines.append("详细验证结果:")
        report_lines.append("-" * 80)
        report_lines.append(
            f"{'期号':<10} {'红奇偶预测':<12} {'红奇偶实际':<12} {'红大小预测':<12} {'红大小实际':<12} {'蓝大小预测':<12} {'蓝大小实际':<12}"
        )
        report_lines.append("-" * 80)

        for period_result in results["period_results"]:
            period = period_result["period"]
            pred = period_result["predictions"]
            actual = period_result["actuals"]

            line = (
                f"{period:<10} {pred['red_odd_even']:<12} {actual['red_odd_even']:<12} "
            )
            line += f"{pred['red_size']:<12} {actual['red_size']:<12} "
            line += f"{pred['blue_size']:<12} {actual['blue_size']:<12}"
            report_lines.append(line)

        # 保存到文件
        with open(filepath, "w", encoding="utf-8") as f:
            f.write("\n".join(report_lines))

        print(f"✅ 验证报告已保存到: {filepath}")

    def get_performance_summary(self) -> Dict[str, float]:
        """获取性能摘要"""
        if not self.validation_results:
            return {}

        results = self.validation_results
        return {
            "red_odd_even_accuracy": results.get("red_odd_even_accuracy", 0),
            "red_size_accuracy": results.get("red_size_accuracy", 0),
            "blue_size_accuracy": results.get("blue_size_accuracy", 0),
            "average_accuracy": results.get("average_accuracy", 0),
            "total_periods_tested": len(results.get("period_results", [])),
        }
