#!/usr/bin/env python3
"""
改进红球奇偶比预测器的适配器
用于集成到现有的集成学习系统中
"""

import pandas as pd
from typing import Dict, Any, Optional
from src.algorithms.improved_red_odd_even_predictor import ImprovedRedOddEvenPredictor


class ImprovedRedOddEvenAdapter:
    """改进红球奇偶比预测器适配器"""
    
    def __init__(self):
        super().__init__()
        self.name = "improved_red_odd_even"
        self.predictor = ImprovedRedOddEvenPredictor()
        self.prediction_type = "ratio"
        
    def predict(self, data: pd.DataFrame, target_index: Optional[int] = None, **kwargs) -> Dict[str, Any]:
        """
        预测接口
        
        Args:
            data: 历史数据
            target_index: 目标索引（未使用）
            **kwargs: 其他参数
            
        Returns:
            预测结果
        """
        try:
            # 调用改进的预测器
            result = self.predictor.predict_ratio(data, "red_odd_even")
            
            # 转换为标准格式
            return {
                "prediction": result["prediction"],
                "confidence": result["confidence"],
                "method": f"improved_{result['method']}",
                "predictor_name": self.name,
                "prediction_type": self.prediction_type
            }
            
        except Exception as e:
            # 错误处理，返回默认预测
            return {
                "prediction": "2:3",
                "confidence": 0.4,
                "method": "error_fallback",
                "predictor_name": self.name,
                "prediction_type": self.prediction_type,
                "error": str(e)
            }
    
    def train(self, data: pd.DataFrame, **kwargs) -> None:
        """训练接口（此预测器不需要显式训练）"""
        pass
    
    def get_name(self) -> str:
        """获取预测器名称"""
        return self.name
    
    def get_prediction_type(self) -> str:
        """获取预测类型"""
        return self.prediction_type
