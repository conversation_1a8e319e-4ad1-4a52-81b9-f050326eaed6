"""
红球奇偶比专用优化器
专门针对红球奇偶比预测准确率低的问题进行优化
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from collections import Counter, deque
import random
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers,
    calculate_odd_even_ratio,
    ratio_to_state,
    get_all_red_odd_even_states,
)


class RedOddEvenOptimizer:
    """红球奇偶比专用优化器"""

    def __init__(self):
        self.name = "red_odd_even_optimizer"
        self.historical_patterns = {}
        self.transition_patterns = {}
        self.extreme_state_tracker = deque(maxlen=20)  # 跟踪极端状态

    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """
        专门优化红球奇偶比预测

        Args:
            data: 历史数据
            ratio_type: 必须是 'odd_even'

        Returns:
            Dict: 预测结果
        """
        if ratio_type != "odd_even":
            return {"prediction": "3:2", "confidence": 0.3, "method": "default"}

        if len(data) < 10:
            return {
                "prediction": "2:3",
                "confidence": 0.4,
                "method": "insufficient_data",
            }

        # 分析历史奇偶比模式
        self._analyze_historical_patterns(data)

        # 多策略预测
        strategies = [
            self._extreme_state_prediction(data),
            self._transition_pattern_prediction(data),
            self._frequency_deviation_prediction(data),
            self._recent_trend_reversal_prediction(data),
            self._balance_seeking_prediction(data),
        ]

        # 选择最有信心的预测
        best_strategy = max(strategies, key=lambda x: x["confidence"])

        return best_strategy

    def _analyze_historical_patterns(self, data: pd.DataFrame):
        """分析历史奇偶比模式"""
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)

        # 统计状态频率
        self.historical_patterns = Counter(states)

        # 分析转移模式
        for i in range(len(states) - 1):
            current = states[i]
            next_state = states[i + 1]
            if current not in self.transition_patterns:
                self.transition_patterns[current] = Counter()
            self.transition_patterns[current][next_state] += 1

        # 跟踪极端状态
        for state in states[-10:]:  # 最近10期
            if state in ["5:0", "0:5", "4:1", "1:4"]:
                self.extreme_state_tracker.append(state)

    def _extreme_state_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """极端状态预测策略 - 预测极端奇偶比"""
        recent_states = []
        for i in range(min(5, len(data))):
            row = data.iloc[-(i + 1)]
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            recent_states.append(state)

        # 如果最近都是中等状态，预测极端状态
        moderate_states = ["3:2", "2:3"]
        if all(state in moderate_states for state in recent_states[:3]):
            extreme_states = ["4:1", "1:4", "5:0", "0:5"]
            predicted_state = random.choice(extreme_states)
            return {
                "prediction": predicted_state,
                "confidence": 0.7,
                "method": "extreme_state_prediction",
            }

        return {
            "prediction": "3:2",
            "confidence": 0.3,
            "method": "extreme_state_fallback",
        }

    def _transition_pattern_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """基于转移模式的预测"""
        if len(data) < 2:
            return {
                "prediction": "2:3",
                "confidence": 0.3,
                "method": "insufficient_transition_data",
            }

        # 获取当前状态
        current_row = data.iloc[-1]
        red_balls, _ = parse_numbers(current_row)
        red_odd, red_even = calculate_odd_even_ratio(red_balls)
        current_state = ratio_to_state((red_odd, red_even))

        # 查找转移模式
        if current_state in self.transition_patterns:
            transitions = self.transition_patterns[current_state]
            if transitions:
                # 选择转移概率最高的状态
                most_likely = transitions.most_common(1)[0]
                confidence = most_likely[1] / sum(transitions.values())
                return {
                    "prediction": most_likely[0],
                    "confidence": confidence * 0.8,  # 适度降低置信度
                    "method": "transition_pattern",
                }

        return {"prediction": "3:2", "confidence": 0.4, "method": "transition_fallback"}

    def _frequency_deviation_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """频率偏差预测 - 预测出现频率较低的状态"""
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)

        # 统计各状态频率
        state_counts = Counter(states)
        total_count = len(states)

        # 计算期望频率（理论上应该相对均匀）
        all_states = get_all_red_odd_even_states()
        expected_freq = total_count / len(all_states)

        # 找出频率最低的状态
        underrepresented_states = []
        for state in all_states:
            actual_freq = state_counts.get(state, 0)
            if actual_freq < expected_freq * 0.8:  # 低于期望频率80%
                underrepresented_states.append((state, expected_freq - actual_freq))

        if underrepresented_states:
            # 选择最被低估的状态
            underrepresented_states.sort(key=lambda x: x[1], reverse=True)
            predicted_state = underrepresented_states[0][0]
            confidence = min(0.8, underrepresented_states[0][1] / expected_freq)
            return {
                "prediction": predicted_state,
                "confidence": confidence,
                "method": "frequency_deviation",
            }

        return {"prediction": "2:3", "confidence": 0.4, "method": "frequency_balanced"}

    def _recent_trend_reversal_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """近期趋势反转预测"""
        if len(data) < 3:
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "insufficient_trend_data",
            }

        # 分析最近3期的趋势
        recent_states = []
        for i in range(3):
            row = data.iloc[-(i + 1)]
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            recent_states.append(state)

        # 如果最近3期都偏向奇数多，预测偶数多
        odd_heavy_states = ["5:0", "4:1", "3:2"]
        even_heavy_states = ["0:5", "1:4", "2:3"]

        if all(state in odd_heavy_states for state in recent_states):
            predicted_state = random.choice(even_heavy_states)
            return {
                "prediction": predicted_state,
                "confidence": 0.75,
                "method": "trend_reversal_to_even",
            }
        elif all(state in even_heavy_states for state in recent_states):
            predicted_state = random.choice(odd_heavy_states)
            return {
                "prediction": predicted_state,
                "confidence": 0.75,
                "method": "trend_reversal_to_odd",
            }

        return {"prediction": "3:2", "confidence": 0.4, "method": "no_clear_trend"}

    def _balance_seeking_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """平衡寻求预测 - 基于长期平衡理论"""
        if len(data) < 20:
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "insufficient_balance_data",
            }

        # 统计最近20期的奇偶数总数
        total_odd = 0
        total_even = 0

        for i in range(20):
            row = data.iloc[-(i + 1)]
            red_balls, _ = parse_numbers(row)
            for num in red_balls:
                if num % 2 == 1:
                    total_odd += 1
                else:
                    total_even += 1

        total_numbers = total_odd + total_even
        odd_ratio = total_odd / total_numbers

        # 如果奇数过多，预测偶数多的状态
        if odd_ratio > 0.55:
            even_states = ["0:5", "1:4", "2:3"]
            predicted_state = random.choice(even_states)
            confidence = min(0.8, (odd_ratio - 0.5) * 2)
            return {
                "prediction": predicted_state,
                "confidence": confidence,
                "method": "balance_seeking_even",
            }
        elif odd_ratio < 0.45:
            odd_states = ["5:0", "4:1", "3:2"]
            predicted_state = random.choice(odd_states)
            confidence = min(0.8, (0.5 - odd_ratio) * 2)
            return {
                "prediction": predicted_state,
                "confidence": confidence,
                "method": "balance_seeking_odd",
            }

        return {"prediction": "3:2", "confidence": 0.5, "method": "balance_neutral"}
