#!/usr/bin/env python3
"""
Phase 3 简化验证脚本 - 权重系统重构验证
验证智能权重管理系统的基本功能（不依赖numpy）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_weight_manager_basic():
    """测试权重管理器基本功能"""
    print("=" * 60)
    print("测试 1: 权重管理器基本功能")
    print("=" * 60)
    
    try:
        from src.integrations.weight_manager_adapter import WeightManagerAdapter, PerformanceMetrics
        
        # 创建权重管理器
        manager = WeightManagerAdapter()
        
        # 测试获取权重
        ensemble_weights = manager.get_ensemble_weights()
        model_weights = manager.get_model_weights()
        feature_weights = manager.get_feature_weights()
        loss_weights = manager.get_loss_weights()
        
        print("✅ 权重管理器创建成功")
        print(f"   集成权重数量: {len(ensemble_weights)}")
        print(f"   模型权重数量: {len(model_weights)}")
        print(f"   特征权重数量: {len(feature_weights)}")
        print(f"   损失权重数量: {len(loss_weights)}")
        
        # 验证权重值
        assert len(ensemble_weights) > 0, "集成权重不能为空"
        assert len(feature_weights) > 0, "特征权重不能为空"
        assert 'red_odd_even' in feature_weights, "缺少红球奇偶比权重"
        assert 'red_size' in feature_weights, "缺少红球大小比权重"
        
        print("✅ 权重配置验证通过")
        
        # 测试权重更新
        manager.update_weight_performance('red_odd_even', 0.35, 0.8)
        print("✅ 权重性能更新成功")
        
        # 获取状态
        status = manager.get_status()
        print(f"✅ 状态获取成功: {status['total_weights']} 个权重")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_config_structure():
    """测试权重配置结构"""
    print("\n" + "=" * 60)
    print("测试 2: 权重配置结构")
    print("=" * 60)
    
    try:
        from src.integrations.weight_manager_adapter import WeightConfig, PerformanceMetrics
        
        # 测试权重配置类
        config = WeightConfig(
            name="test_weight",
            category="feature",
            base_weight=0.5,
            min_weight=0.1,
            max_weight=1.0
        )
        
        print("✅ WeightConfig 类创建成功")
        print(f"   权重名称: {config.name}")
        print(f"   权重类别: {config.category}")
        print(f"   基础权重: {config.base_weight}")
        
        # 测试性能指标类
        metrics = PerformanceMetrics(
            accuracy=0.8,
            hit_rate_2_plus_1=0.35,
            stability=0.9,
            confidence=0.75
        )
        
        print("✅ PerformanceMetrics 类创建成功")
        print(f"   准确率: {metrics.accuracy}")
        print(f"   2+1命中率: {metrics.hit_rate_2_plus_1}")
        print(f"   稳定性: {metrics.stability}")
        print(f"   置信度: {metrics.confidence}")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重配置结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_integration_basic():
    """测试权重集成基本功能"""
    print("\n" + "=" * 60)
    print("测试 3: 权重集成基本功能")
    print("=" * 60)
    
    try:
        from src.integrations.weight_integration_adapter import WeightIntegrationAdapter
        
        # 创建集成适配器（禁用学习和优化以避免numpy依赖）
        adapter = WeightIntegrationAdapter(enable_learning=False, enable_optimization=False)
        
        print("✅ 权重集成适配器创建成功")
        
        # 获取集成权重
        weights = adapter.get_integrated_weights()
        total_weights = sum(len(category_weights) for category_weights in weights.values())
        print(f"✅ 集成权重获取成功: {total_weights} 个权重")
        
        for category, category_weights in weights.items():
            print(f"   {category}: {len(category_weights)} 个权重")
        
        # 验证权重结构
        assert 'ensemble' in weights, "缺少集成权重"
        assert 'feature' in weights, "缺少特征权重"
        assert 'model' in weights, "缺少模型权重"
        assert 'loss' in weights, "缺少损失权重"
        
        print("✅ 权重结构验证通过")
        
        # 模拟性能反馈
        adapter.update_performance_feedback(0.35, accuracy=0.8)
        print("✅ 性能反馈更新成功")
        
        # 获取状态
        status = adapter.get_integration_status()
        print(f"✅ 集成状态获取成功")
        print(f"   预测次数: {status['performance_tracking']['prediction_count']}")
        print(f"   性能缓冲区大小: {status['performance_tracking']['performance_buffer_size']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重集成基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_categories():
    """测试权重分类功能"""
    print("\n" + "=" * 60)
    print("测试 4: 权重分类功能")
    print("=" * 60)
    
    try:
        from src.integrations.weight_integration_adapter import WeightIntegrationAdapter
        
        adapter = WeightIntegrationAdapter(enable_learning=False, enable_optimization=False)
        
        # 验证权重分类映射
        expected_categories = {
            'ensemble': ['bayesian', 'markov1', 'markov2', 'frequency', 'trend', 'correlation'],
            'model': ['markov', 'bayes', 'neural'],
            'feature': ['red_odd_even', 'red_size', 'blue_size'],
            'loss': ['red_odd_even_loss', 'red_size_loss', 'blue_size_loss', 'red_numbers_loss', 'blue_numbers_loss']
        }
        
        weights = adapter.get_integrated_weights()
        
        for category, expected_weights in expected_categories.items():
            actual_weights = weights.get(category, {})
            print(f"   {category} 类别:")
            print(f"     期望权重: {len(expected_weights)}")
            print(f"     实际权重: {len(actual_weights)}")
            
            # 检查关键权重是否存在
            for weight_name in expected_weights:
                if weight_name in actual_weights:
                    print(f"     ✅ {weight_name}: {actual_weights[weight_name]:.3f}")
                else:
                    print(f"     ⚠️  {weight_name}: 未找到")
        
        print("✅ 权重分类功能验证完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重分类功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_tracking():
    """测试性能跟踪功能"""
    print("\n" + "=" * 60)
    print("测试 5: 性能跟踪功能")
    print("=" * 60)
    
    try:
        from src.integrations.weight_integration_adapter import WeightIntegrationAdapter
        
        adapter = WeightIntegrationAdapter(enable_learning=False, enable_optimization=False)
        
        print("模拟多次性能反馈...")
        
        # 模拟多次性能反馈
        test_performances = [0.25, 0.30, 0.28, 0.35, 0.32, 0.38, 0.36, 0.40]
        
        for i, performance in enumerate(test_performances):
            adapter.update_performance_feedback(
                hit_rate_2_plus_1=performance,
                accuracy=performance * 0.9,
                additional_metrics={
                    'prediction_consistency': 0.8,
                    'model_agreement': 0.75
                }
            )
            print(f"   第{i+1}次: 2+1命中率={performance:.3f}")
        
        # 获取最终状态
        status = adapter.get_integration_status()
        
        print(f"✅ 性能跟踪验证成功")
        print(f"   总预测次数: {status['performance_tracking']['prediction_count']}")
        print(f"   当前性能: {status['performance_tracking'].get('current_performance', 0):.3f}")
        print(f"   平均性能: {status['performance_tracking'].get('average_performance', 0):.3f}")
        print(f"   性能趋势: {status['performance_tracking'].get('performance_trend', 'unknown')}")
        
        # 验证性能数据
        assert status['performance_tracking']['prediction_count'] == len(test_performances)
        assert status['performance_tracking']['performance_buffer_size'] > 0
        
        print("✅ 性能数据验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能跟踪功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_weights():
    """测试后备权重功能"""
    print("\n" + "=" * 60)
    print("测试 6: 后备权重功能")
    print("=" * 60)
    
    try:
        from src.integrations.weight_integration_adapter import WeightIntegrationAdapter
        
        adapter = WeightIntegrationAdapter(enable_learning=False, enable_optimization=False)
        
        # 获取后备权重
        fallback_weights = adapter._get_fallback_weights()
        
        print("✅ 后备权重获取成功")
        
        # 验证后备权重结构
        required_categories = ['ensemble', 'model', 'feature', 'loss']
        for category in required_categories:
            assert category in fallback_weights, f"缺少{category}类别的后备权重"
            assert len(fallback_weights[category]) > 0, f"{category}类别的后备权重为空"
            print(f"   {category}: {len(fallback_weights[category])} 个权重")
        
        # 验证关键权重
        feature_weights = fallback_weights['feature']
        assert 'red_odd_even' in feature_weights, "缺少红球奇偶比后备权重"
        assert 'red_size' in feature_weights, "缺少红球大小比后备权重"
        assert 'blue_size' in feature_weights, "缺少蓝球大小比后备权重"
        
        print("✅ 后备权重结构验证通过")
        print(f"   红球奇偶比权重: {feature_weights['red_odd_even']}")
        print(f"   红球大小比权重: {feature_weights['red_size']}")
        print(f"   蓝球大小比权重: {feature_weights['blue_size']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 后备权重功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主验证函数"""
    print("Phase 3 权重系统重构简化验证")
    print("=" * 80)
    print("验证智能权重管理系统的核心功能（不依赖numpy）:")
    print("1. 权重管理器基本功能")
    print("2. 权重配置结构")
    print("3. 权重集成基本功能")
    print("4. 权重分类功能")
    print("5. 性能跟踪功能")
    print("6. 后备权重功能")
    
    # 执行所有测试
    tests = [
        ("权重管理器基本功能", test_weight_manager_basic),
        ("权重配置结构", test_weight_config_structure),
        ("权重集成基本功能", test_weight_integration_basic),
        ("权重分类功能", test_weight_categories),
        ("性能跟踪功能", test_performance_tracking),
        ("后备权重功能", test_fallback_weights)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结报告
    print("\n" + "=" * 80)
    print("Phase 3 简化验证总结报告")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 Phase 3 权重系统重构基本功能验证成功！")
        print("\n核心功能验证:")
        print("✅ 智能权重管理 - 统一权重配置管理")
        print("✅ 权重分类 - 按类别组织权重")
        print("✅ 性能跟踪 - 记录和分析性能数据")
        print("✅ 系统集成 - 基本集成功能正常")
        print("✅ 后备机制 - 提供可靠的后备权重")
        print("\nPhase 3 基本功能已验证，可以进行下一步集成！")
        print("\n注意: 高级功能（优化器和自适应学习）需要numpy支持")
    else:
        print(f"\n⚠️  Phase 3 验证部分失败 ({passed}/{total})")
        print("需要修复失败的组件后再进行集成。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)