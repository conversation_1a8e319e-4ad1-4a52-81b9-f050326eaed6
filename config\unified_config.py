"""
统一配置管理系统
整合所有分散的配置类，提供单例模式的配置获取方式
遵循DRY原则，避免配置重复和不一致
"""

import os
import json
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path
from threading import Lock

# 导入现有配置类
from .settings import DataConfig, GeneratorConfig, BacktestConfig, LoggingConfig
from .model_config import MarkovConfig, BayesConfig, NeuralConfig, EnsembleConfig


@dataclass
class PredictorConfig:
    """预测器统一配置"""
    # 奇偶比预测器配置
    odd_even_algorithm: str = "enhanced"  # enhanced, optimized, advanced
    odd_even_weights: Dict[str, float] = field(default_factory=lambda: {
        "markov": 0.4,
        "frequency": 0.3,
        "pattern": 0.3
    })
    
    # 大小比预测器配置
    size_ratio_algorithm: str = "enhanced"
    size_ratio_weights: Dict[str, float] = field(default_factory=lambda: {
        "markov": 0.5,
        "frequency": 0.5
    })
    
    # 决策树配置
    decision_tree_max_depth: int = 10
    decision_tree_min_samples_split: int = 5
    decision_tree_min_samples_leaf: int = 2
    decision_tree_random_state: int = 42
    
    # 神经网络预测器配置
    neural_predictor_type: str = "enhanced"  # basic, enhanced, advanced
    neural_ensemble_size: int = 5
    
    # 通用预测器配置
    prediction_confidence_threshold: float = 0.6
    enable_ensemble_voting: bool = True
    ensemble_voting_strategy: str = "weighted"  # weighted, majority
    
    def validate(self) -> bool:
        """验证预测器配置"""
        try:
            # 验证权重总和
            for weights_name, weights in [
                ("odd_even_weights", self.odd_even_weights),
                ("size_ratio_weights", self.size_ratio_weights)
            ]:
                total = sum(weights.values())
                if abs(total - 1.0) > 0.01:
                    raise ValueError(f"{weights_name}权重总和必须为1.0，当前为{total}")
            
            # 验证阈值范围
            if not 0 < self.prediction_confidence_threshold < 1:
                raise ValueError("预测置信度阈值必须在0-1之间")
            
            return True
        except Exception as e:
            print(f"预测器配置验证失败: {e}")
            return False


@dataclass
class RatioDefinitionConfig:
    """比例定义标准化配置 - 与LotteryConfig保持一致"""
    # 红球大小比分界点（统一标准）
    red_ball_small_max: int = 17  # 小号：1-17
    red_ball_big_min: int = 18    # 大号：18-35

    # 蓝球大小比分界点（统一标准）
    blue_ball_small_max: int = 6  # 小号：1-6
    blue_ball_big_min: int = 7    # 大号：7-12
    
    # 红球奇偶比（无需配置，奇数偶数固定）
    # 蓝球奇偶比（无需配置，奇数偶数固定）
    
    def get_red_ball_size_category(self, number: int) -> str:
        """获取红球大小类别"""
        if 1 <= number <= self.red_ball_small_max:
            return "small"
        elif self.red_ball_big_min <= number <= 35:
            return "big"
        else:
            raise ValueError(f"红球号码{number}超出有效范围1-35")
    
    def get_blue_ball_size_category(self, number: int) -> str:
        """获取蓝球大小类别"""
        if 1 <= number <= self.blue_ball_small_max:
            return "small"
        elif self.blue_ball_big_min <= number <= 12:
            return "big"
        else:
            raise ValueError(f"蓝球号码{number}超出有效范围1-12")
    
    def validate(self) -> bool:
        """验证比例定义配置"""
        try:
            # 验证红球分界点
            if self.red_ball_small_max + 1 != self.red_ball_big_min:
                raise ValueError("红球大小比分界点必须连续")
            if not (1 <= self.red_ball_small_max < 35):
                raise ValueError("红球小号上限必须在1-34之间")
            if not (2 <= self.red_ball_big_min <= 35):
                raise ValueError("红球大号下限必须在2-35之间")
            
            # 验证蓝球分界点
            if self.blue_ball_small_max + 1 != self.blue_ball_big_min:
                raise ValueError("蓝球大小比分界点必须连续")
            if not (1 <= self.blue_ball_small_max < 12):
                raise ValueError("蓝球小号上限必须在1-11之间")
            if not (2 <= self.blue_ball_big_min <= 12):
                raise ValueError("蓝球大号下限必须在2-12之间")
            
            return True
        except Exception as e:
            print(f"比例定义配置验证失败: {e}")
            return False


@dataclass
class UnifiedConfig:
    """统一配置管理类"""
    # 基础信息
    project_name: str = "大乐透预测系统"
    version: str = "2.1.0"
    debug: bool = False
    
    # 子配置模块
    data: DataConfig = field(default_factory=DataConfig)
    model: Dict[str, Any] = field(default_factory=lambda: {
        "markov": MarkovConfig(),
        "bayes": BayesConfig(),
        "neural": NeuralConfig(),
        "ensemble": EnsembleConfig()
    })
    predictor: PredictorConfig = field(default_factory=PredictorConfig)
    ratio_definition: RatioDefinitionConfig = field(default_factory=RatioDefinitionConfig)
    generator: GeneratorConfig = field(default_factory=GeneratorConfig)
    backtest: BacktestConfig = field(default_factory=BacktestConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # 环境变量覆盖
    def __post_init__(self):
        """从环境变量加载配置覆盖"""
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 数据配置
        if os.getenv('DATA_FILE'):
            self.data.data_file = os.getenv('DATA_FILE')
        
        # 调试模式
        if os.getenv('DEBUG'):
            self.debug = os.getenv('DEBUG').lower() == 'true'
        
        # 日志级别
        if os.getenv('LOG_LEVEL'):
            self.logging.level = os.getenv('LOG_LEVEL')
        
        # 回测期数
        if os.getenv('BACKTEST_PERIODS'):
            self.backtest.default_periods = int(os.getenv('BACKTEST_PERIODS'))
    
    def validate_all(self) -> bool:
        """验证所有配置的有效性"""
        try:
            # 验证数据文件存在
            data_path = Path(self.data.data_file)
            if not data_path.exists():
                raise FileNotFoundError(f"数据文件不存在: {data_path}")
            
            # 验证各子配置
            if not self.predictor.validate():
                return False
            
            if not self.ratio_definition.validate():
                return False
            
            # 验证模型配置
            for model_name, model_config in self.model.items():
                if hasattr(model_config, 'validate') and not model_config.validate():
                    print(f"模型配置{model_name}验证失败")
                    return False
            
            return True
            
        except Exception as e:
            print(f"配置验证失败: {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "project_name": self.project_name,
            "version": self.version,
            "debug": self.debug,
            "data": self.data.__dict__,
            "model": {k: v.__dict__ if hasattr(v, '__dict__') else v for k, v in self.model.items()},
            "predictor": self.predictor.__dict__,
            "ratio_definition": self.ratio_definition.__dict__,
            "generator": self.generator.__dict__,
            "backtest": self.backtest.__dict__,
            "logging": self.logging.__dict__
        }
    
    def save_to_file(self, file_path: str):
        """保存配置到文件"""
        config_dict = self.to_dict()
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_from_file(cls, file_path: str) -> 'UnifiedConfig':
        """从文件加载配置"""
        with open(file_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        # 这里需要实现从字典重建配置对象的逻辑
        # 为简化，先返回默认配置
        return cls()


class ConfigManager:
    """配置管理器 - 单例模式"""
    _instance: Optional[UnifiedConfig] = None
    _lock = Lock()
    
    @classmethod
    def get_instance(cls) -> UnifiedConfig:
        """获取配置实例（单例模式）"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = UnifiedConfig()
                    # 验证配置
                    if not cls._instance.validate_all():
                        print("⚠️ 配置验证失败，使用默认配置")
        return cls._instance
    
    @classmethod
    def reload_config(cls, config_file: Optional[str] = None) -> UnifiedConfig:
        """重新加载配置"""
        with cls._lock:
            if config_file:
                cls._instance = UnifiedConfig.load_from_file(config_file)
            else:
                cls._instance = UnifiedConfig()
            
            if not cls._instance.validate_all():
                print("⚠️ 配置验证失败，使用默认配置")
                cls._instance = UnifiedConfig()
            
            return cls._instance
    
    @classmethod
    def get_data_config(cls) -> DataConfig:
        """获取数据配置"""
        return cls.get_instance().data
    
    @classmethod
    def get_model_config(cls, model_name: str) -> Any:
        """获取指定模型配置"""
        return cls.get_instance().model.get(model_name)
    
    @classmethod
    def get_predictor_config(cls) -> PredictorConfig:
        """获取预测器配置"""
        return cls.get_instance().predictor
    
    @classmethod
    def get_ratio_definition(cls) -> RatioDefinitionConfig:
        """获取比例定义配置"""
        return cls.get_instance().ratio_definition
    
    @classmethod
    def get_backtest_config(cls) -> BacktestConfig:
        """获取回测配置"""
        return cls.get_instance().backtest


# 便捷函数
def get_config() -> UnifiedConfig:
    """获取统一配置实例"""
    return ConfigManager.get_instance()

def get_data_config() -> DataConfig:
    """获取数据配置"""
    return ConfigManager.get_data_config()

def get_model_config(model_name: str) -> Any:
    """获取模型配置"""
    return ConfigManager.get_model_config(model_name)

def get_predictor_config() -> PredictorConfig:
    """获取预测器配置"""
    return ConfigManager.get_predictor_config()

def get_ratio_definition() -> RatioDefinitionConfig:
    """获取比例定义配置"""
    return ConfigManager.get_ratio_definition()
