#!/usr/bin/env python3
"""
交互式参数调优界面
提供用户友好的参数微调体验
"""

import sys
import os
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.advanced_tuning_config import AdvancedTuningConfig, TuningTemplates
from src.systems.intelligent_tuner import IntelligentTuner, TuningRecommendationEngine
from src.systems.number_selection_system import NumberSelectionSystem


class InteractiveTuningInterface:
    """交互式调优界面"""
    
    def __init__(self):
        self.tuner = IntelligentTuner()
        self.recommendation_engine = TuningRecommendationEngine()
        self.logger = logging.getLogger(__name__)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
    
    def run(self):
        """运行交互式界面"""
        print("=" * 80)
        print("🎯 智能参数调优系统")
        print("=" * 80)
        print("欢迎使用智能参数调优系统！")
        print("本系统将帮助您优化彩票预测模型的训练参数。")
        print()
        
        while True:
            self.show_main_menu()
            choice = input("请选择操作 (1-7): ").strip()
            
            if choice == "1":
                self.quick_diagnosis()
            elif choice == "2":
                self.strategy_selection()
            elif choice == "3":
                self.custom_tuning()
            elif choice == "4":
                self.ab_testing()
            elif choice == "5":
                self.auto_tuning()
            elif choice == "6":
                self.view_recommendations()
            elif choice == "7":
                print("感谢使用智能参数调优系统！")
                break
            else:
                print("❌ 无效选择，请重试。")
            
            input("\n按回车键继续...")
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "=" * 60)
        print("📋 主菜单")
        print("=" * 60)
        print("1. 🔍 快速诊断 - 分析当前训练问题")
        print("2. 🎯 策略选择 - 选择预设调优策略")
        print("3. ⚙️  自定义调优 - 手动调整参数")
        print("4. 🧪 A/B测试 - 对比多个配置")
        print("5. 🤖 自动调优 - AI自动优化参数")
        print("6. 💡 查看建议 - 获取个性化建议")
        print("7. 🚪 退出系统")
        print("-" * 60)
    
    def quick_diagnosis(self):
        """快速诊断"""
        print("\n" + "=" * 60)
        print("🔍 快速诊断")
        print("=" * 60)
        
        print("正在分析当前训练状态...")
        
        # 模拟获取训练日志（实际应用中从真实日志获取）
        mock_training_logs = {
            "training_history": {
                "train_loss": [1.2, 1.0, 0.9, 0.87, 0.86, 0.86, 0.86],
                "val_loss": [1.1, 0.95, 0.88, 0.86, 0.86, 0.87, 0.88],
            },
            "performance_metrics": {
                "deep_learning_accuracy": 0.08,  # 8%
                "confidence": 0.12,
            }
        }
        
        diagnosis = self.tuner.diagnose_training_issues(mock_training_logs)
        
        print(f"\n📊 诊断结果 (严重程度: {diagnosis['severity']})")
        print("-" * 40)
        
        if diagnosis["issues"]:
            print("发现的问题:")
            for i, issue in enumerate(diagnosis["issues"], 1):
                severity_icon = {"low": "🟡", "medium": "🟠", "high": "🔴"}
                icon = severity_icon.get(issue["severity"], "⚪")
                print(f"  {i}. {icon} {issue['description']}")
        else:
            print("✅ 未发现明显问题")
        
        if diagnosis["recommendations"]:
            print("\n💡 建议:")
            for i, rec in enumerate(diagnosis["recommendations"], 1):
                print(f"  {i}. {rec}")
        
        # 生成建议配置
        current_performance = mock_training_logs["performance_metrics"]
        suggested_config = self.tuner.suggest_optimal_config(current_performance)
        
        print(f"\n🎯 建议配置要点:")
        print(f"  学习率: {suggested_config.initial_learning_rate}")
        print(f"  序列长度: {suggested_config.sequence_length}")
        print(f"  早停耐心: {suggested_config.early_stopping_patience}")
        print(f"  LSTM隐藏层: {suggested_config.lstm_config['hidden_size']}")
        
        apply = input("\n是否应用建议配置? (y/n): ").strip().lower()
        if apply == 'y':
            print("✅ 配置已应用！")
            # 这里应该保存配置并应用到系统
        else:
            print("配置未应用。")
    
    def strategy_selection(self):
        """策略选择"""
        print("\n" + "=" * 60)
        print("🎯 策略选择")
        print("=" * 60)
        
        strategies = {
            "1": ("激进学习", "aggressive_learning", "提高学习率，减少正则化，适合突破训练瓶颈"),
            "2": ("深度模型", "deep_model", "增加模型容量，适合提升预测能力"),
            "3": ("数据增强", "data_enhanced", "改进数据处理，适合数据质量问题"),
            "4": ("集成优化", "ensemble_optimized", "优化集成学习，平衡准确率和稳定性"),
            "5": ("突破性配置", "breakthrough", "最激进的配置，适合寻求重大突破"),
        }
        
        print("可用的调优策略:")
        for key, (name, _, desc) in strategies.items():
            print(f"  {key}. {name} - {desc}")
        
        choice = input("\n请选择策略 (1-5): ").strip()
        
        if choice in strategies:
            name, strategy_key, desc = strategies[choice]
            print(f"\n选择了: {name}")
            print(f"描述: {desc}")
            
            # 获取配置
            config_func = getattr(TuningTemplates, f"get_{strategy_key}_config")
            config = config_func()
            
            # 显示配置详情
            self.show_config_details(config)
            
            apply = input("\n是否应用此策略? (y/n): ").strip().lower()
            if apply == 'y':
                print("✅ 策略已应用！")
                # 这里应该保存配置并应用到系统
            else:
                print("策略未应用。")
        else:
            print("❌ 无效选择。")
    
    def custom_tuning(self):
        """自定义调优"""
        print("\n" + "=" * 60)
        print("⚙️ 自定义调优")
        print("=" * 60)
        
        # 获取当前配置
        current_config = AdvancedTuningConfig()
        
        print("当前配置:")
        self.show_config_summary(current_config)
        
        print("\n可调整的参数:")
        print("1. 学习率")
        print("2. 序列长度")
        print("3. 训练轮数")
        print("4. 批次大小")
        print("5. 早停耐心值")
        print("6. 正则化强度")
        print("7. 模型大小")
        
        while True:
            choice = input("\n选择要调整的参数 (1-7, 0退出): ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                self.adjust_learning_rate(current_config)
            elif choice == "2":
                self.adjust_sequence_length(current_config)
            elif choice == "3":
                self.adjust_epochs(current_config)
            elif choice == "4":
                self.adjust_batch_size(current_config)
            elif choice == "5":
                self.adjust_patience(current_config)
            elif choice == "6":
                self.adjust_regularization(current_config)
            elif choice == "7":
                self.adjust_model_size(current_config)
            else:
                print("❌ 无效选择。")
                continue
            
            print("\n更新后的配置:")
            self.show_config_summary(current_config)
        
        save = input("\n是否保存自定义配置? (y/n): ").strip().lower()
        if save == 'y':
            print("✅ 自定义配置已保存！")
    
    def ab_testing(self):
        """A/B测试"""
        print("\n" + "=" * 60)
        print("🧪 A/B测试")
        print("=" * 60)
        
        print("A/B测试将比较多个配置的性能。")
        print("注意: 这将需要较长时间来训练多个模型。")
        
        # 预设测试组合
        test_configs = [
            ("当前配置", AdvancedTuningConfig()),
            ("激进学习", TuningTemplates.get_aggressive_learning_config()),
            ("深度模型", TuningTemplates.get_deep_model_config()),
        ]
        
        print(f"\n将测试 {len(test_configs)} 个配置:")
        for i, (name, _) in enumerate(test_configs, 1):
            print(f"  {i}. {name}")
        
        proceed = input("\n是否开始A/B测试? (y/n): ").strip().lower()
        if proceed == 'y':
            print("🚀 开始A/B测试...")
            
            configs = [config for _, config in test_configs]
            results = self.tuner.run_ab_test(configs, "interactive_ab_test")
            
            print("\n📊 A/B测试结果:")
            print("-" * 40)
            
            for i, (name, _) in enumerate(test_configs):
                result = results["results"][i]
                performance = result.get("performance", {})
                score = performance.get("overall_score", 0)
                print(f"  {name}: {score:.4f}")
            
            best_index = results["best_config_index"]
            best_name = test_configs[best_index][0]
            print(f"\n🏆 最佳配置: {best_name}")
            
        else:
            print("A/B测试已取消。")
    
    def auto_tuning(self):
        """自动调优"""
        print("\n" + "=" * 60)
        print("🤖 自动调优")
        print("=" * 60)
        
        print("自动调优将使用AI算法自动优化参数。")
        print("系统将进行多轮迭代，每轮都会改进配置。")
        
        iterations = input("\n请输入迭代次数 (建议3-5次): ").strip()
        try:
            iterations = int(iterations)
            if iterations < 1 or iterations > 10:
                print("❌ 迭代次数应在1-10之间。")
                return
        except ValueError:
            print("❌ 请输入有效的数字。")
            return
        
        proceed = input(f"\n开始 {iterations} 轮自动调优? (y/n): ").strip().lower()
        if proceed == 'y':
            print("🚀 开始自动调优...")
            
            best_config = self.tuner.auto_tune(max_iterations=iterations)
            
            print("\n🎉 自动调优完成！")
            print("最优配置:")
            self.show_config_details(best_config)
            
            apply = input("\n是否应用最优配置? (y/n): ").strip().lower()
            if apply == 'y':
                print("✅ 最优配置已应用！")
            else:
                print("配置未应用。")
        else:
            print("自动调优已取消。")
    
    def view_recommendations(self):
        """查看建议"""
        print("\n" + "=" * 60)
        print("💡 个性化建议")
        print("=" * 60)
        
        print("请告诉我们您的偏好:")
        
        # 收集用户偏好
        print("\n1. 您更关注什么?")
        print("   a) 预测准确率")
        print("   b) 训练速度")
        print("   c) 平衡两者")
        
        priority_choice = input("选择 (a/b/c): ").strip().lower()
        priority_map = {"a": "accuracy", "b": "speed", "c": "balance"}
        priority = priority_map.get(priority_choice, "balance")
        
        print("\n2. 您的风险承受能力?")
        print("   a) 保守 (稳定改进)")
        print("   b) 中等 (适度冒险)")
        print("   c) 激进 (追求突破)")
        
        risk_choice = input("选择 (a/b/c): ").strip().lower()
        risk_map = {"a": "low", "b": "medium", "c": "high"}
        risk_tolerance = risk_map.get(risk_choice, "medium")
        
        # 生成个性化建议
        user_preferences = {
            "priority": priority,
            "risk_tolerance": risk_tolerance,
        }
        
        current_performance = {"accuracy": 0.08, "confidence": 0.12}
        
        recommendations = self.recommendation_engine.get_personalized_recommendations(
            user_preferences, current_performance
        )
        
        print(f"\n🎯 基于您的偏好的建议:")
        print("-" * 40)
        
        for i, rec in enumerate(recommendations, 1):
            risk_icon = {"low": "🟢", "medium": "🟡", "high": "🔴"}
            icon = risk_icon.get(rec["risk"], "⚪")
            
            print(f"{i}. {rec['strategy'].upper()}")
            print(f"   {icon} 风险: {rec['risk']}")
            print(f"   📈 预期改进: {rec['expected_improvement']}")
            print(f"   📝 描述: {rec['description']}")
            print()
    
    def show_config_details(self, config: AdvancedTuningConfig):
        """显示配置详情"""
        print("\n📋 配置详情:")
        print("-" * 30)
        print(f"学习率: {config.initial_learning_rate}")
        print(f"序列长度: {config.sequence_length}")
        print(f"训练轮数: {config.epochs}")
        print(f"批次大小: {config.batch_size}")
        print(f"早停耐心: {config.early_stopping_patience}")
        print(f"权重衰减: {config.weight_decay}")
        print(f"Dropout: {config.dropout_rate}")
        print(f"LSTM隐藏层: {config.lstm_config['hidden_size']}")
        print(f"Transformer维度: {config.transformer_config['d_model']}")
    
    def show_config_summary(self, config: AdvancedTuningConfig):
        """显示配置摘要"""
        print(f"学习率: {config.initial_learning_rate}, "
              f"序列长度: {config.sequence_length}, "
              f"轮数: {config.epochs}, "
              f"批次: {config.batch_size}")
    
    def adjust_learning_rate(self, config: AdvancedTuningConfig):
        """调整学习率"""
        current = config.initial_learning_rate
        print(f"当前学习率: {current}")
        print("建议范围: 0.001 - 0.01")
        
        new_lr = input("输入新的学习率: ").strip()
        try:
            new_lr = float(new_lr)
            if 0.0001 <= new_lr <= 0.1:
                config.initial_learning_rate = new_lr
                print(f"✅ 学习率已更新为: {new_lr}")
            else:
                print("❌ 学习率超出合理范围。")
        except ValueError:
            print("❌ 请输入有效的数字。")
    
    def adjust_sequence_length(self, config: AdvancedTuningConfig):
        """调整序列长度"""
        current = config.sequence_length
        print(f"当前序列长度: {current}")
        print("建议范围: 15 - 100")
        
        new_length = input("输入新的序列长度: ").strip()
        try:
            new_length = int(new_length)
            if 10 <= new_length <= 200:
                config.sequence_length = new_length
                print(f"✅ 序列长度已更新为: {new_length}")
            else:
                print("❌ 序列长度超出合理范围。")
        except ValueError:
            print("❌ 请输入有效的整数。")
    
    def adjust_epochs(self, config: AdvancedTuningConfig):
        """调整训练轮数"""
        current = config.epochs
        print(f"当前训练轮数: {current}")
        print("建议范围: 50 - 500")
        
        new_epochs = input("输入新的训练轮数: ").strip()
        try:
            new_epochs = int(new_epochs)
            if 20 <= new_epochs <= 1000:
                config.epochs = new_epochs
                print(f"✅ 训练轮数已更新为: {new_epochs}")
            else:
                print("❌ 训练轮数超出合理范围。")
        except ValueError:
            print("❌ 请输入有效的整数。")
    
    def adjust_batch_size(self, config: AdvancedTuningConfig):
        """调整批次大小"""
        current = config.batch_size
        print(f"当前批次大小: {current}")
        print("建议范围: 8 - 128")
        
        new_batch = input("输入新的批次大小: ").strip()
        try:
            new_batch = int(new_batch)
            if 4 <= new_batch <= 256:
                config.batch_size = new_batch
                print(f"✅ 批次大小已更新为: {new_batch}")
            else:
                print("❌ 批次大小超出合理范围。")
        except ValueError:
            print("❌ 请输入有效的整数。")
    
    def adjust_patience(self, config: AdvancedTuningConfig):
        """调整早停耐心值"""
        current = config.early_stopping_patience
        print(f"当前早停耐心值: {current}")
        print("建议范围: 10 - 100")
        
        new_patience = input("输入新的早停耐心值: ").strip()
        try:
            new_patience = int(new_patience)
            if 5 <= new_patience <= 200:
                config.early_stopping_patience = new_patience
                print(f"✅ 早停耐心值已更新为: {new_patience}")
            else:
                print("❌ 早停耐心值超出合理范围。")
        except ValueError:
            print("❌ 请输入有效的整数。")
    
    def adjust_regularization(self, config: AdvancedTuningConfig):
        """调整正则化强度"""
        print(f"当前Dropout: {config.dropout_rate}")
        print(f"当前权重衰减: {config.weight_decay}")
        
        print("\n1. 调整Dropout")
        print("2. 调整权重衰减")
        choice = input("选择 (1/2): ").strip()
        
        if choice == "1":
            new_dropout = input("输入新的Dropout值 (0.0-0.8): ").strip()
            try:
                new_dropout = float(new_dropout)
                if 0.0 <= new_dropout <= 0.9:
                    config.dropout_rate = new_dropout
                    print(f"✅ Dropout已更新为: {new_dropout}")
                else:
                    print("❌ Dropout超出合理范围。")
            except ValueError:
                print("❌ 请输入有效的数字。")
        
        elif choice == "2":
            new_decay = input("输入新的权重衰减值 (0.0-0.1): ").strip()
            try:
                new_decay = float(new_decay)
                if 0.0 <= new_decay <= 0.2:
                    config.weight_decay = new_decay
                    print(f"✅ 权重衰减已更新为: {new_decay}")
                else:
                    print("❌ 权重衰减超出合理范围。")
            except ValueError:
                print("❌ 请输入有效的数字。")
    
    def adjust_model_size(self, config: AdvancedTuningConfig):
        """调整模型大小"""
        print(f"当前LSTM隐藏层: {config.lstm_config['hidden_size']}")
        print(f"当前Transformer维度: {config.transformer_config['d_model']}")
        
        print("\n1. 调整LSTM隐藏层大小")
        print("2. 调整Transformer维度")
        choice = input("选择 (1/2): ").strip()
        
        if choice == "1":
            new_hidden = input("输入新的LSTM隐藏层大小 (64-1024): ").strip()
            try:
                new_hidden = int(new_hidden)
                if 32 <= new_hidden <= 2048:
                    config.lstm_config["hidden_size"] = new_hidden
                    print(f"✅ LSTM隐藏层已更新为: {new_hidden}")
                else:
                    print("❌ 隐藏层大小超出合理范围。")
            except ValueError:
                print("❌ 请输入有效的整数。")
        
        elif choice == "2":
            new_dim = input("输入新的Transformer维度 (128-1024): ").strip()
            try:
                new_dim = int(new_dim)
                if 64 <= new_dim <= 2048:
                    config.transformer_config["d_model"] = new_dim
                    print(f"✅ Transformer维度已更新为: {new_dim}")
                else:
                    print("❌ Transformer维度超出合理范围。")
            except ValueError:
                print("❌ 请输入有效的整数。")


def main():
    """主函数"""
    interface = InteractiveTuningInterface()
    interface.run()


if __name__ == "__main__":
    main()
