#!/usr/bin/env python3
"""
高级特征工程系统
Advanced Feature Engineering System

探索时间序列分析、频域分析、图论分析等新的特征提取方法
发现更深层的数据模式，提升预测准确率
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, field
import logging
from pathlib import Path
import warnings

warnings.filterwarnings("ignore")

# 尝试导入可选依赖
try:
    from scipy import signal, stats
    from scipy.fft import fft, fftfreq

    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

try:
    import networkx as nx

    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False

try:
    from sklearn.decomposition import PCA, FastICA
    from sklearn.manifold import TSNE
    from sklearn.cluster import KMeans
    from sklearn.preprocessing import StandardScaler

    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


@dataclass
class FeatureConfig:
    """特征工程配置"""

    # 时间序列特征
    enable_time_series: bool = True
    ts_window_sizes: List[int] = field(default_factory=lambda: [5, 10, 20, 30])
    ts_lag_features: List[int] = field(default_factory=lambda: [1, 2, 3, 5, 7])

    # 频域分析特征
    enable_frequency_domain: bool = True
    fft_window_size: int = 50
    frequency_bands: int = 10

    # 图论分析特征
    enable_graph_analysis: bool = True
    graph_window_size: int = 30
    graph_threshold: float = 0.3

    # 统计特征
    enable_statistical: bool = True
    stat_window_sizes: List[int] = field(default_factory=lambda: [10, 20, 50])

    # 模式识别特征
    enable_pattern_recognition: bool = True
    pattern_lengths: List[int] = field(default_factory=lambda: [3, 4, 5])

    # 聚类特征
    enable_clustering: bool = True
    n_clusters: int = 8

    # 降维特征
    enable_dimensionality_reduction: bool = True
    pca_components: int = 10

    # 交互特征
    enable_interaction: bool = True
    interaction_degree: int = 2


class TimeSeriesFeatureExtractor:
    """时间序列特征提取器"""

    def __init__(self, config: FeatureConfig):
        """
        初始化时间序列特征提取器

        Args:
            config: 特征工程配置
        """
        self.config = config
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")

    def extract_features(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """
        提取时间序列特征

        Args:
            data: 历史数据

        Returns:
            时间序列特征字典
        """
        features = {}

        try:
            # 解析数字序列
            red_sequences = self._parse_number_sequences(data, "red")
            blue_sequences = self._parse_number_sequences(data, "blue")

            # 提取滞后特征
            features.update(self._extract_lag_features(red_sequences, "red"))
            features.update(self._extract_lag_features(blue_sequences, "blue"))

            # 提取滑动窗口统计特征
            features.update(self._extract_rolling_features(red_sequences, "red"))
            features.update(self._extract_rolling_features(blue_sequences, "blue"))

            # 提取趋势特征
            features.update(self._extract_trend_features(red_sequences, "red"))
            features.update(self._extract_trend_features(blue_sequences, "blue"))

            # 提取季节性特征
            features.update(self._extract_seasonal_features(red_sequences, "red"))
            features.update(self._extract_seasonal_features(blue_sequences, "blue"))

            self.logger.info(f"提取时间序列特征: {len(features)} 个特征")

        except Exception as e:
            self.logger.error(f"时间序列特征提取失败: {e}")

        return features

    def _parse_number_sequences(self, data: pd.DataFrame, ball_type: str) -> np.ndarray:
        """解析数字序列"""
        column_name = "红球" if ball_type == "red" else "蓝球"

        sequences = []
        for _, row in data.iterrows():
            try:
                numbers_str = str(row[column_name])
                numbers = [
                    int(x.strip()) for x in numbers_str.split() if x.strip().isdigit()
                ]
                sequences.append(numbers)
            except:
                sequences.append([])

        return np.array(sequences, dtype=object)

    def _extract_lag_features(
        self, sequences: np.ndarray, prefix: str
    ) -> Dict[str, np.ndarray]:
        """提取滞后特征"""
        features = {}

        # 将序列转换为特征向量
        max_numbers = 35 if prefix == "red" else 12
        feature_matrix = np.zeros((len(sequences), max_numbers))

        for i, seq in enumerate(sequences):
            if len(seq) > 0:
                for num in seq:
                    if 1 <= num <= max_numbers:
                        feature_matrix[i, num - 1] = 1

        # 提取滞后特征
        for lag in self.config.ts_lag_features:
            if lag < len(feature_matrix):
                lagged_features = np.roll(feature_matrix, lag, axis=0)
                lagged_features[:lag] = 0  # 前lag行设为0

                for j in range(max_numbers):
                    features[f"{prefix}_lag_{lag}_num_{j+1}"] = lagged_features[:, j]

        return features

    def _extract_rolling_features(
        self, sequences: np.ndarray, prefix: str
    ) -> Dict[str, np.ndarray]:
        """提取滑动窗口统计特征"""
        features = {}

        # 计算每期的统计量
        stats_data = []
        for seq in sequences:
            if len(seq) > 0:
                stats_data.append(
                    {
                        "mean": np.mean(seq),
                        "std": np.std(seq),
                        "min": np.min(seq),
                        "max": np.max(seq),
                        "range": np.max(seq) - np.min(seq),
                        "count": len(seq),
                    }
                )
            else:
                stats_data.append(
                    {"mean": 0, "std": 0, "min": 0, "max": 0, "range": 0, "count": 0}
                )

        stats_df = pd.DataFrame(stats_data)

        # 计算滑动窗口特征
        for window_size in self.config.ts_window_sizes:
            if window_size < len(stats_df):
                for stat_name in ["mean", "std", "min", "max", "range"]:
                    rolling_values = (
                        stats_df[stat_name]
                        .rolling(window=window_size, min_periods=1)
                        .mean()
                    )
                    features[f"{prefix}_rolling_{window_size}_{stat_name}"] = (
                        rolling_values.values
                    )

        return features

    def _extract_trend_features(
        self, sequences: np.ndarray, prefix: str
    ) -> Dict[str, np.ndarray]:
        """提取趋势特征"""
        features = {}

        # 计算数字出现频率的趋势
        max_numbers = 35 if prefix == "red" else 12

        for num in range(1, max_numbers + 1):
            # 计算每个数字的出现指示器
            indicators = []
            for seq in sequences:
                indicators.append(1 if num in seq else 0)

            indicators = np.array(indicators)

            # 计算趋势特征
            if len(indicators) > 10:
                # 简单线性趋势
                x = np.arange(len(indicators))
                trend_slope = np.polyfit(x, indicators, 1)[0] if len(x) > 1 else 0
                features[f"{prefix}_trend_slope_num_{num}"] = np.full(
                    len(indicators), trend_slope
                )

                # 移动平均趋势
                ma_short = pd.Series(indicators).rolling(window=5, min_periods=1).mean()
                ma_long = pd.Series(indicators).rolling(window=20, min_periods=1).mean()
                trend_signal = (ma_short - ma_long).fillna(0)
                features[f"{prefix}_trend_signal_num_{num}"] = trend_signal.values

        return features

    def _extract_seasonal_features(
        self, sequences: np.ndarray, prefix: str
    ) -> Dict[str, np.ndarray]:
        """提取季节性特征"""
        features = {}

        # 基于期号的周期性特征
        n_periods = len(sequences)

        # 周期性特征（假设存在7期、14期、30期的周期）
        for period in [7, 14, 30]:
            if period < n_periods:
                # 正弦和余弦特征
                phase = 2 * np.pi * np.arange(n_periods) / period
                features[f"{prefix}_seasonal_sin_{period}"] = np.sin(phase)
                features[f"{prefix}_seasonal_cos_{period}"] = np.cos(phase)

        return features


class FrequencyDomainFeatureExtractor:
    """频域特征提取器"""

    def __init__(self, config: FeatureConfig):
        """
        初始化频域特征提取器

        Args:
            config: 特征工程配置
        """
        self.config = config
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")

    def extract_features(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """
        提取频域特征

        Args:
            data: 历史数据

        Returns:
            频域特征字典
        """
        features = {}

        if not SCIPY_AVAILABLE:
            self.logger.warning("SciPy不可用，跳过频域特征提取")
            return features

        try:
            # 解析数字序列
            red_sequences = self._parse_number_sequences(data, "red")
            blue_sequences = self._parse_number_sequences(data, "blue")

            # 提取频域特征
            features.update(self._extract_fft_features(red_sequences, "red"))
            features.update(self._extract_fft_features(blue_sequences, "blue"))

            # 提取功率谱特征
            features.update(self._extract_power_spectrum_features(red_sequences, "red"))
            features.update(
                self._extract_power_spectrum_features(blue_sequences, "blue")
            )

            self.logger.info(f"提取频域特征: {len(features)} 个特征")

        except Exception as e:
            self.logger.error(f"频域特征提取失败: {e}")

        return features

    def _parse_number_sequences(self, data: pd.DataFrame, ball_type: str) -> np.ndarray:
        """解析数字序列"""
        column_name = "红球" if ball_type == "red" else "蓝球"
        max_numbers = 35 if ball_type == "red" else 12

        # 转换为二进制特征矩阵
        feature_matrix = np.zeros((len(data), max_numbers))

        for i, (_, row) in enumerate(data.iterrows()):
            try:
                numbers_str = str(row[column_name])
                numbers = [
                    int(x.strip()) for x in numbers_str.split() if x.strip().isdigit()
                ]
                for num in numbers:
                    if 1 <= num <= max_numbers:
                        feature_matrix[i, num - 1] = 1
            except:
                continue

        return feature_matrix

    def _extract_fft_features(
        self, sequences: np.ndarray, prefix: str
    ) -> Dict[str, np.ndarray]:
        """提取FFT特征"""
        features = {}

        n_periods, n_numbers = sequences.shape
        window_size = min(self.config.fft_window_size, n_periods)

        if window_size < 10:
            return features

        # 对每个数字的时间序列进行FFT分析
        for num_idx in range(n_numbers):
            time_series = sequences[:, num_idx]

            # 滑动窗口FFT
            fft_features = []
            for i in range(len(time_series) - window_size + 1):
                window_data = time_series[i : i + window_size]

                # 计算FFT
                fft_values = fft(window_data)
                fft_magnitude = np.abs(fft_values)

                # 提取主要频率成分
                dominant_freqs = np.argsort(fft_magnitude)[-3:]  # 前3个主要频率
                fft_features.append(fft_magnitude[dominant_freqs])

            if fft_features:
                fft_features = np.array(fft_features)

                # 填充到原始长度
                full_features = np.zeros((n_periods, 3))
                full_features[-len(fft_features) :] = fft_features

                for freq_idx in range(3):
                    features[f"{prefix}_fft_freq_{freq_idx}_num_{num_idx+1}"] = (
                        full_features[:, freq_idx]
                    )

        return features

    def _extract_power_spectrum_features(
        self, sequences: np.ndarray, prefix: str
    ) -> Dict[str, np.ndarray]:
        """提取功率谱特征"""
        features = {}

        n_periods, n_numbers = sequences.shape

        # 计算整体功率谱特征
        for num_idx in range(n_numbers):
            time_series = sequences[:, num_idx]

            if len(time_series) > 20:
                # 计算功率谱密度
                freqs, psd = signal.periodogram(time_series)

                # 分频段统计
                n_bands = self.config.frequency_bands
                band_size = len(psd) // n_bands

                for band_idx in range(n_bands):
                    start_idx = band_idx * band_size
                    end_idx = (
                        (band_idx + 1) * band_size
                        if band_idx < n_bands - 1
                        else len(psd)
                    )

                    band_power = np.mean(psd[start_idx:end_idx])
                    features[f"{prefix}_psd_band_{band_idx}_num_{num_idx+1}"] = np.full(
                        n_periods, band_power
                    )

        return features


class GraphAnalysisFeatureExtractor:
    """图论分析特征提取器"""

    def __init__(self, config: FeatureConfig):
        """
        初始化图论分析特征提取器

        Args:
            config: 特征工程配置
        """
        self.config = config
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")

    def extract_features(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """
        提取图论特征

        Args:
            data: 历史数据

        Returns:
            图论特征字典
        """
        features = {}

        if not NETWORKX_AVAILABLE:
            self.logger.warning("NetworkX不可用，跳过图论特征提取")
            return features

        try:
            # 构建数字共现图
            red_graph_features = self._extract_graph_features(data, "red")
            blue_graph_features = self._extract_graph_features(data, "blue")

            features.update(red_graph_features)
            features.update(blue_graph_features)

            self.logger.info(f"提取图论特征: {len(features)} 个特征")

        except Exception as e:
            self.logger.error(f"图论特征提取失败: {e}")

        return features

    def _extract_graph_features(
        self, data: pd.DataFrame, ball_type: str
    ) -> Dict[str, np.ndarray]:
        """提取图论特征"""
        features = {}
        column_name = "红球" if ball_type == "red" else "蓝球"
        max_numbers = 35 if ball_type == "red" else 12
        prefix = ball_type

        # 解析数字序列
        sequences = []
        for _, row in data.iterrows():
            try:
                numbers_str = str(row[column_name])
                numbers = [
                    int(x.strip()) for x in numbers_str.split() if x.strip().isdigit()
                ]
                sequences.append(numbers)
            except:
                sequences.append([])

        # 滑动窗口图分析
        window_size = self.config.graph_window_size
        graph_features = []

        for i in range(len(sequences)):
            start_idx = max(0, i - window_size + 1)
            window_sequences = sequences[start_idx : i + 1]

            # 构建共现图
            G = nx.Graph()

            # 添加节点
            for num in range(1, max_numbers + 1):
                G.add_node(num)

            # 添加边（基于共现关系）
            co_occurrence = np.zeros((max_numbers, max_numbers))

            for seq in window_sequences:
                for j, num1 in enumerate(seq):
                    for k, num2 in enumerate(seq):
                        if (
                            j != k
                            and 1 <= num1 <= max_numbers
                            and 1 <= num2 <= max_numbers
                        ):
                            co_occurrence[num1 - 1, num2 - 1] += 1

            # 添加权重边
            for num1 in range(1, max_numbers + 1):
                for num2 in range(num1 + 1, max_numbers + 1):
                    weight = (
                        co_occurrence[num1 - 1, num2 - 1]
                        + co_occurrence[num2 - 1, num1 - 1]
                    )
                    if weight > self.config.graph_threshold:
                        G.add_edge(num1, num2, weight=weight)

            # 计算图特征
            try:
                # 度中心性
                degree_centrality = nx.degree_centrality(G)

                # 介数中心性
                betweenness_centrality = nx.betweenness_centrality(G)

                # 接近中心性
                closeness_centrality = nx.closeness_centrality(G)

                # 聚类系数
                clustering_coefficient = nx.clustering(G)

                # 页面排名
                pagerank = nx.pagerank(G)

                # 收集特征
                period_features = {}
                for num in range(1, max_numbers + 1):
                    period_features[f"degree_centrality_{num}"] = degree_centrality.get(
                        num, 0
                    )
                    period_features[f"betweenness_centrality_{num}"] = (
                        betweenness_centrality.get(num, 0)
                    )
                    period_features[f"closeness_centrality_{num}"] = (
                        closeness_centrality.get(num, 0)
                    )
                    period_features[f"clustering_coefficient_{num}"] = (
                        clustering_coefficient.get(num, 0)
                    )
                    period_features[f"pagerank_{num}"] = pagerank.get(num, 0)

                graph_features.append(period_features)

            except:
                # 如果图分析失败，使用零特征
                period_features = {}
                for num in range(1, max_numbers + 1):
                    period_features[f"degree_centrality_{num}"] = 0
                    period_features[f"betweenness_centrality_{num}"] = 0
                    period_features[f"closeness_centrality_{num}"] = 0
                    period_features[f"clustering_coefficient_{num}"] = 0
                    period_features[f"pagerank_{num}"] = 0

                graph_features.append(period_features)

        # 转换为特征数组
        if graph_features:
            feature_names = list(graph_features[0].keys())
            for feature_name in feature_names:
                feature_values = [gf[feature_name] for gf in graph_features]
                features[f"{prefix}_graph_{feature_name}"] = np.array(feature_values)

        return features


class AdvancedFeatureEngineer:
    """高级特征工程器"""

    def __init__(self, config: FeatureConfig = None):
        """
        初始化高级特征工程器

        Args:
            config: 特征工程配置
        """
        self.config = config or FeatureConfig()
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")

        # 初始化特征提取器
        self.ts_extractor = TimeSeriesFeatureExtractor(self.config)
        self.freq_extractor = FrequencyDomainFeatureExtractor(self.config)
        self.graph_extractor = GraphAnalysisFeatureExtractor(self.config)

        # 特征缓存
        self.feature_cache = {}

    def extract_all_features(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """
        提取所有高级特征

        Args:
            data: 历史数据

        Returns:
            所有特征字典
        """
        self.logger.info("开始提取高级特征...")

        all_features = {}

        # 时间序列特征
        if self.config.enable_time_series:
            self.logger.info("提取时间序列特征...")
            ts_features = self.ts_extractor.extract_features(data)
            all_features.update(ts_features)

        # 频域特征
        if self.config.enable_frequency_domain:
            self.logger.info("提取频域特征...")
            freq_features = self.freq_extractor.extract_features(data)
            all_features.update(freq_features)

        # 图论特征
        if self.config.enable_graph_analysis:
            self.logger.info("提取图论特征...")
            graph_features = self.graph_extractor.extract_features(data)
            all_features.update(graph_features)

        # 统计特征
        if self.config.enable_statistical:
            self.logger.info("提取统计特征...")
            stat_features = self._extract_statistical_features(data)
            all_features.update(stat_features)

        # 模式识别特征
        if self.config.enable_pattern_recognition:
            self.logger.info("提取模式识别特征...")
            pattern_features = self._extract_pattern_features(data)
            all_features.update(pattern_features)

        # 聚类特征
        if self.config.enable_clustering and SKLEARN_AVAILABLE:
            self.logger.info("提取聚类特征...")
            cluster_features = self._extract_clustering_features(data)
            all_features.update(cluster_features)

        # 降维特征
        if self.config.enable_dimensionality_reduction and SKLEARN_AVAILABLE:
            self.logger.info("提取降维特征...")
            dim_features = self._extract_dimensionality_features(data)
            all_features.update(dim_features)

        # 交互特征
        if self.config.enable_interaction:
            self.logger.info("提取交互特征...")
            interaction_features = self._extract_interaction_features(all_features)
            all_features.update(interaction_features)

        self.logger.info(f"特征提取完成，总计 {len(all_features)} 个特征")

        return all_features

    def _extract_statistical_features(
        self, data: pd.DataFrame
    ) -> Dict[str, np.ndarray]:
        """提取统计特征"""
        features = {}

        # 基本统计特征已在时间序列特征中实现
        # 这里可以添加更高级的统计特征

        return features

    def _extract_pattern_features(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """提取模式识别特征"""
        features = {}

        # 模式识别特征的实现
        # 可以识别重复模式、周期性模式等

        return features

    def _extract_clustering_features(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """提取聚类特征"""
        features = {}

        # 聚类特征的实现
        # 基于历史数据进行聚类分析

        return features

    def _extract_dimensionality_features(
        self, data: pd.DataFrame
    ) -> Dict[str, np.ndarray]:
        """提取降维特征"""
        features = {}

        # 降维特征的实现
        # 使用PCA、t-SNE等方法

        return features

    def _extract_interaction_features(
        self, base_features: Dict[str, np.ndarray]
    ) -> Dict[str, np.ndarray]:
        """提取交互特征"""
        features = {}

        # 交互特征的实现
        # 特征之间的交互作用

        return features
