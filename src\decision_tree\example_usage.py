"""决策树模块使用示例

展示如何使用决策树预测器进行回测和预测。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import pandas as pd
from src.framework.backtest_framework import BacktestFramework
from src.framework.data_models import BacktestConfig
from src.framework.result_display import ResultDisplayer
from src.decision_tree import (
    OddEvenTreePredictor,
    SizeRatioTreePredictor,
    TreeEnsemblePredictor,
)
from src.utils.logger import get_logger


def load_sample_data(data_source: str = "file", **kwargs) -> pd.DataFrame:
    """
    使用数据加载模块加载数据

    Args:
        data_source: 数据源类型 ("file", "sample", "extended")
        **kwargs: 加载参数

    Returns:
        加载的数据DataFrame
    """
    logger = get_logger("DataLoader")

    try:
        # 尝试导入统一的数据加载器
        sys.path.insert(0, str(project_root))
        from simple_demo import DataLoader

        # 创建数据加载器
        loader = DataLoader(data_source)
        data = loader.load_data(**kwargs)

        logger.info(f"使用{data_source}数据源加载了{len(data)}条记录")
        return data

    except ImportError as e:
        logger.warning(f"无法导入DataLoader: {e}，使用备用数据加载方法")
        return _load_fallback_data()
    except Exception as e:
        logger.error(f"数据加载失败: {e}，使用备用数据")
        return _load_fallback_data()


def _load_fallback_data() -> pd.DataFrame:
    """
    备用数据加载方法

    Returns:
        示例数据DataFrame
    """
    # 备用的模拟数据
    data = {
        "期号": ["2024001", "2024002", "2024003", "2024004", "2024005"],
        "红球": [
            "1,8,15,22,29,6",
            "5,12,19,26,33,11",
            "2,9,16,23,30,13",
            "7,14,21,28,32,18",
            "3,10,17,24,31,25",
        ],
        "蓝球": [9, 3, 12, 7, 15],
    }

    return pd.DataFrame(data)


def demo_odd_even_tree():
    """
    演示奇偶比决策树预测器
    """
    logger = get_logger("OddEvenTreeDemo")
    logger.info("=== 奇偶比决策树预测器演示 ===")

    # 创建预测器
    predictor = OddEvenTreePredictor(max_depth=10, min_samples_split=3, random_state=42)

    # 加载数据 - 优先尝试从文件加载，失败则使用示例数据
    try:
        data = load_sample_data("file", file_path="dlt_data.csv")
        logger.info("成功从文件加载数据")
    except Exception as e:
        logger.warning(f"文件加载失败: {e}，使用示例数据")
        data = load_sample_data("sample", num_records=100)

    # 配置回测
    config = BacktestConfig(
        num_periods=3, display_periods=3, enable_detailed_output=True
    )

    # 创建回测框架
    framework = BacktestFramework(data)

    # 运行回测
    results = framework.run_backtest(predictor, config)

    # 显示结果
    displayer = ResultDisplayer()
    displayer.display_backtest_result(results)

    logger.info("奇偶比决策树预测器演示完成")


def demo_size_ratio_tree():
    """
    演示大小比决策树预测器
    """
    logger = get_logger("SizeRatioTreeDemo")
    logger.info("=== 大小比决策树预测器演示 ===")

    # 创建预测器
    predictor = SizeRatioTreePredictor(
        max_depth=12, min_samples_split=4, random_state=123
    )

    # 加载数据 - 优先尝试从文件加载，失败则使用示例数据
    try:
        data = load_sample_data("file", file_path="dlt_data.csv")
        logger.info("成功从文件加载数据")
    except Exception as e:
        logger.warning(f"文件加载失败: {e}，使用示例数据")
        data = load_sample_data("sample", num_records=100)

    # 配置回测
    config = BacktestConfig(
        num_periods=3, display_periods=3, enable_detailed_output=True
    )

    # 创建回测框架
    framework = BacktestFramework(data)

    # 运行回测
    results = framework.run_backtest(predictor, config)

    # 显示结果
    displayer = ResultDisplayer()
    displayer.display_backtest_result(results)

    logger.info("大小比决策树预测器演示完成")


def demo_tree_ensemble():
    """
    演示决策树集成预测器
    """
    logger = get_logger("TreeEnsembleDemo")
    logger.info("=== 决策树集成预测器演示 ===")

    # 创建集成预测器
    predictor = TreeEnsemblePredictor(
        ensemble_size=5, voting_strategy="weighted", random_state=456
    )

    # 显示集成信息
    ensemble_info = predictor.get_ensemble_info()
    logger.info(f"集成配置: {ensemble_info}")

    # 加载数据 - 优先尝试从扩展数据集加载
    try:
        data = load_sample_data("extended", base_file="dlt_data.csv")
        logger.info("成功从扩展数据集加载数据")
    except Exception as e:
        logger.warning(f"扩展数据集加载失败: {e}，尝试基础文件")
        try:
            data = load_sample_data("file", file_path="dlt_data.csv")
            logger.info("成功从文件加载数据")
        except Exception as e2:
            logger.warning(f"文件加载失败: {e2}，使用示例数据")
            data = load_sample_data("sample", num_records=150)

    # 配置回测
    config = BacktestConfig(
        num_periods=3,
        display_periods=3,
        enable_detailed_output=True,
        enable_statistics=True,
    )

    # 创建回测框架
    framework = BacktestFramework(data)

    # 运行回测
    results = framework.run_backtest(predictor, config)

    # 显示结果
    displayer = ResultDisplayer()
    displayer.display_backtest_result(results)

    logger.info("决策树集成预测器演示完成")


def compare_predictors():
    """
    比较不同决策树预测器的性能
    """
    logger = get_logger("PredictorComparison")
    logger.info("=== 决策树预测器性能比较 ===")

    # 创建不同的预测器
    predictors = {
        "OddEvenTree": OddEvenTreePredictor(random_state=42),
        "SizeRatioTree": SizeRatioTreePredictor(random_state=42),
        "TreeEnsemble_Weighted": TreeEnsemblePredictor(
            ensemble_size=3, voting_strategy="weighted", random_state=42
        ),
        "TreeEnsemble_Majority": TreeEnsemblePredictor(
            ensemble_size=3, voting_strategy="majority", random_state=42
        ),
    }

    # 加载数据 - 为比较测试使用更多数据
    try:
        data = load_sample_data("extended", base_file="dlt_data.csv")
        logger.info("成功从扩展数据集加载数据用于比较测试")
    except Exception as e:
        logger.warning(f"扩展数据集加载失败: {e}，尝试基础文件")
        try:
            data = load_sample_data("file", file_path="dlt_data.csv")
            logger.info("成功从文件加载数据")
        except Exception as e2:
            logger.warning(f"文件加载失败: {e2}，使用大量示例数据")
            data = load_sample_data("sample", num_records=200)

    # 配置回测
    config = BacktestConfig(num_periods=3, display_periods=3, enable_statistics=True)

    # 比较结果
    comparison_results = {}

    for name, predictor in predictors.items():
        logger.info(f"\n--- 测试 {name} ---")

        try:
            framework = BacktestFramework(data)
            results = framework.run_backtest(predictor, config)

            # 提取关键指标
            if results.statistics:
                stats = results.statistics
                comparison_results[name] = {
                    "red_odd_even_hit_rate": stats.red_odd_even_rate,
                    "red_size_hit_rate": stats.red_size_rate,
                    "blue_size_hit_rate": stats.blue_size_rate,
                    "total_periods": len(results.period_results),
                }

            # 显示结果
            displayer = ResultDisplayer()
            displayer.display_backtest_result(results)

        except Exception as e:
            logger.error(f"{name} 测试失败: {e}")
            comparison_results[name] = {"error": str(e)}

    # 显示比较结果
    logger.info("\n=== 性能比较总结 ===")
    for name, metrics in comparison_results.items():
        if "error" in metrics:
            logger.info(f"{name}: 测试失败 - {metrics['error']}")
        else:
            logger.info(f"{name}:")
            logger.info(
                f"  奇偶比命中率: {metrics.get('red_odd_even_hit_rate', 0):.2%}"
            )
            logger.info(
                f"  红球大小比命中率: {metrics.get('red_size_hit_rate', 0):.2%}"
            )
            logger.info(f"  蓝球大小命中率: {metrics.get('blue_size_hit_rate', 0):.2%}")


def main():
    """
    主函数
    """
    logger = get_logger("DecisionTreeExample")
    logger.info("决策树模块使用示例开始")

    try:
        # 演示各个预测器
        demo_odd_even_tree()
        print("\n" + "=" * 50 + "\n")

        demo_size_ratio_tree()
        print("\n" + "=" * 50 + "\n")

        demo_tree_ensemble()
        print("\n" + "=" * 50 + "\n")

        # 比较预测器性能
        compare_predictors()

    except Exception as e:
        logger.error(f"示例运行过程中出错: {e}")
        raise

    logger.info("决策树模块使用示例完成")


if __name__ == "__main__":
    main()
