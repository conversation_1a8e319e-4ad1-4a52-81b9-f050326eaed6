#!/usr/bin/env python3
"""
改进的红球奇偶比预测器
基于历史数据分析和多策略融合
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple
from collections import Counter, defaultdict
from src.utils.utils import parse_numbers, calculate_odd_even_ratio, ratio_to_state
from src.config.lottery_config import LotteryConfig


class ImprovedRedOddEvenPredictor:
    """改进的红球奇偶比预测器"""
    
    def __init__(self):
        self.name = "ImprovedRedOddEvenPredictor"
        self.historical_patterns = {}
        self.transition_matrix = defaultdict(lambda: defaultdict(int))
        self.recent_trends = []
        
    def predict_ratio(self, data: pd.DataFrame, ratio_type: str = "red_odd_even") -> Dict[str, Any]:
        """
        预测红球奇偶比
        
        Args:
            data: 历史数据
            ratio_type: 比率类型
            
        Returns:
            预测结果字典
        """
        if ratio_type != "red_odd_even":
            return {"prediction": "2:3", "confidence": 0.0, "method": "unsupported"}
            
        if len(data) < 20:
            return {
                "prediction": "2:3",  # 使用最常见的比值
                "confidence": 0.4,
                "method": "insufficient_data"
            }
        
        # 分析历史模式
        self._analyze_historical_patterns(data)
        
        # 多策略预测
        strategies = [
            self._frequency_based_prediction(data),
            self._trend_reversal_prediction(data),
            self._transition_pattern_prediction(data),
            self._recent_bias_prediction(data),
            self._statistical_balance_prediction(data),
            self._seasonal_pattern_prediction(data),
            self._cycle_pattern_prediction(data)
        ]
        
        # 加权融合预测结果
        ensemble_prediction = self._weighted_ensemble(strategies)

        # 应用反向偏向修正机制
        final_prediction = self._apply_bias_correction(ensemble_prediction, data)

        return final_prediction
    
    def _analyze_historical_patterns(self, data: pd.DataFrame) -> None:
        """分析历史模式"""
        ratios = []
        
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            ratio = ratio_to_state((red_odd, red_even))
            ratios.append(ratio)
        
        # 统计频率
        self.historical_patterns = Counter(ratios)
        
        # 构建转移矩阵
        for i in range(1, len(ratios)):
            prev_ratio = ratios[i-1]
            curr_ratio = ratios[i]
            self.transition_matrix[prev_ratio][curr_ratio] += 1
        
        # 记录最近趋势
        self.recent_trends = ratios[-10:] if len(ratios) >= 10 else ratios
    
    def _frequency_based_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """基于频率的预测 - 优化版本"""
        # 分析最近100期的频率分布（扩大样本）
        recent_data = data.tail(100)
        recent_ratios = []

        for _, row in recent_data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            ratio = ratio_to_state((red_odd, red_even))
            recent_ratios.append(ratio)

        ratio_counts = Counter(recent_ratios)

        # 基于验证结果的先验知识：2:3最常见（42%），3:2次之（30%）
        expected_distribution = {
            "2:3": 0.42,
            "3:2": 0.30,
            "1:4": 0.14,
            "4:1": 0.12,
            "5:0": 0.01,
            "0:5": 0.01
        }

        if ratio_counts:
            # 结合历史频率和先验分布
            total_count = len(recent_ratios)
            best_ratio = "2:3"  # 默认最常见的
            best_score = 0

            for ratio, expected_prob in expected_distribution.items():
                actual_count = ratio_counts.get(ratio, 0)
                actual_prob = actual_count / total_count
                # 综合考虑实际频率和期望频率
                combined_score = 0.6 * expected_prob + 0.4 * actual_prob

                if combined_score > best_score:
                    best_score = combined_score
                    best_ratio = ratio

            return {
                "prediction": best_ratio,
                "confidence": best_score,
                "method": "frequency_based"
            }

        return {
            "prediction": "2:3",  # 基于验证结果的默认值
            "confidence": 0.42,
            "method": "frequency_fallback"
        }
    
    def _trend_reversal_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """趋势反转预测"""
        if len(self.recent_trends) < 3:
            return {
                "prediction": "2:3",
                "confidence": 0.3,
                "method": "trend_insufficient_data"
            }
        
        # 分析最近3期的模式
        last_3 = self.recent_trends[-3:]
        
        # 如果连续出现相同比值，预测反转
        if len(set(last_3)) == 1:
            current_ratio = last_3[0]
            # 预测反转到对立比值
            reversal_map = {
                "5:0": "0:5", "4:1": "1:4", "3:2": "2:3",
                "2:3": "3:2", "1:4": "4:1", "0:5": "5:0"
            }
            predicted_ratio = reversal_map.get(current_ratio, "2:3")
            
            return {
                "prediction": predicted_ratio,
                "confidence": 0.7,
                "method": "trend_reversal"
            }
        
        # 如果出现交替模式，继续交替
        if len(set(last_3)) == 2 and last_3[0] == last_3[2]:
            predicted_ratio = last_3[1]  # 继续交替
            
            return {
                "prediction": predicted_ratio,
                "confidence": 0.6,
                "method": "alternating_pattern"
            }
        
        return {
            "prediction": "2:3",
            "confidence": 0.3,
            "method": "no_clear_trend"
        }
    
    def _transition_pattern_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """基于转移模式的预测"""
        if not self.recent_trends:
            return {
                "prediction": "2:3",
                "confidence": 0.3,
                "method": "no_transition_data"
            }
        
        last_ratio = self.recent_trends[-1]
        
        if last_ratio in self.transition_matrix:
            transitions = self.transition_matrix[last_ratio]
            if transitions:
                # 找出最可能的下一个状态
                total_transitions = sum(transitions.values())
                most_likely_next = max(transitions.items(), key=lambda x: x[1])
                
                confidence = most_likely_next[1] / total_transitions
                
                return {
                    "prediction": most_likely_next[0],
                    "confidence": confidence,
                    "method": "transition_pattern"
                }
        
        return {
            "prediction": "2:3",
            "confidence": 0.3,
            "method": "transition_fallback"
        }
    
    def _recent_bias_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """最近偏向预测"""
        recent_10 = data.tail(10)
        odd_counts = []
        even_counts = []
        
        for _, row in recent_10.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            odd_counts.append(red_odd)
            even_counts.append(red_even)
        
        avg_odd = np.mean(odd_counts)
        avg_even = np.mean(even_counts)
        
        # 基于平均值预测下一期的倾向
        if avg_odd > 2.7:  # 奇数偏多，预测偶数增加
            predicted_ratio = "2:3"
            confidence = 0.6
        elif avg_odd < 2.3:  # 奇数偏少，预测奇数增加
            predicted_ratio = "3:2"
            confidence = 0.6
        else:  # 平衡状态
            predicted_ratio = "2:3"  # 倾向于最常见的
            confidence = 0.5
        
        return {
            "prediction": predicted_ratio,
            "confidence": confidence,
            "method": "recent_bias"
        }
    
    def _statistical_balance_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """统计平衡预测"""
        # 分析长期统计平衡
        recent_100 = data.tail(100)
        total_odd = 0
        total_even = 0
        
        for _, row in recent_100.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            total_odd += red_odd
            total_even += red_even
        
        # 理论上奇偶应该接近平衡
        expected_odd = len(recent_100) * 2.5  # 期望奇数总数
        expected_even = len(recent_100) * 2.5  # 期望偶数总数
        
        odd_deviation = total_odd - expected_odd
        even_deviation = total_even - expected_even
        
        # 如果奇数过多，预测偶数增加
        if odd_deviation > 20:
            predicted_ratio = "1:4"
            confidence = 0.6
        elif odd_deviation > 10:
            predicted_ratio = "2:3"
            confidence = 0.5
        elif even_deviation > 20:
            predicted_ratio = "4:1"
            confidence = 0.6
        elif even_deviation > 10:
            predicted_ratio = "3:2"
            confidence = 0.5
        else:
            predicted_ratio = "2:3"  # 默认最常见
            confidence = 0.4
        
        return {
            "prediction": predicted_ratio,
            "confidence": confidence,
            "method": "statistical_balance"
        }
    
    def _weighted_ensemble(self, strategies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """加权集成多个策略"""
        # 使用配置类中的策略权重
        weights = LotteryConfig.STRATEGY_WEIGHTS
        
        # 收集所有预测 - 优化版本
        predictions = defaultdict(float)
        confidence_weights = defaultdict(float)
        method_contributions = defaultdict(list)

        for strategy in strategies:
            method = strategy["method"]
            prediction = strategy["prediction"]
            confidence = strategy["confidence"]

            # 获取基础权重
            base_weight = weights.get(method.split('_')[0] + "_" + method.split('_')[1] if '_' in method else method, 0.1)

            # 动态调整权重：高置信度的策略获得更大权重
            confidence_bonus = 1.0 + (confidence - 0.5) * 0.5  # 置信度奖励
            dynamic_weight = base_weight * confidence_bonus

            # 加权投票
            predictions[prediction] += dynamic_weight * confidence
            confidence_weights[prediction] += dynamic_weight
            method_contributions[prediction].append((method, dynamic_weight, confidence))
        
        if not predictions:
            return {
                "prediction": "2:3",
                "confidence": 0.4,
                "method": "ensemble_fallback"
            }
        
        # 选择得分最高的预测
        best_prediction = max(predictions.items(), key=lambda x: x[1])
        prediction_name = best_prediction[0]

        # 计算最终置信度：考虑支持该预测的策略数量和质量
        supporting_methods = method_contributions[prediction_name]
        avg_confidence = sum(conf for _, _, conf in supporting_methods) / len(supporting_methods)
        method_diversity_bonus = min(len(supporting_methods) / len(strategies), 1.0) * 0.2

        final_confidence = min(avg_confidence + method_diversity_bonus, 0.95)

        return {
            "prediction": prediction_name,
            "confidence": final_confidence,
            "method": "weighted_ensemble"
        }

    def _calculate_prediction_debt(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算每个比值的预测债务"""
        debt_window = data.tail(LotteryConfig.DEBT_TRACKING_WINDOW)

        # 统计实际出现次数
        actual_counts = Counter()
        for _, row in debt_window.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            ratio = ratio_to_state((red_odd, red_even))
            actual_counts[ratio] += 1

        # 获取理想分布
        ideal_distribution = LotteryConfig.get_ideal_distribution('odd_even', 'red')
        total_periods = len(debt_window)

        # 计算债务（实际应该出现的次数 - 实际出现的次数）
        debt_scores = {}
        for ratio, ideal_prob in ideal_distribution.items():
            expected_count = ideal_prob * total_periods
            actual_count = actual_counts.get(ratio, 0)
            # 债务 = 期望次数 - 实际次数（正数表示欠债，负数表示超额）
            debt_scores[ratio] = expected_count - actual_count

        return debt_scores

    def _apply_bias_correction(self, prediction: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """应用强化的反向偏向修正机制"""
        predicted_ratio = prediction["prediction"]
        confidence = prediction["confidence"]

        # 使用配置类中的观察窗口
        recent_data = data.tail(LotteryConfig.BIAS_OBSERVATION_WINDOW)

        # 计算债务追踪
        debt_analysis = self._calculate_prediction_debt(data)

        # 强制轮换检查：如果2:3被过度预测，直接强制选择其他比值
        if predicted_ratio == "2:3":
            # 检查债务情况，如果2:3已经"超额"，强制替代
            ratio_23_debt = debt_analysis.get("2:3", 0)
            if ratio_23_debt < -0.5:  # 负债务表示超额
                # 直接选择债务最高的比值
                debt_candidates = [(ratio, debt) for ratio, debt in debt_analysis.items()
                                 if debt > 0.5 and ratio != "2:3"]
                if debt_candidates:
                    debt_candidates.sort(key=lambda x: x[1], reverse=True)
                    forced_alternative = debt_candidates[0][0]

                    return {
                        "prediction": forced_alternative,
                        "confidence": confidence * LotteryConfig.CONFIDENCE_BOOST_DEBT_REPAYMENT,
                        "method": f"{prediction['method']}_forced_rotation"
                    }
        recent_ratios = []

        for _, row in recent_data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            ratio = ratio_to_state((red_odd, red_even))
            recent_ratios.append(ratio)

        if not recent_ratios:
            return prediction

        # 统计最近的分布
        ratio_counts = Counter(recent_ratios)
        total_count = len(recent_ratios)

        # 使用配置类中的理想分布
        ideal_distribution = LotteryConfig.get_ideal_distribution('odd_even', 'red')

        # 计算当前预测比值的出现频率
        predicted_count = ratio_counts.get(predicted_ratio, 0)
        predicted_freq = predicted_count / total_count
        ideal_freq = ideal_distribution.get(predicted_ratio, 0.1)

        # 强化修正：结合频率分析和债务追踪
        predicted_debt = debt_analysis.get(predicted_ratio, 0)

        # 第零层：零预测惩罚 - 对于从未被预测但在历史中出现过的比值给予最高优先级
        # 首先统计历史中实际出现过的比值
        historical_data = data.tail(200)  # 查看更长的历史
        historical_ratios = set()
        for _, row in historical_data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            ratio = ratio_to_state((red_odd, red_even))
            historical_ratios.add(ratio)

        zero_prediction_candidates = []
        for ratio in ideal_distribution.keys():
            # 只考虑历史中出现过的比值
            if (ratio in historical_ratios and
                ratio_counts.get(ratio, 0) == 0 and
                debt_analysis.get(ratio, 0) > 0.5):  # 提高债务阈值
                zero_prediction_candidates.append((ratio, debt_analysis[ratio]))

        if zero_prediction_candidates and predicted_freq > ideal_freq * 1.1:
            # 只有当前预测比值明显过度时才触发零预测修正
            zero_prediction_candidates.sort(key=lambda x: x[1], reverse=True)
            best_zero_candidate = zero_prediction_candidates[0][0]

            return {
                "prediction": best_zero_candidate,
                "confidence": confidence * LotteryConfig.CONFIDENCE_BOOST_DEBT_REPAYMENT,
                "method": f"{prediction['method']}_zero_corrected"
            }

        # 第一层：强制平衡修正 - 如果预测的比值明显过度，强制替代
        if (predicted_freq > ideal_freq * LotteryConfig.FORCE_BALANCE_THRESHOLD or
            predicted_debt < -0.5):  # 降低债务阈值，更容易触发
            debt_candidates = []
            for ratio, debt in debt_analysis.items():
                if debt > 0.5:  # 降低债务阈值，更容易触发修正
                    debt_candidates.append((ratio, debt))

            if debt_candidates:
                # 选择欠债最多的比值
                debt_candidates.sort(key=lambda x: x[1], reverse=True)
                best_debt_repay = debt_candidates[0][0]

                return {
                    "prediction": best_debt_repay,
                    "confidence": confidence * LotteryConfig.CONFIDENCE_BOOST_DEBT_REPAYMENT,
                    "method": f"{prediction['method']}_debt_corrected"
                }

        # 第二层：频率修正 - 如果预测的比值最近出现过多，强制替代
        if predicted_freq > ideal_freq * LotteryConfig.BIAS_CORRECTION_AGGRESSIVE_THRESHOLD:
            # 优先考虑债务，其次考虑频率缺失
            alternative_candidates = []
            for ratio, ideal_prob in ideal_distribution.items():
                if ratio == predicted_ratio:
                    continue

                actual_freq = ratio_counts.get(ratio, 0) / total_count
                deficit = ideal_prob - actual_freq
                debt = debt_analysis.get(ratio, 0)

                # 综合评分：债务权重更高
                if deficit > 0 or debt > 0:
                    combined_score = debt * LotteryConfig.DEBT_PRIORITY_MULTIPLIER + deficit
                    alternative_candidates.append((ratio, combined_score, debt, deficit))

            if alternative_candidates:
                # 选择综合评分最高的比值
                alternative_candidates.sort(key=lambda x: x[1], reverse=True)
                best_alternative = alternative_candidates[0][0]

                return {
                    "prediction": best_alternative,
                    "confidence": confidence * LotteryConfig.CONFIDENCE_REDUCTION_AGGRESSIVE,
                    "method": f"{prediction['method']}_enhanced_corrected"
                }

        # 中等程度的过度预测也进行修正
        elif predicted_freq > ideal_freq * 1.1:
            # 轻微调整置信度，但不改变预测
            confidence = confidence * 0.9

        # 如果预测的比值出现频率正常或偏低，保持原预测但可能提高置信度
        if predicted_freq < ideal_freq * 0.8:
            confidence = min(confidence * 1.2, 0.95)  # 提高置信度但不超过95%

        return {
            "prediction": predicted_ratio,
            "confidence": confidence,
            "method": f"{prediction['method']}_bias_checked"
        }

    def _seasonal_pattern_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """季节性模式预测"""
        if len(data) < 100:
            return {
                "prediction": "2:3",
                "confidence": 0.3,
                "method": "seasonal_insufficient_data"
            }

        # 分析不同月份的奇偶比分布
        monthly_patterns = defaultdict(list)

        for _, row in data.tail(LotteryConfig.HISTORICAL_ANALYSIS_WINDOW).iterrows():
            try:
                # 假设期号格式包含年月信息，这里简化处理
                period = str(row.get('期号', ''))
                if len(period) >= 5:
                    month = int(period[-2:]) % 12 + 1  # 简化的月份提取
                else:
                    continue

                red_balls, _ = parse_numbers(row)
                red_odd, red_even = calculate_odd_even_ratio(red_balls)
                ratio = ratio_to_state((red_odd, red_even))
                monthly_patterns[month].append(ratio)
            except:
                continue

        # 获取当前月份（简化处理）
        if len(data) > 0:
            last_period = str(data.iloc[-1].get('期号', ''))
            if len(last_period) >= 5:
                current_month = int(last_period[-2:]) % 12 + 1
            else:
                current_month = 1
        else:
            current_month = 1

        # 分析当前月份的历史模式
        if current_month in monthly_patterns and monthly_patterns[current_month]:
            month_ratios = monthly_patterns[current_month]
            ratio_counts = Counter(month_ratios)
            most_common = ratio_counts.most_common(1)[0]

            return {
                "prediction": most_common[0],
                "confidence": most_common[1] / len(month_ratios),
                "method": "seasonal_pattern"
            }

        return {
            "prediction": "2:3",
            "confidence": 0.35,
            "method": "seasonal_fallback"
        }

    def _cycle_pattern_prediction(self, data: pd.DataFrame) -> Dict[str, Any]:
        """周期性模式预测"""
        if len(data) < 50:
            return {
                "prediction": "2:3",
                "confidence": 0.3,
                "method": "cycle_insufficient_data"
            }

        # 分析7期、14期、21期等周期性模式
        cycles = [7, 14, 21]
        best_cycle_prediction = None
        best_confidence = 0

        for cycle_length in cycles:
            if len(data) < cycle_length * 3:  # 至少需要3个完整周期
                continue

            # 分析周期内的位置模式
            position_patterns = defaultdict(list)

            for i, (_, row) in enumerate(data.tail(cycle_length * 5).iterrows()):
                try:
                    position = i % cycle_length
                    red_balls, _ = parse_numbers(row)
                    red_odd, red_even = calculate_odd_even_ratio(red_balls)
                    ratio = ratio_to_state((red_odd, red_even))
                    position_patterns[position].append(ratio)
                except:
                    continue

            # 预测下一期的位置
            next_position = len(data) % cycle_length

            if next_position in position_patterns and position_patterns[next_position]:
                position_ratios = position_patterns[next_position]
                ratio_counts = Counter(position_ratios)
                most_common = ratio_counts.most_common(1)[0]
                confidence = most_common[1] / len(position_ratios)

                if confidence > best_confidence:
                    best_confidence = confidence
                    best_cycle_prediction = most_common[0]

        if best_cycle_prediction:
            return {
                "prediction": best_cycle_prediction,
                "confidence": best_confidence,
                "method": "cycle_pattern"
            }

        return {
            "prediction": "2:3",
            "confidence": 0.35,
            "method": "cycle_fallback"
        }
