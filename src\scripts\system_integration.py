"""
System Integration Script
将成功适配的预测器集成到主系统中
"""

import sys
from pathlib import Path
import json
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.specialized_adapters import create_specialized_adapter
from src.core.interfaces import BallType, PredictionType
from src.core.predictor_factory import get_predictor_factory
from src.models.optimized_odd_even_predictor import OptimizedOddEvenPredictor


class SystemIntegrator:
    """系统集成器"""
    
    def __init__(self):
        self.integration_results = {}
        self.factory = get_predictor_factory()
        
    def integrate_adapted_predictors(self) -> Dict[str, Any]:
        """集成适配后的预测器"""
        print("System Integration Tool")
        print("=" * 60)
        print("集成适配后的预测器到主系统...")
        
        results = {
            'integrated_predictors': [],
            'failed_integrations': [],
            'total_processed': 0
        }
        
        # 集成OptimizedOddEvenPredictor
        try:
            print("\n集成 OptimizedOddEvenPredictor...")
            
            # 创建原始预测器
            original_predictor = OptimizedOddEvenPredictor()
            
            # 创建适配器
            adapter = create_specialized_adapter(
                original_predictor,
                predictor_type='odd_even',
                name="集成的奇偶比预测器",
                version="1.0"
            )
            
            # 注册到工厂
            predictor_id = "integrated_odd_even_predictor"
            self.factory.register_predictor(
                name=predictor_id,
                module="src.core.specialized_adapters",
                class_name="OddEvenPredictorAdapter",
                predictor_type="odd_even_ratio",
                singleton=False,
                config={
                    'description': '集成的奇偶比预测器',
                    'version': '1.0',
                    'adapter_instance': adapter
                }
            )
            
            # 验证集成
            test_predictor = self.factory.create_predictor(predictor_id)
            if test_predictor is not None:
                print(f"✅ OptimizedOddEvenPredictor 集成成功")
                print(f"   - 预测器ID: {predictor_id}")
                print(f"   - 预测类型: {adapter.get_prediction_type()}")
                print(f"   - 适配器类型: {type(adapter).__name__}")
                
                results['integrated_predictors'].append({
                    'original_class': 'OptimizedOddEvenPredictor',
                    'predictor_id': predictor_id,
                    'adapter_type': type(adapter).__name__,
                    'prediction_type': adapter.get_prediction_type().value
                })
            else:
                raise Exception("工厂创建预测器失败")
                
        except Exception as e:
            print(f"❌ OptimizedOddEvenPredictor 集成失败: {e}")
            results['failed_integrations'].append({
                'original_class': 'OptimizedOddEvenPredictor',
                'error': str(e)
            })
        
        results['total_processed'] = len(results['integrated_predictors']) + len(results['failed_integrations'])
        
        return results
    
    def verify_system_functionality(self) -> Dict[str, Any]:
        """验证系统功能"""
        print("\n" + "="*50)
        print("验证系统功能")
        print("="*50)
        
        verification_results = {
            'factory_tests': [],
            'predictor_tests': [],
            'overall_success': True
        }
        
        # 测试工厂功能
        try:
            available_predictors = self.factory.get_available_predictors()
            print(f"可用预测器数量: {len(available_predictors)}")
            
            for predictor_id in available_predictors:
                try:
                    predictor = self.factory.create_predictor(predictor_id)
                    if predictor is not None:
                        print(f"✅ {predictor_id}: 创建成功")
                        verification_results['factory_tests'].append({
                            'predictor_id': predictor_id,
                            'status': 'success'
                        })
                    else:
                        print(f"❌ {predictor_id}: 创建失败")
                        verification_results['factory_tests'].append({
                            'predictor_id': predictor_id,
                            'status': 'failed',
                            'error': '创建返回None'
                        })
                        verification_results['overall_success'] = False
                except Exception as e:
                    print(f"❌ {predictor_id}: 创建异常 - {e}")
                    verification_results['factory_tests'].append({
                        'predictor_id': predictor_id,
                        'status': 'error',
                        'error': str(e)
                    })
                    verification_results['overall_success'] = False
                    
        except Exception as e:
            print(f"❌ 工厂测试失败: {e}")
            verification_results['overall_success'] = False
        
        # 测试集成的预测器
        try:
            integrated_predictor = self.factory.create_predictor("integrated_odd_even_predictor")
            if integrated_predictor is not None:
                # 创建测试数据
                import pandas as pd
                test_data = pd.DataFrame({
                    '期号': [25001, 25002, 25003],
                    '红球1': [1, 3, 5],
                    '红球2': [8, 10, 12],
                    '红球3': [15, 17, 19],
                    '红球4': [22, 24, 26],
                    '红球5': [29, 31, 33],
                    '蓝球1': [2, 4, 6],
                    '蓝球2': [8, 10, 12]
                })
                
                # 执行预测测试
                result = integrated_predictor.predict(test_data, 2, ball_type=BallType.RED)
                print(f"✅ 集成预测器测试成功")
                print(f"   - 预测结果: {result.value}")
                print(f"   - 置信度: {result.confidence:.3f}")
                print(f"   - 预测类型: {result.prediction_type}")
                
                verification_results['predictor_tests'].append({
                    'predictor_id': 'integrated_odd_even_predictor',
                    'status': 'success',
                    'prediction': result.value,
                    'confidence': result.confidence
                })
            else:
                print(f"❌ 集成预测器创建失败")
                verification_results['predictor_tests'].append({
                    'predictor_id': 'integrated_odd_even_predictor',
                    'status': 'failed',
                    'error': '创建返回None'
                })
                verification_results['overall_success'] = False
                
        except Exception as e:
            print(f"❌ 集成预测器测试失败: {e}")
            verification_results['predictor_tests'].append({
                'predictor_id': 'integrated_odd_even_predictor',
                'status': 'error',
                'error': str(e)
            })
            verification_results['overall_success'] = False
        
        return verification_results
    
    def generate_integration_report(self, integration_results: Dict[str, Any], 
                                  verification_results: Dict[str, Any]) -> str:
        """生成集成报告"""
        report = []
        report.append("# System Integration Report")
        report.append("=" * 50)
        report.append("")
        
        report.append("## 集成结果")
        report.append(f"总共处理: {integration_results['total_processed']} 个预测器")
        report.append(f"成功集成: {len(integration_results['integrated_predictors'])} 个")
        report.append(f"集成失败: {len(integration_results['failed_integrations'])} 个")
        report.append("")
        
        if integration_results['integrated_predictors']:
            report.append("### 成功集成的预测器:")
            for predictor in integration_results['integrated_predictors']:
                report.append(f"- **{predictor['original_class']}**")
                report.append(f"  - 预测器ID: `{predictor['predictor_id']}`")
                report.append(f"  - 适配器类型: `{predictor['adapter_type']}`")
                report.append(f"  - 预测类型: `{predictor['prediction_type']}`")
            report.append("")
        
        if integration_results['failed_integrations']:
            report.append("### 集成失败的预测器:")
            for predictor in integration_results['failed_integrations']:
                report.append(f"- **{predictor['original_class']}**: {predictor['error']}")
            report.append("")
        
        report.append("## 系统验证")
        report.append(f"工厂测试: {len(verification_results['factory_tests'])} 项")
        report.append(f"预测器测试: {len(verification_results['predictor_tests'])} 项")
        report.append(f"整体状态: {'✅ 通过' if verification_results['overall_success'] else '❌ 失败'}")
        report.append("")
        
        if verification_results['factory_tests']:
            report.append("### 工厂测试结果:")
            for test in verification_results['factory_tests']:
                status_icon = "✅" if test['status'] == 'success' else "❌"
                report.append(f"- {status_icon} {test['predictor_id']}")
                if test['status'] != 'success':
                    report.append(f"  - 错误: {test.get('error', '未知错误')}")
            report.append("")
        
        if verification_results['predictor_tests']:
            report.append("### 预测器测试结果:")
            for test in verification_results['predictor_tests']:
                status_icon = "✅" if test['status'] == 'success' else "❌"
                report.append(f"- {status_icon} {test['predictor_id']}")
                if test['status'] == 'success':
                    report.append(f"  - 预测结果: {test['prediction']}")
                    report.append(f"  - 置信度: {test['confidence']:.3f}")
                else:
                    report.append(f"  - 错误: {test.get('error', '未知错误')}")
            report.append("")
        
        report.append("## 结论")
        if verification_results['overall_success'] and integration_results['integrated_predictors']:
            report.append("✅ **系统集成成功**: 所有适配的预测器已成功集成到主系统中")
            report.append("")
            report.append("### 后续步骤:")
            report.append("1. 可以通过工厂使用集成的预测器")
            report.append("2. 建议进行更全面的端到端测试")
            report.append("3. 考虑更新系统配置文件")
        else:
            report.append("❌ **需要进一步处理**: 系统集成存在问题")
            report.append("")
            report.append("### 建议:")
            report.append("1. 检查失败的集成项目")
            report.append("2. 修复验证测试中的问题")
            report.append("3. 重新运行集成流程")
        
        return "\n".join(report)


def main():
    """主函数"""
    integrator = SystemIntegrator()
    
    # 步骤1: 集成适配后的预测器
    integration_results = integrator.integrate_adapted_predictors()
    
    # 步骤2: 验证系统功能
    verification_results = integrator.verify_system_functionality()
    
    # 步骤3: 生成集成报告
    report = integrator.generate_integration_report(integration_results, verification_results)
    print("\n" + report)
    
    # 保存报告
    report_path = project_root / "docs" / "system_integration_report.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"\n报告已保存到: {report_path}")
    
    return integrator


if __name__ == "__main__":
    integrator = main()
