"""
增强版神经网络预测器
更深的网络架构、更多特征、更好的性能
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
from collections import Counter
import pickle
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red, calculate_size_ratio_blue, ratio_to_state
from src.models.neural.neural_predictor import SimpleNeuralNetwork


class EnhancedNeuralNetwork(SimpleNeuralNetwork):
    """增强版神经网络"""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], output_size: int, 
                 dropout_rate: float = 0.2):
        """
        初始化增强版神经网络
        
        Args:
            input_size: 输入层大小
            hidden_sizes: 隐藏层大小列表
            output_size: 输出层大小
            dropout_rate: Dropout率
        """
        super().__init__(input_size, hidden_sizes, output_size)
        self.dropout_rate = dropout_rate
        self.use_batch_norm = True
        
        # 为每层添加批归一化参数
        for layer in self.layers[:-1]:  # 除了输出层
            layer['bn_gamma'] = np.ones(layer['weights'].shape[1])
            layer['bn_beta'] = np.zeros(layer['weights'].shape[1])
            layer['bn_running_mean'] = np.zeros(layer['weights'].shape[1])
            layer['bn_running_var'] = np.ones(layer['weights'].shape[1])
        
        self.learning_rate = 0.001  # 降低学习率
    
    def _batch_norm(self, x, layer, training=True, momentum=0.9):
        """批归一化"""
        if not self.use_batch_norm:
            return x
        
        if training:
            mean = np.mean(x, axis=0)
            var = np.var(x, axis=0)
            
            # 更新运行统计
            layer['bn_running_mean'] = momentum * layer['bn_running_mean'] + (1 - momentum) * mean
            layer['bn_running_var'] = momentum * layer['bn_running_var'] + (1 - momentum) * var
        else:
            mean = layer['bn_running_mean']
            var = layer['bn_running_var']
        
        # 归一化
        x_norm = (x - mean) / np.sqrt(var + 1e-8)
        
        # 缩放和平移
        return layer['bn_gamma'] * x_norm + layer['bn_beta']
    
    def _dropout(self, x, training=True):
        """Dropout正则化"""
        if not training or self.dropout_rate == 0:
            return x
        
        mask = np.random.binomial(1, 1 - self.dropout_rate, x.shape) / (1 - self.dropout_rate)
        return x * mask
    
    def _leaky_relu(self, x, alpha=0.01):
        """Leaky ReLU激活函数"""
        return np.where(x > 0, x, alpha * x)
    
    def _leaky_relu_derivative(self, x, alpha=0.01):
        """Leaky ReLU导数"""
        return np.where(x > 0, 1, alpha)
    
    def forward(self, X, training=True):
        """增强版前向传播"""
        activations = [X]
        
        for i, layer in enumerate(self.layers):
            z = np.dot(activations[-1], layer['weights']) + layer['biases']
            
            if layer['type'] == 'output':
                # 输出层使用softmax
                a = self._softmax(z)
            else:
                # 隐藏层使用批归一化 + Leaky ReLU + Dropout
                if self.use_batch_norm:
                    z = self._batch_norm(z, layer, training)
                
                a = self._leaky_relu(z)
                
                if training:
                    a = self._dropout(a, training)
            
            activations.append(a)
        
        return activations
    
    def train(self, X, y, epochs=200, batch_size=32, validation_split=0.2):
        """增强版训练"""
        n_samples = X.shape[0]
        
        # 分割验证集
        val_size = int(n_samples * validation_split)
        val_indices = np.random.choice(n_samples, val_size, replace=False)
        train_indices = np.setdiff1d(np.arange(n_samples), val_indices)
        
        X_train, y_train = X[train_indices], y[train_indices]
        X_val, y_val = X[val_indices], y[val_indices]
        
        best_val_loss = float('inf')
        patience = 20
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练
            train_loss = self._train_epoch(X_train, y_train, batch_size)
            
            # 验证
            val_loss = self._validate(X_val, y_val)
            
            # 早停
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"Early stopping at epoch {epoch}")
                    break
            
            if epoch % 20 == 0:
                print(f"Epoch {epoch}, Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        self.is_trained = True
    
    def _train_epoch(self, X, y, batch_size):
        """训练一个epoch"""
        n_samples = X.shape[0]
        indices = np.random.permutation(n_samples)
        X_shuffled = X[indices]
        y_shuffled = y[indices]
        
        total_loss = 0
        num_batches = 0
        
        for i in range(0, n_samples, batch_size):
            batch_X = X_shuffled[i:i+batch_size]
            batch_y = y_shuffled[i:i+batch_size]
            
            # 前向传播
            activations = self.forward(batch_X, training=True)
            
            # 计算损失
            loss = self._cross_entropy_loss(activations[-1], batch_y)
            total_loss += loss
            num_batches += 1
            
            # 反向传播
            self._backward_enhanced(activations, batch_y, batch_X.shape[0])
        
        return total_loss / num_batches
    
    def _validate(self, X, y):
        """验证"""
        activations = self.forward(X, training=False)
        return self._cross_entropy_loss(activations[-1], y)
    
    def _backward_enhanced(self, activations, targets, batch_size):
        """增强版反向传播"""
        # 计算输出层误差
        output_error = activations[-1] - targets
        
        # 从输出层向前传播误差
        errors = [output_error]
        
        for i in range(len(self.layers) - 2, -1, -1):
            if self.layers[i]['type'] == 'hidden':
                # 隐藏层误差传播（考虑Leaky ReLU）
                error = np.dot(errors[0], self.layers[i + 1]['weights'].T) * \
                       self._leaky_relu_derivative(activations[i + 1])
            else:
                error = np.dot(errors[0], self.layers[i + 1]['weights'].T)
            
            errors.insert(0, error)
        
        # 更新权重和偏置
        for i, layer in enumerate(self.layers):
            # 权重更新
            layer['weights'] -= self.learning_rate * np.dot(activations[i].T, errors[i]) / batch_size
            layer['biases'] -= self.learning_rate * np.mean(errors[i], axis=0)
            
            # 批归一化参数更新
            if layer['type'] == 'hidden' and self.use_batch_norm:
                # 简化的批归一化参数更新
                layer['bn_gamma'] -= self.learning_rate * 0.01 * np.random.randn(*layer['bn_gamma'].shape)
                layer['bn_beta'] -= self.learning_rate * 0.01 * np.random.randn(*layer['bn_beta'].shape)


class EnhancedNeuralPredictor:
    """增强版神经网络预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.feature_extractors = {}
        self.models = {}
        self.ensemble_models = {}  # 集成模型
        self.label_encoders = {}
        self.is_trained = False
        
        # 增强网络架构配置
        self.network_configs = {
            'red_odd_even': {'hidden_sizes': [128, 64, 32, 16], 'output_size': 6},
            'red_size': {'hidden_sizes': [128, 64, 32, 16], 'output_size': 6},
            'blue_size': {'hidden_sizes': [64, 32, 16], 'output_size': 3},
            'number_prediction': {'hidden_sizes': [256, 128, 64, 32], 'output_size': 47}
        }
        
        # 集成学习配置
        self.ensemble_size = 3  # 每个任务训练3个模型
    
    def extract_enhanced_features(self, data: pd.DataFrame, lookback: int = 15) -> np.ndarray:
        """
        提取增强特征
        
        Args:
            data: 历史数据
            lookback: 回看期数
            
        Returns:
            np.ndarray: 增强特征矩阵
        """
        features = []
        
        for i in range(lookback, len(data)):
            period_features = []
            
            # 提取最近lookback期的特征
            for j in range(i - lookback, i):
                row = data.iloc[j]
                red_balls, blue_balls = parse_numbers(row)
                
                # 基础特征
                period_features.extend([
                    # 红球特征
                    *red_balls,  # 5个红球号码
                    sum(red_balls),  # 红球和值
                    max(red_balls) - min(red_balls),  # 红球跨度
                    sum(1 for x in red_balls if x % 2 == 1),  # 奇数个数
                    sum(1 for x in red_balls if x <= 17),  # 小号个数
                    
                    # 蓝球特征
                    *blue_balls,  # 2个蓝球号码
                    sum(blue_balls),  # 蓝球和值
                    abs(blue_balls[1] - blue_balls[0]) if len(blue_balls) == 2 else 0,  # 蓝球间距
                    sum(1 for x in blue_balls if x <= 6),  # 蓝球小号个数
                ])
                
                # 高级特征
                period_features.extend([
                    self._calculate_ac_value(red_balls),  # AC值
                    self._count_consecutive(red_balls),  # 连号个数
                    self._calculate_variance(red_balls),  # 方差
                    self._calculate_skewness(red_balls),  # 偏度
                    self._calculate_prime_count(red_balls),  # 质数个数
                    self._calculate_fibonacci_count(red_balls),  # 斐波那契数个数
                ])
                
                # 重复号码特征
                if j > 0:
                    prev_row = data.iloc[j - 1]
                    prev_red, prev_blue = parse_numbers(prev_row)
                    red_repeat = len(set(red_balls) & set(prev_red))
                    blue_repeat = len(set(blue_balls) & set(prev_blue))
                else:
                    red_repeat = 0
                    blue_repeat = 0
                
                period_features.extend([red_repeat, blue_repeat])
                
                # 区间分布特征
                period_features.extend(self._calculate_zone_distribution(red_balls))
                
                # 和值尾数特征
                period_features.append(sum(red_balls) % 10)
                period_features.append(sum(blue_balls) % 10)
            
            # 时间序列特征
            recent_reds = []
            recent_blues = []
            for j in range(max(0, i - 5), i):
                row = data.iloc[j]
                red_balls, blue_balls = parse_numbers(row)
                recent_reds.extend(red_balls)
                recent_blues.extend(blue_balls)
            
            # 最近5期的频率特征
            red_freq = Counter(recent_reds)
            blue_freq = Counter(recent_blues)
            
            # 添加频率特征（前10个最热的号码）
            hot_red_freq = [red_freq.get(i, 0) for i in range(1, 11)]
            hot_blue_freq = [blue_freq.get(i, 0) for i in range(1, 7)]
            
            period_features.extend(hot_red_freq)
            period_features.extend(hot_blue_freq)
            
            features.append(period_features)

        return np.array(features)
        
        return np.array(features)
    
    def train_ensemble_models(self, data: pd.DataFrame, lookback: int = 15, epochs: int = 150):
        """
        训练集成模型
        
        Args:
            data: 训练数据
            lookback: 回看期数
            epochs: 训练轮数
        """
        print("开始训练增强版集成神经网络模型...")
        
        # 提取增强特征和标签
        X = self.extract_enhanced_features(data, lookback)
        labels = self.extract_labels(data, lookback)
        
        if len(X) == 0:
            print("没有足够的数据进行训练")
            return
        
        print(f"增强特征维度: {X.shape}")
        
        # 特征标准化
        self.feature_mean = np.mean(X, axis=0)
        self.feature_std = np.std(X, axis=0) + 1e-8
        X_normalized = (X - self.feature_mean) / self.feature_std
        
        input_size = X.shape[1]
        
        # 训练集成模型
        for task in ['red_odd_even', 'red_size', 'blue_size']:
            print(f"\n训练 {task} 集成模型...")
            
            # 编码标签
            unique_labels = list(set(labels[task]))
            self.label_encoders[task] = {label: i for i, label in enumerate(unique_labels)}
            
            y_encoded = np.array([self.label_encoders[task][label] for label in labels[task]])
            y_onehot = np.eye(len(unique_labels))[y_encoded]
            
            # 训练多个模型进行集成
            self.ensemble_models[task] = []
            
            for model_idx in range(self.ensemble_size):
                print(f"  训练模型 {model_idx + 1}/{self.ensemble_size}...")
                
                config = self.network_configs[task]
                model = EnhancedNeuralNetwork(
                    input_size, 
                    config['hidden_sizes'], 
                    len(unique_labels),
                    dropout_rate=0.3
                )
                
                # 使用不同的随机种子训练
                np.random.seed(42 + model_idx)
                model.train(X_normalized, y_onehot, epochs=epochs)
                
                self.ensemble_models[task].append(model)
        
        self.is_trained = True
        print("\n增强版集成神经网络模型训练完成!")
    
    def predict_ensemble(self, recent_data: pd.DataFrame, lookback: int = 15) -> Dict[str, Tuple[str, float]]:
        """
        使用集成模型进行预测
        
        Args:
            recent_data: 最近的数据
            lookback: 回看期数
            
        Returns:
            Dict[str, Tuple[str, float]]: 预测结果
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        if len(recent_data) < lookback:
            raise ValueError(f"数据不足，需要至少 {lookback} 期数据")
        
        # 提取最近的特征
        features = self.extract_enhanced_features(recent_data.tail(lookback + 1), lookback)
        if len(features) == 0:
            raise ValueError("无法提取特征")
        
        # 使用最后一个特征向量进行预测
        X = features[-1:] 
        X_normalized = (X - self.feature_mean) / self.feature_std
        
        predictions = {}
        
        for task in ['red_odd_even', 'red_size', 'blue_size']:
            if task in self.ensemble_models:
                # 集成预测
                ensemble_probs = []
                
                for model in self.ensemble_models[task]:
                    pred_probs = model.predict(X_normalized)[0]
                    ensemble_probs.append(pred_probs)
                
                # 平均集成
                avg_probs = np.mean(ensemble_probs, axis=0)
                
                # 找到最大概率的类别
                pred_class_idx = np.argmax(avg_probs)
                pred_prob = avg_probs[pred_class_idx]
                
                # 计算集成置信度（考虑模型间的一致性）
                consistency = 1.0 - np.std([probs[pred_class_idx] for probs in ensemble_probs])
                adjusted_prob = pred_prob * consistency
                
                # 解码标签
                reverse_encoder = {i: label for label, i in self.label_encoders[task].items()}
                pred_label = reverse_encoder[pred_class_idx]
                
                predictions[task] = (pred_label, float(adjusted_prob))
        
        return predictions
    
    def extract_labels(self, data: pd.DataFrame, lookback: int = 15) -> Dict[str, np.ndarray]:
        """提取标签（与基础版本相同）"""
        labels = {
            'red_odd_even': [],
            'red_size': [],
            'blue_size': []
        }
        
        for i in range(lookback, len(data)):
            row = data.iloc[i]
            red_balls, blue_balls = parse_numbers(row)
            
            # 状态标签
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            red_odd_even_state = ratio_to_state((red_odd, red_even))
            labels['red_odd_even'].append(red_odd_even_state)
            
            red_big, red_small = calculate_size_ratio_red(red_balls)
            red_size_state = ratio_to_state((red_big, red_small))
            labels['red_size'].append(red_size_state)

            blue_big, blue_small = calculate_size_ratio_blue(blue_balls)
            blue_size_state = ratio_to_state((blue_big, blue_small))
            labels['blue_size'].append(blue_size_state)
        
        # 转换为numpy数组
        for key in labels:
            labels[key] = np.array(labels[key])
        
        return labels
    
    def _calculate_ac_value(self, numbers: List[int]) -> int:
        """计算AC值"""
        if len(numbers) < 2:
            return 0
        
        differences = set()
        sorted_nums = sorted(numbers)
        
        for i in range(len(sorted_nums)):
            for j in range(i + 1, len(sorted_nums)):
                differences.add(abs(sorted_nums[j] - sorted_nums[i]))
        
        return len(differences) - (len(numbers) - 1)
    
    def _count_consecutive(self, numbers: List[int]) -> int:
        """计算连号个数"""
        sorted_nums = sorted(numbers)
        consecutive = 0
        
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i + 1] - sorted_nums[i] == 1:
                consecutive += 1
        
        return consecutive
    
    def _calculate_variance(self, numbers: List[int]) -> float:
        """计算方差"""
        return np.var(numbers)
    
    def _calculate_skewness(self, numbers: List[int]) -> float:
        """计算偏度"""
        mean = np.mean(numbers)
        std = np.std(numbers)
        if std == 0:
            return 0
        return np.mean([(x - mean) ** 3 for x in numbers]) / (std ** 3)
    
    def _calculate_prime_count(self, numbers: List[int]) -> int:
        """计算质数个数"""
        primes = {2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31}
        return sum(1 for num in numbers if num in primes)
    
    def _calculate_fibonacci_count(self, numbers: List[int]) -> int:
        """计算斐波那契数个数"""
        fibs = {1, 2, 3, 5, 8, 13, 21, 34}
        return sum(1 for num in numbers if num in fibs)
    
    def _calculate_zone_distribution(self, numbers: List[int]) -> List[int]:
        """计算区间分布"""
        zones = [0, 0, 0, 0, 0]  # 5个区间：1-7, 8-14, 15-21, 22-28, 29-35
        
        for num in numbers:
            if 1 <= num <= 7:
                zones[0] += 1
            elif 8 <= num <= 14:
                zones[1] += 1
            elif 15 <= num <= 21:
                zones[2] += 1
            elif 22 <= num <= 28:
                zones[3] += 1
            elif 29 <= num <= 35:
                zones[4] += 1
        
        return zones


def test_enhanced_neural_predictor():
    """测试增强版神经网络预测器"""
    from src.utils.utils import load_data
    
    predictor = EnhancedNeuralPredictor()
    
    print("测试增强版神经网络预测器...")
    
    # 加载数据
    data = load_data()
    if len(data) < 100:
        print("数据不足，无法测试")
        return
    
    # 训练集成模型
    train_data = data.iloc[20:]  # 使用后面的数据训练
    predictor.train_ensemble_models(train_data, lookback=10, epochs=50)
    
    # 测试预测
    test_data = data.head(30)  # 使用前面的数据测试
    try:
        predictions = predictor.predict_ensemble(test_data, lookback=10)
        
        print("\n增强版预测结果:")
        for task, (pred_label, prob) in predictions.items():
            print(f"  {task}: {pred_label} (置信度: {prob:.3f})")
    except Exception as e:
        print(f"预测失败: {e}")


if __name__ == "__main__":
    test_enhanced_neural_predictor()
