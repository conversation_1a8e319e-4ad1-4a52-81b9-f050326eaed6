#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应参数调优器

主要功能:
1. 贝叶斯优化
2. 遗传算法优化
3. 网格搜索优化
4. 在线学习调优
5. 多目标优化
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from collections import defaultdict, deque
import logging
from datetime import datetime
import json
import random
from scipy import stats
from scipy.optimize import minimize, differential_evolution
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern


@dataclass
class ParameterSpace:
    """参数空间定义"""

    name: str
    param_type: str  # 'continuous', 'discrete', 'categorical'
    bounds: Tuple[float, float] = None  # 连续参数边界
    choices: List[Any] = None  # 离散/分类参数选择
    default_value: Any = None
    description: str = ""


@dataclass
class OptimizationResult:
    """优化结果"""

    best_params: Dict[str, Any]
    best_score: float
    optimization_history: List[Dict[str, Any]] = field(default_factory=list)
    convergence_info: Dict[str, Any] = field(default_factory=dict)
    total_evaluations: int = 0
    optimization_time: float = 0.0


@dataclass
class TuningConfig:
    """调优配置"""

    # 优化方法
    optimization_method: str = (
        "bayesian"  # 'bayesian', 'genetic', 'grid', 'random', 'online'
    )
    max_evaluations: int = 100
    max_time: float = 3600.0  # 最大优化时间(秒)

    # 贝叶斯优化参数
    acquisition_function: str = "ei"  # 'ei', 'pi', 'ucb'
    exploration_weight: float = 0.1

    # 遗传算法参数
    population_size: int = 20
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8

    # 在线学习参数
    online_learning_rate: float = 0.01
    online_window_size: int = 50

    # 多目标优化
    objectives: List[str] = field(default_factory=lambda: ["accuracy"])
    objective_weights: List[float] = field(default_factory=lambda: [1.0])

    # 早停参数
    early_stopping: bool = True
    patience: int = 20
    min_improvement: float = 0.001


class AdaptiveParameterTuner:
    """自适应参数调优器"""

    def __init__(self, config: TuningConfig = None):
        """
        初始化自适应参数调优器

        Args:
            config: 调优配置
        """
        self.config = config or TuningConfig()

        # 参数空间
        self.parameter_spaces = {}

        # 优化历史
        self.optimization_history = []
        self.best_params_history = deque(maxlen=100)

        # 贝叶斯优化组件
        self.gp_model = None
        self.acquisition_history = []

        # 遗传算法组件
        self.population = []
        self.fitness_history = []

        # 在线学习组件
        self.online_params = {}
        self.online_gradients = {}
        self.performance_buffer = deque(maxlen=self.config.online_window_size)

        # 性能跟踪
        self.evaluation_count = 0
        self.start_time = None

        self.logger = logging.getLogger(__name__)

    def define_parameter_space(self, param_spaces: List[ParameterSpace]) -> None:
        """定义参数空间"""
        for space in param_spaces:
            self.parameter_spaces[space.name] = space

        self.logger.info(f"参数空间定义完成，共 {len(param_spaces)} 个参数")

    def optimize(
        self,
        objective_function: Callable[[Dict[str, Any]], float],
        algorithm_instance: Any = None,
    ) -> OptimizationResult:
        """执行参数优化"""
        self.start_time = datetime.now()
        self.evaluation_count = 0

        if self.config.optimization_method == "bayesian":
            result = self._bayesian_optimization(objective_function)
        elif self.config.optimization_method == "genetic":
            result = self._genetic_algorithm_optimization(objective_function)
        elif self.config.optimization_method == "grid":
            result = self._grid_search_optimization(objective_function)
        elif self.config.optimization_method == "random":
            result = self._random_search_optimization(objective_function)
        elif self.config.optimization_method == "online":
            result = self._online_learning_optimization(
                objective_function, algorithm_instance
            )
        else:
            raise ValueError(f"不支持的优化方法: {self.config.optimization_method}")

        # 记录优化时间
        result.optimization_time = (datetime.now() - self.start_time).total_seconds()
        result.total_evaluations = self.evaluation_count

        self.logger.info(
            f"参数优化完成，最佳得分: {result.best_score:.4f}，评估次数: {self.evaluation_count}"
        )

        return result

    def _bayesian_optimization(
        self, objective_function: Callable
    ) -> OptimizationResult:
        """贝叶斯优化"""
        # 初始化高斯过程
        kernel = Matern(length_scale=1.0, nu=2.5)
        self.gp_model = GaussianProcessRegressor(
            kernel=kernel, alpha=1e-6, normalize_y=True
        )

        # 初始随机采样
        initial_samples = min(10, self.config.max_evaluations // 4)
        X_observed = []
        y_observed = []

        for _ in range(initial_samples):
            params = self._sample_random_params()
            score = self._evaluate_objective(objective_function, params)

            X_observed.append(self._params_to_vector(params))
            y_observed.append(score)

        best_score = max(y_observed)
        best_params = self._vector_to_params(X_observed[np.argmax(y_observed)])

        # 贝叶斯优化主循环
        patience_counter = 0

        while (
            self.evaluation_count < self.config.max_evaluations
            and (datetime.now() - self.start_time).total_seconds()
            < self.config.max_time
        ):

            # 拟合高斯过程
            self.gp_model.fit(X_observed, y_observed)

            # 获取下一个采样点
            next_params = self._acquire_next_point(X_observed, y_observed)
            next_score = self._evaluate_objective(objective_function, next_params)

            # 更新观测数据
            X_observed.append(self._params_to_vector(next_params))
            y_observed.append(next_score)

            # 更新最佳结果
            if next_score > best_score + self.config.min_improvement:
                best_score = next_score
                best_params = next_params.copy()
                patience_counter = 0
            else:
                patience_counter += 1

            # 早停检查
            if self.config.early_stopping and patience_counter >= self.config.patience:
                self.logger.info(f"早停触发，patience: {patience_counter}")
                break

        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=self.optimization_history.copy(),
            convergence_info={
                "converged": patience_counter < self.config.patience,
                "final_patience": patience_counter,
            },
        )

    def _acquire_next_point(
        self, X_observed: List[List[float]], y_observed: List[float]
    ) -> Dict[str, Any]:
        """获取下一个采样点"""

        # 定义采集函数
        def acquisition_function(x):
            x = x.reshape(1, -1)
            mu, sigma = self.gp_model.predict(x, return_std=True)

            if self.config.acquisition_function == "ei":
                # Expected Improvement
                best_y = max(y_observed)
                z = (mu - best_y) / (sigma + 1e-9)
                ei = (mu - best_y) * stats.norm.cdf(z) + sigma * stats.norm.pdf(z)
                return -ei[0]  # 负号因为要最小化

            elif self.config.acquisition_function == "pi":
                # Probability of Improvement
                best_y = max(y_observed)
                z = (mu - best_y) / (sigma + 1e-9)
                pi = stats.norm.cdf(z)
                return -pi[0]

            elif self.config.acquisition_function == "ucb":
                # Upper Confidence Bound
                ucb = mu + self.config.exploration_weight * sigma
                return -ucb[0]

            else:
                return -mu[0]

        # 优化采集函数
        bounds = self._get_optimization_bounds()

        # 多次随机初始化以避免局部最优
        best_x = None
        best_acq_value = float("inf")

        for _ in range(10):
            x0 = self._sample_random_vector()

            try:
                result = minimize(
                    acquisition_function, x0, bounds=bounds, method="L-BFGS-B"
                )

                if result.fun < best_acq_value:
                    best_acq_value = result.fun
                    best_x = result.x

            except Exception as e:
                self.logger.debug(f"采集函数优化失败: {e}")
                continue

        if best_x is None:
            # 备用方案：随机采样
            return self._sample_random_params()

        return self._vector_to_params(best_x)

    def _genetic_algorithm_optimization(
        self, objective_function: Callable
    ) -> OptimizationResult:
        """遗传算法优化"""
        # 初始化种群
        self.population = []
        fitness_scores = []

        for _ in range(self.config.population_size):
            individual = self._sample_random_params()
            fitness = self._evaluate_objective(objective_function, individual)

            self.population.append(individual)
            fitness_scores.append(fitness)

        best_score = max(fitness_scores)
        best_params = self.population[np.argmax(fitness_scores)].copy()

        generation = 0
        patience_counter = 0

        while (
            self.evaluation_count < self.config.max_evaluations
            and (datetime.now() - self.start_time).total_seconds()
            < self.config.max_time
        ):

            # 选择
            selected_indices = self._tournament_selection(fitness_scores)

            # 交叉和变异
            new_population = []
            new_fitness = []

            for i in range(0, len(selected_indices), 2):
                parent1 = self.population[selected_indices[i]]
                parent2 = self.population[
                    selected_indices[min(i + 1, len(selected_indices) - 1)]
                ]

                # 交叉
                if random.random() < self.config.crossover_rate:
                    child1, child2 = self._crossover(parent1, parent2)
                else:
                    child1, child2 = parent1.copy(), parent2.copy()

                # 变异
                if random.random() < self.config.mutation_rate:
                    child1 = self._mutate(child1)
                if random.random() < self.config.mutation_rate:
                    child2 = self._mutate(child2)

                # 评估
                fitness1 = self._evaluate_objective(objective_function, child1)
                fitness2 = self._evaluate_objective(objective_function, child2)

                new_population.extend([child1, child2])
                new_fitness.extend([fitness1, fitness2])

                if self.evaluation_count >= self.config.max_evaluations:
                    break

            # 更新种群
            self.population = new_population[: self.config.population_size]
            fitness_scores = new_fitness[: self.config.population_size]

            # 更新最佳结果
            current_best = max(fitness_scores)
            if current_best > best_score + self.config.min_improvement:
                best_score = current_best
                best_params = self.population[np.argmax(fitness_scores)].copy()
                patience_counter = 0
            else:
                patience_counter += 1

            generation += 1

            # 早停检查
            if self.config.early_stopping and patience_counter >= self.config.patience:
                self.logger.info(f"遗传算法早停，代数: {generation}")
                break

        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=self.optimization_history.copy(),
            convergence_info={
                "generations": generation,
                "converged": patience_counter < self.config.patience,
            },
        )

    def _tournament_selection(
        self, fitness_scores: List[float], tournament_size: int = 3
    ) -> List[int]:
        """锦标赛选择"""
        selected = []

        for _ in range(len(fitness_scores)):
            # 随机选择参赛者
            tournament_indices = random.sample(
                range(len(fitness_scores)), min(tournament_size, len(fitness_scores))
            )

            # 选择最佳
            winner_idx = max(tournament_indices, key=lambda i: fitness_scores[i])
            selected.append(winner_idx)

        return selected

    def _crossover(
        self, parent1: Dict[str, Any], parent2: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """交叉操作"""
        child1 = parent1.copy()
        child2 = parent2.copy()

        for param_name in parent1.keys():
            if random.random() < 0.5:
                child1[param_name] = parent2[param_name]
                child2[param_name] = parent1[param_name]

        return child1, child2

    def _mutate(self, individual: Dict[str, Any]) -> Dict[str, Any]:
        """变异操作"""
        mutated = individual.copy()

        for param_name, space in self.parameter_spaces.items():
            if random.random() < 0.1:  # 变异概率
                if space.param_type == "continuous":
                    # 高斯变异
                    current_value = mutated[param_name]
                    mutation_strength = (space.bounds[1] - space.bounds[0]) * 0.1
                    new_value = current_value + random.gauss(0, mutation_strength)
                    mutated[param_name] = np.clip(
                        new_value, space.bounds[0], space.bounds[1]
                    )

                elif space.param_type in ["discrete", "categorical"]:
                    # 随机选择
                    mutated[param_name] = random.choice(space.choices)

        return mutated

    def _grid_search_optimization(
        self, objective_function: Callable
    ) -> OptimizationResult:
        """网格搜索优化"""
        # 生成网格点
        grid_points = self._generate_grid_points()

        best_score = float("-inf")
        best_params = None

        for params in grid_points:
            if self.evaluation_count >= self.config.max_evaluations:
                break

            score = self._evaluate_objective(objective_function, params)

            if score > best_score:
                best_score = score
                best_params = params.copy()

        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=self.optimization_history.copy(),
            convergence_info={"grid_points_evaluated": len(grid_points)},
        )

    def _generate_grid_points(self) -> List[Dict[str, Any]]:
        """生成网格点"""
        # 简化网格生成
        grid_points = []

        # 为每个参数生成候选值
        param_candidates = {}

        for name, space in self.parameter_spaces.items():
            if space.param_type == "continuous":
                # 连续参数：生成等间距点
                num_points = min(
                    10,
                    int(
                        self.config.max_evaluations ** (1 / len(self.parameter_spaces))
                    ),
                )
                candidates = np.linspace(space.bounds[0], space.bounds[1], num_points)
                param_candidates[name] = candidates.tolist()

            elif space.param_type in ["discrete", "categorical"]:
                param_candidates[name] = space.choices

        # 生成笛卡尔积
        import itertools

        param_names = list(param_candidates.keys())
        param_values = [param_candidates[name] for name in param_names]

        for combination in itertools.product(*param_values):
            params = dict(zip(param_names, combination))
            grid_points.append(params)

            # 限制网格点数量
            if len(grid_points) >= self.config.max_evaluations:
                break

        return grid_points

    def _random_search_optimization(
        self, objective_function: Callable
    ) -> OptimizationResult:
        """随机搜索优化"""
        best_score = float("-inf")
        best_params = None

        while (
            self.evaluation_count < self.config.max_evaluations
            and (datetime.now() - self.start_time).total_seconds()
            < self.config.max_time
        ):

            params = self._sample_random_params()
            score = self._evaluate_objective(objective_function, params)

            if score > best_score:
                best_score = score
                best_params = params.copy()

        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=self.optimization_history.copy(),
            convergence_info={"random_samples": self.evaluation_count},
        )

    def _online_learning_optimization(
        self, objective_function: Callable, algorithm_instance: Any
    ) -> OptimizationResult:
        """在线学习优化"""
        # 初始化参数
        if not self.online_params:
            self.online_params = self._sample_random_params()

        best_score = float("-inf")
        best_params = self.online_params.copy()

        # 在线学习主循环
        for iteration in range(self.config.max_evaluations):
            # 评估当前参数
            current_score = self._evaluate_objective(
                objective_function, self.online_params
            )
            self.performance_buffer.append(current_score)

            if current_score > best_score:
                best_score = current_score
                best_params = self.online_params.copy()

            # 计算梯度（数值梯度）
            gradients = self._compute_numerical_gradients(
                objective_function, self.online_params
            )

            # 更新参数
            self._update_online_params(gradients)

            # 自适应学习率
            if iteration % 10 == 0:
                self._adapt_learning_rate()

        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=self.optimization_history.copy(),
            convergence_info={"online_iterations": iteration + 1},
        )

    def _compute_numerical_gradients(
        self, objective_function: Callable, params: Dict[str, Any]
    ) -> Dict[str, float]:
        """计算数值梯度"""
        gradients = {}
        epsilon = 1e-5

        base_score = self._evaluate_objective(objective_function, params)

        for param_name, space in self.parameter_spaces.items():
            if space.param_type == "continuous":
                # 计算数值梯度
                params_plus = params.copy()
                params_plus[param_name] += epsilon

                # 确保在边界内
                params_plus[param_name] = np.clip(
                    params_plus[param_name], space.bounds[0], space.bounds[1]
                )

                score_plus = self._evaluate_objective(objective_function, params_plus)
                gradient = (score_plus - base_score) / epsilon
                gradients[param_name] = gradient

            else:
                # 离散参数：随机梯度
                gradients[param_name] = random.gauss(0, 0.1)

        return gradients

    def _update_online_params(self, gradients: Dict[str, float]) -> None:
        """更新在线参数"""
        for param_name, gradient in gradients.items():
            space = self.parameter_spaces[param_name]

            if space.param_type == "continuous":
                # 梯度上升
                current_value = self.online_params[param_name]
                new_value = current_value + self.config.online_learning_rate * gradient

                # 边界约束
                self.online_params[param_name] = np.clip(
                    new_value, space.bounds[0], space.bounds[1]
                )

            elif space.param_type in ["discrete", "categorical"]:
                # 概率性更新
                if abs(gradient) > 0.1:  # 阈值
                    self.online_params[param_name] = random.choice(space.choices)

    def _adapt_learning_rate(self) -> None:
        """自适应学习率"""
        if len(self.performance_buffer) < 10:
            return

        # 分析性能趋势
        recent_scores = list(self.performance_buffer)[-10:]
        trend = np.polyfit(range(len(recent_scores)), recent_scores, 1)[0]

        if trend > 0:
            # 性能提升，保持或略微增加学习率
            self.config.online_learning_rate *= 1.01
        else:
            # 性能下降，减少学习率
            self.config.online_learning_rate *= 0.99

        # 边界约束
        self.config.online_learning_rate = np.clip(
            self.config.online_learning_rate, 1e-5, 0.1
        )

    def _sample_random_params(self) -> Dict[str, Any]:
        """随机采样参数"""
        params = {}

        for name, space in self.parameter_spaces.items():
            if space.param_type == "continuous":
                params[name] = random.uniform(space.bounds[0], space.bounds[1])
            elif space.param_type in ["discrete", "categorical"]:
                params[name] = random.choice(space.choices)
            else:
                params[name] = space.default_value

        return params

    def _params_to_vector(self, params: Dict[str, Any]) -> List[float]:
        """参数字典转向量"""
        vector = []

        for name, space in self.parameter_spaces.items():
            if space.param_type == "continuous":
                vector.append(params[name])
            elif space.param_type == "discrete":
                # 离散值映射到连续空间
                index = space.choices.index(params[name])
                normalized = index / (len(space.choices) - 1)
                vector.append(normalized)
            elif space.param_type == "categorical":
                # 分类值one-hot编码
                for choice in space.choices:
                    vector.append(1.0 if params[name] == choice else 0.0)

        return vector

    def _vector_to_params(self, vector: List[float]) -> Dict[str, Any]:
        """向量转参数字典"""
        params = {}
        idx = 0

        for name, space in self.parameter_spaces.items():
            if space.param_type == "continuous":
                params[name] = np.clip(vector[idx], space.bounds[0], space.bounds[1])
                idx += 1
            elif space.param_type == "discrete":
                normalized = np.clip(vector[idx], 0.0, 1.0)
                choice_idx = int(normalized * (len(space.choices) - 1))
                params[name] = space.choices[choice_idx]
                idx += 1
            elif space.param_type == "categorical":
                # one-hot解码
                choice_scores = vector[idx : idx + len(space.choices)]
                best_choice_idx = np.argmax(choice_scores)
                params[name] = space.choices[best_choice_idx]
                idx += len(space.choices)

        return params

    def _sample_random_vector(self) -> List[float]:
        """随机采样向量"""
        params = self._sample_random_params()
        return self._params_to_vector(params)

    def _get_optimization_bounds(self) -> List[Tuple[float, float]]:
        """获取优化边界"""
        bounds = []

        for name, space in self.parameter_spaces.items():
            if space.param_type == "continuous":
                bounds.append(space.bounds)
            elif space.param_type == "discrete":
                bounds.append((0.0, 1.0))
            elif space.param_type == "categorical":
                # one-hot编码边界
                for _ in space.choices:
                    bounds.append((0.0, 1.0))

        return bounds

    def _evaluate_objective(
        self, objective_function: Callable, params: Dict[str, Any]
    ) -> float:
        """评估目标函数"""
        try:
            score = objective_function(params)

            # 记录评估历史
            self.optimization_history.append(
                {
                    "evaluation": self.evaluation_count,
                    "params": params.copy(),
                    "score": score,
                    "timestamp": datetime.now(),
                }
            )

            self.evaluation_count += 1

            return score

        except Exception as e:
            self.logger.warning(f"目标函数评估失败: {e}")
            return float("-inf")

    def suggest_next_params(
        self, performance_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """建议下一组参数"""
        if not performance_history:
            return self._sample_random_params()

        # 基于历史性能建议参数
        if len(performance_history) < 5:
            # 数据不足，随机采样
            return self._sample_random_params()

        # 分析最佳参数模式
        best_performances = sorted(
            performance_history, key=lambda x: x.get("score", 0), reverse=True
        )[:5]

        # 计算最佳参数的平均值
        suggested_params = {}

        for name, space in self.parameter_spaces.items():
            if space.param_type == "continuous":
                values = [
                    perf["params"][name]
                    for perf in best_performances
                    if name in perf["params"]
                ]
                if values:
                    suggested_params[name] = np.mean(values)
                else:
                    suggested_params[name] = (space.bounds[0] + space.bounds[1]) / 2

            elif space.param_type in ["discrete", "categorical"]:
                values = [
                    perf["params"][name]
                    for perf in best_performances
                    if name in perf["params"]
                ]
                if values:
                    # 选择最频繁的值
                    from collections import Counter

                    counter = Counter(values)
                    suggested_params[name] = counter.most_common(1)[0][0]
                else:
                    suggested_params[name] = random.choice(space.choices)

        return suggested_params

    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化摘要"""
        if not self.optimization_history:
            return {"status": "no_optimization_performed"}

        scores = [entry["score"] for entry in self.optimization_history]

        summary = {
            "total_evaluations": len(self.optimization_history),
            "best_score": max(scores),
            "worst_score": min(scores),
            "mean_score": np.mean(scores),
            "score_std": np.std(scores),
            "improvement_rate": self._calculate_improvement_rate(),
            "convergence_analysis": self._analyze_convergence(),
            "parameter_importance": self._analyze_parameter_importance(),
        }

        return summary

    def _calculate_improvement_rate(self) -> float:
        """计算改进率"""
        if len(self.optimization_history) < 10:
            return 0.0

        early_scores = [entry["score"] for entry in self.optimization_history[:10]]
        late_scores = [entry["score"] for entry in self.optimization_history[-10:]]

        early_mean = np.mean(early_scores)
        late_mean = np.mean(late_scores)

        if early_mean == 0:
            return 0.0

        return (late_mean - early_mean) / abs(early_mean)

    def _analyze_convergence(self) -> Dict[str, Any]:
        """分析收敛性"""
        if len(self.optimization_history) < 20:
            return {"status": "insufficient_data"}

        scores = [entry["score"] for entry in self.optimization_history]

        # 计算移动平均
        window_size = min(10, len(scores) // 4)
        moving_avg = []

        for i in range(window_size, len(scores)):
            avg = np.mean(scores[i - window_size : i])
            moving_avg.append(avg)

        # 分析趋势
        if len(moving_avg) > 5:
            x = np.arange(len(moving_avg))
            slope, _, r_value, _, _ = stats.linregress(x, moving_avg)

            convergence_info = {
                "trend_slope": slope,
                "trend_correlation": r_value**2,
                "is_converging": abs(slope) < 0.001 and r_value**2 > 0.8,
                "stability": 1.0 / (1.0 + np.var(moving_avg[-10:])),
            }
        else:
            convergence_info = {"status": "insufficient_data_for_trend"}

        return convergence_info

    def _analyze_parameter_importance(self) -> Dict[str, float]:
        """分析参数重要性"""
        if len(self.optimization_history) < 20:
            return {}

        importance = {}

        for param_name in self.parameter_spaces.keys():
            # 计算参数与性能的相关性
            param_values = []
            scores = []

            for entry in self.optimization_history:
                if param_name in entry["params"]:
                    param_values.append(entry["params"][param_name])
                    scores.append(entry["score"])

            if len(param_values) > 10:
                try:
                    # 对于分类参数，使用方差分析
                    space = self.parameter_spaces[param_name]
                    if space.param_type in ["discrete", "categorical"]:
                        # 计算不同取值的性能差异
                        value_groups = defaultdict(list)
                        for val, score in zip(param_values, scores):
                            value_groups[val].append(score)

                        if len(value_groups) > 1:
                            group_means = [
                                np.mean(group) for group in value_groups.values()
                            ]
                            importance[param_name] = np.var(group_means)
                        else:
                            importance[param_name] = 0.0

                    else:
                        # 连续参数使用相关系数
                        correlation, _ = stats.pearsonr(param_values, scores)
                        importance[param_name] = abs(correlation)

                except Exception:
                    importance[param_name] = 0.0
            else:
                importance[param_name] = 0.0

        return importance

    def save_optimization_state(self, filepath: str) -> None:
        """保存优化状态"""
        state = {
            "parameter_spaces": {},
            "optimization_history": self.optimization_history,
            "config": self.config.__dict__,
            "evaluation_count": self.evaluation_count,
            "online_params": self.online_params,
        }

        # 序列化参数空间
        for name, space in self.parameter_spaces.items():
            state["parameter_spaces"][name] = {
                "name": space.name,
                "param_type": space.param_type,
                "bounds": space.bounds,
                "choices": space.choices,
                "default_value": space.default_value,
                "description": space.description,
            }

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(state, f, ensure_ascii=False, indent=2, default=str)

        self.logger.info(f"优化状态已保存到: {filepath}")

    def load_optimization_state(self, filepath: str) -> None:
        """加载优化状态"""
        try:
            with open(filepath, "r", encoding="utf-8") as f:
                state = json.load(f)

            # 恢复参数空间
            self.parameter_spaces = {}
            for name, space_data in state.get("parameter_spaces", {}).items():
                space = ParameterSpace(
                    name=space_data["name"],
                    param_type=space_data["param_type"],
                    bounds=(
                        tuple(space_data["bounds"]) if space_data["bounds"] else None
                    ),
                    choices=space_data["choices"],
                    default_value=space_data["default_value"],
                    description=space_data["description"],
                )
                self.parameter_spaces[name] = space

            # 恢复历史
            self.optimization_history = state.get("optimization_history", [])
            self.evaluation_count = state.get("evaluation_count", 0)
            self.online_params = state.get("online_params", {})

            self.logger.info(f"优化状态已从 {filepath} 加载")

        except Exception as e:
            self.logger.error(f"加载优化状态失败: {e}")
