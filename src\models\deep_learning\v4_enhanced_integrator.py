#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V4增强集成器
将红球大小比专项优化器集成到V4系统中
"""

import pandas as pd
import numpy as np
from pathlib import Path
import joblib
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# TensorFlow导入
try:
    import tensorflow as tf
    from tensorflow import keras
    TF_AVAILABLE = True
except ImportError:
    print("[WARN] TensorFlow不可用")
    TF_AVAILABLE = False

class V4EnhancedIntegrator:
    """V4增强集成器"""
    
    def __init__(self, 
                 v4_model_path: str = None,
                 red_size_optimizer_path: str = None,
                 data_path: str = "data/raw/dlt_data.csv"):
        
        self.data_path = Path(data_path)
        self.v4_model = None
        self.v4_scaler = None
        self.red_size_models = {}
        self.red_size_scaler = None
        self.red_size_features = []
        
        # 自动查找最新模型
        if v4_model_path is None:
            v4_model_path = self._find_latest_v4_model()
        if red_size_optimizer_path is None:
            red_size_optimizer_path = self._find_latest_red_size_optimizer()
            
        self.v4_model_path = v4_model_path
        self.red_size_optimizer_path = red_size_optimizer_path

        # 自动加载模型
        self.load_models()
        
    def _find_latest_v4_model(self):
        """查找最新的V4模型"""
        v4_dir = Path("models/v4_trained")
        if v4_dir.exists():
            model_files = list(v4_dir.glob("v4_final_*.h5"))
            if model_files:
                latest = max(model_files, key=lambda x: x.stat().st_mtime)
                print(f"[INFO] 找到V4模型: {latest}")
                return str(latest)
        return None
    
    def _find_latest_red_size_optimizer(self):
        """查找最新的红球大小比优化器"""
        opt_dir = Path("models/red_size_ratio_optimized")
        if opt_dir.exists():
            subdirs = [d for d in opt_dir.iterdir() if d.is_dir()]
            if subdirs:
                latest = max(subdirs, key=lambda x: x.stat().st_mtime)
                print(f"[INFO] 找到红球大小比优化器: {latest}")
                return str(latest)
        return None
    
    def load_models(self):
        """加载所有模型"""
        print("[INFO] 加载模型...")
        
        # 加载V4模型
        if self.v4_model_path and TF_AVAILABLE:
            try:
                self.v4_model = keras.models.load_model(self.v4_model_path)
                print(f"[OK] V4模型加载成功")
                
                # 尝试加载V4标准化器
                v4_dir = Path(self.v4_model_path).parent
                scaler_files = list(v4_dir.glob("scaler_final_*.pkl"))
                if scaler_files:
                    with open(scaler_files[0], 'rb') as f:
                        self.v4_scaler = joblib.load(f)
                    print(f"[OK] V4标准化器加载成功")
                    
            except Exception as e:
                print(f"[WARN] V4模型加载失败: {e}")
        
        # 加载红球大小比优化器
        if self.red_size_optimizer_path:
            opt_dir = Path(self.red_size_optimizer_path)
            
            # 加载传统机器学习模型
            model_files = {
                'RandomForest': 'randomforest_model.pkl',
                'GradientBoosting': 'gradientboosting_model.pkl',
                'LogisticRegression': 'logisticregression_model.pkl'
            }
            
            for name, filename in model_files.items():
                model_path = opt_dir / filename
                if model_path.exists():
                    self.red_size_models[name] = joblib.load(model_path)
                    print(f"[OK] {name}模型加载成功")
            
            # 加载神经网络
            nn_path = opt_dir / "neural_network_model.h5"
            if nn_path.exists() and TF_AVAILABLE:
                try:
                    self.red_size_models['NeuralNetwork'] = keras.models.load_model(str(nn_path))
                    print(f"[OK] 神经网络模型加载成功")
                except Exception as e:
                    print(f"[WARN] 神经网络加载失败: {e}")
            
            # 加载标准化器
            scaler_path = opt_dir / "scaler.pkl"
            if scaler_path.exists():
                self.red_size_scaler = joblib.load(scaler_path)
                print(f"[OK] 红球大小比标准化器加载成功")
            
            # 加载特征列表
            features_path = opt_dir / "feature_columns.txt"
            if features_path.exists():
                with open(features_path, 'r', encoding='utf-8') as f:
                    self.red_size_features = [line.strip() for line in f.readlines()]
                print(f"[OK] 特征列表加载成功: {len(self.red_size_features)}个特征")
    
    def prepare_data_for_prediction(self, df: pd.DataFrame):
        """为预测准备数据"""
        # V4特征工程
        v4_features = self._v4_feature_engineering(df.copy())
        
        # 红球大小比特征工程
        red_size_features = self._red_size_feature_engineering(df.copy())
        
        return v4_features, red_size_features
    
    def _v4_feature_engineering(self, df: pd.DataFrame):
        """V4特征工程"""
        red_balls = ['红球1', '红球2', '红球3', '红球4', '红球5']
        blue_balls = ['蓝球1', '蓝球2']
        
        # 基础特征
        df['红球奇数个数'] = df[red_balls].apply(lambda row: sum(x % 2 == 1 for x in row), axis=1)
        df['红球大数个数'] = df[red_balls].apply(lambda row: sum(x > 17 for x in row), axis=1)
        df['蓝球大数个数'] = df[blue_balls].apply(lambda row: sum(x > 6 for x in row), axis=1)
        
        # 统计特征
        df['红球和值'] = df[red_balls].sum(axis=1)
        df['红球均值'] = df[red_balls].mean(axis=1)
        df['红球方差'] = df[red_balls].var(axis=1)
        df['红球跨度'] = df[red_balls].max(axis=1) - df[red_balls].min(axis=1)
        df['红球标准差'] = df[red_balls].std(axis=1)
        
        df['蓝球和值'] = df[blue_balls].sum(axis=1)
        df['蓝球跨度'] = df[blue_balls].max(axis=1) - df[blue_balls].min(axis=1)
        
        # 区间分布特征
        for i, (start, end) in enumerate([(1, 7), (8, 14), (15, 21), (22, 28), (29, 35)]):
            df[f'红球区间{i+1}个数'] = df[red_balls].apply(
                lambda row: sum(start <= x <= end for x in row), axis=1
            )
        
        # 连号特征
        df['红球连号个数'] = df[red_balls].apply(self._count_consecutive, axis=1)
        df['蓝球连号个数'] = df[blue_balls].apply(self._count_consecutive, axis=1)
        
        # 历史趋势特征
        for window in [3, 5, 10]:
            for col in ['红球奇数个数', '红球大数个数', '蓝球大数个数']:
                df[f'{col}_趋势{window}'] = df[col].rolling(window=window, min_periods=1).mean()
                df[f'{col}_波动{window}'] = df[col].rolling(window=window, min_periods=1).std().fillna(0)
        
        # 新增特征
        df['红球奇偶平衡度'] = df['红球奇数个数'].apply(lambda x: abs(x - 2.5))
        df['红球大小平衡度'] = df['红球大数个数'].apply(lambda x: abs(x - 2.5))
        df['红球最大值'] = df[red_balls].max(axis=1)
        df['红球最小值'] = df[red_balls].min(axis=1)
        df['红球中位数'] = df[red_balls].median(axis=1)
        
        return df
    
    def _red_size_feature_engineering(self, df: pd.DataFrame):
        """红球大小比特征工程"""
        red_balls = ['红球1', '红球2', '红球3', '红球4', '红球5']
        
        # 核心特征
        df['红球和值'] = df[red_balls].sum(axis=1)
        df['红球均值'] = df[red_balls].mean(axis=1)
        df['红球方差'] = df[red_balls].var(axis=1)
        df['红球跨度'] = df[red_balls].max(axis=1) - df[red_balls].min(axis=1)
        df['红球标准差'] = df[red_balls].std(axis=1)
        df['红球中位数'] = df[red_balls].median(axis=1)
        
        # 分段特征
        df['红球和值分段'] = pd.cut(df['红球和值'], 
                                bins=[0, 70, 85, 100, 115, 200], 
                                labels=[0, 1, 2, 3, 4])
        df['红球均值分段'] = pd.cut(df['红球均值'], 
                                bins=[0, 14, 17, 20, 23, 40], 
                                labels=[0, 1, 2, 3, 4])
        
        # 历史趋势特征
        df['红球大数个数'] = df[red_balls].apply(lambda row: sum(x > 17 for x in row), axis=1)
        
        for window in [3, 5, 7]:
            df[f'红球和值_趋势{window}'] = df['红球和值'].rolling(window=window, min_periods=1).mean()
            df[f'红球均值_趋势{window}'] = df['红球均值'].rolling(window=window, min_periods=1).mean()
            df[f'红球大数个数_趋势{window}'] = df['红球大数个数'].rolling(window=window, min_periods=1).mean()
        
        # 波动性特征
        for window in [3, 5]:
            df[f'红球和值_波动{window}'] = df['红球和值'].rolling(window=window, min_periods=1).std().fillna(0)
            df[f'红球均值_波动{window}'] = df['红球均值'].rolling(window=window, min_periods=1).std().fillna(0)
        
        # 区间分布特征
        for i, (start, end) in enumerate([(1, 7), (8, 14), (15, 21), (22, 28), (29, 35)]):
            df[f'红球区间{i+1}个数'] = df[red_balls].apply(
                lambda row: sum(start <= x <= end for x in row), axis=1
            )
        
        return df
    
    def _count_consecutive(self, row) -> int:
        """计算连号个数"""
        sorted_nums = sorted(row)
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive_count += 1
        return consecutive_count
    
    def predict_enhanced(self, df: pd.DataFrame, target_period: int):
        """增强预测"""
        print(f"[INFO] 对期号 {target_period} 进行增强预测...")
        
        # 准备数据
        v4_features, red_size_features = self.prepare_data_for_prediction(df)
        
        results = {}
        
        # V4预测（红球奇偶比、蓝球大小比、号码预测）
        if self.v4_model and self.v4_scaler:
            try:
                # 选择V4特征
                v4_feature_cols = [col for col in v4_features.columns 
                                 if col not in ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2', '日期']]
                
                # 准备序列数据
                seq_len = 30
                if len(v4_features) >= seq_len:
                    sequence_data = []
                    for i in range(seq_len):
                        row_features = [v4_features.iloc[-(seq_len-i)][col] for col in v4_feature_cols]
                        sequence_data.append(row_features)
                    
                    X = np.array([sequence_data])
                    
                    # V4预测
                    v4_pred = self.v4_model.predict(X, verbose=0)
                    
                    # 解析V4预测结果
                    if isinstance(v4_pred, list) and len(v4_pred) >= 3:
                        results['红球奇偶比'] = np.argmax(v4_pred[0][0])  # 0-4对应0:5到4:1
                        results['蓝球大小比'] = np.argmax(v4_pred[2][0])  # 0-2对应0:2到2:0
                        
                        # 红球和蓝球号码预测
                        if len(v4_pred) >= 5:
                            results['红球预测'] = v4_pred[3][0]  # 35维概率
                            results['蓝球预测'] = v4_pred[4][0]  # 12维概率
                    
                    print(f"[OK] V4预测完成")
                    
            except Exception as e:
                print(f"[WARN] V4预测失败: {e}")
        
        # 红球大小比专项预测
        if self.red_size_models and self.red_size_scaler and self.red_size_features:
            try:
                # 准备特征数据
                X_red_size = red_size_features[self.red_size_features].iloc[-1:].fillna(0)
                X_red_size_scaled = self.red_size_scaler.transform(X_red_size)
                
                # 集成预测
                predictions = []
                for name, model in self.red_size_models.items():
                    if name == 'NeuralNetwork' and TF_AVAILABLE:
                        pred_proba = model.predict(X_red_size_scaled, verbose=0)
                        pred = np.argmax(pred_proba, axis=1)[0]
                    else:
                        pred = model.predict(X_red_size_scaled)[0]
                    predictions.append(pred)
                
                # 投票决定最终预测
                if predictions:
                    red_size_pred = np.bincount(predictions).argmax()
                    results['红球大小比_专项'] = red_size_pred
                    results['红球大小比_投票详情'] = predictions
                    
                    print(f"[OK] 红球大小比专项预测: {red_size_pred} (投票: {predictions})")
                    
            except Exception as e:
                print(f"[WARN] 红球大小比专项预测失败: {e}")
        
        # 融合预测结果
        final_results = self._merge_predictions(results)
        
        return final_results
    
    def _merge_predictions(self, results):
        """融合预测结果"""
        final = {}
        
        # 红球奇偶比：使用V4预测
        if '红球奇偶比' in results:
            final['红球奇偶比'] = results['红球奇偶比']
        
        # 红球大小比：优先使用专项预测
        if '红球大小比_专项' in results:
            final['红球大小比'] = results['红球大小比_专项']
            final['红球大小比_来源'] = '专项优化器'
        elif 'v4_red_size' in results:
            final['红球大小比'] = results['v4_red_size']
            final['红球大小比_来源'] = 'V4模型'
        
        # 蓝球大小比：使用V4预测
        if '蓝球大小比' in results:
            final['蓝球大小比'] = results['蓝球大小比']
        
        # 号码预测：使用V4预测
        if '红球预测' in results:
            final['红球预测'] = results['红球预测']
        if '蓝球预测' in results:
            final['蓝球预测'] = results['蓝球预测']
        
        return final
    
    def run_enhanced_prediction(self, target_period: int = None):
        """运行增强预测"""
        print("V4增强集成器")
        print("=" * 60)
        
        # 加载模型
        self.load_models()
        
        # 加载数据
        print("[INFO] 加载数据...")
        df = pd.read_csv(self.data_path, header=None)
        df.columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2', '日期']
        
        # 数据类型转换
        numeric_cols = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df = df.dropna().reset_index(drop=True)
        
        # 确定目标期号
        if target_period is None:
            target_period = int(df['期号'].iloc[-1]) + 1
        
        print(f"[INFO] 目标预测期号: {target_period}")
        
        # 执行增强预测
        results = self.predict_enhanced(df, target_period)
        
        # 显示结果
        print(f"\n[RESULTS] 期号 {target_period} 增强预测结果:")
        print("=" * 50)
        
        for key, value in results.items():
            if key.endswith('_来源'):
                continue
            if isinstance(value, (int, float)):
                print(f"{key}: {value}")
            elif isinstance(value, np.ndarray) and len(value) <= 10:
                print(f"{key}: {value}")
        
        print(f"\n[SUCCESS] 增强预测完成！")
        
        return results

if __name__ == "__main__":
    integrator = V4EnhancedIntegrator()
    results = integrator.run_enhanced_prediction()
