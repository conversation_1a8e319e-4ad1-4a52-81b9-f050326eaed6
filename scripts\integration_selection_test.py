#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选号系统集成测试
测试实际的选号系统模块
"""

import sys
import os
import time
import pandas as pd
import numpy as np

# 设置环境
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

# 设置环境变量避免相对导入问题
os.environ['PYTHONPATH'] = src_path

def create_realistic_test_data():
    """创建更真实的测试数据"""
    print("📊 创建真实测试数据...")
    
    # 生成100期历史数据
    data = []
    base_period = 25000
    
    for i in range(100):
        period = f"{base_period + i}"
        
        # 生成符合彩票规律的红球（避免过于随机）
        red_balls = []
        while len(red_balls) < 5:
            # 分区域选择，增加真实性
            if len(red_balls) < 2:
                # 前区 1-12
                ball = np.random.randint(1, 13)
            elif len(red_balls) < 4:
                # 中区 13-24
                ball = np.random.randint(13, 25)
            else:
                # 后区 25-35
                ball = np.random.randint(25, 36)
            
            if ball not in red_balls:
                red_balls.append(ball)
        
        red_balls.sort()
        
        # 生成蓝球
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        
        row = {
            '期号': period,
            '红球1': red_balls[0], '红球2': red_balls[1], '红球3': red_balls[2],
            '红球4': red_balls[3], '红球5': red_balls[4],
            '蓝球1': blue_balls[0], '蓝球2': blue_balls[1]
        }
        data.append(row)
    
    df = pd.DataFrame(data)
    print(f"✅ 创建了 {len(df)} 期真实测试数据")
    return df

def test_traditional_ml_only():
    """测试纯传统ML选号"""
    print("\n🤖 测试传统ML选号...")
    
    try:
        # 直接导入并测试TraditionalMLSelector
        from systems.traditional_ml_selector import TraditionalMLSelector
        from systems.selection_data_processor import DataProcessor
        from systems.optimized_selection_config import OptimizedSelectionConfig
        
        # 创建测试数据
        test_data = create_realistic_test_data()
        
        # 创建组件
        config = OptimizedSelectionConfig()
        processor = DataProcessor(config)
        selector = TraditionalMLSelector(config)
        
        print("   处理数据...")
        processed_data = processor.process(test_data)
        
        print("   执行传统ML选号...")
        start_time = time.time()
        result = selector.train_and_select(processed_data)
        ml_time = time.time() - start_time
        
        print(f"✅ 传统ML选号成功:")
        print(f"   红球: {result.get('red_balls', [])}")
        print(f"   蓝球: {result.get('blue_balls', [])}")
        print(f"   置信度: {result.get('confidence', 0):.3f}")
        print(f"   耗时: {ml_time:.2f}秒")
        
        # 验证结果格式
        red_balls = result.get('red_balls', [])
        blue_balls = result.get('blue_balls', [])
        confidence = result.get('confidence', 0)
        
        if len(red_balls) == 5 and len(blue_balls) == 2:
            print("✅ 选号数量正确")
        else:
            print(f"❌ 选号数量错误: 红球{len(red_balls)}个, 蓝球{len(blue_balls)}个")
            return False
            
        if isinstance(confidence, (int, float)) and 0 <= confidence <= 1:
            print("✅ 置信度格式正确")
        else:
            print(f"❌ 置信度格式错误: {type(confidence)}, 值: {confidence}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 传统ML测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_deep_learning_fallback():
    """测试深度学习后备机制"""
    print("\n🧠 测试深度学习后备机制...")
    
    try:
        from systems.deep_learning_selector import DeepLearningSelector
        from systems.selection_data_processor import DataProcessor
        from systems.optimized_selection_config import OptimizedSelectionConfig
        
        # 创建测试数据
        test_data = create_realistic_test_data()
        
        # 创建组件
        config = OptimizedSelectionConfig()
        processor = DataProcessor(config)
        selector = DeepLearningSelector(config)
        
        print("   处理数据...")
        processed_data = processor.process(test_data)
        
        print("   执行深度学习选号（预期使用后备机制）...")
        start_time = time.time()
        result = selector.train_and_select(processed_data)
        dl_time = time.time() - start_time
        
        print(f"✅ 深度学习选号完成（后备机制）:")
        print(f"   红球: {result.get('red_balls', [])}")
        print(f"   蓝球: {result.get('blue_balls', [])}")
        print(f"   置信度: {result.get('confidence', 0)}")
        print(f"   耗时: {dl_time:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 深度学习测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_processor():
    """测试数据处理器"""
    print("\n📊 测试数据处理器...")
    
    try:
        from systems.selection_data_processor import DataProcessor
        from systems.optimized_selection_config import OptimizedSelectionConfig
        
        # 创建测试数据
        test_data = create_realistic_test_data()
        
        # 创建数据处理器
        config = OptimizedSelectionConfig()
        processor = DataProcessor(config)
        
        print("   执行数据处理...")
        start_time = time.time()
        processed_data = processor.process(test_data)
        process_time = time.time() - start_time
        
        print(f"✅ 数据处理完成:")
        print(f"   训练集大小: {len(processed_data['train_data']['features'])}")
        print(f"   验证集大小: {len(processed_data['val_data']['features'])}")
        print(f"   测试集大小: {len(processed_data['test_data']['features'])}")
        print(f"   特征维度: {processed_data['train_data']['features'].shape[1] if hasattr(processed_data['train_data']['features'], 'shape') else 'N/A'}")
        print(f"   耗时: {process_time:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_optimized_config():
    """测试优化配置"""
    print("\n⚙️ 测试优化配置...")
    
    try:
        from systems.optimized_selection_config import OptimizedSelectionConfig
        
        # 创建优化配置
        config = OptimizedSelectionConfig()
        
        print(f"✅ 优化配置创建成功:")
        print(f"   智能选号: {config.enable_smart_selection}")
        print(f"   比例约束: {config.enable_ratio_constraints}")
        print(f"   杀号集成: {config.enable_kill_integration}")
        print(f"   自适应权重: {config.enable_adaptive_weights}")
        print(f"   性能监控: {config.enable_performance_monitoring}")
        
        # 验证配置参数
        if hasattr(config, 'smart_selection_threshold'):
            print(f"   智能选号阈值: {config.smart_selection_threshold}")
        
        if hasattr(config, 'ratio_constraints'):
            print(f"   比例约束配置: {getattr(config, 'ratio_constraints', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 优化配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🎯 选号系统集成测试 - Phase 4 优化验证")
    print("=" * 70)
    
    tests = [
        ("优化配置", test_optimized_config),
        ("数据处理器", test_data_processor),
        ("传统ML选号", test_traditional_ml_only),
        ("深度学习后备", test_deep_learning_fallback)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*25} {test_name} {'='*25}")
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    # 总结
    print("\n" + "=" * 70)
    print("📋 集成测试结果总结")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有集成测试通过！")
        print("选号系统Phase 4优化已成功集成并可正常运行！")
    elif passed >= total * 0.75:
        print("✅ 大部分测试通过，系统基本可用！")
        print("少数问题不影响核心功能。")
    else:
        print("⚠️ 多个测试失败，需要进一步调试。")
    
    return passed >= total * 0.75

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
