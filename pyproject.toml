[project]
name = "lottery-prediction-system"
version = "1.0.0"
description = "大乐透预测系统 - 智能彩票预测与分析系统"
authors = [
    {name = "Lottery Prediction Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
keywords = ["lottery", "prediction", "machine-learning", "data-analysis"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Games/Entertainment",
]

dependencies = [
    "pandas>=1.5.0",
    "numpy>=1.21.0",
    "scikit-learn>=1.1.0",
    "scipy>=1.9.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "python-dateutil>=2.8.0",
    "joblib>=1.1.0",
    "pyyaml>=6.0.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=3.0.0",
    "pytest-mock>=3.8.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=0.971",
]

deep-learning = [
    "tensorflow>=2.10.0",
    "torch>=1.12.0",
]

visualization = [
    "plotly>=5.10.0",
    "rich>=12.5.0",
]

web = [
    "flask>=2.0.0",
    "gunicorn>=20.1.0",
]

[project.urls]
Homepage = "https://github.com/lottery-prediction/lottery-system"
Documentation = "https://lottery-prediction.readthedocs.io/"
Repository = "https://github.com/lottery-prediction/lottery-system.git"
"Bug Tracker" = "https://github.com/lottery-prediction/lottery-system/issues"

[project.scripts]
lottery-predict = "src.systems.main:main"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/.*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
