"""
V4.0 Transformer训练与评估系统
Training and Evaluation System for V4.0 Transformer
使用真实历史数据进行训练和性能评估
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
import json
import time
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, "..", "..", "..")
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入V4.0组件
from v4_transformer_config import V4TransformerConfig, V4ConfigTemplates
from v4_transformer_predictor import V4TransformerPredictor

# 导入核心接口
try:
    from src.core.interfaces import PredictionResult
    from src.utils.data_utils import calculate_ratios
except ImportError:
    print("[WARN] 部分核心模块导入失败，使用简化版本")
    PredictionResult = None


class V4TrainingEvaluator:
    """V4.0 Transformer训练评估器"""

    def __init__(self, config: Optional[V4TransformerConfig] = None):
        self.config = config or V4TransformerConfig()
        self.project_root = Path(project_root)
        self.data_path = self.project_root / "data" / "raw" / "dlt_data.csv"
        self.results_dir = self.project_root / "logs" / "v4_training"
        self.results_dir.mkdir(parents=True, exist_ok=True)

        # 训练历史记录
        self.training_history = {}
        self.evaluation_results = {}

    def load_and_preprocess_data(self) -> pd.DataFrame:
        """加载并预处理真实历史数据"""
        print("[INFO] 加载并预处理历史数据...")

        # 读取原始数据
        df = pd.read_csv(self.data_path, header=None)
        df.columns = [
            "期号",
            "红球1",
            "红球2",
            "红球3",
            "红球4",
            "红球5",
            "蓝球1",
            "蓝球2",
            "日期",
        ]

        # 确保数值列是正确的数据类型
        numeric_cols = [
            "期号",
            "红球1",
            "红球2",
            "红球3",
            "红球4",
            "红球5",
            "蓝球1",
            "蓝球2",
        ]
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors="coerce")

        # 数据清洗
        df = df.dropna()
        df = df.sort_values("期号").reset_index(drop=True)

        # 特征工程
        df = self._add_engineered_features(df)

        print(f"[OK] 数据预处理完成，共 {len(df)} 期数据")
        return df

    def _add_engineered_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加工程特征"""
        print("[INFO] 添加工程特征...")

        # 计算比例特征
        red_balls = ["红球1", "红球2", "红球3", "红球4", "红球5"]
        blue_balls = ["蓝球1", "蓝球2"]

        # 红球奇偶比
        df["红球奇数个数"] = df[red_balls].apply(
            lambda row: sum(x % 2 == 1 for x in row), axis=1
        )
        df["红球偶数个数"] = 5 - df["红球奇数个数"]
        df["红球奇偶比"] = (
            df["红球奇数个数"].astype(str) + ":" + df["红球偶数个数"].astype(str)
        )

        # 红球大小比（大数：>17，小数：<=17）
        df["红球大数个数"] = df[red_balls].apply(
            lambda row: sum(x > 17 for x in row), axis=1
        )
        df["红球小数个数"] = 5 - df["红球大数个数"]
        df["红球大小比"] = (
            df["红球大数个数"].astype(str) + ":" + df["红球小数个数"].astype(str)
        )

        # 蓝球大小比（大数：>6，小数：<=6）
        df["蓝球大数个数"] = df[blue_balls].apply(
            lambda row: sum(x > 6 for x in row), axis=1
        )
        df["蓝球小数个数"] = 2 - df["蓝球大数个数"]
        df["蓝球大小比"] = (
            df["蓝球大数个数"].astype(str) + ":" + df["蓝球小数个数"].astype(str)
        )

        # 历史统计特征
        df["红球和值"] = df[red_balls].sum(axis=1)
        df["红球跨度"] = df[red_balls].max(axis=1) - df[red_balls].min(axis=1)
        df["蓝球和值"] = df[blue_balls].sum(axis=1)

        # 连号特征
        df["红球连号个数"] = df[red_balls].apply(self._count_consecutive, axis=1)
        df["蓝球连号个数"] = df[blue_balls].apply(self._count_consecutive, axis=1)

        print("[OK] 工程特征添加完成")
        return df

    def _count_consecutive(self, row) -> int:
        """计算连号个数"""
        sorted_nums = sorted(row)
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i + 1] - sorted_nums[i] == 1:
                consecutive_count += 1
        return consecutive_count

    def prepare_training_data(
        self, df: pd.DataFrame, train_ratio: float = 0.8
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """准备训练和测试数据"""
        print(f"[INFO] 准备训练数据，训练比例: {train_ratio:.1%}")

        # 时间序列分割（保持时间顺序）
        split_idx = int(len(df) * train_ratio)

        train_data = df.iloc[:split_idx].copy()
        test_data = df.iloc[split_idx:].copy()

        print(
            f"[OK] 训练数据: {len(train_data)} 期 ({train_data['期号'].min()} - {train_data['期号'].max()})"
        )
        print(
            f"[OK] 测试数据: {len(test_data)} 期 ({test_data['期号'].min()} - {test_data['期号'].max()})"
        )

        return train_data, test_data

    def train_v4_model(self, train_data: pd.DataFrame) -> V4TransformerPredictor:
        """训练V4.0 Transformer模型"""
        print("\n=== V4.0 Transformer模型训练 ===")

        # 创建预测器
        predictor = V4TransformerPredictor(self.config)
        print(f"[OK] 创建V4.0预测器: {predictor.name}")

        # 开始训练
        start_time = time.time()
        print(f"[INFO] 开始训练，训练数据: {len(train_data)} 期")

        try:
            # 执行训练（使用预测器的内部训练逻辑）
            training_result = predictor.train(train_data)

            training_time = time.time() - start_time

            # 记录训练历史
            self.training_history = {
                "training_time": training_time,
                "training_data_size": len(train_data),
                "config": self.config.__dict__,
                "training_result": training_result,
                "timestamp": datetime.now().isoformat(),
            }

            print(f"[OK] 模型训练完成，耗时: {training_time:.2f}秒")

            # 保存训练历史
            history_path = self.results_dir / "training_history.json"
            with open(history_path, "w", encoding="utf-8") as f:
                json.dump(
                    self.training_history, f, indent=2, ensure_ascii=False, default=str
                )

            return predictor

        except Exception as e:
            print(f"[ERROR] 模型训练失败: {e}")
            import traceback

            traceback.print_exc()
            raise

    def evaluate_model_performance(
        self, predictor: V4TransformerPredictor, test_data: pd.DataFrame
    ) -> Dict[str, Any]:
        """评估模型性能"""
        print("\n=== 模型性能评估 ===")

        evaluation_results = {
            "test_data_size": len(test_data),
            "predictions": [],
            "accuracy_metrics": {},
            "performance_metrics": {},
            "detailed_results": [],
        }

        # 执行预测评估
        correct_predictions = {
            "red_odd_even_ratio": 0,
            "red_size_ratio": 0,
            "blue_size_ratio": 0,
            "red_ball_hits": [],
            "blue_ball_hits": [],
        }

        prediction_times = []

        print(f"[INFO] 开始评估，测试数据: {len(test_data)} 期")

        for i in range(min(50, len(test_data))):  # 限制评估数量
            try:
                test_idx = len(test_data) - len(test_data) + i  # 相对于完整数据集的索引
                current_period = test_data.iloc[i]

                start_time = time.time()

                # 执行预测
                prediction_result = predictor.predict(test_data, test_idx)
                prediction_time = time.time() - start_time
                prediction_times.append(prediction_time)

                # 获取实际结果
                actual_red_balls = [current_period[f"红球{j}"] for j in range(1, 6)]
                actual_blue_balls = [current_period[f"蓝球{j}"] for j in range(1, 3)]

                # 计算实际比例
                actual_red_odd = sum(1 for x in actual_red_balls if x % 2 == 1)
                actual_red_even = 5 - actual_red_odd
                actual_red_odd_even_ratio = f"{actual_red_odd}:{actual_red_even}"

                actual_red_large = sum(1 for x in actual_red_balls if x > 17)
                actual_red_small = 5 - actual_red_large
                actual_red_size_ratio = f"{actual_red_large}:{actual_red_small}"

                actual_blue_large = sum(1 for x in actual_blue_balls if x > 6)
                actual_blue_small = 2 - actual_blue_large
                actual_blue_size_ratio = f"{actual_blue_large}:{actual_blue_small}"

                # 比较预测结果
                predicted_ratios = (
                    prediction_result.value if prediction_result.value else {}
                )

                # 记录准确性
                if (
                    predicted_ratios.get("red_odd_even_ratio")
                    == actual_red_odd_even_ratio
                ):
                    correct_predictions["red_odd_even_ratio"] += 1

                if predicted_ratios.get("red_size_ratio") == actual_red_size_ratio:
                    correct_predictions["red_size_ratio"] += 1

                if predicted_ratios.get("blue_size_ratio") == actual_blue_size_ratio:
                    correct_predictions["blue_size_ratio"] += 1

                # 记录详细结果
                detailed_result = {
                    "period": current_period["期号"],
                    "actual": {
                        "red_balls": actual_red_balls,
                        "blue_balls": actual_blue_balls,
                        "red_odd_even_ratio": actual_red_odd_even_ratio,
                        "red_size_ratio": actual_red_size_ratio,
                        "blue_size_ratio": actual_blue_size_ratio,
                    },
                    "predicted": predicted_ratios,
                    "confidence": prediction_result.confidence,
                    "prediction_time": prediction_time,
                }

                evaluation_results["detailed_results"].append(detailed_result)

                if i % 10 == 0:
                    print(f"[INFO] 已评估 {i+1}/{min(50, len(test_data))} 期")

            except Exception as e:
                print(f"[ERROR] 期号 {current_period['期号']} 评估失败: {e}")
                continue

        # 计算准确率指标
        total_predictions = len(evaluation_results["detailed_results"])
        if total_predictions > 0:
            evaluation_results["accuracy_metrics"] = {
                "red_odd_even_accuracy": correct_predictions["red_odd_even_ratio"]
                / total_predictions,
                "red_size_accuracy": correct_predictions["red_size_ratio"]
                / total_predictions,
                "blue_size_accuracy": correct_predictions["blue_size_ratio"]
                / total_predictions,
                "overall_ratio_accuracy": (
                    correct_predictions["red_odd_even_ratio"]
                    + correct_predictions["red_size_ratio"]
                    + correct_predictions["blue_size_ratio"]
                )
                / (total_predictions * 3),
            }

            evaluation_results["performance_metrics"] = {
                "avg_prediction_time": np.mean(prediction_times),
                "max_prediction_time": np.max(prediction_times),
                "min_prediction_time": np.min(prediction_times),
                "total_predictions": total_predictions,
            }

        # 输出评估结果
        print(f"\n[RESULTS] 评估完成，共评估 {total_predictions} 期")
        if total_predictions > 0:
            acc_metrics = evaluation_results["accuracy_metrics"]
            print(f"  红球奇偶比准确率: {acc_metrics['red_odd_even_accuracy']:.2%}")
            print(f"  红球大小比准确率: {acc_metrics['red_size_accuracy']:.2%}")
            print(f"  蓝球大小比准确率: {acc_metrics['blue_size_accuracy']:.2%}")
            print(f"  总体比例准确率: {acc_metrics['overall_ratio_accuracy']:.2%}")

            perf_metrics = evaluation_results["performance_metrics"]
            print(f"  平均预测时间: {perf_metrics['avg_prediction_time']:.3f}秒")

        self.evaluation_results = evaluation_results
        return evaluation_results

    def generate_performance_report(self) -> str:
        """生成性能报告"""
        print("\n=== 生成性能报告 ===")

        report_path = (
            self.results_dir
            / f"v4_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        )

        report_content = f"""# V4.0 Transformer性能评估报告

## 训练配置
- 模型维度: {self.config.d_model}
- 注意力头数: {self.config.num_heads}
- Transformer层数: {self.config.num_layers}
- 序列长度: {self.config.sequence_length}
- 训练轮数: {self.config.epochs}
- 批次大小: {self.config.batch_size}

## 训练结果
"""

        if self.training_history:
            report_content += f"""
- 训练数据量: {self.training_history['training_data_size']} 期
- 训练时间: {self.training_history['training_time']:.2f} 秒
- 训练完成时间: {self.training_history['timestamp']}
"""

        if self.evaluation_results and self.evaluation_results.get("accuracy_metrics"):
            acc_metrics = self.evaluation_results["accuracy_metrics"]
            perf_metrics = self.evaluation_results["performance_metrics"]

            report_content += f"""
## 评估结果

### 准确率指标
- 红球奇偶比准确率: {acc_metrics['red_odd_even_accuracy']:.2%}
- 红球大小比准确率: {acc_metrics['red_size_accuracy']:.2%}
- 蓝球大小比准确率: {acc_metrics['blue_size_accuracy']:.2%}
- 总体比例准确率: {acc_metrics['overall_ratio_accuracy']:.2%}

### 性能指标
- 测试数据量: {self.evaluation_results['test_data_size']} 期
- 实际评估量: {perf_metrics['total_predictions']} 期
- 平均预测时间: {perf_metrics['avg_prediction_time']:.3f} 秒
- 最大预测时间: {perf_metrics['max_prediction_time']:.3f} 秒
- 最小预测时间: {perf_metrics['min_prediction_time']:.3f} 秒

## 详细结果
"""

            # 添加前10个详细结果示例
            for i, result in enumerate(
                self.evaluation_results["detailed_results"][:10]
            ):
                report_content += f"""
### 期号 {result['period']}
- 实际红球奇偶比: {result['actual']['red_odd_even_ratio']}
- 预测红球奇偶比: {result['predicted'].get('red_odd_even_ratio', 'N/A')}
- 实际红球大小比: {result['actual']['red_size_ratio']}
- 预测红球大小比: {result['predicted'].get('red_size_ratio', 'N/A')}
- 预测置信度: {result['confidence']:.3f}
"""

        report_content += f"""
## 结论

V4.0 Transformer模型在真实历史数据上的表现：
"""

        if self.evaluation_results and self.evaluation_results.get("accuracy_metrics"):
            overall_acc = self.evaluation_results["accuracy_metrics"][
                "overall_ratio_accuracy"
            ]
            if overall_acc > 0.4:
                report_content += "- ✅ 模型表现良好，准确率超过40%\n"
            elif overall_acc > 0.3:
                report_content += "- ⚠️ 模型表现一般，准确率在30-40%之间\n"
            else:
                report_content += "- ❌ 模型表现需要改进，准确率低于30%\n"

        report_content += f"""
- 模型训练和预测流程稳定
- 可以成功集成到主系统中
- 建议进一步优化超参数以提升性能

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # 保存报告
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(report_content)

        print(f"[OK] 性能报告已保存: {report_path}")
        return str(report_path)

    def run_complete_training_evaluation(self) -> Dict[str, Any]:
        """运行完整的训练评估流程"""
        print("V4.0 Transformer 训练与评估系统")
        print("=" * 60)

        complete_results = {
            "start_time": time.time(),
            "data_loading": {},
            "training": {},
            "evaluation": {},
            "report_path": "",
            "success": False,
        }

        try:
            # 1. 数据加载和预处理
            print("\n[STEP 1] 数据加载和预处理")
            data = self.load_and_preprocess_data()
            complete_results["data_loading"] = {
                "success": True,
                "data_size": len(data),
                "features_added": True,
            }

            # 2. 准备训练测试数据
            print("\n[STEP 2] 准备训练测试数据")
            train_data, test_data = self.prepare_training_data(data)

            # 3. 模型训练
            print("\n[STEP 3] 模型训练")
            trained_predictor = self.train_v4_model(train_data)
            complete_results["training"] = {
                "success": True,
                "training_history": self.training_history,
            }

            # 4. 模型评估
            print("\n[STEP 4] 模型评估")
            evaluation_results = self.evaluate_model_performance(
                trained_predictor, test_data
            )
            complete_results["evaluation"] = evaluation_results

            # 5. 生成报告
            print("\n[STEP 5] 生成性能报告")
            report_path = self.generate_performance_report()
            complete_results["report_path"] = report_path

            complete_results["success"] = True
            complete_results["end_time"] = time.time()
            complete_results["total_duration"] = (
                complete_results["end_time"] - complete_results["start_time"]
            )

            print(f"\n[SUCCESS] 完整训练评估流程完成！")
            print(f"总耗时: {complete_results['total_duration']:.2f}秒")
            print(f"性能报告: {report_path}")

        except Exception as e:
            complete_results["error"] = str(e)
            print(f"\n[ERROR] 训练评估流程失败: {e}")
            import traceback

            traceback.print_exc()

        # 保存完整结果
        results_path = self.results_dir / "complete_training_evaluation_results.json"
        with open(results_path, "w", encoding="utf-8") as f:
            json.dump(complete_results, f, indent=2, ensure_ascii=False, default=str)

        return complete_results


def main():
    """主函数"""
    # 使用默认配置
    config = V4TransformerConfig()

    # 创建训练评估器
    evaluator = V4TrainingEvaluator(config)

    # 运行完整流程
    results = evaluator.run_complete_training_evaluation()

    return results["success"]


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
