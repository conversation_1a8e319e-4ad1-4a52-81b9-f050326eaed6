#!/usr/bin/env python3
"""
预测准确率统计模块
用于跟踪和分析历史预测的准确性
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import pandas as pd
from collections import defaultdict


class AccuracyTracker:
    """预测准确率跟踪器"""

    def __init__(self, stats_file: str = "data/results/accuracy_stats.json"):
        """
        初始化准确率跟踪器

        Args:
            stats_file: 统计数据保存文件路径
        """
        self.stats_file = stats_file
        self.stats = self._load_stats()

    def _load_stats(self) -> Dict:
        """加载历史统计数据"""
        if os.path.exists(self.stats_file):
            try:
                with open(self.stats_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ 加载统计数据失败: {e}")

        return {
            "predictions": [],
            "summary": {
                "total_predictions": 0,
                "ratio_accuracy": {
                    "red_odd_even": {"correct": 0, "total": 0},
                    "red_size": {"correct": 0, "total": 0},
                    "blue_size": {"correct": 0, "total": 0},
                },
                "number_accuracy": {
                    "red_hits": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0},
                    "blue_hits": {"0": 0, "1": 0, "2": 0},
                    "2_plus_1": 0,
                    "total_combinations": 0,
                },
                "kill_accuracy": {
                    "red_success": 0,
                    "blue_success": 0,
                    "total_periods": 0,
                },
            },
        }

    def _save_stats(self):
        """保存统计数据"""
        try:
            os.makedirs(os.path.dirname(self.stats_file), exist_ok=True)
            with open(self.stats_file, "w", encoding="utf-8") as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 保存统计数据失败: {e}")

    def add_prediction_result(
        self,
        period: int,
        predicted_ratios: Dict,
        actual_ratios: Dict,
        predicted_numbers: Tuple[List[int], List[int]],
        actual_numbers: Tuple[List[int], List[int]],
        kill_numbers: Dict,
        prediction_time: str = None,
    ):
        """
        添加预测结果

        Args:
            period: 期号
            predicted_ratios: 预测的比例 {"red_odd_even": "3:2", "red_size": "2:3", "blue_size": "1:1"}
            actual_ratios: 实际的比例
            predicted_numbers: 预测号码 ([红球], [蓝球])
            actual_numbers: 实际号码 ([红球], [蓝球])
            kill_numbers: 杀号 {"red": [杀号列表], "blue": [杀号列表]}
            prediction_time: 预测时间
        """
        if prediction_time is None:
            prediction_time = datetime.now().isoformat()

        # 计算比例准确性
        ratio_results = {}
        for ratio_type in ["red_odd_even", "red_size", "blue_size"]:
            predicted = predicted_ratios.get(ratio_type, "")
            actual = actual_ratios.get(ratio_type, "")
            ratio_results[ratio_type] = {
                "predicted": predicted,
                "actual": actual,
                "correct": predicted == actual,
            }

        # 计算号码命中情况
        pred_red, pred_blue = predicted_numbers
        actual_red, actual_blue = actual_numbers

        red_hits = len(set(pred_red) & set(actual_red))
        blue_hits = len(set(pred_blue) & set(actual_blue))
        is_2_plus_1 = red_hits >= 2 and blue_hits >= 1

        # 计算杀号成功率
        kill_red = kill_numbers.get("red", [])
        kill_blue = kill_numbers.get("blue", [])

        red_kill_success = (
            len(set(kill_red) & set(actual_red)) == 0 if kill_red else True
        )
        blue_kill_success = (
            len(set(kill_blue) & set(actual_blue)) == 0 if kill_blue else True
        )

        # 构建预测记录
        prediction_record = {
            "period": period,
            "prediction_time": prediction_time,
            "ratios": ratio_results,
            "numbers": {
                "predicted": {"red": pred_red, "blue": pred_blue},
                "actual": {"red": actual_red, "blue": actual_blue},
                "hits": {"red": red_hits, "blue": blue_hits},
                "is_2_plus_1": is_2_plus_1,
            },
            "kill_numbers": {
                "red": kill_red,
                "blue": kill_blue,
                "red_success": red_kill_success,
                "blue_success": blue_kill_success,
            },
        }

        # 添加到历史记录
        self.stats["predictions"].append(prediction_record)

        # 更新汇总统计
        self._update_summary(prediction_record)

        # 保存数据
        self._save_stats()

        return prediction_record

    def _update_summary(self, record: Dict):
        """更新汇总统计"""
        summary = self.stats["summary"]
        summary["total_predictions"] += 1

        # 更新比例准确率
        for ratio_type, result in record["ratios"].items():
            ratio_stats = summary["ratio_accuracy"][ratio_type]
            ratio_stats["total"] += 1
            if result["correct"]:
                ratio_stats["correct"] += 1

        # 更新号码准确率
        red_hits = record["numbers"]["hits"]["red"]
        blue_hits = record["numbers"]["hits"]["blue"]

        summary["number_accuracy"]["red_hits"][str(red_hits)] += 1
        summary["number_accuracy"]["blue_hits"][str(blue_hits)] += 1
        summary["number_accuracy"]["total_combinations"] += 1

        if record["numbers"]["is_2_plus_1"]:
            summary["number_accuracy"]["2_plus_1"] += 1

        # 更新杀号准确率
        kill_stats = summary["kill_accuracy"]
        kill_stats["total_periods"] += 1

        if record["kill_numbers"]["red_success"]:
            kill_stats["red_success"] += 1
        if record["kill_numbers"]["blue_success"]:
            kill_stats["blue_success"] += 1

    def get_accuracy_report(self) -> Dict:
        """获取准确率报告"""
        summary = self.stats["summary"]
        total = summary["total_predictions"]

        if total == 0:
            return {"error": "暂无预测数据"}

        # 计算比例准确率
        ratio_accuracy = {}
        for ratio_type, stats in summary["ratio_accuracy"].items():
            if stats["total"] > 0:
                accuracy = stats["correct"] / stats["total"]
                ratio_accuracy[ratio_type] = {
                    "accuracy": accuracy,
                    "correct": stats["correct"],
                    "total": stats["total"],
                    "percentage": f"{accuracy:.1%}",
                }

        # 计算号码命中率
        number_stats = summary["number_accuracy"]
        red_hit_distribution = {}
        for hits, count in number_stats["red_hits"].items():
            if number_stats["total_combinations"] > 0:
                percentage = count / number_stats["total_combinations"]
                red_hit_distribution[f"{hits}个"] = f"{percentage:.1%} ({count}次)"

        blue_hit_distribution = {}
        for hits, count in number_stats["blue_hits"].items():
            if number_stats["total_combinations"] > 0:
                percentage = count / number_stats["total_combinations"]
                blue_hit_distribution[f"{hits}个"] = f"{percentage:.1%} ({count}次)"

        # 2+1命中率
        two_plus_one_rate = 0
        if number_stats["total_combinations"] > 0:
            two_plus_one_rate = (
                number_stats["2_plus_1"] / number_stats["total_combinations"]
            )

        # 杀号成功率
        kill_stats = summary["kill_accuracy"]
        red_kill_rate = 0
        blue_kill_rate = 0
        if kill_stats["total_periods"] > 0:
            red_kill_rate = kill_stats["red_success"] / kill_stats["total_periods"]
            blue_kill_rate = kill_stats["blue_success"] / kill_stats["total_periods"]

        return {
            "总预测次数": total,
            "比例预测准确率": ratio_accuracy,
            "号码命中分布": {
                "红球命中": red_hit_distribution,
                "蓝球命中": blue_hit_distribution,
                "2+1命中率": f"{two_plus_one_rate:.1%} ({number_stats['2_plus_1']}次)",
            },
            "杀号成功率": {
                "红球杀号": f"{red_kill_rate:.1%} ({kill_stats['red_success']}/{kill_stats['total_periods']})",
                "蓝球杀号": f"{blue_kill_rate:.1%} ({kill_stats['blue_success']}/{kill_stats['total_periods']})",
            },
        }

    def get_recent_predictions(self, count: int = 10) -> List[Dict]:
        """获取最近的预测记录"""
        predictions = self.stats["predictions"]
        return predictions[-count:] if len(predictions) >= count else predictions

    def print_accuracy_report(self):
        """打印准确率报告"""
        report = self.get_accuracy_report()

        if "error" in report:
            print(f"❌ {report['error']}")
            return

        print("📊 预测准确率统计报告")
        print("=" * 50)
        print(f"📈 总预测次数: {report['总预测次数']}")
        print()

        print("🎯 比例预测准确率:")
        ratio_names = {
            "red_odd_even": "红球奇偶比",
            "red_size": "红球大小比",
            "blue_size": "蓝球大小比",
        }

        for ratio_type, stats in report["比例预测准确率"].items():
            name = ratio_names.get(ratio_type, ratio_type)
            print(
                f"   {name}: {stats['percentage']} ({stats['correct']}/{stats['total']})"
            )
        print()

        print("🎲 号码命中分布:")
        print("   红球命中:")
        for hits, percentage in report["号码命中分布"]["红球命中"].items():
            print(f"     {hits}: {percentage}")

        print("   蓝球命中:")
        for hits, percentage in report["号码命中分布"]["蓝球命中"].items():
            print(f"     {hits}: {percentage}")

        print(f"   2+1命中率: {report['号码命中分布']['2+1命中率']}")
        print()

        print("🔪 杀号成功率:")
        print(f"   红球杀号: {report['杀号成功率']['红球杀号']}")
        print(f"   蓝球杀号: {report['杀号成功率']['蓝球杀号']}")
