"""
统一回测框架核心实现
解决期号作为循环条件的问题，提供标准化的回测接口
"""

import pandas as pd
import time
from datetime import datetime
from typing import List, Dict, Any, Tuple
from .interfaces import PredictorInterface, EvaluatorInterface
from .data_models import (
    BacktestConfig,
    BacktestResult,
    PredictionResult,
    EvaluationResult,
    PeriodResult,
    Statistics,
)


class BacktestFramework:
    """
    统一回测框架

    核心设计原则：
    1. 期号只作为标志，不作为循环条件
    2. 基于数据索引进行循环
    3. 提供标准化的接口
    4. 支持不同的预测器和评估器
    """

    def __init__(self, data: pd.DataFrame):
        """
        初始化回测框架

        Args:
            data: 历史数据，必须包含'期号'列
        """
        self.data = data
        self.evaluator = DefaultEvaluator()
        self._validate_data()

    def _validate_data(self):
        """验证数据格式"""
        if self.data is None or self.data.empty:
            raise ValueError("数据不能为空")

        if "期号" not in self.data.columns:
            raise ValueError("数据必须包含'期号'列")

        print(f"✅ 数据验证通过：共 {len(self.data)} 期数据")

    def run_backtest(
        self, predictor: PredictorInterface, config: BacktestConfig
    ) -> BacktestResult:
        """
        运行回测

        Args:
            predictor: 预测器实例
            config: 回测配置

        Returns:
            BacktestResult: 完整的回测结果
        """
        if not config.validate():
            raise ValueError("回测配置无效")

        print(f"🧪 开始回测 - {predictor.get_predictor_name()}")
        print(
            f"📊 配置：{config.num_periods}期回测，最少{config.min_train_periods}期训练"
        )
        print("=" * 60)

        start_time = datetime.now()

        # 修正回测逻辑：数据现在按期号从旧到新排列
        # 实现正确的回测逻辑：用历史数据预测下一期，与实际结果对比
        total_data_len = len(self.data)

        # 确保有足够的数据进行回测
        # 需要至少 config.num_periods + 1 期数据（最后一期用于验证）
        if total_data_len < config.num_periods + 1:
            raise ValueError(
                f"数据不足：需要至少{config.num_periods + 1}期数据，实际{total_data_len}期"
            )

        # 回测逻辑说明：
        # 数据按期号从旧到新排列：索引0是最旧期号，索引越大期号越新
        # 回测目标：验证能否用历史数据正确预测下一期
        #
        # 例如回测最近10期：
        # - 回测期号25059：使用索引0到1489的数据训练，预测25060，与实际25060对比
        # - 回测期号25060：使用索引0到1490的数据训练，预测25061，与实际25061对比
        # - ...
        # - 回测期号25067：使用索引0到1497的数据训练，预测25068，与实际25068对比

        # 计算回测范围
        # 最后一期用于验证，所以回测从倒数第(num_periods+1)期开始
        start_index = total_data_len - config.num_periods - 1  # 回测起始索引
        end_index = total_data_len - 2  # 回测结束索引（倒数第二期）

        # 确保索引有效
        if start_index < config.min_train_periods:
            start_index = config.min_train_periods
        if end_index >= total_data_len - 1:
            end_index = total_data_len - 2

        start_period = self.data.iloc[start_index]["期号"]
        end_period = self.data.iloc[end_index]["期号"]

        print(f"📈 正确的回测逻辑：用历史预测下一期")
        print(
            f"   数据排列：从旧到新（索引0={self.data.iloc[0]['期号']}，索引{total_data_len-1}={self.data.iloc[total_data_len-1]['期号']}）"
        )
        print(
            f"   回测范围：索引{start_index}到{end_index}（期号{start_period}到{end_period}）"
        )
        print(f"   回测期数：{end_index - start_index + 1}期")

        period_results = []

        # 核心循环：按时间顺序回测
        for current_index in range(start_index, end_index + 1):
            # 当前期号（用于训练的最新数据）
            current_period = self.data.iloc[current_index]["期号"]
            # 下一期索引（预测目标）
            next_index = current_index + 1
            next_period = self.data.iloc[next_index]["期号"]

            # 训练数据：从索引0到current_index（包含当前期及之前的所有历史数据）
            train_start_index = 0
            train_end_index = current_index + 1  # 不包含下一期
            train_periods = train_end_index - train_start_index

            print(f"回测期号 {current_period}：")
            print(
                f"  📊 使用 {train_periods} 期历史数据训练（索引 {train_start_index} 到 {current_index}）"
            )
            print(f"  🎯 预测目标：期号 {next_period}（索引 {next_index}）")
            print(
                f"  🔍 进度：{current_index - start_index + 1}/{end_index - start_index + 1}"
            )

            try:
                # 使用历史数据预测下一期
                # 注意：预测器需要知道要预测的是next_index，但训练数据只到current_index
                prediction = predictor.predict_for_period(current_index, self.data)

                # 获取下一期的实际开奖数据（用于验证预测结果）
                actual_data = self._extract_actual_data(next_index)

                # 评估预测结果
                evaluation = self.evaluator.evaluate(prediction, actual_data)

                # 记录结果
                period_result = PeriodResult(
                    prediction=prediction, evaluation=evaluation, success=True
                )
                period_results.append(period_result)

                print(f"  ✅ 预测完成")

            except Exception as e:
                print(f"❌ 回测期号 {current_period} 处理失败: {e}")
                # 记录失败的结果
                period_result = PeriodResult(
                    prediction=PredictionResult(
                        period_number=str(current_period),
                        data_index=current_index,
                        red_odd_even_predictions=[],
                        red_size_predictions=[],
                        blue_size_predictions=[],
                        generated_numbers=([], []),
                        kill_numbers={},
                    ),
                    evaluation=EvaluationResult(
                        period_number=str(next_period),
                        data_index=next_index,
                        actual_red=[],
                        actual_blue=[],
                        actual_red_odd_even="",
                        actual_red_size="",
                        actual_blue_size="",
                    ),
                    success=False,
                    error_message=str(e),
                )
                period_results.append(period_result)
                continue

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        print(f"✅ 回测完成，共处理 {len(period_results)} 期，耗时 {duration:.2f} 秒")

        # 计算统计信息
        statistics = self._calculate_statistics(period_results, config)

        # 构建回测结果
        result = BacktestResult(
            config=config,
            period_results=period_results,
            statistics=statistics,
            predictor_name=predictor.get_predictor_name(),
            start_time=start_time,
            end_time=end_time,
            total_duration=duration,
            data_source="lottery_data",
            total_data_periods=len(self.data),
            backtest_data_range=f"索引{start_index}-{end_index-1}",
        )

        return result

    def _extract_actual_data(self, data_index: int) -> Dict[str, Any]:
        """提取实际开奖数据"""
        row = self.data.iloc[data_index]

        # 解析红球和蓝球
        red_balls, blue_balls = self._parse_numbers(row)

        # 计算比例
        red_odd_even = self._calculate_odd_even_ratio(red_balls)
        red_size = self._calculate_size_ratio(red_balls, 18)  # 红球分界线18
        blue_size = self._calculate_size_ratio(blue_balls, 6)  # 蓝球分界线6

        return {
            "period_number": str(row["期号"]),
            "red_balls": red_balls,
            "blue_balls": blue_balls,
            "red_odd_even": red_odd_even,
            "red_size": red_size,
            "blue_size": blue_size,
        }

    def _parse_numbers(self, row) -> Tuple[List[int], List[int]]:
        """解析号码"""
        try:
            # 检查是否有分列的红球数据（红球1、红球2、红球3、红球4、红球5）
            red_ball_columns = ["红球1", "红球2", "红球3", "红球4", "红球5"]
            blue_ball_columns = ["蓝球1", "蓝球2"]

            if all(col in row for col in red_ball_columns):
                red_balls = [int(row[col]) for col in red_ball_columns]
                blue_balls = [int(row[col]) for col in blue_ball_columns if col in row]
                return red_balls, blue_balls

            # 检查是否有合并的红球蓝球列
            elif "红球" in row and "蓝球" in row:
                red_str = str(row["红球"]).strip()
                blue_str = str(row["蓝球"]).strip()
                red_balls = [int(x) for x in red_str.split() if x.isdigit()]
                blue_balls = [int(x) for x in blue_str.split() if x.isdigit()]
                return red_balls, blue_balls

            # 假设格式为 "01 02 03 04 05 + 01 02"
            elif "开奖号码" in row:
                numbers_str = str(row["开奖号码"])
                if "+" in numbers_str:
                    red_str, blue_str = numbers_str.split("+")
                    red_balls = [int(x) for x in red_str.split() if x.isdigit()]
                    blue_balls = [int(x) for x in blue_str.split() if x.isdigit()]
                    return red_balls, blue_balls

            # 如果都没找到，返回空列表
            return [], []

        except Exception as e:
            print(f"❌ 解析号码失败: {e}")
            return [], []

    def _calculate_odd_even_ratio(self, numbers: List[int]) -> str:
        """计算奇偶比"""
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        even_count = len(numbers) - odd_count
        return f"{odd_count}:{even_count}"

    def _calculate_size_ratio(self, numbers: List[int], boundary: int) -> str:
        """计算大小比"""
        small_count = sum(1 for n in numbers if n <= boundary)
        big_count = len(numbers) - small_count
        return f"{small_count}:{big_count}"

    def _calculate_statistics(
        self, period_results: List[PeriodResult], config: BacktestConfig
    ) -> Statistics:
        """计算统计信息"""
        if not period_results:
            return Statistics()

        successful_results = [r for r in period_results if r.success]
        total_periods = len(successful_results)

        if total_periods == 0:
            return Statistics()

        # 计算各项命中率
        hit_counts = {}
        hit_rates = {}

        for metric in config.metrics:
            if metric in ["red_odd_even_hit", "red_size_hit", "blue_size_hit"]:
                count = sum(
                    1
                    for r in successful_results
                    if r.evaluation.hits.get(metric, False)
                )
                hit_counts[metric] = count
                hit_rates[metric] = count / total_periods

        # 计算2+1命中率
        hit_2_plus_1_count = sum(
            1 for r in successful_results if r.evaluation.hit_2_plus_1
        )
        hit_2_plus_1_rate = hit_2_plus_1_count / total_periods

        # 计算杀号成功率
        kill_success_counts = {}
        kill_success_rates = {}

        for kill_type in ["red_kill_success", "blue_kill_success"]:
            count = sum(
                1
                for r in successful_results
                if r.evaluation.kill_success.get(kill_type, False)
            )
            kill_success_counts[kill_type] = count
            kill_success_rates[kill_type] = count / total_periods

        # 计算平均命中数
        avg_red_hits = (
            sum(r.evaluation.red_hits for r in successful_results) / total_periods
        )
        avg_blue_hits = (
            sum(r.evaluation.blue_hits for r in successful_results) / total_periods
        )
        avg_total_hits = (
            sum(r.evaluation.total_hits for r in successful_results) / total_periods
        )

        return Statistics(
            total_periods=total_periods,
            successful_periods=len(successful_results),
            hit_rates=hit_rates,
            hit_counts=hit_counts,
            kill_success_rates=kill_success_rates,
            kill_success_counts=kill_success_counts,
            hit_2_plus_1_rate=hit_2_plus_1_rate,
            hit_2_plus_1_count=hit_2_plus_1_count,
            avg_red_hits=avg_red_hits,
            avg_blue_hits=avg_blue_hits,
            avg_total_hits=avg_total_hits,
        )


class DefaultEvaluator(EvaluatorInterface):
    """默认评估器"""

    def evaluate(
        self, prediction: PredictionResult, actual_data: dict
    ) -> EvaluationResult:
        """评估预测结果"""
        # 提取实际数据
        actual_red = actual_data["red_balls"]
        actual_blue = actual_data["blue_balls"]
        actual_red_odd_even = actual_data["red_odd_even"]
        actual_red_size = actual_data["red_size"]
        actual_blue_size = actual_data["blue_size"]

        # 计算命中情况
        hits = {}

        # 比例预测命中
        if prediction.red_odd_even_predictions:
            pred_red_odd_even = prediction.red_odd_even_predictions[0][0]
            hits["red_odd_even_hit"] = pred_red_odd_even == actual_red_odd_even

        if prediction.red_size_predictions:
            pred_red_size = prediction.red_size_predictions[0][0]
            hits["red_size_hit"] = pred_red_size == actual_red_size

        if prediction.blue_size_predictions:
            pred_blue_size = prediction.blue_size_predictions[0][0]
            hits["blue_size_hit"] = pred_blue_size == actual_blue_size

        # 号码命中
        pred_red, pred_blue = prediction.generated_numbers
        red_hits = len(set(pred_red) & set(actual_red))
        blue_hits = len(set(pred_blue) & set(actual_blue))
        total_hits = red_hits + blue_hits

        # 2+1命中
        hit_2_plus_1 = (
            hits.get("red_odd_even_hit", False)
            and hits.get("red_size_hit", False)
            and hits.get("blue_size_hit", False)
        )

        # 杀号成功（简化版本）
        kill_success = {}
        if "red_universal" in prediction.kill_numbers:
            red_kills = prediction.kill_numbers["red_universal"]
            kill_success["red_kill_success"] = not any(
                k in actual_red for k in red_kills
            )

        if "blue_universal" in prediction.kill_numbers:
            blue_kills = prediction.kill_numbers["blue_universal"]
            kill_success["blue_kill_success"] = not any(
                k in actual_blue for k in blue_kills
            )

        return EvaluationResult(
            period_number=prediction.period_number,
            data_index=prediction.data_index,
            actual_red=actual_red,
            actual_blue=actual_blue,
            actual_red_odd_even=actual_red_odd_even,
            actual_red_size=actual_red_size,
            actual_blue_size=actual_blue_size,
            hits=hits,
            kill_success=kill_success,
            red_hits=red_hits,
            blue_hits=blue_hits,
            total_hits=total_hits,
            hit_2_plus_1=hit_2_plus_1,
        )

    def get_supported_metrics(self) -> list:
        """获取支持的评估指标"""
        return [
            "red_odd_even_hit",
            "red_size_hit",
            "blue_size_hit",
            "hit_2_plus_1",
            "red_kill_success",
            "blue_kill_success",
            "red_hits",
            "blue_hits",
            "total_hits",
        ]
