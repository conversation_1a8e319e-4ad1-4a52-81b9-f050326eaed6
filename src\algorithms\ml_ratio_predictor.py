"""
机器学习比值预测器
使用决策树等机器学习算法进行彩票比值预测
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier
from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import accuracy_score, classification_report
import joblib
import os
import warnings

warnings.filterwarnings("ignore")

from .feature_engineering import LotteryFeatureExtractor


class MLRatioPredictor:
    """机器学习比值预测器"""

    def __init__(self, model_type: str = "random_forest", lookback_periods: int = 20):
        """
        初始化ML比值预测器

        Args:
            model_type: 模型类型 ('decision_tree', 'random_forest', 'extra_trees')
            lookback_periods: 特征提取的回看期数
        """
        self.model_type = model_type
        self.lookback_periods = lookback_periods
        self.feature_extractor = LotteryFeatureExtractor(lookback_periods)

        # 初始化模型
        self.models = {
            "red_odd_even": self._create_model(),
            "red_size": self._create_model(),
            "blue_size": self._create_model(),
        }

        self.is_trained = False
        self.model_performance = {}

    def _create_model(self):
        """创建机器学习模型"""
        if self.model_type == "decision_tree":
            return DecisionTreeClassifier(
                max_depth=10, min_samples_split=5, min_samples_leaf=3, random_state=42
            )
        elif self.model_type == "random_forest":
            return RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=3,
                random_state=42,
                n_jobs=-1,
            )
        elif self.model_type == "extra_trees":
            return ExtraTreesClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=3,
                random_state=42,
                n_jobs=-1,
            )
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")

    def train(self, data: pd.DataFrame, min_history: int = 30) -> Dict[str, float]:
        """
        训练模型

        Args:
            data: 训练数据
            min_history: 最小历史期数

        Returns:
            训练性能指标
        """
        print(f"🔄 开始训练ML比值预测器 (模型类型: {self.model_type})")

        # 准备训练数据
        features_df, targets = self.feature_extractor.prepare_training_data(
            data, min_history
        )

        print(
            f"📊 训练数据规模: {len(features_df)} 样本, {len(features_df.columns)} 特征"
        )

        performance = {}

        # 训练每个目标的模型
        for target_name, target_values in targets.items():
            print(f"🎯 训练 {target_name} 预测模型...")

            # 时间序列交叉验证
            tscv = TimeSeriesSplit(n_splits=5)
            cv_scores = cross_val_score(
                self.models[target_name],
                features_df,
                target_values,
                cv=tscv,
                scoring="accuracy",
            )

            # 训练最终模型
            self.models[target_name].fit(features_df, target_values)

            # 记录性能
            performance[target_name] = {
                "cv_mean": cv_scores.mean(),
                "cv_std": cv_scores.std(),
                "train_accuracy": accuracy_score(
                    target_values, self.models[target_name].predict(features_df)
                ),
            }

            print(
                f"✅ {target_name} - CV准确率: {cv_scores.mean():.3f} (±{cv_scores.std():.3f})"
            )

        self.is_trained = True
        self.model_performance = performance

        return performance

    def predict_ratios(self, data: pd.DataFrame, target_period: int) -> Dict[str, Any]:
        """
        预测比值

        Args:
            data: 历史数据 (1到target_period-1期)
            target_period: 要预测的期数索引

        Returns:
            预测结果字典
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，请先调用train()方法")

        # 提取特征
        features = self.feature_extractor.extract_features(data, target_period)
        features_df = pd.DataFrame([features])

        # 处理分类特征
        categorical_features = [
            col for col in features_df.columns if "last_" in col or "most_common" in col
        ]

        for col in categorical_features:
            if col in self.feature_extractor.label_encoders:
                # 处理缺失值
                features_df[col] = features_df[col].fillna("unknown")
                try:
                    features_df[col] = self.feature_extractor.label_encoders[
                        col
                    ].transform(features_df[col].astype(str))
                except ValueError:
                    # 处理未见过的类别
                    features_df[col] = 0

        # 处理数值特征
        numerical_features = [
            col for col in features_df.columns if col not in categorical_features
        ]
        features_df[numerical_features] = features_df[numerical_features].fillna(0)

        # 标准化数值特征
        if numerical_features and hasattr(self.feature_extractor.scaler, "mean_"):
            features_df[numerical_features] = self.feature_extractor.scaler.transform(
                features_df[numerical_features]
            )

        predictions = {}
        confidences = {}

        # 预测每个比值
        for target_name, model in self.models.items():
            # 获取预测
            pred_encoded = model.predict(features_df)[0]
            pred_proba = model.predict_proba(features_df)[0]

            # 解码预测结果
            if target_name in self.feature_extractor.label_encoders:
                pred_ratio = self.feature_extractor.label_encoders[
                    target_name
                ].inverse_transform([pred_encoded])[0]
            else:
                pred_ratio = str(pred_encoded)

            predictions[target_name] = pred_ratio
            confidences[target_name] = max(pred_proba)

        return {
            "predictions": predictions,
            "confidences": confidences,
            "features_used": len(features_df.columns),
        }

    def backtest(self, data: pd.DataFrame, test_periods: int = 10) -> Dict[str, Any]:
        """
        回测模型性能

        Args:
            data: 完整数据
            test_periods: 测试期数

        Returns:
            回测结果
        """
        if len(data) < test_periods + 30:
            raise ValueError("数据不足以进行回测")

        # 分割数据
        train_data = data.iloc[:-test_periods]
        test_data = data.iloc[-test_periods:]

        # 训练模型
        self.train(train_data)

        results = {
            "predictions": [],
            "actuals": [],
            "accuracies": {"red_odd_even": [], "red_size": [], "blue_size": []},
            "confidences": {"red_odd_even": [], "red_size": [], "blue_size": []},
        }

        # 逐期预测
        for i in range(test_periods):
            # 使用到当前期的所有历史数据进行预测
            current_train_data = data.iloc[: len(train_data) + i]
            target_index = len(current_train_data)

            # 预测下一期
            pred_result = self.predict_ratios(current_train_data, target_index)

            # 获取实际值
            actual_row = test_data.iloc[i]
            red_nums = [int(actual_row[f"红球{j}"]) for j in range(1, 6)]
            blue_nums = [int(actual_row[f"蓝球{j}"]) for j in range(1, 3)]

            # 计算实际比值
            red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
            red_even_count = len(red_nums) - red_odd_count
            actual_red_odd_even = f"{red_odd_count}:{red_even_count}"

            red_big_count = sum(1 for x in red_nums if x > 17)
            red_small_count = len(red_nums) - red_big_count
            actual_red_size = f"{red_big_count}:{red_small_count}"

            blue_big_count = sum(1 for x in blue_nums if x > 6)
            blue_small_count = len(blue_nums) - blue_big_count
            actual_blue_size = f"{blue_big_count}:{blue_small_count}"

            actuals = {
                "red_odd_even": actual_red_odd_even,
                "red_size": actual_red_size,
                "blue_size": actual_blue_size,
            }

            # 记录结果
            results["predictions"].append(pred_result["predictions"])
            results["actuals"].append(actuals)

            # 计算准确率
            for target_name in ["red_odd_even", "red_size", "blue_size"]:
                is_correct = (
                    pred_result["predictions"][target_name] == actuals[target_name]
                )
                results["accuracies"][target_name].append(is_correct)
                results["confidences"][target_name].append(
                    pred_result["confidences"][target_name]
                )

        # 计算总体准确率
        overall_accuracies = {}
        for target_name in ["red_odd_even", "red_size", "blue_size"]:
            overall_accuracies[target_name] = sum(
                results["accuracies"][target_name]
            ) / len(results["accuracies"][target_name])

        results["overall_accuracies"] = overall_accuracies
        results["average_accuracy"] = sum(overall_accuracies.values()) / len(
            overall_accuracies
        )

        return results

    def save_models(self, save_dir: str):
        """保存训练好的模型"""
        os.makedirs(save_dir, exist_ok=True)

        for target_name, model in self.models.items():
            model_path = os.path.join(save_dir, f"{target_name}_model.pkl")
            joblib.dump(model, model_path)

        # 保存特征提取器
        extractor_path = os.path.join(save_dir, "feature_extractor.pkl")
        joblib.dump(self.feature_extractor, extractor_path)

        print(f"✅ 模型已保存到: {save_dir}")

    def load_models(self, save_dir: str):
        """加载训练好的模型"""
        for target_name in self.models.keys():
            model_path = os.path.join(save_dir, f"{target_name}_model.pkl")
            if os.path.exists(model_path):
                self.models[target_name] = joblib.load(model_path)

        # 加载特征提取器
        extractor_path = os.path.join(save_dir, "feature_extractor.pkl")
        if os.path.exists(extractor_path):
            self.feature_extractor = joblib.load(extractor_path)

        self.is_trained = True
        print(f"✅ 模型已从 {save_dir} 加载")
