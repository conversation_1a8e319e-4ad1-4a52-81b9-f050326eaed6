"""
Transformer预测器

使用Transformer架构的自注意力机制来捕捉彩票数据中的复杂模式，
特别适合处理长序列依赖关系。
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import logging
from dataclasses import dataclass

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, models, optimizers

    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    from sklearn.ensemble import GradientBoostingRegressor
    from sklearn.preprocessing import StandardScaler

from ...core.interfaces import (
    IStandardPredictor,
    PredictionResult,
    PredictionType,
    BallType,
)
from ...core.base import StandardBasePredictor


@dataclass
class TransformerConfig:
    """Transformer配置参数"""

    # 模型架构
    d_model: int = 128  # 模型维度
    num_heads: int = 8  # 注意力头数
    num_layers: int = 4  # Transformer层数
    dff: int = 512  # 前馈网络维度
    dropout_rate: float = 0.1

    # 位置编码
    max_position_encoding: int = 1000

    # 训练参数
    learning_rate: float = 0.0001
    batch_size: int = 32
    epochs: int = 100
    validation_split: float = 0.2
    early_stopping_patience: int = 15

    # 数据参数
    sequence_length: int = 30  # 输入序列长度
    prediction_horizon: int = 1

    # 正则化
    label_smoothing: float = 0.1
    weight_decay: float = 0.0001

    # 优化器
    beta_1: float = 0.9
    beta_2: float = 0.98
    epsilon: float = 1e-9


class MultiHeadAttention(layers.Layer):
    """多头自注意力层"""

    def __init__(self, d_model: int, num_heads: int):
        super(MultiHeadAttention, self).__init__()
        self.num_heads = num_heads
        self.d_model = d_model

        assert d_model % self.num_heads == 0

        self.depth = d_model // self.num_heads

        self.wq = layers.Dense(d_model)
        self.wk = layers.Dense(d_model)
        self.wv = layers.Dense(d_model)

        self.dense = layers.Dense(d_model)

    def split_heads(self, x, batch_size):
        """分割最后一个维度到 (num_heads, depth)"""
        x = tf.reshape(x, (batch_size, -1, self.num_heads, self.depth))
        return tf.transpose(x, perm=[0, 2, 1, 3])

    def call(self, v, k, q, mask=None):
        batch_size = tf.shape(q)[0]

        q = self.wq(q)
        k = self.wk(k)
        v = self.wv(v)

        q = self.split_heads(q, batch_size)
        k = self.split_heads(k, batch_size)
        v = self.split_heads(v, batch_size)

        # 计算注意力
        scaled_attention, attention_weights = self.scaled_dot_product_attention(
            q, k, v, mask
        )

        scaled_attention = tf.transpose(scaled_attention, perm=[0, 2, 1, 3])

        concat_attention = tf.reshape(scaled_attention, (batch_size, -1, self.d_model))

        output = self.dense(concat_attention)

        return output, attention_weights

    def scaled_dot_product_attention(self, q, k, v, mask):
        """计算注意力权重"""
        matmul_qk = tf.matmul(q, k, transpose_b=True)

        # 缩放 matmul_qk
        dk = tf.cast(tf.shape(k)[-1], tf.float32)
        scaled_attention_logits = matmul_qk / tf.math.sqrt(dk)

        # 将 mask 加入到缩放的张量上
        if mask is not None:
            scaled_attention_logits += mask * -1e9

        # softmax 在最后一个轴上归一化
        attention_weights = tf.nn.softmax(scaled_attention_logits, axis=-1)

        output = tf.matmul(attention_weights, v)

        return output, attention_weights


class TransformerBlock(layers.Layer):
    """Transformer块"""

    def __init__(
        self, d_model: int, num_heads: int, dff: int, dropout_rate: float = 0.1
    ):
        super(TransformerBlock, self).__init__()

        self.att = MultiHeadAttention(d_model, num_heads)
        self.ffn = keras.Sequential(
            [layers.Dense(dff, activation="relu"), layers.Dense(d_model)]
        )

        self.layernorm1 = layers.LayerNormalization(epsilon=1e-6)
        self.layernorm2 = layers.LayerNormalization(epsilon=1e-6)

        self.dropout1 = layers.Dropout(dropout_rate)
        self.dropout2 = layers.Dropout(dropout_rate)

    def call(self, x, training, mask=None):
        attn_output, _ = self.att(x, x, x, mask)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(x + attn_output)

        ffn_output = self.ffn(out1)
        ffn_output = self.dropout2(ffn_output, training=training)
        out2 = self.layernorm2(out1 + ffn_output)

        return out2


class PositionalEncoding(layers.Layer):
    """位置编码"""

    def __init__(self, position: int, d_model: int):
        super(PositionalEncoding, self).__init__()
        self.pos_encoding = self.positional_encoding(position, d_model)

    def get_angles(self, pos, i, d_model):
        angle_rates = 1 / np.power(10000, (2 * (i // 2)) / np.float32(d_model))
        return pos * angle_rates

    def positional_encoding(self, position, d_model):
        angle_rads = self.get_angles(
            np.arange(position)[:, np.newaxis],
            np.arange(d_model)[np.newaxis, :],
            d_model,
        )

        # 将 sin 应用于数组中的偶数索引（indices）；2i
        angle_rads[:, 0::2] = np.sin(angle_rads[:, 0::2])

        # 将 cos 应用于数组中的奇数索引；2i+1
        angle_rads[:, 1::2] = np.cos(angle_rads[:, 1::2])

        pos_encoding = angle_rads[np.newaxis, ...]

        return tf.cast(pos_encoding, dtype=tf.float32)

    def call(self, x):
        return x + self.pos_encoding[:, : tf.shape(x)[1], :]


class TransformerPredictor(StandardBasePredictor):
    """Transformer预测器"""

    def __init__(self, config: Optional[TransformerConfig] = None):
        super().__init__("Transformer预测器", PredictionType.DEEP_LEARNING)
        self.config = config or TransformerConfig()
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.is_trained_flag = False

        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("TensorFlow未安装，将使用传统机器学习方法")
            self.fallback_model = GradientBoostingRegressor(
                n_estimators=100, random_state=42
            )

    def _prepare_sequences(
        self, data: pd.DataFrame, target_col: str = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """准备Transformer输入序列"""
        from sklearn.preprocessing import StandardScaler

        # 选择特征列
        if self.feature_columns is None:
            numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
            if target_col and target_col in numeric_cols:
                numeric_cols.remove(target_col)
            self.feature_columns = numeric_cols

        # 提取特征数据
        feature_data = data[self.feature_columns].values

        # 数据标准化
        if self.scaler is None:
            self.scaler = StandardScaler()
            feature_data = self.scaler.fit_transform(feature_data)
        else:
            feature_data = self.scaler.transform(feature_data)

        # 创建序列
        X, y = [], []
        seq_len = self.config.sequence_length

        for i in range(seq_len, len(feature_data)):
            X.append(feature_data[i - seq_len : i])
            if target_col:
                y.append(data[target_col].iloc[i])
            else:
                y.append(feature_data[i])

        return np.array(X), np.array(y)

    def _build_model(
        self, input_shape: Tuple[int, int], output_dim: int = 1
    ) -> keras.Model:
        """构建Transformer模型"""
        if not TENSORFLOW_AVAILABLE:
            return None

        # 输入层
        inputs = layers.Input(shape=input_shape)

        # 投影到模型维度
        x = layers.Dense(self.config.d_model)(inputs)

        # 位置编码
        pos_encoding = PositionalEncoding(
            self.config.max_position_encoding, self.config.d_model
        )
        x = pos_encoding(x)

        # Transformer块
        for _ in range(self.config.num_layers):
            transformer_block = TransformerBlock(
                self.config.d_model,
                self.config.num_heads,
                self.config.dff,
                self.config.dropout_rate,
            )
            x = transformer_block(x, training=True)

        # 全局平均池化
        x = layers.GlobalAveragePooling1D()(x)

        # 输出层
        x = layers.Dropout(self.config.dropout_rate)(x)

        if output_dim == 1:
            outputs = layers.Dense(1, activation="linear")(x)
        else:
            outputs = layers.Dense(output_dim, activation="softmax")(x)

        model = keras.Model(inputs=inputs, outputs=outputs)

        # 使用固定学习率避免调度器问题
        optimizer = optimizers.Adam(
            learning_rate=self.config.learning_rate,
            beta_1=self.config.beta_1,
            beta_2=self.config.beta_2,
            epsilon=self.config.epsilon,
        )

        loss = "mse" if output_dim == 1 else "categorical_crossentropy"
        metrics = ["mae"] if output_dim == 1 else ["accuracy"]

        model.compile(optimizer=optimizer, loss=loss, metrics=metrics)

        return model

    def train(self, data: pd.DataFrame, target_col: str = None) -> Dict[str, Any]:
        """训练Transformer模型"""
        try:
            self.logger.info("开始训练Transformer模型...")

            # 准备数据
            X, y = self._prepare_sequences(data, target_col)

            if len(X) == 0:
                raise ValueError("训练数据不足，无法创建序列")

            self.logger.info(f"训练数据形状: X={X.shape}, y={y.shape}")

            if not TENSORFLOW_AVAILABLE:
                # 使用传统机器学习方法
                X_flat = X.reshape(X.shape[0], -1)
                self.fallback_model.fit(X_flat, y)
                self.is_trained_flag = True
                return {"method": "fallback", "samples": len(X)}

            # 构建模型
            output_dim = 1 if len(y.shape) == 1 else y.shape[1]
            self.model = self._build_model(X.shape[1:], output_dim)

            # 设置回调
            callbacks_list = [
                keras.callbacks.EarlyStopping(
                    monitor="val_loss",
                    patience=self.config.early_stopping_patience,
                    restore_best_weights=True,
                ),
                keras.callbacks.ReduceLROnPlateau(
                    monitor="val_loss", factor=0.5, patience=8, min_lr=1e-7
                ),
            ]

            # 训练模型
            history = self.model.fit(
                X,
                y,
                batch_size=self.config.batch_size,
                epochs=self.config.epochs,
                validation_split=self.config.validation_split,
                callbacks=callbacks_list,
                verbose=0,
            )

            self.is_trained_flag = True

            # 返回训练结果
            final_loss = history.history["loss"][-1]
            final_val_loss = (
                history.history["val_loss"][-1]
                if "val_loss" in history.history
                else None
            )

            self.logger.info(f"Transformer训练完成 - 损失: {final_loss:.4f}")

            return {
                "method": "transformer",
                "samples": len(X),
                "epochs_trained": len(history.history["loss"]),
                "final_loss": final_loss,
                "final_val_loss": final_val_loss,
                "model_params": self.model.count_params() if self.model else 0,
            }

        except Exception as e:
            self.logger.error(f"Transformer训练失败: {e}")
            return {"error": str(e)}

    def predict(
        self, data: pd.DataFrame, target_index: int, **kwargs
    ) -> PredictionResult:
        """执行预测"""
        if not self.is_trained_flag:
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": "模型未训练", "predictions": {}},
            )

        try:
            # 准备预测数据
            seq_len = self.config.sequence_length
            if target_index < seq_len:
                return PredictionResult(
                    prediction_type=PredictionType.DEEP_LEARNING,
                    ball_type=BallType.RED,
                    value=None,
                    confidence=0.0,
                    metadata={
                        "success": False,
                        "error": f"目标索引{target_index}小于序列长度{seq_len}",
                        "predictions": {},
                    },
                )

            # 提取最近的序列
            recent_data = data.iloc[target_index - seq_len : target_index]
            feature_data = recent_data[self.feature_columns].values

            # 标准化
            feature_data = self.scaler.transform(feature_data)
            X = feature_data.reshape(1, seq_len, -1)

            if not TENSORFLOW_AVAILABLE:
                # 使用传统方法预测
                X_flat = X.reshape(1, -1)
                pred = self.fallback_model.predict(X_flat)[0]
            else:
                # 使用Transformer预测
                pred = self.model.predict(X, verbose=0)[0]

            # 处理预测结果
            if isinstance(pred, np.ndarray):
                if len(pred) == 1:
                    prediction_value = float(pred[0])
                else:
                    prediction_value = pred.tolist()
            else:
                prediction_value = float(pred)

            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=prediction_value,
                confidence=0.85,
                metadata={
                    "success": True,
                    "predictions": {
                        "transformer_prediction": prediction_value,
                        "confidence": 0.85,
                        "method": "transformer" if TENSORFLOW_AVAILABLE else "fallback",
                    },
                    "sequence_length": seq_len,
                    "feature_count": (
                        len(self.feature_columns) if self.feature_columns else 0
                    ),
                    "model_type": (
                        "Transformer" if TENSORFLOW_AVAILABLE else "GradientBoosting"
                    ),
                    "attention_heads": self.config.num_heads,
                    "transformer_layers": self.config.num_layers,
                },
            )

        except Exception as e:
            self.logger.error(f"Transformer预测失败: {e}")
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": str(e), "predictions": {}},
            )

    def predict_batch(
        self, data: pd.DataFrame, target_indices: List[int], **kwargs
    ) -> List[PredictionResult]:
        """批量预测"""
        results = []
        for idx in target_indices:
            result = self.predict(data, idx, **kwargs)
            results.append(result)
        return results

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            "name": "Transformer预测器",
            "version": "1.0.0",
            "is_trained": self.is_trained_flag,
            "tensorflow_available": TENSORFLOW_AVAILABLE,
            "config": {
                "d_model": self.config.d_model,
                "num_heads": self.config.num_heads,
                "num_layers": self.config.num_layers,
                "sequence_length": self.config.sequence_length,
                "learning_rate": self.config.learning_rate,
            },
        }

        if self.model and TENSORFLOW_AVAILABLE:
            info["model_params"] = self.model.count_params()
            info["model_layers"] = len(self.model.layers)

        return info
