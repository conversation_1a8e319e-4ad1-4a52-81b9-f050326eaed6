"""
Performance Benchmark Script
测试重构前后的预测准确性，确保架构改变不影响核心功能
"""

import sys
from pathlib import Path
import pandas as pd
import time
from typing import Dict, Any, List, Tuple
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.specialized_adapters import create_specialized_adapter
from src.core.interfaces import BallType, PredictionType
from src.models.optimized_odd_even_predictor import OptimizedOddEvenPredictor


class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self):
        self.test_results = []
        self.benchmark_data = None
        
    def create_benchmark_data(self, num_periods: int = 50) -> pd.DataFrame:
        """创建基准测试数据"""
        print(f"创建基准测试数据 ({num_periods} 期)...")
        
        # 创建模拟的彩票数据
        data = []
        for i in range(num_periods):
            period = 25000 + i
            # 模拟红球数据 (1-35)
            red_balls = [
                min(35, max(1, 5 + (i % 7))),
                min(35, max(1, 12 + (i % 8))),
                min(35, max(1, 18 + (i % 9))),
                min(35, max(1, 25 + (i % 10))),
                min(35, max(1, 32 + (i % 11)))
            ]
            red_balls.sort()
            
            # 模拟蓝球数据 (1-12)
            blue_balls = [
                min(12, max(1, 3 + (i % 5))),
                min(12, max(1, 8 + (i % 6)))
            ]
            blue_balls.sort()
            
            data.append({
                '期号': period,
                '红球1': red_balls[0],
                '红球2': red_balls[1],
                '红球3': red_balls[2],
                '红球4': red_balls[3],
                '红球5': red_balls[4],
                '蓝球1': blue_balls[0],
                '蓝球2': blue_balls[1]
            })
        
        self.benchmark_data = pd.DataFrame(data)
        print(f"✅ 基准数据创建完成: {len(self.benchmark_data)} 期")
        return self.benchmark_data
    
    def test_original_predictor(self, data: pd.DataFrame) -> Dict[str, Any]:
        """测试原始预测器性能"""
        print("\n" + "="*50)
        print("测试原始预测器")
        print("="*50)
        
        predictor = OptimizedOddEvenPredictor()
        
        # 训练预测器
        state_sequence = self._extract_state_sequence(data)
        predictor.train(state_sequence)
        
        # 性能测试
        start_time = time.time()
        predictions = []
        
        for i in range(10, len(data)):  # 从第10期开始预测
            current_state = self._get_current_state(data, i)
            try:
                result = predictor.predict(current_state)
                predictions.append({
                    'period': data.iloc[i]['期号'],
                    'prediction': result[0] if isinstance(result, tuple) else str(result),
                    'confidence': result[1] if isinstance(result, tuple) and len(result) > 1 else 0.5,
                    'actual': self._get_actual_state(data, i)
                })
            except Exception as e:
                print(f"预测失败 (期号 {data.iloc[i]['期号']}): {e}")
        
        end_time = time.time()
        
        # 计算准确率
        correct = sum(1 for p in predictions if p['prediction'] == p['actual'])
        accuracy = correct / len(predictions) if predictions else 0
        
        result = {
            'type': 'original',
            'predictions_count': len(predictions),
            'correct_predictions': correct,
            'accuracy': accuracy,
            'execution_time': end_time - start_time,
            'avg_confidence': sum(p['confidence'] for p in predictions) / len(predictions) if predictions else 0,
            'predictions': predictions[:5]  # 保存前5个预测作为样本
        }
        
        print(f"原始预测器结果:")
        print(f"  - 预测次数: {result['predictions_count']}")
        print(f"  - 正确预测: {result['correct_predictions']}")
        print(f"  - 准确率: {result['accuracy']:.2%}")
        print(f"  - 执行时间: {result['execution_time']:.3f}秒")
        print(f"  - 平均置信度: {result['avg_confidence']:.3f}")
        
        return result
    
    def test_adapted_predictor(self, data: pd.DataFrame) -> Dict[str, Any]:
        """测试适配后预测器性能"""
        print("\n" + "="*50)
        print("测试适配后预测器")
        print("="*50)
        
        # 创建原始预测器
        original_predictor = OptimizedOddEvenPredictor()
        
        # 创建适配器
        adapter = create_specialized_adapter(
            original_predictor,
            predictor_type='odd_even',
            name="性能测试适配器",
            version="1.0"
        )
        
        # 性能测试
        start_time = time.time()
        predictions = []
        
        for i in range(10, len(data)):  # 从第10期开始预测
            try:
                # 使用适配器进行预测
                result = adapter.predict(data, i, ball_type=BallType.RED)
                predictions.append({
                    'period': data.iloc[i]['期号'],
                    'prediction': result.value,
                    'confidence': result.confidence,
                    'actual': self._get_actual_state(data, i)
                })
            except Exception as e:
                print(f"适配器预测失败 (期号 {data.iloc[i]['期号']}): {e}")
        
        end_time = time.time()
        
        # 计算准确率
        correct = sum(1 for p in predictions if p['prediction'] == p['actual'])
        accuracy = correct / len(predictions) if predictions else 0
        
        result = {
            'type': 'adapted',
            'predictions_count': len(predictions),
            'correct_predictions': correct,
            'accuracy': accuracy,
            'execution_time': end_time - start_time,
            'avg_confidence': sum(p['confidence'] for p in predictions) / len(predictions) if predictions else 0,
            'predictions': predictions[:5]  # 保存前5个预测作为样本
        }
        
        print(f"适配器预测器结果:")
        print(f"  - 预测次数: {result['predictions_count']}")
        print(f"  - 正确预测: {result['correct_predictions']}")
        print(f"  - 准确率: {result['accuracy']:.2%}")
        print(f"  - 执行时间: {result['execution_time']:.3f}秒")
        print(f"  - 平均置信度: {result['avg_confidence']:.3f}")
        
        return result
    
    def _extract_state_sequence(self, data: pd.DataFrame) -> List[str]:
        """从数据中提取奇偶比状态序列"""
        states = []
        for _, row in data.iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
            even_count = 5 - odd_count
            state = f"{odd_count}:{even_count}"
            states.append(state)
        return states[::-1]  # 返回从新到旧的序列
    
    def _get_current_state(self, data: pd.DataFrame, index: int) -> str:
        """获取当前状态"""
        if index > 0:
            row = data.iloc[index - 1]
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
            even_count = 5 - odd_count
            return f"{odd_count}:{even_count}"
        return "3:2"
    
    def _get_actual_state(self, data: pd.DataFrame, index: int) -> str:
        """获取实际状态"""
        row = data.iloc[index]
        red_balls = [row[f'红球{i}'] for i in range(1, 6)]
        odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
        even_count = 5 - odd_count
        return f"{odd_count}:{even_count}"
    
    def compare_results(self, original_result: Dict[str, Any], adapted_result: Dict[str, Any]) -> Dict[str, Any]:
        """比较测试结果"""
        print("\n" + "="*50)
        print("性能比较结果")
        print("="*50)
        
        comparison = {
            'accuracy_difference': adapted_result['accuracy'] - original_result['accuracy'],
            'time_difference': adapted_result['execution_time'] - original_result['execution_time'],
            'confidence_difference': adapted_result['avg_confidence'] - original_result['avg_confidence'],
            'predictions_difference': adapted_result['predictions_count'] - original_result['predictions_count'],
            'accuracy_maintained': abs(adapted_result['accuracy'] - original_result['accuracy']) < 0.05,  # 5%容差
            'performance_acceptable': adapted_result['execution_time'] < original_result['execution_time'] * 2  # 性能不超过2倍
        }
        
        print(f"准确率差异: {comparison['accuracy_difference']:+.2%}")
        print(f"执行时间差异: {comparison['time_difference']:+.3f}秒")
        print(f"置信度差异: {comparison['confidence_difference']:+.3f}")
        print(f"预测次数差异: {comparison['predictions_difference']:+d}")
        print(f"准确率保持: {'✅' if comparison['accuracy_maintained'] else '❌'}")
        print(f"性能可接受: {'✅' if comparison['performance_acceptable'] else '❌'}")
        
        # 总体评估
        overall_success = comparison['accuracy_maintained'] and comparison['performance_acceptable']
        print(f"\n总体评估: {'✅ 通过' if overall_success else '❌ 失败'}")
        
        comparison['overall_success'] = overall_success
        return comparison
    
    def generate_benchmark_report(self, original_result: Dict[str, Any], 
                                adapted_result: Dict[str, Any], 
                                comparison: Dict[str, Any]) -> str:
        """生成基准测试报告"""
        report = []
        report.append("# Performance Benchmark Report")
        report.append("=" * 50)
        report.append("")
        
        report.append("## 测试概述")
        report.append(f"测试数据: {len(self.benchmark_data)} 期")
        report.append(f"预测范围: 第10期到第{len(self.benchmark_data)}期")
        report.append("")
        
        report.append("## 原始预测器结果")
        report.append(f"- 预测次数: {original_result['predictions_count']}")
        report.append(f"- 正确预测: {original_result['correct_predictions']}")
        report.append(f"- 准确率: {original_result['accuracy']:.2%}")
        report.append(f"- 执行时间: {original_result['execution_time']:.3f}秒")
        report.append(f"- 平均置信度: {original_result['avg_confidence']:.3f}")
        report.append("")
        
        report.append("## 适配器预测器结果")
        report.append(f"- 预测次数: {adapted_result['predictions_count']}")
        report.append(f"- 正确预测: {adapted_result['correct_predictions']}")
        report.append(f"- 准确率: {adapted_result['accuracy']:.2%}")
        report.append(f"- 执行时间: {adapted_result['execution_time']:.3f}秒")
        report.append(f"- 平均置信度: {adapted_result['avg_confidence']:.3f}")
        report.append("")
        
        report.append("## 性能比较")
        report.append(f"- 准确率差异: {comparison['accuracy_difference']:+.2%}")
        report.append(f"- 执行时间差异: {comparison['time_difference']:+.3f}秒")
        report.append(f"- 置信度差异: {comparison['confidence_difference']:+.3f}")
        report.append(f"- 准确率保持: {'✅' if comparison['accuracy_maintained'] else '❌'}")
        report.append(f"- 性能可接受: {'✅' if comparison['performance_acceptable'] else '❌'}")
        report.append("")
        
        report.append("## 结论")
        if comparison['overall_success']:
            report.append("✅ **架构重构成功**: 适配器保持了原始预测器的准确性和性能")
        else:
            report.append("❌ **需要优化**: 适配器在准确性或性能方面存在问题")
        
        return "\n".join(report)


def main():
    """主函数"""
    print("Performance Benchmark Tool")
    print("=" * 60)
    
    benchmark = PerformanceBenchmark()
    
    # 步骤1: 创建基准数据
    data = benchmark.create_benchmark_data(50)
    
    # 步骤2: 测试原始预测器
    original_result = benchmark.test_original_predictor(data)
    
    # 步骤3: 测试适配后预测器
    adapted_result = benchmark.test_adapted_predictor(data)
    
    # 步骤4: 比较结果
    comparison = benchmark.compare_results(original_result, adapted_result)
    
    # 步骤5: 生成报告
    report = benchmark.generate_benchmark_report(original_result, adapted_result, comparison)
    print("\n" + report)
    
    # 保存报告
    report_path = project_root / "docs" / "performance_benchmark_report.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"\n报告已保存到: {report_path}")
    
    return benchmark


if __name__ == "__main__":
    benchmark = main()
