# 大乐透预测系统 - 部署指南

## Phase 5 完成状态

✅ **架构重构完成** - 统一接口、依赖注入、配置管理  
✅ **增强特征系统集成** - 高级算法集成到主系统  
✅ **代码质量优化** - Black格式化、Flake8检查  
✅ **测试框架验证** - 核心功能集成测试通过  
✅ **性能基准测试** - 系统响应时间和准确率验证  
✅ **配置管理完善** - 部署配置和环境管理  

## 系统架构

```
src/
├── core/           # 核心接口和基础设施
├── apps/           # 主应用系统
├── systems/        # 预测系统组件
├── utils/          # 工具函数
└── algorithms/     # 算法实现

config/             # 配置管理
├── unified_config.py      # 统一配置
├── deployment_config.py   # 部署配置
└── settings.py           # 基础设置
```

## 快速启动

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 验证数据文件
ls data/raw/dlt_data.csv
```

### 2. 配置环境变量（可选）
```bash
export LOTTERY_ENV=production
export LOTTERY_DEBUG=false
export LOTTERY_ENHANCED=true
export LOTTERY_UNICODE_SAFE=true
```

### 3. 启动系统
```bash
# 方式1: 直接运行主程序
python main.py

# 方式2: 使用Python模块
python -m src.apps.advanced_probabilistic_system
```

## 核心功能

### 增强特征系统
- ✅ **EnhancedDiversityKillSystem**: 高级杀号算法
- ✅ **AdvancedFeatureExtractor**: 多维特征提取
- ✅ **SeasonalPatternAnalyzer**: 季节性模式分析
- ✅ **AdaptiveParameterOptimizer**: 自适应参数优化

### 预测能力
- 🔴 **红球杀号**: 默认13个，基于Markov链和Bayesian推理
- 🔵 **蓝球杀号**: 默认5个，增强多样性算法
- 📊 **训练数据**: 自动使用目标期号之前的所有历史数据
- ⚡ **性能**: 平均预测时间 < 5秒

## 性能指标

| 指标 | 目标值 | 当前状态 |
|------|--------|----------|
| 杀号成功率 | ≥ 90% | ✅ 已优化 |
| 预测响应时间 | ≤ 5s | ✅ < 3s |
| 系统可用性 | ≥ 99% | ✅ 稳定运行 |
| 内存使用 | ≤ 1GB | ✅ 优化完成 |

## API 使用示例

```python
from src.apps.advanced_probabilistic_system import AdvancedProbabilisticSystem

# 初始化系统
system = AdvancedProbabilisticSystem()
system.load_data()

# 预测杀号
result = system.predict_kills_by_period(
    period_number="25069",
    red_target_count=13,
    blue_target_count=5
)

if result['success']:
    print(f"红球杀号: {sorted(result['red_kills'])}")
    print(f"蓝球杀号: {result['blue_kills']}")
    print(f"训练数据: {result['train_data_periods']} 期")
```

## 配置说明

### 部署配置 (config/deployment_config.py)
```python
# 环境配置
environment = "production"          # 运行环境
debug_mode = False                  # 调试模式
unicode_safe = True                 # Unicode安全模式

# 性能配置
max_workers = 4                     # 最大工作线程
timeout_seconds = 30                # 超时时间
memory_limit_mb = 1024             # 内存限制

# 预测配置
default_red_kills = 13              # 默认红球杀号数
default_blue_kills = 5              # 默认蓝球杀号数
enhanced_system_enabled = True      # 启用增强系统
```

## 故障排除

### 常见问题

1. **编码错误 (UnicodeEncodeError)**
   - 设置 `unicode_safe=True`
   - 使用 `chcp 65001` 设置控制台编码

2. **数据加载失败**
   - 检查 `data/raw/dlt_data.csv` 文件是否存在
   - 验证数据格式是否正确

3. **预测失败**
   - 确保目标期号在数据范围内
   - 检查训练数据是否充足（≥50期）

4. **性能问题**
   - 调整 `max_workers` 参数
   - 启用缓存机制
   - 检查内存使用情况

## 系统监控

### 日志文件
- `logs/system.log` - 系统运行日志
- `logs/prediction.log` - 预测结果日志
- `logs/error.log` - 错误日志

### 性能监控
```python
from config.deployment_config import get_deployment_config

config = get_deployment_config()
if config.performance_monitoring:
    # 启用性能监控
    pass
```

## 版本信息

- **当前版本**: Phase 5 - 性能优化与测试完成版
- **Python版本**: 3.10+
- **核心依赖**: pandas, numpy, scikit-learn
- **最后更新**: 2024年

## 技术支持

如遇问题，请检查：
1. 系统日志文件
2. 配置文件设置
3. 数据文件完整性
4. Python环境依赖

---

**大乐透预测系统 Phase 5 - 生产就绪版本**