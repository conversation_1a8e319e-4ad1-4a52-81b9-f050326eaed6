#!/usr/bin/env python3
"""
实际预测演示 - 使用修复后的系统
"""

import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.models.optimized_predictor import OptimizedPredictor
from src.utils.utils import load_data, parse_numbers, calculate_size_ratio_red, ratio_to_state


def format_prediction_result(result):
    """格式化预测结果"""
    print(f"\n📋 预测期号: {result['period']}")
    print(f"🎯 预测置信度: {result['kill_success_rate']:.1%}")
    
    predictions = result['predictions']
    
    print("\n🔮 预测结果:")
    
    # 红球奇偶比预测
    if 'red_odd_even' in predictions:
        red_odd_even = predictions['red_odd_even']
        print(f"   红球奇偶比:")
        for i, (state, prob) in enumerate(red_odd_even[:2], 1):
            print(f"     {i}. {state} (概率: {prob:.1%})")
    
    # 红球大小比预测
    if 'red_size' in predictions:
        red_size = predictions['red_size']
        print(f"   红球大小比:")
        for i, (state, prob) in enumerate(red_size[:2], 1):
            print(f"     {i}. {state} (概率: {prob:.1%})")
    
    # 蓝球大小比预测
    if 'blue_size' in predictions:
        blue_size = predictions['blue_size']
        print(f"   蓝球大小比:")
        for i, (state, prob) in enumerate(blue_size[:2], 1):
            print(f"     {i}. {state} (概率: {prob:.1%})")
    
    # 置信度分数
    confidence_scores = result['confidence_scores']
    print(f"\n📊 特征置信度:")
    for feature, score in confidence_scores.items():
        print(f"   {feature}: {score:.1%}")


def predict_next_period():
    """预测下一期"""
    print("🚀 启动实际预测系统...")
    print("=" * 60)
    
    # 初始化预测器
    start_time = time.time()
    predictor = OptimizedPredictor()
    init_time = time.time() - start_time
    print(f"✅ 预测器初始化完成 ({init_time:.2f}秒)")
    
    # 获取最新数据
    data = load_data()
    latest_index = len(data) - 1
    latest_row = data.iloc[latest_index]
    
    print(f"\n📊 基于最新数据预测:")
    print(f"   数据总期数: {len(data)}")
    print(f"   最新期号: {latest_row['期号']}")

    # 分析最新期状态
    red_balls, blue_balls = parse_numbers(latest_row)
    red_big, red_small = calculate_size_ratio_red(red_balls)
    current_red_size = ratio_to_state((red_big, red_small))

    print(f"   最新开奖红球: {red_balls}")
    print(f"   最新开奖蓝球: {blue_balls}")
    print(f"   当前红球大小比状态: {current_red_size}")
    
    # 进行预测
    print(f"\n🔄 正在预测下一期...")
    start_time = time.time()
    
    # 预测下一期（使用最新期作为当前状态）
    result = predictor.predict_with_insights(latest_index)
    
    predict_time = time.time() - start_time
    print(f"✅ 预测完成 ({predict_time:.3f}秒)")
    
    # 显示预测结果
    format_prediction_result(result)
    
    return result


def predict_multiple_periods():
    """预测多期进行验证"""
    print("\n\n🔍 多期预测验证...")
    print("=" * 60)
    
    predictor = OptimizedPredictor()
    data = load_data()
    
    # 预测最近5期进行验证
    print("📈 预测最近5期（用于验证准确性）:")
    
    for i in range(5):
        test_index = len(data) - 10 + i  # 从倒数第10期开始预测5期
        if test_index >= 0 and test_index < len(data) - 1:
            
            # 获取实际结果
            actual_row = data.iloc[test_index + 1]
            actual_red, actual_blue = parse_numbers(actual_row)
            actual_red_big, actual_red_small = calculate_size_ratio_red(actual_red)
            actual_red_size = ratio_to_state((actual_red_big, actual_red_small))
            
            # 进行预测
            result = predictor.predict_with_insights(test_index)
            
            # 检查预测准确性
            predictions = result['predictions']
            if 'red_size' in predictions:
                red_size_pred = predictions['red_size']
                if isinstance(red_size_pred, list) and len(red_size_pred) >= 1:
                    predicted_state = red_size_pred[0][0]
                    predicted_prob = red_size_pred[0][1]
                    
                    is_correct = predicted_state == actual_red_size
                    status = "✅" if is_correct else "❌"
                    
                    print(f"\n   期号 {actual_row['期号']}:")
                    print(f"     预测: {predicted_state} ({predicted_prob:.1%})")
                    print(f"     实际: {actual_red_size}")
                    print(f"     结果: {status}")


def analyze_prediction_performance():
    """分析预测性能"""
    print("\n\n📊 预测性能分析...")
    print("=" * 60)
    
    predictor = OptimizedPredictor()
    data = load_data()
    
    # 测试最近50期的预测准确率
    correct_predictions = 0
    total_predictions = 0
    
    print("🔄 分析最近50期预测准确率...")
    
    for i in range(50):
        test_index = len(data) - 100 + i
        if test_index >= 0 and test_index < len(data) - 1:
            
            # 获取实际结果
            actual_row = data.iloc[test_index + 1]
            actual_red, _ = parse_numbers(actual_row)
            actual_red_big, actual_red_small = calculate_size_ratio_red(actual_red)
            actual_red_size = ratio_to_state((actual_red_big, actual_red_small))
            
            # 进行预测
            try:
                result = predictor.predict_with_insights(test_index)
                predictions = result['predictions']
                
                if 'red_size' in predictions:
                    red_size_pred = predictions['red_size']
                    if isinstance(red_size_pred, list) and len(red_size_pred) >= 1:
                        predicted_state = red_size_pred[0][0]
                        
                        if predicted_state == actual_red_size:
                            correct_predictions += 1
                        total_predictions += 1
                        
            except Exception as e:
                print(f"⚠️ 预测失败: {e}")
                continue
    
    if total_predictions > 0:
        accuracy = correct_predictions / total_predictions
        print(f"\n📈 预测准确率统计:")
        print(f"   总预测期数: {total_predictions}")
        print(f"   正确预测: {correct_predictions}")
        print(f"   准确率: {accuracy:.1%}")
        
        if accuracy >= 0.4:
            print("✅ 预测准确率良好")
        elif accuracy >= 0.3:
            print("⚠️ 预测准确率一般")
        else:
            print("❌ 预测准确率较低")
    else:
        print("❌ 无法计算准确率")


def main():
    """主演示函数"""
    print("🎯 实际预测演示 - 修复后的系统")
    print(f"⏰ 运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 预测下一期
        next_prediction = predict_next_period()
        
        # 2. 多期验证
        predict_multiple_periods()
        
        # 3. 性能分析
        analyze_prediction_performance()
        
        print("\n" + "=" * 60)
        print("🎉 实际预测演示完成！")
        print("✅ 系统运行稳定，预测功能正常")
        print("✅ 所有性能问题已解决")
        print("✅ 预测结果格式标准化")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
