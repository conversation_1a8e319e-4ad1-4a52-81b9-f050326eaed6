#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习命中率增强器

集成深度学习命中率优化器到现有系统，专门解决：
1. 2+1命中率为0%的问题
2. 比例预测准确率低的问题
3. 号码生成策略失效的问题

通过深度学习技术显著提升彩票预测的命中率。
"""

import os
import sys
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple
import json
from datetime import datetime
import logging

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from src.models.deep_learning.hit_rate_optimizer import HitRateOptimizer, HitRateOptimizerConfig
    from src.utils.utils import load_data
    DEEP_LEARNING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 深度学习模块导入失败: {e}")
    DEEP_LEARNING_AVAILABLE = False


class DeepLearningHitRateEnhancer:
    """深度学习命中率增强器"""
    
    def __init__(self, data_file: str = "data/raw/dlt_data.csv"):
        """
        初始化深度学习命中率增强器
        
        Args:
            data_file: 数据文件路径
        """
        self.data_file = data_file
        self.data = None
        self.hit_rate_optimizer = None
        self.enhancement_results = {}
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 加载数据
        self._load_data()
        
        # 初始化深度学习优化器
        self._initialize_optimizer()
    
    def _load_data(self):
        """加载彩票数据"""
        try:
            self.data = load_data(self.data_file)
            self.logger.info(f"✅ 数据加载成功，共{len(self.data)}期数据")
        except Exception as e:
            self.logger.error(f"❌ 数据加载失败: {e}")
            # 创建模拟数据用于测试
            self._create_mock_data()
    
    def _create_mock_data(self):
        """创建模拟数据用于测试"""
        self.logger.info("创建模拟数据用于测试...")
        
        periods = []
        for i in range(100):
            period = f"250{i:02d}"
            
            # 生成随机红球（1-35）
            red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
            
            # 生成随机蓝球（1-12）
            blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
            
            period_data = {'period': period}
            for j, ball in enumerate(red_balls, 1):
                period_data[f'red_{j}'] = ball
            for j, ball in enumerate(blue_balls, 1):
                period_data[f'blue_{j}'] = ball
            
            periods.append(period_data)
        
        self.data = pd.DataFrame(periods)
        self.logger.info(f"✅ 模拟数据创建成功，共{len(self.data)}期数据")
    
    def _initialize_optimizer(self):
        """初始化深度学习优化器"""
        if not DEEP_LEARNING_AVAILABLE:
            self.logger.warning("⚠️ 深度学习模块不可用，将使用传统方法")
            return
        
        try:
            # 创建V3.2增强温度采样配置
            config = HitRateOptimizerConfig(
                # V3.1增强架构参数
                sequence_length=25,      # 序列长度（V3.1增加到25）
                embedding_dim=128,       # 嵌入维度（V3.1增强）
                lstm_units=256,          # LSTM单元数（V3.1增强到256）
                attention_heads=12,      # 注意力头数（V3.1增强到12）
                dense_units=512,         # 全连接层单元（V3.1增强）
                dropout_rate=0.25,       # Dropout率（V3.1优化）

                # V3.1优化比例预测子网络参数
                ratio_subnet_depth=4,           # 比例预测子网络深度（V3.1增加到4层）
                ratio_subnet_units=512,         # 比例预测子网络单元数（V3.1增强）
                ratio_attention_heads=8,        # 比例预测专用注意力头数（V3.1增强）
                ratio_dropout_rate=0.15,        # 比例预测专用Dropout率（V3.1优化）

                # V3.1增强不确定性建模参数
                use_variational_layers=True,    # 使用变分层
                mc_dropout_samples=15,          # Monte Carlo Dropout采样次数（V3.1增加）
                uncertainty_weight=0.15,        # 不确定性损失权重（V3.1增强）

                # V3.2增强温度采样参数
                temperature_min=0.5,            # 最小温度参数（V3.2降低）
                temperature_max=3.0,            # 最大温度参数（V3.2提高）
                adaptive_temperature=True,      # 自适应温度调整
                temperature_mapping="sigmoid",  # V3.2新增：非线性温度映射
                temperature_sensitivity=2.0,    # V3.2新增：温度敏感度

                # V3.2新增：多源噪声参数
                gaussian_noise_scale=0.15,      # 高斯噪声强度
                uniform_noise_scale=0.08,       # 均匀噪声强度
                periodic_noise_scale=0.05,      # 周期性噪声强度

                # V3.2新增：采样策略参数
                sampling_strategy="nucleus",    # 核采样策略
                nucleus_p=0.8,                  # 核采样参数

                # V3.2新增：历史避免机制
                enable_history_avoidance=True,  # 启用历史避免
                history_window=5,               # 历史窗口大小
                avoidance_strength=0.2,         # 避免强度

                # V3.1重新平衡的损失权重
                ratio_prediction_weight=5.0,    # 比例预测权重（V3.1提升到5.0）
                number_generation_weight=0.4,   # 号码生成权重（V3.1微调）
                hit_rate_weight=0.3,            # 命中率权重（V3.1增强）

                # 训练参数
                learning_rate=0.001,     # 学习率
                batch_size=32,           # 批次大小（V3.0恢复到32）
                epochs=50,               # 训练轮数（减少以快速测试）
                early_stopping_patience=20,
                target_hit_rate=0.25,    # 目标命中率25%
                target_ratio_accuracy=0.6  # 目标比例准确率60%
            )
            
            # 创建优化器实例
            self.hit_rate_optimizer = HitRateOptimizer(config)
            self.logger.info("✅ 深度学习命中率优化器初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ 深度学习优化器初始化失败: {e}")
            self.hit_rate_optimizer = None
    
    def train_deep_learning_model(self) -> Dict[str, Any]:
        """训练深度学习模型"""
        if self.hit_rate_optimizer is None:
            return {"success": False, "error": "优化器未初始化"}
        
        self.logger.info("🚀 开始训练深度学习命中率优化模型...")
        
        try:
            # 使用80%的数据进行训练
            train_size = int(len(self.data) * 0.8)
            train_data = self.data.iloc[:train_size]
            
            # 训练模型
            training_result = self.hit_rate_optimizer.train(train_data)
            
            if training_result.get("success", False):
                self.logger.info("✅ 深度学习模型训练成功")
                self.logger.info(f"   训练样本数: {training_result.get('samples', 0)}")
                self.logger.info(f"   训练方法: {training_result.get('method', 'unknown')}")
                
                if 'performance_metrics' in training_result:
                    self.logger.info("   性能指标:")
                    for metric, value in training_result['performance_metrics'].items():
                        self.logger.info(f"     {metric}: {value:.4f}")
            else:
                self.logger.error(f"❌ 深度学习模型训练失败: {training_result.get('error', 'unknown')}")
            
            return training_result
            
        except Exception as e:
            self.logger.error(f"❌ 训练过程异常: {e}")
            return {"success": False, "error": str(e)}
    
    def test_deep_learning_predictions(self, test_periods: int = 10) -> Dict[str, Any]:
        """测试深度学习预测效果"""
        if self.hit_rate_optimizer is None or not self.hit_rate_optimizer.is_trained_flag:
            return {"success": False, "error": "模型未训练"}
        
        self.logger.info(f"🧪 开始测试深度学习预测效果（测试{test_periods}期）...")
        
        try:
            # 使用最后的数据进行测试
            test_start = len(self.data) - test_periods - 1
            test_results = []
            
            for i in range(test_start, len(self.data) - 1):
                # 预测下一期
                prediction_result = self.hit_rate_optimizer.predict(self.data, i)
                
                if prediction_result.metadata.get("success", False):
                    predictions = prediction_result.value
                    actual_row = self.data.iloc[i + 1]
                    
                    # 计算预测准确性
                    # 处理列名差异（中文vs英文）
                    period_col = '期号' if '期号' in actual_row.index else 'period'
                    red_cols = [f'红球{j}' if f'红球{j}' in actual_row.index else f'red_{j}' for j in range(1, 6)]
                    blue_cols = [f'蓝球{j}' if f'蓝球{j}' in actual_row.index else f'blue_{j}' for j in range(1, 3)]

                    test_result = {
                        "period": actual_row.get(period_col, f'test_{i}'),
                        "predictions": predictions,
                        "confidence": prediction_result.confidence,
                        "actual": {
                            "red_balls": [actual_row[col] for col in red_cols],
                            "blue_balls": [actual_row[col] for col in blue_cols]
                        }
                    }
                    
                    # 评估预测质量
                    if isinstance(predictions, dict):
                        test_result["evaluation"] = self._evaluate_prediction(predictions, test_result["actual"])
                    
                    test_results.append(test_result)
                else:
                    self.logger.warning(f"⚠️ 期号{i}预测失败: {prediction_result.metadata.get('error', 'unknown')}")
            
            # 计算总体性能
            overall_performance = self._calculate_overall_performance(test_results)
            
            self.logger.info("✅ 深度学习预测测试完成")
            self.logger.info(f"   测试期数: {len(test_results)}")
            self.logger.info(f"   平均置信度: {overall_performance.get('avg_confidence', 0):.3f}")
            
            return {
                "success": True,
                "test_results": test_results,
                "overall_performance": overall_performance,
                "test_periods": len(test_results)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 测试过程异常: {e}")
            return {"success": False, "error": str(e)}
    
    def _evaluate_prediction(self, predictions: Dict[str, Any], actual: Dict[str, Any]) -> Dict[str, Any]:
        """评估单次预测的质量"""
        evaluation = {}
        
        try:
            # 评估比例预测（如果有的话）
            if 'red_odd_even_ratio' in predictions:
                # 计算实际奇偶比
                actual_odd_count = sum(1 for ball in actual['red_balls'] if ball % 2 == 1)
                actual_ratio = f"{actual_odd_count}:{5-actual_odd_count}"
                evaluation['red_odd_even_correct'] = predictions['red_odd_even_ratio'] == actual_ratio
            
            # 评估号码候选质量（如果有的话）
            if 'red_ball_candidates' in predictions:
                red_candidates = set(predictions['red_ball_candidates'])
                actual_red = set(actual['red_balls'])
                red_hits = len(red_candidates.intersection(actual_red))
                evaluation['red_ball_hits'] = red_hits
                evaluation['red_ball_hit_rate'] = red_hits / len(actual_red)
            
            if 'blue_ball_candidates' in predictions:
                blue_candidates = set(predictions['blue_ball_candidates'])
                actual_blue = set(actual['blue_balls'])
                blue_hits = len(blue_candidates.intersection(actual_blue))
                evaluation['blue_ball_hits'] = blue_hits
                evaluation['blue_ball_hit_rate'] = blue_hits / len(actual_blue)
            
            # 计算综合评分
            total_score = 0
            score_count = 0
            
            if 'red_odd_even_correct' in evaluation:
                total_score += 1 if evaluation['red_odd_even_correct'] else 0
                score_count += 1
            
            if 'red_ball_hit_rate' in evaluation:
                total_score += evaluation['red_ball_hit_rate']
                score_count += 1
            
            if 'blue_ball_hit_rate' in evaluation:
                total_score += evaluation['blue_ball_hit_rate']
                score_count += 1
            
            evaluation['overall_score'] = total_score / score_count if score_count > 0 else 0
            
        except Exception as e:
            evaluation['error'] = str(e)
        
        return evaluation
    
    def _calculate_overall_performance(self, test_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算总体性能指标"""
        if not test_results:
            return {}
        
        # 收集所有评估指标
        confidences = []
        overall_scores = []
        red_hit_rates = []
        blue_hit_rates = []
        ratio_correct_count = 0
        ratio_total_count = 0
        
        for result in test_results:
            confidences.append(result.get('confidence', 0))
            
            evaluation = result.get('evaluation', {})
            if 'overall_score' in evaluation:
                overall_scores.append(evaluation['overall_score'])
            
            if 'red_ball_hit_rate' in evaluation:
                red_hit_rates.append(evaluation['red_ball_hit_rate'])
            
            if 'blue_ball_hit_rate' in evaluation:
                blue_hit_rates.append(evaluation['blue_ball_hit_rate'])
            
            if 'red_odd_even_correct' in evaluation:
                ratio_total_count += 1
                if evaluation['red_odd_even_correct']:
                    ratio_correct_count += 1
        
        # 计算平均值
        performance = {
            'avg_confidence': np.mean(confidences) if confidences else 0,
            'avg_overall_score': np.mean(overall_scores) if overall_scores else 0,
            'avg_red_hit_rate': np.mean(red_hit_rates) if red_hit_rates else 0,
            'avg_blue_hit_rate': np.mean(blue_hit_rates) if blue_hit_rates else 0,
            'ratio_accuracy': ratio_correct_count / ratio_total_count if ratio_total_count > 0 else 0,
            'test_count': len(test_results)
        }
        
        return performance
    
    def run_comprehensive_enhancement(self) -> Dict[str, Any]:
        """运行综合命中率增强（V3.0版本）"""
        self.logger.info("🎯 开始运行深度学习命中率综合增强（V3.0版本）...")

        # 显示V3.0优化内容
        self.logger.info("   ✨ V3.0核心优化内容：")
        self.logger.info("      - 专门比例预测子网络架构（3层深度，256单元）")
        self.logger.info("      - Monte Carlo Dropout多样性预测（10次采样）")
        self.logger.info("      - 大幅增强损失权重（1.5 → 4.0）")
        self.logger.info("      - 变分层不确定性建模")
        self.logger.info("      - 温度采样增加预测多样性")

        results = {
            "enhancement_date": datetime.now().isoformat(),
            "optimization_version": "v3.1",
            "optimization_features": [
                "自适应温度采样",
                "增强子网络架构（4层深度，512单元）",
                "优化损失权重分配（5.0）",
                "增强Monte Carlo采样（15次）",
                "噪声增强多样性预测",
                "增加训练数据多样性（25期序列）"
            ],
            "v3_config": {
                "ratio_prediction_weight": 4.0,
                "ratio_subnet_depth": 3,
                "ratio_subnet_units": 256,
                "mc_dropout_samples": 10,
                "use_variational_layers": True
            },
            "data_info": {
                "total_periods": len(self.data) if self.data is not None else 0,
                "data_file": self.data_file
            }
        }

        # 1. 训练优化的深度学习模型
        self.logger.info("📚 第1步：训练优化的深度学习模型")
        training_result = self.train_deep_learning_model()
        results["training"] = training_result
        
        if not training_result.get("success", False):
            self.logger.error("❌ 训练失败，终止增强流程")
            return results
        
        # 2. 测试预测效果
        self.logger.info("🧪 第2步：测试预测效果")
        testing_result = self.test_deep_learning_predictions(test_periods=10)
        results["testing"] = testing_result
        
        # 3. 生成增强报告
        self.logger.info("📊 第3步：生成增强报告")
        enhancement_report = self._generate_enhancement_report(results)
        results["enhancement_report"] = enhancement_report
        
        # 4. 保存结果
        self._save_enhancement_results(results)
        
        self.logger.info("✅ 深度学习命中率增强完成！")
        return results
    
    def _generate_enhancement_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成增强报告"""
        report = {
            "summary": "深度学习命中率增强报告",
            "enhancement_status": "success" if results.get("training", {}).get("success", False) else "failed"
        }
        
        # 训练结果分析
        training = results.get("training", {})
        if training.get("success", False):
            report["training_analysis"] = {
                "method": training.get("method", "unknown"),
                "samples": training.get("samples", 0),
                "performance_metrics": training.get("performance_metrics", {})
            }
        
        # 测试结果分析
        testing = results.get("testing", {})
        if testing.get("success", False):
            performance = testing.get("overall_performance", {})
            report["testing_analysis"] = {
                "test_periods": testing.get("test_periods", 0),
                "avg_confidence": performance.get("avg_confidence", 0),
                "avg_overall_score": performance.get("avg_overall_score", 0),
                "ratio_accuracy": performance.get("ratio_accuracy", 0)
            }
            
            # 评估改进效果
            if performance.get("ratio_accuracy", 0) > 0.5:
                report["improvement_assessment"] = "显著改进"
            elif performance.get("ratio_accuracy", 0) > 0.3:
                report["improvement_assessment"] = "中等改进"
            else:
                report["improvement_assessment"] = "需要进一步优化"
        
        return report
    
    def _save_enhancement_results(self, results: Dict[str, Any]):
        """保存增强结果"""
        try:
            os.makedirs("optimization_results", exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = f"optimization_results/deep_learning_enhancement_{timestamp}.json"
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"📁 增强结果已保存: {result_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存结果失败: {e}")


def main():
    """主函数"""
    print("🚀 深度学习命中率增强器")
    print("=" * 50)
    
    # 创建增强器
    enhancer = DeepLearningHitRateEnhancer()
    
    # 运行综合增强
    results = enhancer.run_comprehensive_enhancement()
    
    # 显示结果摘要
    print("\n📊 增强结果摘要:")
    print("=" * 50)
    
    enhancement_report = results.get("enhancement_report", {})
    print(f"增强状态: {enhancement_report.get('enhancement_status', 'unknown')}")
    
    if "training_analysis" in enhancement_report:
        training = enhancement_report["training_analysis"]
        print(f"训练方法: {training.get('method', 'unknown')}")
        print(f"训练样本: {training.get('samples', 0)}")
    
    if "testing_analysis" in enhancement_report:
        testing = enhancement_report["testing_analysis"]
        print(f"测试期数: {testing.get('test_periods', 0)}")
        print(f"平均置信度: {testing.get('avg_confidence', 0):.3f}")
        print(f"比例准确率: {testing.get('ratio_accuracy', 0):.1%}")
        print(f"改进评估: {enhancement_report.get('improvement_assessment', 'unknown')}")
    
    print("\n🎯 深度学习命中率增强完成！")


if __name__ == "__main__":
    main()
