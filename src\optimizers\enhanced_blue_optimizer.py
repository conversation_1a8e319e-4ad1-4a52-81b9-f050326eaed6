"""
增强版蓝球优化器 - 解决多样性和动态调整问题
专门提升2+1命中率的蓝球预测
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
from collections import Counter, defaultdict
import logging
import random
from dataclasses import dataclass

from src.utils.utils import parse_numbers, calculate_size_ratio_blue


class EnhancedBlueBallOptimizer:
    """增强版蓝球优化器 - 动态多样性预测"""

    def __init__(self, seed: int = None):
        self.logger = logging.getLogger(__name__)

        # 蓝球范围和分界
        self.blue_range = list(range(1, 13))  # 1-12
        self.small_blues = list(range(1, 7))  # 1-6为小
        self.big_blues = list(range(7, 13))  # 7-12为大

        # 动态参数
        self.recent_window = 10  # 最近期数窗口
        self.trend_weight = 0.4  # 趋势权重
        self.frequency_weight = 0.3  # 频率权重
        self.diversity_weight = 0.2  # 多样性权重
        self.random_weight = 0.1  # 随机权重

        # 多样性控制
        self.prediction_history = []  # 预测历史
        self.max_history = 20

        # 随机种子
        self.seed = seed
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)

        # 训练数据
        self.training_data = None
        self.is_trained = False

    def train(self, data: pd.DataFrame) -> None:
        """训练蓝球优化器"""
        try:
            self.logger.info("开始训练增强版蓝球优化器...")

            # 保存训练数据
            self.training_data = data.copy()
            self.is_trained = True

            self.logger.info(f"增强版蓝球优化器训练完成，分析了{len(data)}期数据")

        except Exception as e:
            self.logger.error(f"增强版蓝球优化器训练失败: {e}")
            raise

    def predict_optimal_blues(
        self,
        target_size_ratio: str = None,
        kill_numbers: List[int] = None,
        period_context: Dict = None,
    ) -> List[int]:
        """预测最优蓝球组合 - 动态多样性版本"""
        if not self.is_trained or self.training_data is None:
            return self._fallback_prediction()

        try:
            # 获取最近数据进行分析
            recent_data = self.training_data.head(self.recent_window)

            # 计算动态得分
            ball_scores = self._calculate_dynamic_scores(recent_data, period_context)

            # 应用杀号过滤
            if kill_numbers:
                for kill in kill_numbers:
                    if kill in ball_scores:
                        ball_scores[kill] *= 0.1

            # 应用多样性约束
            ball_scores = self._apply_diversity_constraints(ball_scores)

            # 根据目标大小比选择
            if target_size_ratio:
                result = self._select_by_size_ratio_dynamic(
                    ball_scores, target_size_ratio
                )
            else:
                # 动态选择最优组合
                result = self._select_optimal_combination(ball_scores)

            # 记录预测历史
            self._record_prediction(result)

            return result

        except Exception as e:
            self.logger.error(f"增强版蓝球预测失败: {e}")
            return self._fallback_prediction()

    def _calculate_dynamic_scores(
        self, recent_data: pd.DataFrame, period_context: Dict = None
    ) -> Dict[int, float]:
        """计算动态得分"""
        scores = {ball: 0.0 for ball in self.blue_range}

        # 1. 趋势分析得分
        trend_scores = self._calculate_trend_scores(recent_data)

        # 2. 频率分析得分
        frequency_scores = self._calculate_frequency_scores(recent_data)

        # 3. 模式分析得分
        pattern_scores = self._calculate_pattern_scores(recent_data)

        # 4. 随机扰动得分
        random_scores = self._calculate_random_scores(period_context)

        # 综合得分
        for ball in self.blue_range:
            scores[ball] = (
                trend_scores.get(ball, 0) * self.trend_weight
                + frequency_scores.get(ball, 0) * self.frequency_weight
                + pattern_scores.get(ball, 0) * self.diversity_weight
                + random_scores.get(ball, 0) * self.random_weight
            )

        return scores

    def _calculate_trend_scores(self, recent_data: pd.DataFrame) -> Dict[int, float]:
        """计算趋势得分 - 最近期偏好"""
        scores = {ball: 0.0 for ball in self.blue_range}

        # 分析最近几期的蓝球出现情况
        recent_blues = []
        weights = [1.0, 0.8, 0.6, 0.4, 0.2]  # 越近权重越高

        for i, (_, row) in enumerate(recent_data.iterrows()):
            if i >= len(weights):
                break
            _, blue_balls = parse_numbers(row)
            weight = weights[i]
            for ball in blue_balls:
                scores[ball] += weight

        # 归一化
        max_score = max(scores.values()) if scores.values() else 1
        if max_score > 0:
            scores = {k: v / max_score for k, v in scores.items()}

        return scores

    def _calculate_frequency_scores(
        self, recent_data: pd.DataFrame
    ) -> Dict[int, float]:
        """计算频率得分 - 历史频率分析"""
        scores = {ball: 0.0 for ball in self.blue_range}

        # 统计历史频率
        all_blues = []
        for _, row in recent_data.iterrows():
            _, blue_balls = parse_numbers(row)
            all_blues.extend(blue_balls)

        frequency = Counter(all_blues)
        total = len(all_blues)

        if total > 0:
            for ball in self.blue_range:
                scores[ball] = frequency.get(ball, 0) / total

        return scores

    def _calculate_pattern_scores(self, recent_data: pd.DataFrame) -> Dict[int, float]:
        """计算模式得分 - 基于大小比和间隔模式"""
        scores = {ball: 0.0 for ball in self.blue_range}

        # 分析大小比模式
        size_patterns = []
        for _, row in recent_data.iterrows():
            _, blue_balls = parse_numbers(row)
            big_count, small_count = calculate_size_ratio_blue(blue_balls)
            size_patterns.append((big_count, small_count))

        # 预测下一期可能的大小比
        if size_patterns:
            # 简单的模式识别：如果最近偏向某种模式，给相反模式加分
            recent_pattern = size_patterns[0] if size_patterns else (1, 1)

            if recent_pattern[0] > recent_pattern[1]:  # 最近偏大号
                # 给小号加分
                for ball in self.small_blues:
                    scores[ball] += 0.3
            elif recent_pattern[1] > recent_pattern[0]:  # 最近偏小号
                # 给大号加分
                for ball in self.big_blues:
                    scores[ball] += 0.3
            else:  # 平衡
                # 给所有号码平等机会
                for ball in self.blue_range:
                    scores[ball] += 0.1

        return scores

    def _calculate_random_scores(self, period_context: Dict = None) -> Dict[int, float]:
        """计算随机得分 - 增加预测多样性"""
        scores = {ball: 0.0 for ball in self.blue_range}

        # 基于期号的确定性随机
        if period_context and "current" in period_context:
            period_num = period_context["current"].get("期号", 25068)
            random.seed(int(str(period_num)[-3:]))

        # 给每个号码随机得分
        for ball in self.blue_range:
            scores[ball] = random.random() * 0.5  # 0-0.5的随机得分

        return scores

    def _apply_diversity_constraints(
        self, ball_scores: Dict[int, float]
    ) -> Dict[int, float]:
        """应用多样性约束 - 避免重复预测"""
        if not self.prediction_history:
            return ball_scores

        # 统计最近预测的号码频率
        recent_predictions = self.prediction_history[-5:]  # 最近5次预测
        predicted_freq = Counter()
        for prediction in recent_predictions:
            predicted_freq.update(prediction)

        # 降低频繁预测号码的得分
        adjusted_scores = ball_scores.copy()
        for ball, freq in predicted_freq.items():
            if ball in adjusted_scores:
                penalty = freq * 0.2  # 每次预测降低20%得分
                adjusted_scores[ball] *= 1 - penalty

        return adjusted_scores

    def _select_by_size_ratio_dynamic(
        self, ball_scores: Dict[int, float], target_ratio: str
    ) -> List[int]:
        """根据目标大小比动态选择蓝球"""
        if target_ratio == "2:0":
            # 需要2个大球
            candidates = [
                (ball, score)
                for ball, score in ball_scores.items()
                if ball in self.big_blues
            ]
        elif target_ratio == "0:2":
            # 需要2个小球
            candidates = [
                (ball, score)
                for ball, score in ball_scores.items()
                if ball in self.small_blues
            ]
        else:  # "1:1"
            # 需要1个大球1个小球
            big_candidates = [
                (ball, score)
                for ball, score in ball_scores.items()
                if ball in self.big_blues
            ]
            small_candidates = [
                (ball, score)
                for ball, score in ball_scores.items()
                if ball in self.small_blues
            ]

            # 使用加权随机选择而非总是选最高分
            big_ball = self._weighted_random_select(big_candidates, 1)
            small_ball = self._weighted_random_select(small_candidates, 1)

            return big_ball + small_ball

        # 对于2:0或0:2的情况，也使用加权随机选择
        return self._weighted_random_select(candidates, 2)

    def _select_optimal_combination(self, ball_scores: Dict[int, float]) -> List[int]:
        """选择最优组合 - 平衡得分和多样性"""
        # 将所有候选按得分排序
        candidates = list(ball_scores.items())

        # 使用加权随机选择，偏向高分但保持多样性
        return self._weighted_random_select(candidates, 2)

    def _weighted_random_select(
        self, candidates: List[Tuple[int, float]], count: int
    ) -> List[int]:
        """加权随机选择"""
        if not candidates:
            return random.sample(self.blue_range, min(count, len(self.blue_range)))

        # 确保所有权重为正数
        min_score = min(score for _, score in candidates)
        if min_score < 0:
            candidates = [(ball, score - min_score + 0.1) for ball, score in candidates]

        balls = [ball for ball, _ in candidates]
        weights = [score for _, score in candidates]

        # 归一化权重
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(weights)] * len(weights)

        # 加权随机选择
        try:
            selected = np.random.choice(
                balls, size=min(count, len(balls)), replace=False, p=weights
            )
            return selected.tolist()
        except:
            # 如果加权选择失败，使用简单随机选择
            return random.sample(balls, min(count, len(balls)))

    def _record_prediction(self, prediction: List[int]) -> None:
        """记录预测历史"""
        self.prediction_history.append(prediction.copy())
        if len(self.prediction_history) > self.max_history:
            self.prediction_history.pop(0)

    def _fallback_prediction(self) -> List[int]:
        """备用预测方案 - 随机选择"""
        return random.sample(self.blue_range, 2)

    def get_kill_suggestions(self, period_data: Dict, kill_count: int = 2) -> List[int]:
        """获取蓝球杀号建议 - 动态版本"""
        if not self.is_trained or self.training_data is None:
            return random.sample(self.blue_range, kill_count)

        try:
            # 计算动态得分
            recent_data = self.training_data.head(self.recent_window)
            ball_scores = self._calculate_dynamic_scores(recent_data, period_data)

            # 选择得分最低的作为杀号，但避免杀掉高频号码
            sorted_balls = sorted(ball_scores.items(), key=lambda x: x[1])

            # 安全过滤：避免杀掉历史高频号码
            safe_kills = []
            high_freq_balls = self._get_high_frequency_balls()

            for ball, score in sorted_balls:
                if len(safe_kills) >= kill_count:
                    break
                if ball not in high_freq_balls:  # 不杀高频号码
                    safe_kills.append(ball)

            # 如果安全杀号不够，补充低分号码
            while len(safe_kills) < kill_count:
                for ball, _ in sorted_balls:
                    if ball not in safe_kills:
                        safe_kills.append(ball)
                        break
                if len(safe_kills) >= kill_count:
                    break

            return safe_kills[:kill_count]

        except Exception as e:
            self.logger.error(f"动态蓝球杀号失败: {e}")
            return random.sample(self.blue_range, kill_count)

    def _get_high_frequency_balls(self) -> List[int]:
        """获取历史高频蓝球"""
        if self.training_data is None:
            return []

        all_blues = []
        for _, row in self.training_data.iterrows():
            _, blue_balls = parse_numbers(row)
            all_blues.extend(blue_balls)

        frequency = Counter(all_blues)
        # 选择频率前50%的号码作为高频号码
        sorted_freq = sorted(frequency.items(), key=lambda x: x[1], reverse=True)
        high_freq_count = max(1, len(sorted_freq) // 2)

        return [ball for ball, _ in sorted_freq[:high_freq_count]]
