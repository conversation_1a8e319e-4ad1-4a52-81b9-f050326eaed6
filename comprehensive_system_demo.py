#!/usr/bin/env python3
"""
综合系统演示
展示完整的预测系统：实际预测 + 系统集成 + 算法优化
"""

import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.models.optimized_predictor import OptimizedPredictor
from src.integrations.optimized_predictor_integration import (
    get_integration_manager, 
    create_enhanced_system
)
from src.algorithms.advanced_optimizations import AdvancedOptimizedPredictor
from src.utils.utils import load_data, parse_numbers, calculate_size_ratio_red, ratio_to_state


class ComprehensiveSystemDemo:
    """综合系统演示类"""

    def __init__(self):
        """初始化演示系统"""
        print("🚀 初始化综合预测系统...")
        print("=" * 80)
        
        # 1. 基础优化预测器
        self.basic_predictor = OptimizedPredictor()
        
        # 2. 集成管理器
        self.integration_manager = get_integration_manager()
        
        # 3. 高级优化预测器
        self.advanced_predictor = AdvancedOptimizedPredictor()
        
        # 4. 增强系统
        self.enhanced_system = None
        
        print("✅ 综合预测系统初始化完成")

    def demo_1_basic_prediction(self):
        """演示1：基础优化预测"""
        print("\n" + "=" * 80)
        print("📊 演示1：基础优化预测 (OptimizedPredictor)")
        print("=" * 80)
        
        data = load_data()
        latest_index = len(data) - 1
        latest_row = data.iloc[latest_index]
        
        print(f"📈 数据概况:")
        print(f"   总期数: {len(data)}")
        print(f"   最新期号: {latest_row['期号']}")
        
        # 分析当前状态
        red_balls, blue_balls = parse_numbers(latest_row)
        red_big, red_small = calculate_size_ratio_red(red_balls)
        current_state = ratio_to_state((red_big, red_small))
        
        print(f"   当前红球: {red_balls}")
        print(f"   当前蓝球: {blue_balls}")
        print(f"   当前红球大小比: {current_state}")
        
        # 进行预测
        print(f"\n🔮 基础优化预测:")
        start_time = time.time()
        result = self.basic_predictor.predict_with_insights(latest_index)
        prediction_time = time.time() - start_time
        
        print(f"   预测耗时: {prediction_time:.4f}秒")
        print(f"   预测置信度: {result.get('kill_success_rate', 0.8):.1%}")
        
        predictions = result.get('predictions', {})
        if 'red_size' in predictions:
            red_size = predictions['red_size']
            print(f"   红球大小比预测:")
            for i, (state, prob) in enumerate(red_size[:3], 1):
                print(f"     {i}. {state} (概率: {prob:.1%})")

    def demo_2_system_integration(self):
        """演示2：系统集成"""
        print("\n" + "=" * 80)
        print("🔧 演示2：系统集成 (Enhanced LotteryPredictor)")
        print("=" * 80)
        
        # 创建增强系统
        print("🔄 创建增强版预测系统...")
        self.enhanced_system = create_enhanced_system()
        
        if self.enhanced_system:
            print("✅ 增强系统创建成功")
            
            # 显示集成状态
            status = self.integration_manager.get_integration_status()
            print(f"\n📊 集成状态:")
            print(f"   集成完成: {status['is_integrated']}")
            print(f"   适配器就绪: {status['adapter_ready']}")
            print(f"   优化预测器就绪: {status['optimized_predictor_ready']}")
            
            performance = status['performance_info']
            print(f"\n⚡ 性能信息:")
            print(f"   初始化时间: {performance['initialization_time']}")
            print(f"   预测时间: {performance['prediction_time']}")
            print(f"   内存使用: {performance['memory_usage']}")
            
            # 使用快速预测功能
            print(f"\n🎯 快速预测演示:")
            if hasattr(self.enhanced_system, 'quick_predict'):
                self.enhanced_system.quick_predict()
            else:
                print("⚠️ 快速预测功能不可用")
        else:
            print("❌ 增强系统创建失败")

    def demo_3_advanced_optimization(self):
        """演示3：高级算法优化"""
        print("\n" + "=" * 80)
        print("🧠 演示3：高级算法优化 (AdvancedOptimizedPredictor)")
        print("=" * 80)
        
        data = load_data()
        latest_index = len(data) - 1
        
        print("🔄 运行高级优化算法...")
        start_time = time.time()
        result = self.advanced_predictor.predict_with_advanced_optimization(latest_index)
        total_time = time.time() - start_time
        
        print(f"✅ 高级优化完成 (总耗时: {total_time:.4f}秒)")
        
        print(f"\n🎯 高级优化结果:")
        print(f"   预测期号: {result['period']}")
        print(f"   优化级别: {result['optimization_level']}")
        print(f"   算法预测时间: {result['prediction_time']:.4f}秒")
        
        # 显示预测结果
        red_size_pred = result['predictions']['red_size']
        print(f"\n📈 红球大小比预测 (高级优化):")
        for i, (state, prob) in enumerate(red_size_pred[:3], 1):
            print(f"   {i}. {state} (概率: {prob:.1%})")
        
        # 显示算法权重
        weights = result['algorithm_weights']
        print(f"\n⚖️ 动态算法权重:")
        for alg, weight in weights.items():
            print(f"   {alg}: {weight:.1%}")
        
        # 显示置信度
        confidence = result['confidence_scores']
        print(f"\n📊 特征置信度:")
        for feature, score in confidence.items():
            print(f"   {feature}: {score:.1%}")

    def demo_4_performance_comparison(self):
        """演示4：性能对比"""
        print("\n" + "=" * 80)
        print("⚡ 演示4：性能对比分析")
        print("=" * 80)
        
        data = load_data()
        test_indices = [len(data) - 10, len(data) - 5, len(data) - 1]
        
        print("🔄 进行性能对比测试...")
        
        results = {
            'basic': [],
            'advanced': []
        }
        
        for idx in test_indices:
            # 基础预测器测试
            start_time = time.time()
            basic_result = self.basic_predictor.predict_with_insights(idx)
            basic_time = time.time() - start_time
            results['basic'].append(basic_time)
            
            # 高级预测器测试
            start_time = time.time()
            advanced_result = self.advanced_predictor.predict_with_advanced_optimization(idx)
            advanced_time = time.time() - start_time
            results['advanced'].append(advanced_time)
        
        # 计算平均性能
        avg_basic = sum(results['basic']) / len(results['basic'])
        avg_advanced = sum(results['advanced']) / len(results['advanced'])
        
        print(f"\n📊 性能对比结果:")
        print(f"   基础优化预测器:")
        print(f"     平均预测时间: {avg_basic:.4f}秒")
        print(f"     性能特点: 极快响应，适合实时预测")
        
        print(f"   高级优化预测器:")
        print(f"     平均预测时间: {avg_advanced:.4f}秒")
        print(f"     性能特点: 深度分析，适合精确预测")
        
        if avg_basic > 0 and avg_advanced > avg_basic:
            ratio = avg_advanced / avg_basic
            print(f"   性能差异: 高级版比基础版慢 {ratio:.1f}倍，但提供更深入的分析")
        elif avg_basic == 0:
            print(f"   性能差异: 基础版极快（<0.001秒），高级版提供更深入分析")
        else:
            print(f"   性能差异: 两者性能相近")

    def demo_5_accuracy_validation(self):
        """演示5：准确率验证"""
        print("\n" + "=" * 80)
        print("🎯 演示5：准确率验证")
        print("=" * 80)
        
        data = load_data()
        
        print("🔄 进行准确率验证测试...")
        
        # 测试最近20期的预测准确率
        correct_basic = 0
        correct_advanced = 0
        total_tests = 0
        
        for i in range(20):
            test_index = len(data) - 30 + i
            if test_index >= 0 and test_index < len(data) - 1:
                # 获取实际结果
                actual_row = data.iloc[test_index + 1]
                actual_red, _ = parse_numbers(actual_row)
                actual_red_big, actual_red_small = calculate_size_ratio_red(actual_red)
                actual_state = ratio_to_state((actual_red_big, actual_red_small))
                
                # 基础预测器测试
                try:
                    basic_result = self.basic_predictor.predict_with_insights(test_index)
                    basic_pred = basic_result['predictions']['red_size'][0][0]
                    if basic_pred == actual_state:
                        correct_basic += 1
                except:
                    pass
                
                # 高级预测器测试
                try:
                    advanced_result = self.advanced_predictor.predict_with_advanced_optimization(test_index)
                    advanced_pred = advanced_result['predictions']['red_size'][0][0]
                    if advanced_pred == actual_state:
                        correct_advanced += 1
                except:
                    pass
                
                total_tests += 1
        
        if total_tests > 0:
            basic_accuracy = correct_basic / total_tests
            advanced_accuracy = correct_advanced / total_tests
            
            print(f"\n📈 准确率验证结果 (测试{total_tests}期):")
            print(f"   基础优化预测器: {basic_accuracy:.1%} ({correct_basic}/{total_tests})")
            print(f"   高级优化预测器: {advanced_accuracy:.1%} ({correct_advanced}/{total_tests})")
            
            if advanced_accuracy > basic_accuracy:
                improvement = (advanced_accuracy - basic_accuracy) * 100
                print(f"   准确率提升: +{improvement:.1f}个百分点")
            elif basic_accuracy > advanced_accuracy:
                difference = (basic_accuracy - advanced_accuracy) * 100
                print(f"   准确率差异: -{difference:.1f}个百分点")
            else:
                print(f"   准确率相当")
        else:
            print("❌ 无法进行准确率验证")

    def run_comprehensive_demo(self):
        """运行综合演示"""
        print("🎯 综合预测系统演示")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        try:
            # 运行所有演示
            self.demo_1_basic_prediction()
            self.demo_2_system_integration()
            self.demo_3_advanced_optimization()
            self.demo_4_performance_comparison()
            self.demo_5_accuracy_validation()
            
            # 总结
            print("\n" + "=" * 80)
            print("🎉 综合演示完成！")
            print("=" * 80)
            print("✅ 系统功能验证:")
            print("   ✓ 基础优化预测 - 极快响应")
            print("   ✓ 系统集成 - 无缝兼容")
            print("   ✓ 高级算法优化 - 深度分析")
            print("   ✓ 性能对比 - 量化评估")
            print("   ✓ 准确率验证 - 实际效果")
            
            print("\n🚀 系统已准备好投入使用！")
            print("💡 建议:")
            print("   - 日常预测使用基础优化预测器")
            print("   - 重要预测使用高级优化预测器")
            print("   - 通过集成系统享受完整功能")
            
        except Exception as e:
            print(f"\n❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    demo = ComprehensiveSystemDemo()
    demo.run_comprehensive_demo()


if __name__ == "__main__":
    main()
