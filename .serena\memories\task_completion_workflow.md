# 任务完成工作流程

## 开发完成后的标准流程

### 1. 代码质量检查
```bash
# 代码格式化
black src/ tests/

# 代码风格检查
flake8 src/ tests/

# 类型检查
mypy src/

# 安全检查（可选）
bandit -r src/
```

### 2. 测试执行
```bash
# 运行所有测试
python -m pytest tests/

# 运行单元测试
python -m pytest tests/unit/ -v

# 运行集成测试
python -m pytest tests/integration/ -v

# 生成覆盖率报告
python -m pytest --cov=src --cov-report=html tests/
```

### 3. 功能验证
```bash
# 运行主程序验证
python main.py

# 运行重构版本验证
python -m src.systems.refactored_main

# 运行特定验证脚本
python phase4_validation.py
```

### 4. 性能检查
```bash
# 内存使用分析
python -m memory_profiler main.py

# 性能分析
python -m cProfile -o profile.stats main.py
```

### 5. 文档更新
- 更新README.md（如有新功能）
- 更新API文档（如有接口变更）
- 更新项目结构文档（如有架构变更）
- 添加或更新代码注释

### 6. 版本控制
```bash
# 检查状态
git status

# 添加更改
git add .

# 提交更改
git commit -m "描述性提交信息"

# 推送到远程（需要权限确认）
# git push origin main
```

### 7. 部署准备（如需要）
```bash
# 构建分发包
python setup.py sdist bdist_wheel

# 安装测试
pip install -e .
```

## 质量标准
- 所有测试必须通过
- 代码覆盖率 ≥ 80%
- 无代码风格警告
- 无类型检查错误
- 性能无明显退化