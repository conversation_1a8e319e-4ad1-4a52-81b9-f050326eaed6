#!/usr/bin/env python3
"""
彩票预测系统配置常量
统一管理所有参数和常量
"""

from typing import Dict


class LotteryConfig:
    """彩票预测系统配置常量类"""
    
    # ==================== 球的分类边界 ====================
    # 红球分类
    RED_BALL_SMALL_MIN = 1
    RED_BALL_SMALL_MAX = 17
    RED_BALL_LARGE_MIN = 18
    RED_BALL_LARGE_MAX = 35
    
    # 蓝球分类
    BLUE_BALL_SMALL_MIN = 1
    BLUE_BALL_SMALL_MAX = 6
    BLUE_BALL_LARGE_MIN = 7
    BLUE_BALL_LARGE_MAX = 12
    
    # ==================== 理想分布参数 ====================
    # 红球奇偶比理想分布（基于历史统计）
    IDEAL_RED_ODD_EVEN_DISTRIBUTION: Dict[str, float] = {
        "2:3": 0.42,  # 2个奇球:3个偶球
        "3:2": 0.30,  # 3个奇球:2个偶球
        "1:4": 0.14,  # 1个奇球:4个偶球
        "4:1": 0.12,  # 4个奇球:1个偶球
        "5:0": 0.01,  # 5个奇球:0个偶球
        "0:5": 0.01   # 0个奇球:5个偶球
    }
    
    # 红球大小比理想分布（基于历史统计）
    IDEAL_RED_SIZE_DISTRIBUTION: Dict[str, float] = {
        "2:3": 0.35,  # 2个大球:3个小球
        "3:2": 0.35,  # 3个大球:2个小球
        "1:4": 0.15,  # 1个大球:4个小球
        "4:1": 0.10,  # 4个大球:1个小球
        "0:5": 0.03,  # 0个大球:5个小球
        "5:0": 0.02   # 5个大球:0个小球
    }
    
    # 蓝球奇偶比理想分布
    IDEAL_BLUE_ODD_EVEN_DISTRIBUTION: Dict[str, float] = {
        "1:1": 0.50,  # 1个奇球:1个偶球
        "2:0": 0.25,  # 2个奇球:0个偶球
        "0:2": 0.25   # 0个奇球:2个偶球
    }
    
    # 蓝球大小比理想分布
    IDEAL_BLUE_SIZE_DISTRIBUTION: Dict[str, float] = {
        "1:1": 0.50,  # 1个大球:1个小球
        "2:0": 0.25,  # 2个大球:0个小球
        "0:2": 0.25   # 0个大球:2个小球
    }
    
    # ==================== 预测器策略权重 ====================
    STRATEGY_WEIGHTS: Dict[str, float] = {
        "frequency_based": 0.10,       # 基础频率策略（合并权重）
        "trend_reversal": 0.25,        # 趋势反转策略
        "transition_pattern": 0.25,    # 转移模式策略
        "recent_bias": 0.25,           # 近期偏向策略
        "statistical_balance": 0.10,   # 统计平衡策略
        "seasonal_pattern": 0.025,     # 季节性模式策略
        "cycle_pattern": 0.025         # 周期模式策略
    }
    
    # ==================== 偏向修正参数 ====================
    # 观察窗口大小（用于分析最近的分布）
    BIAS_OBSERVATION_WINDOW = 20
    
    # 偏向修正阈值
    BIAS_CORRECTION_THRESHOLD = 1.2  # 超过理想频率的倍数
    BIAS_CORRECTION_AGGRESSIVE_THRESHOLD = 1.2  # 激进修正阈值
    BIAS_CORRECTION_MILD_THRESHOLD = 1.1  # 轻微修正阈值
    
    # 替代候选阈值
    ALTERNATIVE_CANDIDATE_THRESHOLD = 0.9  # 低于理想频率的倍数
    
    # 置信度调整系数
    CONFIDENCE_REDUCTION_AGGRESSIVE = 0.8  # 激进修正时的置信度折扣
    CONFIDENCE_REDUCTION_MILD = 0.9        # 轻微修正时的置信度折扣
    CONFIDENCE_BOOST_UNDERREPRESENTED = 1.2  # 代表不足时的置信度提升
    
    # ==================== 预测器通用参数 ====================
    # 最小训练数据量
    MIN_TRAINING_DATA = 50
    MIN_TRAINING_DATA_ADVANCED = 100  # 高级策略需要更多数据
    
    # 置信度范围
    MIN_CONFIDENCE = 0.1
    MAX_CONFIDENCE = 0.95
    DEFAULT_CONFIDENCE = 0.4
    
    # 动态权重调整参数
    CONFIDENCE_BONUS_FACTOR = 0.5  # 置信度奖励系数
    METHOD_DIVERSITY_BONUS_MAX = 0.2  # 方法多样性奖励上限
    
    # ==================== 分析参数 ====================
    # 历史分析窗口
    HISTORICAL_ANALYSIS_WINDOW = 200  # 季节性分析窗口
    RECENT_ANALYSIS_WINDOW = 50       # 近期分析窗口
    TREND_ANALYSIS_WINDOW = 10        # 趋势分析窗口
    
    # 周期分析参数
    CYCLE_LENGTHS = [7, 14, 21, 30]   # 分析的周期长度
    
    # ==================== 验证和测试参数 ====================
    # 测试数据集大小
    TEST_RECENT_PERIODS = [10, 20, 30, 50]
    
    # 性能评估阈值
    GOOD_ACCURACY_THRESHOLD = 0.5     # 良好准确率阈值
    EXCELLENT_ACCURACY_THRESHOLD = 0.6  # 优秀准确率阈值
    
    # ==================== 辅助方法 ====================
    @classmethod
    def is_red_ball_small(cls, number: int) -> bool:
        """判断红球是否为小球"""
        return cls.RED_BALL_SMALL_MIN <= number <= cls.RED_BALL_SMALL_MAX
    
    @classmethod
    def is_red_ball_large(cls, number: int) -> bool:
        """判断红球是否为大球"""
        return cls.RED_BALL_LARGE_MIN <= number <= cls.RED_BALL_LARGE_MAX
    
    @classmethod
    def is_blue_ball_small(cls, number: int) -> bool:
        """判断蓝球是否为小球"""
        return cls.BLUE_BALL_SMALL_MIN <= number <= cls.BLUE_BALL_SMALL_MAX
    
    @classmethod
    def is_blue_ball_large(cls, number: int) -> bool:
        """判断蓝球是否为大球"""
        return cls.BLUE_BALL_LARGE_MIN <= number <= cls.BLUE_BALL_LARGE_MAX
    
    @classmethod
    def get_ideal_distribution(cls, ratio_type: str, ball_type: str) -> Dict[str, float]:
        """获取理想分布
        
        Args:
            ratio_type: 'odd_even' 或 'size'
            ball_type: 'red' 或 'blue'
        
        Returns:
            对应的理想分布字典
        """
        distribution_map = {
            ('odd_even', 'red'): cls.IDEAL_RED_ODD_EVEN_DISTRIBUTION,
            ('size', 'red'): cls.IDEAL_RED_SIZE_DISTRIBUTION,
            ('odd_even', 'blue'): cls.IDEAL_BLUE_ODD_EVEN_DISTRIBUTION,
            ('size', 'blue'): cls.IDEAL_BLUE_SIZE_DISTRIBUTION,
        }
        
        return distribution_map.get((ratio_type, ball_type), {})
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置的一致性"""
        # 验证权重总和
        total_weight = sum(cls.STRATEGY_WEIGHTS.values())
        if abs(total_weight - 1.0) > 0.001:
            print(f"警告：策略权重总和为 {total_weight}，不等于1.0")
            return False
        
        # 验证分布总和
        for dist_name, distribution in [
            ("红球奇偶比", cls.IDEAL_RED_ODD_EVEN_DISTRIBUTION),
            ("红球大小比", cls.IDEAL_RED_SIZE_DISTRIBUTION),
            ("蓝球奇偶比", cls.IDEAL_BLUE_ODD_EVEN_DISTRIBUTION),
            ("蓝球大小比", cls.IDEAL_BLUE_SIZE_DISTRIBUTION),
        ]:
            total_prob = sum(distribution.values())
            if abs(total_prob - 1.0) > 0.001:
                print(f"警告：{dist_name}理想分布总和为 {total_prob}，不等于1.0")
                return False
        
        return True


# 验证配置
if __name__ == "__main__":
    if LotteryConfig.validate_config():
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败")
    
    # 显示配置摘要
    print(f"\n📊 配置摘要:")
    print(f"红球范围: 小球{LotteryConfig.RED_BALL_SMALL_MIN}-{LotteryConfig.RED_BALL_SMALL_MAX}, "
          f"大球{LotteryConfig.RED_BALL_LARGE_MIN}-{LotteryConfig.RED_BALL_LARGE_MAX}")
    print(f"蓝球范围: 小球{LotteryConfig.BLUE_BALL_SMALL_MIN}-{LotteryConfig.BLUE_BALL_SMALL_MAX}, "
          f"大球{LotteryConfig.BLUE_BALL_LARGE_MIN}-{LotteryConfig.BLUE_BALL_LARGE_MAX}")
    print(f"偏向修正阈值: {LotteryConfig.BIAS_CORRECTION_THRESHOLD}")
    print(f"观察窗口: {LotteryConfig.BIAS_OBSERVATION_WINDOW}期")
