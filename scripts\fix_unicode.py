#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复main_ultimate.py中的Unicode字符
"""

import re

def fix_unicode_in_file(filepath):
    """修复文件中的Unicode字符"""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换所有emoji和特殊Unicode字符
    replacements = {
        '🚀': '',
        '🎯': '',
        '✅': '',
        '⚠️': '警告:',
        '❌': '错误:',
        '🧪': '',
        '📊': '',
        '🏆': '',
        '📋': '',
        '🔮': '',
    }
    
    for old, new in replacements.items():
        content = content.replace(old, new)
    
    # 写回文件
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已修复 {filepath} 中的Unicode字符")

if __name__ == "__main__":
    fix_unicode_in_file("src/systems/main_ultimate.py")
    fix_unicode_in_file("src/systems/main_ultimate_fixed.py")