#!/usr/bin/env python3
"""
深度学习效率优化适配器
将深度学习效率优化器集成到现有系统中

功能：
1. 适配现有深度学习模型
2. 提供统一的高效训练接口
3. 智能缓存和增量学习管理
4. 性能监控和优化
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging
import pandas as pd
import numpy as np

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.optimizers.dl_efficiency_optimizer import UnifiedDeepLearningInterface


class DeepLearningEfficiencyAdapter:
    """深度学习效率优化适配器"""
    
    def __init__(self):
        self.dl_interface = UnifiedDeepLearningInterface()
        self.logger = logging.getLogger(__name__)
        self.is_initialized = False
        self._register_available_models()
    
    def _register_available_models(self):
        """注册可用的深度学习模型"""
        try:
            # 注册LSTM模型
            try:
                from src.models.deep_learning.lstm_predictor import LSTMPredictor, LSTMConfig
                self.dl_interface.register_model(
                    "lstm", 
                    LSTMPredictor,
                    {
                        'lstm_units': 64,
                        'lstm_layers': 2,
                        'dense_units': 32,
                        'dropout_rate': 0.2,
                        'sequence_length': 10,
                        'learning_rate': 0.001
                    }
                )
                self.logger.info("LSTM模型已注册")
            except ImportError as e:
                self.logger.warning(f"LSTM模型注册失败: {e}")
            
            # 注册Transformer模型
            try:
                from src.models.deep_learning.v4_transformer_predictor import V4TransformerPredictor
                self.dl_interface.register_model(
                    "transformer", 
                    V4TransformerPredictor,
                    {
                        'hidden_dim': 128,
                        'num_heads': 8,
                        'num_layers': 4,
                        'dropout_rate': 0.1,
                        'learning_rate': 0.0001
                    }
                )
                self.logger.info("Transformer模型已注册")
            except ImportError as e:
                self.logger.warning(f"Transformer模型注册失败: {e}")
            
            # 注册多任务神经网络
            try:
                from src.models.deep_learning.multi_task_neural import MultiTaskNeuralPredictor, MultiTaskConfig
                self.dl_interface.register_model(
                    "multi_task", 
                    MultiTaskNeuralPredictor,
                    {
                        'shared_layers': [128, 64],
                        'task_layers': [32, 16],
                        'dropout_rate': 0.2,
                        'learning_rate': 0.001
                    }
                )
                self.logger.info("多任务神经网络已注册")
            except ImportError as e:
                self.logger.warning(f"多任务神经网络注册失败: {e}")
            
            self.is_initialized = True
            self.logger.info("深度学习效率优化适配器初始化完成")
            
        except Exception as e:
            self.logger.error(f"注册深度学习模型失败: {e}")
            self.is_initialized = False
    
    def get_efficient_model(self, model_type: str, data: pd.DataFrame, 
                           force_retrain: bool = False) -> Optional[Any]:
        """获取高效训练的模型"""
        try:
            if not self.is_initialized:
                self.logger.warning("适配器未正确初始化")
                return None
            
            self.logger.info(f"获取高效模型: {model_type}")
            
            # 使用统一接口获取或训练模型
            model = self.dl_interface.get_or_train_model(
                model_type, data, force_retrain
            )
            
            if model:
                self.logger.info(f"成功获取模型: {model_type}")
                return model
            else:
                self.logger.error(f"获取模型失败: {model_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取高效模型失败: {e}")
            return None
    
    def predict_with_best_model(self, data: pd.DataFrame, target_index: int = -1) -> Dict[str, Any]:
        """使用最佳模型进行预测"""
        try:
            predictions = {}
            best_model = None
            best_confidence = 0.0
            
            # 获取所有可用模型的预测结果
            available_models = ["lstm", "transformer", "multi_task"]
            
            for model_type in available_models:
                try:
                    model = self.get_efficient_model(model_type, data)
                    if model and hasattr(model, 'predict'):
                        result = model.predict(data, target_index)
                        if result and hasattr(result, 'confidence'):
                            predictions[model_type] = result
                            if result.confidence > best_confidence:
                                best_confidence = result.confidence
                                best_model = model_type
                        else:
                            self.logger.warning(f"模型 {model_type} 预测结果无效")
                except Exception as e:
                    self.logger.warning(f"模型 {model_type} 预测失败: {e}")
            
            return {
                'predictions': predictions,
                'best_model': best_model,
                'best_confidence': best_confidence,
                'model_count': len(predictions)
            }
            
        except Exception as e:
            self.logger.error(f"最佳模型预测失败: {e}")
            return {'predictions': {}, 'best_model': None, 'best_confidence': 0.0}
    
    def get_efficiency_status(self) -> Dict[str, Any]:
        """获取效率优化状态"""
        try:
            status = self.dl_interface.get_model_status()
            
            # 添加适配器特定信息
            status.update({
                'adapter_initialized': self.is_initialized,
                'optimization_features': [
                    '智能模型缓存',
                    '增量学习管理',
                    '训练效率优化',
                    '统一深度学习接口'
                ]
            })
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取效率状态失败: {e}")
            return {'error': str(e)}
    
    def clear_model_cache(self, model_type: str = None):
        """清理模型缓存"""
        try:
            if model_type:
                self.dl_interface.cache_manager.clear_cache(model_type)
                self.logger.info(f"已清理模型 {model_type} 的缓存")
            else:
                self.dl_interface.clear_all_cache()
                self.logger.info("已清理所有模型缓存")
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
    
    def force_retrain_model(self, model_type: str, data: pd.DataFrame) -> bool:
        """强制重新训练模型"""
        try:
            self.logger.info(f"强制重新训练模型: {model_type}")
            
            # 清理缓存
            self.clear_model_cache(model_type)
            
            # 重新训练
            model = self.get_efficient_model(model_type, data, force_retrain=True)
            
            if model:
                self.logger.info(f"模型 {model_type} 重新训练成功")
                return True
            else:
                self.logger.error(f"模型 {model_type} 重新训练失败")
                return False
                
        except Exception as e:
            self.logger.error(f"强制重新训练失败: {e}")
            return False
    
    def optimize_all_models(self, data: pd.DataFrame) -> Dict[str, bool]:
        """优化所有模型"""
        try:
            results = {}
            available_models = ["lstm", "transformer", "multi_task"]
            
            self.logger.info("开始优化所有深度学习模型...")
            
            for model_type in available_models:
                try:
                    model = self.get_efficient_model(model_type, data)
                    results[model_type] = model is not None
                    
                    if results[model_type]:
                        self.logger.info(f"模型 {model_type} 优化成功")
                    else:
                        self.logger.warning(f"模型 {model_type} 优化失败")
                        
                except Exception as e:
                    self.logger.error(f"模型 {model_type} 优化异常: {e}")
                    results[model_type] = False
            
            success_count = sum(results.values())
            total_count = len(results)
            
            self.logger.info(f"模型优化完成: {success_count}/{total_count} 成功")
            
            return results
            
        except Exception as e:
            self.logger.error(f"优化所有模型失败: {e}")
            return {}


class LegacyDeepLearningWrapper:
    """传统深度学习系统包装器"""
    
    def __init__(self, efficiency_adapter: DeepLearningEfficiencyAdapter):
        self.adapter = efficiency_adapter
        self.logger = logging.getLogger(__name__)
    
    def train_and_predict(self, data: pd.DataFrame, model_type: str = "lstm", 
                         target_index: int = -1) -> Dict[str, Any]:
        """训练并预测（兼容传统接口）"""
        try:
            # 获取高效模型
            model = self.adapter.get_efficient_model(model_type, data)
            
            if not model:
                return {
                    'success': False,
                    'error': f'无法获取模型: {model_type}',
                    'predictions': {}
                }
            
            # 进行预测
            if hasattr(model, 'predict'):
                result = model.predict(data, target_index)
                return {
                    'success': True,
                    'model_type': model_type,
                    'result': result,
                    'cached': not self.adapter.dl_interface.cache_manager.should_retrain(model_type, data)
                }
            else:
                return {
                    'success': False,
                    'error': f'模型 {model_type} 不支持预测',
                    'predictions': {}
                }
                
        except Exception as e:
            self.logger.error(f"训练并预测失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'predictions': {}
            }
    
    def get_ensemble_prediction(self, data: pd.DataFrame, target_index: int = -1) -> Dict[str, Any]:
        """获取集成预测结果"""
        return self.adapter.predict_with_best_model(data, target_index)


# 创建全局实例
def create_dl_efficiency_adapter():
    """创建深度学习效率优化适配器"""
    return DeepLearningEfficiencyAdapter()


def create_legacy_wrapper(adapter: DeepLearningEfficiencyAdapter):
    """创建传统系统包装器"""
    return LegacyDeepLearningWrapper(adapter)


if __name__ == "__main__":
    # 测试代码
    print("[测试] 深度学习效率优化适配器")
    
    # 创建适配器
    adapter = create_dl_efficiency_adapter()
    
    # 显示状态
    status = adapter.get_efficiency_status()
    print(f"[状态] {status}")
    
    # 创建包装器
    wrapper = create_legacy_wrapper(adapter)
    print("[完成] 深度学习效率优化适配器创建成功")