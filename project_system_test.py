#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目选号系统测试
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
import logging

# 设置路径
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_systems():
    """测试项目中的选号系统"""
    
    print("="*60)
    print("项目选号系统可用性测试")
    print("="*60)
    
    # 测试1: NumberSelectionAdapter
    try:
        from systems.number_selection_adapter import NumberSelectionAdapter
        print("✓ NumberSelectionAdapter 导入成功")
    except Exception as e:
        print(f"✗ NumberSelectionAdapter 导入失败: {e}")
    
    # 测试2: MarkovModel
    try:
        from models.markov.markov_model import MarkovModel
        print("✓ MarkovModel 导入成功")
    except Exception as e:
        print(f"✗ MarkovModel 导入失败: {e}")
    
    # 测试3: EnhancedMarkovChain
    try:
        from algorithms.enhanced_markov_chain import EnhancedMarkovChain
        print("✓ EnhancedMarkovChain 导入成功")
    except Exception as e:
        print(f"✗ EnhancedMarkovChain 导入失败: {e}")
    
    # 测试4: DeepLearningSelector
    try:
        from systems.deep_learning_selector import DeepLearningSelector
        print("✓ DeepLearningSelector 导入成功")
    except Exception as e:
        print(f"✗ DeepLearningSelector 导入失败: {e}")
    
    # 测试5: TraditionalMLSelector
    try:
        from systems.traditional_ml_selector import TraditionalMLSelector
        print("✓ TraditionalMLSelector 导入成功")
    except Exception as e:
        print(f"✗ TraditionalMLSelector 导入失败: {e}")
    
    # 测试6: PerformanceComparator
    try:
        from systems.performance_comparator import PerformanceComparator
        print("✓ PerformanceComparator 导入成功")
    except Exception as e:
        print(f"✗ PerformanceComparator 导入失败: {e}")
    
    print("\n" + "="*60)
    print("建议使用的选号系统分析结果:")
    print("="*60)
    
    print("\n基于前面的性能测试结果:")
    print("1. 马尔可夫链选号器 - 平均命中率 17-18%")
    print("2. 趋势分析选号器 - 平均命中率 15-17%") 
    print("3. 随机选号器 - 平均命中率 11-18% (波动较大)")
    print("4. 频率分析选号器 - 平均命中率 11-12%")
    
    print("\n推荐使用顺序:")
    print("1. 优先: 项目中的EnhancedMarkovChain (如果可用)")
    print("2. 备选: 简化的马尔可夫选号器")
    print("3. 辅助: 趋势分析选号器")
    print("4. 基准: 随机选号器 (用于对比)")
    
    print("\n实际应用建议:")
    print("- 马尔可夫方法在多次测试中表现稳定")
    print("- 建议结合多种方法进行集成预测")
    print("- 定期评估和调整算法参数")
    print("- 保持理性，彩票预测仅供参考")

if __name__ == "__main__":
    test_systems()