"""
集成预测器
融合多个特征和模型的预测结果
"""

import numpy as np
from typing import Dict, List, Tuple, Any
from collections import defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.analyzer import LotteryAnalyzer
from src.models.markov.markov_model import MarkovModel
from src.models.bayes.bayes_selector import BayesSelector


class EnsemblePredictor:
    """集成预测器"""

    def __init__(self, feature_type: str):
        """
        初始化集成预测器

        Args:
            feature_type: 特征类型 ('red' 或 'blue')
        """
        self.feature_type = feature_type

        # 多个马尔科夫模型（不同阶数）
        self.markov_models = {
            "order1": MarkovModel(feature_type, order=1),
            "order2": MarkovModel(feature_type, order=2),
        }

        # 多个贝叶斯选择器（不同特征）
        self.bayes_selectors = {}

        # 特征重要性权重
        self.feature_weights = {}

        # 模型性能历史
        self.model_performance = defaultdict(list)

    def train_ensemble(self, analyzer: LotteryAnalyzer) -> None:
        """
        训练集成模型

        Args:
            analyzer: 数据分析器
        """
        # 获取特征重要性
        importance_scores = analyzer.get_feature_importance_scores()

        # 确定要使用的特征
        if self.feature_type == "red":
            primary_features = ["red_odd_even", "red_size"]
            secondary_features = [
                "red_sum_range",
                "red_span",
                "red_consecutive",
                "red_ac_value",
                "red_zone_distribution",
                "red_tail_pattern",
                "red_prime_count",
                "red_fibonacci",
                "red_repeat_pattern",
            ]
        else:
            primary_features = ["blue_size"]
            secondary_features = ["blue_sum_range", "blue_gap"]

        all_features = primary_features + secondary_features

        # 训练马尔科夫模型
        for feature in all_features:
            if feature in analyzer.features:
                sequence = analyzer.get_feature_sequence(feature)
                if len(sequence) > 10:  # 确保有足够的数据
                    # 训练不同阶数的模型
                    for order_name, model in self.markov_models.items():
                        try:
                            model.train(sequence)
                        except:
                            continue

        # 设置特征权重
        self.feature_weights = {}
        for feature in all_features:
            if feature in importance_scores:
                # 主要特征给予更高权重
                base_weight = importance_scores[feature]
                if feature in primary_features:
                    self.feature_weights[feature] = base_weight * 2.0
                else:
                    self.feature_weights[feature] = base_weight
            else:
                self.feature_weights[feature] = 0.1

        # 归一化权重
        total_weight = sum(self.feature_weights.values())
        if total_weight > 0:
            for feature in self.feature_weights:
                self.feature_weights[feature] /= total_weight

        # 初始化贝叶斯选择器
        for feature in all_features:
            if feature in analyzer.features:
                selector = BayesSelector(self.feature_type)

                # 设置先验概率
                historical_freq = analyzer.calculate_state_frequencies(feature)
                recent_freq = analyzer.analyze_state_trends(feature, window=15)

                selector.set_prior_probabilities(
                    historical_freq, recent_freq, recent_weight=0.4
                )
                self.bayes_selectors[feature] = selector

    def predict_ensemble(
        self, analyzer: LotteryAnalyzer, current_states: Dict[str, str]
    ) -> Dict[str, Tuple[str, float]]:
        """
        集成预测

        Args:
            analyzer: 数据分析器
            current_states: 当前各特征状态

        Returns:
            Dict[str, Tuple[str, float]]: 各特征的预测结果
        """
        predictions = {}

        # 确定要预测的特征
        if self.feature_type == "red":
            target_features = ["red_odd_even", "red_size"]
        else:
            target_features = ["blue_size"]

        for target_feature in target_features:
            if target_feature not in current_states:
                continue

            # 收集所有模型的预测
            all_predictions = []
            all_weights = []
            all_confidences = []

            # 马尔科夫模型预测
            current_state = current_states[target_feature]
            recent_states = analyzer.get_recent_states(target_feature, n=5)
            recent_states = [current_state] + recent_states

            for model_name, model in self.markov_models.items():
                if model.is_trained:
                    try:
                        state_probs = model.get_state_probabilities(recent_states)
                        confidence = model.calculate_model_confidence()

                        all_predictions.append(state_probs)
                        all_weights.append(1.0)  # 基础权重
                        all_confidences.append(confidence)
                    except:
                        continue

            # 相关特征的影响
            for feature, weight in self.feature_weights.items():
                if (
                    feature != target_feature
                    and feature in self.bayes_selectors
                    and feature in current_states
                ):

                    try:
                        # 计算特征相关性
                        correlation = analyzer.get_feature_correlation(
                            target_feature, feature
                        )

                        if correlation > 0.3:  # 只考虑相关性较高的特征
                            selector = self.bayes_selectors[feature]
                            feature_state = current_states[feature]

                            # 基于相关特征的状态推断目标特征
                            influence_probs = self._calculate_feature_influence(
                                feature, feature_state, target_feature, analyzer
                            )

                            if influence_probs:
                                all_predictions.append(influence_probs)
                                all_weights.append(weight * correlation)
                                all_confidences.append(0.7)  # 中等置信度
                    except:
                        continue

            # 集成预测结果
            if all_predictions and target_feature in self.bayes_selectors:
                try:
                    selector = self.bayes_selectors[target_feature]
                    final_state, final_prob = selector.select_best_state(
                        all_predictions, all_weights, all_confidences
                    )
                    predictions[target_feature] = (final_state, final_prob)
                except:
                    # 备选方案：使用最简单的马尔科夫预测
                    if self.markov_models["order1"].is_trained:
                        try:
                            pred_state, pred_prob = self.markov_models[
                                "order1"
                            ].predict_next_state([current_state])
                            predictions[target_feature] = (pred_state, pred_prob)
                        except:
                            predictions[target_feature] = (current_state, 0.5)
                    else:
                        predictions[target_feature] = (current_state, 0.5)

        return predictions

    def _calculate_feature_influence(
        self,
        source_feature: str,
        source_state: str,
        target_feature: str,
        analyzer: LotteryAnalyzer,
    ) -> Dict[str, float]:
        """
        计算特征间的影响概率

        Args:
            source_feature: 源特征
            source_state: 源特征状态
            target_feature: 目标特征
            analyzer: 分析器

        Returns:
            Dict[str, float]: 目标特征各状态的概率
        """
        try:
            source_sequence = analyzer.get_feature_sequence(source_feature)
            target_sequence = analyzer.get_feature_sequence(target_feature)

            if (
                len(source_sequence) != len(target_sequence)
                or len(source_sequence) == 0
            ):
                return {}

            # 统计条件概率：P(target_state | source_state)
            conditional_counts = defaultdict(int)
            source_state_count = 0

            for i in range(len(source_sequence)):
                if source_sequence[i] == source_state:
                    source_state_count += 1
                    conditional_counts[target_sequence[i]] += 1

            if source_state_count == 0:
                return {}

            # 计算条件概率
            conditional_probs = {}
            for target_state, count in conditional_counts.items():
                conditional_probs[target_state] = count / source_state_count

            # 确保所有可能的目标状态都有概率
            if self.feature_type == "red":
                if self.feature_name == "red_odd_even":
                    from src.utils.utils import get_all_red_odd_even_states
                    all_states = get_all_red_odd_even_states()
                else:  # red_size
                    from src.utils.utils import get_all_red_size_states
                    all_states = get_all_red_size_states()
            else:
                if self.feature_name == "blue_odd_even":
                    from src.utils.utils import get_all_blue_odd_even_states
                    all_states = get_all_blue_odd_even_states()
                else:  # blue_size
                    from src.utils.utils import get_all_blue_size_states
                    all_states = get_all_blue_size_states()

            for state in all_states:
                if state not in conditional_probs:
                    conditional_probs[state] = 0.01  # 最小概率

            return conditional_probs

        except Exception as e:
            return {}

    def update_performance(
        self, feature: str, predicted_state: str, actual_state: str
    ) -> None:
        """
        更新模型性能记录

        Args:
            feature: 特征名称
            predicted_state: 预测状态
            actual_state: 实际状态
        """
        is_correct = predicted_state == actual_state
        self.model_performance[feature].append(is_correct)

        # 只保留最近50次的记录
        if len(self.model_performance[feature]) > 50:
            self.model_performance[feature] = self.model_performance[feature][-50:]

    def get_model_accuracy(self, feature: str) -> float:
        """
        获取模型准确率

        Args:
            feature: 特征名称

        Returns:
            float: 准确率
        """
        if feature not in self.model_performance or not self.model_performance[feature]:
            return 0.5

        correct_predictions = sum(self.model_performance[feature])
        total_predictions = len(self.model_performance[feature])

        return correct_predictions / total_predictions

    def get_ensemble_confidence(self) -> float:
        """
        获取集成模型的整体置信度

        Returns:
            float: 置信度分数
        """
        if not self.model_performance:
            return 0.5

        all_accuracies = []
        for feature_performance in self.model_performance.values():
            if feature_performance:
                accuracy = sum(feature_performance) / len(feature_performance)
                all_accuracies.append(accuracy)

        if not all_accuracies:
            return 0.5

        return sum(all_accuracies) / len(all_accuracies)

    def adaptive_weight_adjustment(self) -> None:
        """
        自适应权重调整
        根据历史表现动态调整特征权重
        """
        if not self.model_performance:
            return

        # 根据准确率调整权重
        for feature in self.feature_weights:
            if feature in self.model_performance:
                accuracy = self.get_model_accuracy(feature)

                # 准确率高的特征增加权重，准确率低的减少权重
                adjustment_factor = 1.0 + (accuracy - 0.5) * 0.5
                self.feature_weights[feature] *= adjustment_factor

        # 重新归一化权重
        total_weight = sum(self.feature_weights.values())
        if total_weight > 0:
            for feature in self.feature_weights:
                self.feature_weights[feature] /= total_weight
