#!/usr/bin/env python3
"""
增强大小比预测器
基于分析结果优化红球大小比和蓝球大小比预测
"""

import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class EnhancedSizeRatioPredictor:
    """增强大小比预测器 - 分别优化红球和蓝球大小比预测"""

    def __init__(self):
        self.name = "增强大小比预测器"

        # 红球大小比历史权重（基于分析结果）
        self.red_state_weights = {
            "3:2": 0.334,  # 历史频率33.4%
            "2:3": 0.307,  # 历史频率30.7%
            "4:1": 0.167,  # 历史频率16.7%
            "1:4": 0.133,  # 历史频率13.3%
            "5:0": 0.035,  # 历史频率3.5%
            "0:5": 0.023,  # 历史频率2.3%
        }

        # 蓝球大小比历史权重（基于分析结果）
        self.blue_state_weights = {
            "1:1": 0.551,  # 历史频率55.1%
            "0:2": 0.227,  # 历史频率22.7%
            "2:0": 0.221,  # 历史频率22.1%
        }

        # 红球大小比转移矩阵（基于分析结果）
        self.red_transition_matrix = {
            "3:2": {
                "3:2": 0.334,
                "2:3": 0.292,
                "4:1": 0.186,
                "1:4": 0.138,
                "5:0": 0.036,
                "0:5": 0.014,
            },
            "2:3": {
                "3:2": 0.321,
                "2:3": 0.302,
                "4:1": 0.161,
                "1:4": 0.154,
                "5:0": 0.033,
                "0:5": 0.030,
            },
            "4:1": {
                "3:2": 0.347,
                "2:3": 0.295,
                "4:1": 0.199,
                "1:4": 0.084,
                "5:0": 0.044,
                "0:5": 0.032,
            },
            "1:4": {
                "2:3": 0.365,
                "3:2": 0.350,
                "4:1": 0.130,
                "1:4": 0.120,
                "5:0": 0.025,
                "0:5": 0.010,
            },
            "5:0": {
                "3:2": 0.346,
                "2:3": 0.327,
                "4:1": 0.154,
                "1:4": 0.115,
                "0:5": 0.058,
                "5:0": 0.000,
            },
            "0:5": {
                "2:3": 0.343,
                "3:2": 0.286,
                "1:4": 0.257,
                "5:0": 0.086,
                "0:5": 0.029,
                "4:1": 0.000,
            },
        }

        # 蓝球大小比转移矩阵（基于分析结果）
        self.blue_transition_matrix = {
            "1:1": {"1:1": 0.557, "0:2": 0.230, "2:0": 0.213},
            "0:2": {"1:1": 0.589, "2:0": 0.205, "0:2": 0.205},
            "2:0": {"1:1": 0.497, "2:0": 0.259, "0:2": 0.244},
        }

        # 红球预测权重（马尔可夫表现最佳）
        self.red_markov_weight = 0.6
        self.red_frequency_weight = 0.4

        # 蓝球预测权重（频率/马尔可夫/组合都很好）
        self.blue_markov_weight = 0.5
        self.blue_frequency_weight = 0.5

        # 训练状态
        self.is_trained = False
        self.red_recent_states = []
        self.blue_recent_states = []

    def train(self, red_state_sequence: List[str], blue_state_sequence: List[str]):
        """
        训练预测器

        Args:
            red_state_sequence: 红球大小比历史状态序列
            blue_state_sequence: 蓝球大小比历史状态序列
        """
        self.red_recent_states = red_state_sequence[-50:]  # 保留最近50期状态
        self.blue_recent_states = blue_state_sequence[-50:]
        self.is_trained = True

        # 动态调整权重（基于近期数据）
        if len(self.red_recent_states) >= 20:
            recent_red_counts = Counter(self.red_recent_states[-20:])
            total_recent = len(self.red_recent_states[-20:])

            # 更新红球权重
            for state in self.red_state_weights:
                recent_freq = recent_red_counts.get(state, 0) / total_recent
                historical_freq = self.red_state_weights[state]
                # 加权平均：70%历史 + 30%近期
                self.red_state_weights[state] = (
                    historical_freq * 0.7 + recent_freq * 0.3
                )

        if len(self.blue_recent_states) >= 20:
            recent_blue_counts = Counter(self.blue_recent_states[-20:])
            total_recent = len(self.blue_recent_states[-20:])

            # 更新蓝球权重
            for state in self.blue_state_weights:
                recent_freq = recent_blue_counts.get(state, 0) / total_recent
                historical_freq = self.blue_state_weights[state]
                # 加权平均：70%历史 + 30%近期
                self.blue_state_weights[state] = (
                    historical_freq * 0.7 + recent_freq * 0.3
                )

    def predict_red_size(self, current_state: str = None) -> Tuple[str, float]:
        """
        预测红球大小比

        Args:
            current_state: 当前红球大小比状态

        Returns:
            Tuple[str, float]: (预测状态, 置信度)
        """
        if not self.is_trained:
            return self._predict_red_by_frequency()

        # 马尔可夫预测（表现最佳）
        markov_pred = self._predict_red_by_markov(current_state)
        frequency_pred = self._predict_red_by_frequency()

        # 融合预测结果
        final_probs = {}
        all_red_states = ["5:0", "4:1", "3:2", "2:3", "1:4", "0:5"]

        for state in all_red_states:
            prob = (
                markov_pred[1].get(state, 0) * self.red_markov_weight
                + frequency_pred[1].get(state, 0) * self.red_frequency_weight
            )
            final_probs[state] = prob

        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v / total_prob for k, v in final_probs.items()}

        # 选择最高概率的状态
        best_state = max(final_probs.items(), key=lambda x: x[1])
        return best_state[0], best_state[1]

    def predict_blue_size(self, current_state: str = None) -> Tuple[str, float]:
        """
        预测蓝球大小比

        Args:
            current_state: 当前蓝球大小比状态

        Returns:
            Tuple[str, float]: (预测状态, 置信度)
        """
        if not self.is_trained:
            return self._predict_blue_by_frequency()

        # 马尔可夫和频率预测（都表现很好）
        markov_pred = self._predict_blue_by_markov(current_state)
        frequency_pred = self._predict_blue_by_frequency()

        # 融合预测结果
        final_probs = {}
        all_blue_states = ["2:0", "1:1", "0:2"]

        for state in all_blue_states:
            prob = (
                markov_pred[1].get(state, 0) * self.blue_markov_weight
                + frequency_pred[1].get(state, 0) * self.blue_frequency_weight
            )
            final_probs[state] = prob

        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v / total_prob for k, v in final_probs.items()}

        # 选择最高概率的状态
        best_state = max(final_probs.items(), key=lambda x: x[1])
        return best_state[0], best_state[1]

    def _predict_red_by_markov(
        self, current_state: str
    ) -> Tuple[str, Dict[str, float]]:
        """基于马尔可夫链预测红球大小比"""
        if not current_state or current_state not in self.red_transition_matrix:
            return self._predict_red_by_frequency()

        probs = self.red_transition_matrix[current_state].copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs

    def _predict_blue_by_markov(
        self, current_state: str
    ) -> Tuple[str, Dict[str, float]]:
        """基于马尔可夫链预测蓝球大小比"""
        if not current_state or current_state not in self.blue_transition_matrix:
            return self._predict_blue_by_frequency()

        probs = self.blue_transition_matrix[current_state].copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs

    def _predict_red_by_frequency(self) -> Tuple[str, Dict[str, float]]:
        """基于历史频率预测红球大小比"""
        probs = self.red_state_weights.copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs

    def _predict_blue_by_frequency(self) -> Tuple[str, Dict[str, float]]:
        """基于历史频率预测蓝球大小比"""
        probs = self.blue_state_weights.copy()
        best_state = max(probs.items(), key=lambda x: x[1])
        return best_state[0], probs

    def predict_red_top_k(
        self, current_state: str = None, k: int = 2
    ) -> List[Tuple[str, float]]:
        """
        预测红球大小比前K个最可能的状态

        Args:
            current_state: 当前状态
            k: 返回前K个预测

        Returns:
            List[Tuple[str, float]]: 前K个预测结果
        """
        if not self.is_trained:
            freq_pred = self._predict_red_by_frequency()
            return [(freq_pred[0], freq_pred[1])]

        # 获取所有状态的概率
        markov_pred = self._predict_red_by_markov(current_state)
        frequency_pred = self._predict_red_by_frequency()

        # 融合预测结果
        final_probs = {}
        all_red_states = ["5:0", "4:1", "3:2", "2:3", "1:4", "0:5"]

        for state in all_red_states:
            prob = (
                markov_pred[1].get(state, 0) * self.red_markov_weight
                + frequency_pred[1].get(state, 0) * self.red_frequency_weight
            )
            final_probs[state] = prob

        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v / total_prob for k, v in final_probs.items()}

        # 返回前K个
        sorted_probs = sorted(final_probs.items(), key=lambda x: x[1], reverse=True)
        return sorted_probs[:k]

    def predict_blue_top_k(
        self, current_state: str = None, k: int = 2
    ) -> List[Tuple[str, float]]:
        """
        预测蓝球大小比前K个最可能的状态

        Args:
            current_state: 当前状态
            k: 返回前K个预测

        Returns:
            List[Tuple[str, float]]: 前K个预测结果
        """
        if not self.is_trained:
            freq_pred = self._predict_blue_by_frequency()
            return [(freq_pred[0], freq_pred[1])]

        # 获取所有状态的概率
        markov_pred = self._predict_blue_by_markov(current_state)
        frequency_pred = self._predict_blue_by_frequency()

        # 融合预测结果
        final_probs = {}
        all_blue_states = ["2:0", "1:1", "0:2"]

        for state in all_blue_states:
            prob = (
                markov_pred[1].get(state, 0) * self.blue_markov_weight
                + frequency_pred[1].get(state, 0) * self.blue_frequency_weight
            )
            final_probs[state] = prob

        # 归一化
        total_prob = sum(final_probs.values())
        if total_prob > 0:
            final_probs = {k: v / total_prob for k, v in final_probs.items()}

        # 返回前K个
        sorted_probs = sorted(final_probs.items(), key=lambda x: x[1], reverse=True)
        return sorted_probs[:k]

    def get_prediction_explanation(
        self, red_current_state: str = None, blue_current_state: str = None
    ) -> Dict:
        """获取预测解释"""
        if not self.is_trained:
            return {"strategy": "frequency", "confidence": "low", "reason": "未训练"}

        red_markov_pred = self._predict_red_by_markov(red_current_state)
        blue_markov_pred = self._predict_blue_by_markov(blue_current_state)

        return {
            "red_strategy": "markov_frequency",
            "blue_strategy": "markov_frequency",
            "red_markov_prediction": red_markov_pred[0],
            "blue_markov_prediction": blue_markov_pred[0],
            "red_current_state": red_current_state,
            "blue_current_state": blue_current_state,
            "red_recent_pattern": (
                self.red_recent_states[-5:]
                if len(self.red_recent_states) >= 5
                else self.red_recent_states
            ),
            "blue_recent_pattern": (
                self.blue_recent_states[-5:]
                if len(self.blue_recent_states) >= 5
                else self.blue_recent_states
            ),
            "confidence": "medium" if len(self.red_recent_states) >= 20 else "low",
        }
