#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版马尔科夫链预测算法

主要改进:
1. 多阶马尔科夫链支持
2. 自适应状态定义
3. 动态权重调整
4. 状态转换概率优化
5. 集成多种状态表示方法
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from collections import defaultdict, Counter
from dataclasses import dataclass
import logging
from ..utils.utils import (
    parse_numbers,
    calculate_odd_even_ratio,
    calculate_size_ratio_red,
    calculate_size_ratio_blue,
    ratio_to_state,
    state_to_ratio,
)


@dataclass
class MarkovState:
    """马尔科夫状态定义"""

    state_id: str
    state_type: str  # 'ratio', 'pattern', 'sequence', 'hybrid'
    state_value: Any
    frequency: int = 0
    last_occurrence: int = 0


class EnhancedMarkovChain:
    """增强版马尔科夫链预测器"""

    def __init__(self, order: int = 2, state_types: List[str] = None):
        """
        初始化增强版马尔科夫链

        Args:
            order: 马尔科夫链阶数 (1-3)
            state_types: 状态类型列表 ['ratio', 'pattern', 'sequence', 'hybrid']
        """
        self.order = max(1, min(3, order))  # 限制阶数在1-3之间
        self.state_types = state_types or ["ratio", "pattern", "hybrid"]

        # 状态管理
        self.states = {}  # state_id -> MarkovState
        self.transition_matrices = {}  # state_type -> transition_matrix
        self.state_sequences = {}  # state_type -> List[state_id]

        # 性能跟踪
        self.prediction_history = []
        self.accuracy_by_type = defaultdict(list)

        # 自适应参数
        self.state_weights = defaultdict(float)
        self.transition_smoothing = 0.01  # 拉普拉斯平滑

        self.logger = logging.getLogger(__name__)

    def fit(self, data: pd.DataFrame) -> None:
        """训练马尔科夫链模型"""
        if len(data) < self.order + 1:
            self.logger.warning(f"数据量不足，需要至少 {self.order + 1} 期数据")
            return

        # 为每种状态类型构建状态序列
        for state_type in self.state_types:
            self._build_state_sequence(data, state_type)
            self._build_transition_matrix(state_type)

        # 计算状态权重
        self._calculate_state_weights()

        self.logger.info(f"马尔科夫链训练完成，支持状态类型: {self.state_types}")

    def _build_state_sequence(self, data: pd.DataFrame, state_type: str) -> None:
        """构建指定类型的状态序列"""
        sequence = []

        for idx, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)

            if state_type == "ratio":
                state_id = self._get_ratio_state(red_balls, blue_balls)
            elif state_type == "pattern":
                state_id = self._get_pattern_state(red_balls, blue_balls)
            elif state_type == "sequence":
                state_id = self._get_sequence_state(red_balls, blue_balls)
            elif state_type == "hybrid":
                state_id = self._get_hybrid_state(red_balls, blue_balls)
            else:
                continue

            sequence.append(state_id)

            # 更新状态信息
            if state_id not in self.states:
                self.states[state_id] = MarkovState(
                    state_id=state_id,
                    state_type=state_type,
                    state_value=self._extract_state_value(state_id, state_type),
                )

            self.states[state_id].frequency += 1
            self.states[state_id].last_occurrence = idx

        self.state_sequences[state_type] = sequence

    def _get_ratio_state(self, red_balls: List[int], blue_balls: List[int]) -> str:
        """获取比值状态"""
        odd_even_ratio = calculate_odd_even_ratio(red_balls)
        size_ratio_red = calculate_size_ratio_red(red_balls)
        size_ratio_blue = calculate_size_ratio_blue(blue_balls)

        odd_even_state = ratio_to_state(odd_even_ratio, "odd_even")
        size_red_state = ratio_to_state(size_ratio_red, "size_red")
        size_blue_state = ratio_to_state(size_ratio_blue, "size_blue")

        return f"ratio_{odd_even_state}_{size_red_state}_{size_blue_state}"

    def _get_pattern_state(self, red_balls: List[int], blue_balls: List[int]) -> str:
        """获取模式状态"""
        # 连号模式
        consecutive_count = self._count_consecutive(red_balls)

        # 跨度模式
        span = max(red_balls) - min(red_balls)
        span_level = "small" if span < 15 else "medium" if span < 25 else "large"

        # 和值模式
        total_sum = sum(red_balls)
        sum_level = "low" if total_sum < 90 else "medium" if total_sum < 130 else "high"

        return f"pattern_{consecutive_count}_{span_level}_{sum_level}"

    def _get_sequence_state(self, red_balls: List[int], blue_balls: List[int]) -> str:
        """获取序列状态"""
        # 基于号码序列的特征
        sorted_reds = sorted(red_balls)

        # 间隔特征
        gaps = [
            sorted_reds[i + 1] - sorted_reds[i] for i in range(len(sorted_reds) - 1)
        ]
        avg_gap = sum(gaps) / len(gaps)
        gap_variance = np.var(gaps)

        gap_level = "uniform" if gap_variance < 2 else "varied"
        avg_level = "small" if avg_gap < 4 else "medium" if avg_gap < 7 else "large"

        return f"sequence_{gap_level}_{avg_level}_{len(blue_balls)}"

    def _get_hybrid_state(self, red_balls: List[int], blue_balls: List[int]) -> str:
        """获取混合状态"""
        # 结合多种特征的混合状态
        odd_count = sum(1 for x in red_balls if x % 2 == 1)
        size_count = sum(1 for x in red_balls if x > 17)

        # 区间分布
        zones = [0, 0, 0]  # 1-12, 13-24, 25-35
        for num in red_balls:
            if num <= 12:
                zones[0] += 1
            elif num <= 24:
                zones[1] += 1
            else:
                zones[2] += 1

        dominant_zone = zones.index(max(zones))
        blue_parity = "odd" if blue_balls[0] % 2 == 1 else "even"

        return f"hybrid_{odd_count}_{size_count}_{dominant_zone}_{blue_parity}"

    def _extract_state_value(self, state_id: str, state_type: str) -> Any:
        """提取状态值"""
        parts = state_id.split("_")[1:]  # 去掉类型前缀

        if state_type == "ratio":
            return {"odd_even": parts[0], "size_red": parts[1], "size_blue": parts[2]}
        elif state_type == "pattern":
            return {
                "consecutive": int(parts[0]),
                "span": parts[1],
                "sum_level": parts[2],
            }
        elif state_type == "sequence":
            return {
                "gap_pattern": parts[0],
                "avg_gap": parts[1],
                "blue_count": int(parts[2]),
            }
        elif state_type == "hybrid":
            return {
                "odd_count": int(parts[0]),
                "size_count": int(parts[1]),
                "dominant_zone": int(parts[2]),
                "blue_parity": parts[3],
            }

        return parts

    def _count_consecutive(self, numbers: List[int]) -> int:
        """计算连号数量"""
        sorted_nums = sorted(numbers)
        consecutive = 0
        current_streak = 1

        for i in range(1, len(sorted_nums)):
            if sorted_nums[i] == sorted_nums[i - 1] + 1:
                current_streak += 1
            else:
                consecutive = max(consecutive, current_streak)
                current_streak = 1

        return max(consecutive, current_streak)

    def _build_transition_matrix(self, state_type: str) -> None:
        """构建转换矩阵"""
        sequence = self.state_sequences.get(state_type, [])
        if len(sequence) < self.order + 1:
            return

        # 收集所有状态
        unique_states = list(set(sequence))
        state_to_idx = {state: idx for idx, state in enumerate(unique_states)}

        # 初始化转换矩阵
        n_states = len(unique_states)
        transition_matrix = np.zeros((n_states, n_states))

        # 统计转换
        for i in range(len(sequence) - self.order):
            current_states = sequence[i : i + self.order]
            next_state = sequence[i + self.order]

            # 对于高阶马尔科夫链，使用状态组合
            if self.order == 1:
                current_idx = state_to_idx[current_states[0]]
            else:
                # 多阶状态组合 (简化处理)
                current_idx = state_to_idx[current_states[-1]]  # 使用最近状态

            next_idx = state_to_idx[next_state]
            transition_matrix[current_idx][next_idx] += 1

        # 拉普拉斯平滑
        transition_matrix += self.transition_smoothing

        # 归一化
        row_sums = transition_matrix.sum(axis=1, keepdims=True)
        row_sums[row_sums == 0] = 1  # 避免除零
        transition_matrix = transition_matrix / row_sums

        self.transition_matrices[state_type] = {
            "matrix": transition_matrix,
            "state_to_idx": state_to_idx,
            "idx_to_state": {idx: state for state, idx in state_to_idx.items()},
        }

    def _calculate_state_weights(self) -> None:
        """计算状态权重"""
        for state_type in self.state_types:
            sequence = self.state_sequences.get(state_type, [])
            if not sequence:
                continue

            # 基于频率和最近性计算权重
            state_counts = Counter(sequence)
            total_count = len(sequence)

            for state_id, count in state_counts.items():
                frequency_weight = count / total_count

                # 最近性权重 (最近出现的状态权重更高)
                last_occurrence = self.states[state_id].last_occurrence
                recency_weight = 1.0 / (total_count - last_occurrence + 1)

                # 综合权重
                self.state_weights[state_id] = (
                    frequency_weight * 0.7 + recency_weight * 0.3
                )

    def predict_next_state(
        self, recent_data: pd.DataFrame, state_type: str = None
    ) -> Dict[str, float]:
        """预测下一个状态"""
        if state_type:
            return self._predict_single_type(recent_data, state_type)
        else:
            # 集成多种状态类型的预测
            return self._predict_ensemble(recent_data)

    def _predict_single_type(
        self, recent_data: pd.DataFrame, state_type: str
    ) -> Dict[str, float]:
        """单一类型状态预测"""
        if state_type not in self.transition_matrices:
            return {}

        matrix_info = self.transition_matrices[state_type]
        transition_matrix = matrix_info["matrix"]
        state_to_idx = matrix_info["state_to_idx"]
        idx_to_state = matrix_info["idx_to_state"]

        # 获取最近状态
        recent_states = []
        for _, row in recent_data.tail(self.order).iterrows():
            red_balls, blue_balls = parse_numbers(row)

            if state_type == "ratio":
                state_id = self._get_ratio_state(red_balls, blue_balls)
            elif state_type == "pattern":
                state_id = self._get_pattern_state(red_balls, blue_balls)
            elif state_type == "sequence":
                state_id = self._get_sequence_state(red_balls, blue_balls)
            elif state_type == "hybrid":
                state_id = self._get_hybrid_state(red_balls, blue_balls)
            else:
                continue

            recent_states.append(state_id)

        if not recent_states or recent_states[-1] not in state_to_idx:
            return {}

        # 获取当前状态的转换概率
        current_idx = state_to_idx[recent_states[-1]]
        probabilities = transition_matrix[current_idx]

        # 转换为状态概率字典
        result = {}
        for idx, prob in enumerate(probabilities):
            if prob > 0.01:  # 过滤低概率状态
                state_id = idx_to_state[idx]
                result[state_id] = float(prob)

        return result

    def _predict_ensemble(self, recent_data: pd.DataFrame) -> Dict[str, float]:
        """集成预测"""
        ensemble_predictions = {}
        total_weight = 0

        for state_type in self.state_types:
            type_predictions = self._predict_single_type(recent_data, state_type)

            if not type_predictions:
                continue

            # 计算该类型的权重 (基于历史准确性)
            type_weight = self._get_type_weight(state_type)
            total_weight += type_weight

            # 加权累加预测结果
            for state_id, prob in type_predictions.items():
                if state_id not in ensemble_predictions:
                    ensemble_predictions[state_id] = 0
                ensemble_predictions[state_id] += prob * type_weight

        # 归一化
        if total_weight > 0:
            for state_id in ensemble_predictions:
                ensemble_predictions[state_id] /= total_weight

        return ensemble_predictions

    def _get_type_weight(self, state_type: str) -> float:
        """获取状态类型权重"""
        accuracy_history = self.accuracy_by_type.get(state_type, [])

        if not accuracy_history:
            return 1.0  # 默认权重

        # 基于最近准确性计算权重
        recent_accuracy = np.mean(accuracy_history[-10:])  # 最近10次
        return max(0.1, recent_accuracy)  # 最小权重0.1

    def predict_ratios(self, recent_data: pd.DataFrame) -> Dict[str, float]:
        """预测比值状态"""
        state_predictions = self.predict_next_state(recent_data, "ratio")

        ratio_predictions = {}
        for state_id, prob in state_predictions.items():
            if state_id.startswith("ratio_"):
                state_value = self.states[state_id].state_value

                # 转换状态为具体比值
                odd_even_ratio = state_to_ratio(state_value["odd_even"], "odd_even")
                size_red_ratio = state_to_ratio(state_value["size_red"], "size_red")
                size_blue_ratio = state_to_ratio(state_value["size_blue"], "size_blue")

                ratio_predictions[state_id] = {
                    "probability": prob,
                    "odd_even_ratio": odd_even_ratio,
                    "size_red_ratio": size_red_ratio,
                    "size_blue_ratio": size_blue_ratio,
                }

        return ratio_predictions

    def update_accuracy(
        self, predicted_state: str, actual_state: str, state_type: str
    ) -> None:
        """更新预测准确性"""
        is_correct = predicted_state == actual_state
        self.accuracy_by_type[state_type].append(1.0 if is_correct else 0.0)

        # 保持历史记录在合理范围内
        if len(self.accuracy_by_type[state_type]) > 100:
            self.accuracy_by_type[state_type] = self.accuracy_by_type[state_type][-100:]

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            "order": self.order,
            "state_types": self.state_types,
            "total_states": len(self.states),
            "states_by_type": {},
            "accuracy_by_type": {},
        }

        # 按类型统计状态数量
        for state_type in self.state_types:
            count = sum(
                1 for state in self.states.values() if state.state_type == state_type
            )
            info["states_by_type"][state_type] = count

        # 计算平均准确性
        for state_type, accuracy_list in self.accuracy_by_type.items():
            if accuracy_list:
                info["accuracy_by_type"][state_type] = np.mean(accuracy_list)

        return info
