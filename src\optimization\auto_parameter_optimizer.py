#!/usr/bin/env python3
"""
算法参数自动调优系统
Automatic Parameter Optimization System

使用多种优化算法（贝叶斯优化、遗传算法、网格搜索等）
自动寻找最优参数组合，提升预测准确率
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional, Callable
from dataclasses import dataclass, field
from pathlib import Path
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

try:
    from skopt import gp_minimize, forest_minimize, gbrt_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args
    from skopt.acquisition import gaussian_ei, gaussian_pi, gaussian_lcb
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False

try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False


@dataclass
class OptimizationConfig:
    """优化配置"""
    # 优化方法
    method: str = 'bayesian'  # 'bayesian', 'genetic', 'grid', 'random', 'optuna'
    max_evaluations: int = 100
    max_time_seconds: float = 3600.0
    
    # 贝叶斯优化参数
    acquisition_function: str = 'EI'  # 'EI', 'PI', 'LCB'
    n_initial_points: int = 10
    
    # 遗传算法参数
    population_size: int = 20
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8
    
    # 并行化
    n_jobs: int = 1
    
    # 早停
    early_stopping: bool = True
    patience: int = 20
    min_improvement: float = 0.001
    
    # 目标函数
    objectives: List[str] = field(default_factory=lambda: ['kill_success_rate'])
    objective_weights: List[float] = field(default_factory=lambda: [1.0])


@dataclass
class ParameterSpace:
    """参数空间定义"""
    name: str
    param_type: str  # 'real', 'integer', 'categorical'
    bounds: Tuple[Any, Any]  # (min, max) for real/integer, (choices,) for categorical
    default: Any = None


class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self, config: OptimizationConfig = None):
        """
        初始化参数优化器
        
        Args:
            config: 优化配置
        """
        self.config = config or OptimizationConfig()
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")
        
        # 优化历史
        self.optimization_history = []
        self.best_params = None
        self.best_score = -np.inf
        
        # 参数空间
        self.parameter_spaces = self._define_parameter_spaces()
        
        # 评估函数
        self.evaluation_function = None
        
        # 早停计数器
        self.no_improvement_count = 0
        self.start_time = None
    
    def _define_parameter_spaces(self) -> Dict[str, List[ParameterSpace]]:
        """定义参数搜索空间"""
        return {
            # 杀号系统参数
            "kill_system": [
                ParameterSpace("diversity_threshold", "real", (0.3, 0.9), 0.7),
                ParameterSpace("max_iterations", "integer", (50, 300), 150),
                ParameterSpace("weight_decay", "real", (0.85, 0.99), 0.95),
                ParameterSpace("markov_weight", "real", (0.3, 0.8), 0.6),
                ParameterSpace("bayesian_weight", "real", (0.2, 0.7), 0.4),
            ],
            
            # 马尔科夫模型参数
            "markov": [
                ParameterSpace("order", "integer", (1, 4), 2),
                ParameterSpace("smoothing_factor", "real", (0.01, 0.3), 0.1),
                ParameterSpace("min_frequency", "integer", (1, 10), 3),
                ParameterSpace("decay_factor", "real", (0.9, 0.99), 0.95),
            ],
            
            # 贝叶斯模型参数
            "bayesian": [
                ParameterSpace("window_size", "integer", (20, 200), 100),
                ParameterSpace("prior_weight", "real", (0.1, 0.6), 0.3),
                ParameterSpace("recent_window", "integer", (10, 50), 30),
                ParameterSpace("smoothing_factor", "real", (0.001, 0.1), 0.01),
                ParameterSpace("learning_rate", "real", (0.01, 0.3), 0.1),
            ],
            
            # 集成学习参数
            "ensemble": [
                ParameterSpace("performance_window", "integer", (5, 50), 20),
                ParameterSpace("adaptation_rate", "real", (0.01, 0.3), 0.1),
                ParameterSpace("min_confidence", "real", (0.1, 0.7), 0.3),
                ParameterSpace("weight_decay", "real", (0.9, 0.99), 0.95),
            ],
            
            # 深度学习模型参数
            "deep_learning": [
                ParameterSpace("lstm_units", "categorical", ([32, 64, 128, 256],), 128),
                ParameterSpace("learning_rate", "real", (0.0001, 0.01), 0.001),
                ParameterSpace("dropout_rate", "real", (0.1, 0.5), 0.3),
                ParameterSpace("sequence_length", "integer", (10, 50), 20),
                ParameterSpace("batch_size", "categorical", ([16, 32, 64],), 32),
            ]
        }
    
    def set_evaluation_function(self, func: Callable[[Dict[str, Any]], float]):
        """设置评估函数"""
        self.evaluation_function = func
    
    def optimize(self, algorithm_name: str, custom_space: Dict[str, ParameterSpace] = None) -> Dict[str, Any]:
        """
        执行参数优化
        
        Args:
            algorithm_name: 算法名称
            custom_space: 自定义参数空间
            
        Returns:
            优化结果
        """
        if self.evaluation_function is None:
            raise ValueError("必须先设置评估函数")
        
        self.logger.info(f"开始优化 {algorithm_name} 参数...")
        self.start_time = time.time()
        
        # 获取参数空间
        param_space = custom_space or self.parameter_spaces.get(algorithm_name, [])
        if not param_space:
            raise ValueError(f"未找到算法 {algorithm_name} 的参数空间定义")
        
        # 根据优化方法执行优化
        if self.config.method == 'bayesian' and SKOPT_AVAILABLE:
            result = self._bayesian_optimization(param_space)
        elif self.config.method == 'optuna' and OPTUNA_AVAILABLE:
            result = self._optuna_optimization(param_space)
        elif self.config.method == 'genetic':
            result = self._genetic_optimization(param_space)
        elif self.config.method == 'grid':
            result = self._grid_search(param_space)
        elif self.config.method == 'random':
            result = self._random_search(param_space)
        else:
            self.logger.warning(f"优化方法 {self.config.method} 不可用，使用随机搜索")
            result = self._random_search(param_space)
        
        # 保存优化结果
        self._save_optimization_result(algorithm_name, result)
        
        self.logger.info(f"优化完成，最佳分数: {result['best_score']:.4f}")
        return result
    
    def _bayesian_optimization(self, param_space: List[ParameterSpace]) -> Dict[str, Any]:
        """贝叶斯优化"""
        # 构建skopt参数空间
        dimensions = []
        param_names = []
        
        for param in param_space:
            param_names.append(param.name)
            if param.param_type == 'real':
                dimensions.append(Real(param.bounds[0], param.bounds[1], name=param.name))
            elif param.param_type == 'integer':
                dimensions.append(Integer(param.bounds[0], param.bounds[1], name=param.name))
            elif param.param_type == 'categorical':
                dimensions.append(Categorical(param.bounds[0], name=param.name))
        
        @use_named_args(dimensions)
        def objective(**params):
            return -self._evaluate_parameters(params)  # skopt最小化，所以取负值
        
        # 执行优化
        result = gp_minimize(
            func=objective,
            dimensions=dimensions,
            n_calls=self.config.max_evaluations,
            n_initial_points=self.config.n_initial_points,
            acq_func=self.config.acquisition_function.lower(),
            random_state=42
        )
        
        # 构建最佳参数字典
        best_params = dict(zip(param_names, result.x))
        
        return {
            'best_params': best_params,
            'best_score': -result.fun,  # 转回正值
            'n_evaluations': len(result.func_vals),
            'optimization_history': self.optimization_history
        }
    
    def _optuna_optimization(self, param_space: List[ParameterSpace]) -> Dict[str, Any]:
        """Optuna优化"""
        def objective(trial):
            params = {}
            for param in param_space:
                if param.param_type == 'real':
                    params[param.name] = trial.suggest_float(param.name, param.bounds[0], param.bounds[1])
                elif param.param_type == 'integer':
                    params[param.name] = trial.suggest_int(param.name, param.bounds[0], param.bounds[1])
                elif param.param_type == 'categorical':
                    params[param.name] = trial.suggest_categorical(param.name, param.bounds[0])
            
            return self._evaluate_parameters(params)
        
        # 创建study
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=self.config.max_evaluations, timeout=self.config.max_time_seconds)
        
        return {
            'best_params': study.best_params,
            'best_score': study.best_value,
            'n_evaluations': len(study.trials),
            'optimization_history': self.optimization_history
        }
    
    def _genetic_optimization(self, param_space: List[ParameterSpace]) -> Dict[str, Any]:
        """遗传算法优化"""
        # 简化的遗传算法实现
        population_size = self.config.population_size
        
        # 初始化种群
        population = []
        for _ in range(population_size):
            individual = {}
            for param in param_space:
                if param.param_type == 'real':
                    individual[param.name] = np.random.uniform(param.bounds[0], param.bounds[1])
                elif param.param_type == 'integer':
                    individual[param.name] = np.random.randint(param.bounds[0], param.bounds[1] + 1)
                elif param.param_type == 'categorical':
                    individual[param.name] = np.random.choice(param.bounds[0])
            population.append(individual)
        
        best_individual = None
        best_fitness = -np.inf
        
        for generation in range(self.config.max_evaluations // population_size):
            # 评估种群
            fitness_scores = []
            for individual in population:
                fitness = self._evaluate_parameters(individual)
                fitness_scores.append(fitness)
                
                if fitness > best_fitness:
                    best_fitness = fitness
                    best_individual = individual.copy()
            
            # 选择、交叉、变异（简化实现）
            # 这里可以实现更复杂的遗传算法操作
            
            if self._should_stop():
                break
        
        return {
            'best_params': best_individual,
            'best_score': best_fitness,
            'n_evaluations': len(self.optimization_history),
            'optimization_history': self.optimization_history
        }
    
    def _grid_search(self, param_space: List[ParameterSpace]) -> Dict[str, Any]:
        """网格搜索"""
        # 生成参数网格
        param_grids = {}
        for param in param_space:
            if param.param_type == 'real':
                param_grids[param.name] = np.linspace(param.bounds[0], param.bounds[1], 5)
            elif param.param_type == 'integer':
                param_grids[param.name] = list(range(param.bounds[0], param.bounds[1] + 1))
            elif param.param_type == 'categorical':
                param_grids[param.name] = param.bounds[0]
        
        # 生成所有参数组合
        import itertools
        param_names = list(param_grids.keys())
        param_values = list(param_grids.values())
        
        best_params = None
        best_score = -np.inf
        
        for combination in itertools.product(*param_values):
            params = dict(zip(param_names, combination))
            score = self._evaluate_parameters(params)
            
            if score > best_score:
                best_score = score
                best_params = params
            
            if self._should_stop():
                break
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'n_evaluations': len(self.optimization_history),
            'optimization_history': self.optimization_history
        }
    
    def _random_search(self, param_space: List[ParameterSpace]) -> Dict[str, Any]:
        """随机搜索"""
        best_params = None
        best_score = -np.inf
        
        for _ in range(self.config.max_evaluations):
            params = {}
            for param in param_space:
                if param.param_type == 'real':
                    params[param.name] = np.random.uniform(param.bounds[0], param.bounds[1])
                elif param.param_type == 'integer':
                    params[param.name] = np.random.randint(param.bounds[0], param.bounds[1] + 1)
                elif param.param_type == 'categorical':
                    params[param.name] = np.random.choice(param.bounds[0])
            
            score = self._evaluate_parameters(params)
            
            if score > best_score:
                best_score = score
                best_params = params
            
            if self._should_stop():
                break
        
        return {
            'best_params': best_params,
            'best_score': best_score,
            'n_evaluations': len(self.optimization_history),
            'optimization_history': self.optimization_history
        }
    
    def _evaluate_parameters(self, params: Dict[str, Any]) -> float:
        """评估参数组合"""
        try:
            score = self.evaluation_function(params)
            
            # 记录评估历史
            self.optimization_history.append({
                'params': params.copy(),
                'score': score,
                'timestamp': time.time()
            })
            
            # 更新最佳结果
            if score > self.best_score:
                self.best_score = score
                self.best_params = params.copy()
                self.no_improvement_count = 0
                self.logger.info(f"发现更好参数: {params}, 分数: {score:.4f}")
            else:
                self.no_improvement_count += 1
            
            return score
            
        except Exception as e:
            self.logger.error(f"参数评估失败: {e}")
            return -np.inf
    
    def _should_stop(self) -> bool:
        """检查是否应该停止优化"""
        # 时间限制
        if time.time() - self.start_time > self.config.max_time_seconds:
            self.logger.info("达到时间限制，停止优化")
            return True
        
        # 早停
        if self.config.early_stopping and self.no_improvement_count >= self.config.patience:
            self.logger.info("达到早停条件，停止优化")
            return True
        
        return False
    
    def _save_optimization_result(self, algorithm_name: str, result: Dict[str, Any]):
        """保存优化结果"""
        try:
            results_dir = Path("optimization_results")
            results_dir.mkdir(exist_ok=True)
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = results_dir / f"{algorithm_name}_optimization_{timestamp}.json"
            
            # 准备保存数据
            save_data = {
                'algorithm_name': algorithm_name,
                'optimization_config': {
                    'method': self.config.method,
                    'max_evaluations': self.config.max_evaluations,
                    'max_time_seconds': self.config.max_time_seconds
                },
                'result': result,
                'timestamp': timestamp
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"优化结果已保存: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存优化结果失败: {e}")


class MultiObjectiveOptimizer(ParameterOptimizer):
    """多目标优化器"""

    def __init__(self, config: OptimizationConfig = None):
        super().__init__(config)
        self.pareto_front = []

    def _evaluate_parameters(self, params: Dict[str, Any]) -> float:
        """多目标评估"""
        if len(self.config.objectives) == 1:
            return super()._evaluate_parameters(params)

        # 多目标评估
        objective_scores = []
        for objective in self.config.objectives:
            # 这里需要根据具体目标函数实现
            score = self.evaluation_function(params, objective)
            objective_scores.append(score)

        # 加权组合
        weighted_score = sum(
            score * weight
            for score, weight in zip(objective_scores, self.config.objective_weights)
        )

        # 更新Pareto前沿
        self._update_pareto_front(params, objective_scores)

        return weighted_score

    def _update_pareto_front(self, params: Dict[str, Any], scores: List[float]):
        """更新Pareto前沿"""
        # 简化的Pareto前沿更新
        is_dominated = False
        for existing_params, existing_scores in self.pareto_front:
            if all(s1 <= s2 for s1, s2 in zip(scores, existing_scores)):
                is_dominated = True
                break

        if not is_dominated:
            # 移除被新解支配的解
            self.pareto_front = [
                (p, s) for p, s in self.pareto_front
                if not all(s1 >= s2 for s1, s2 in zip(s, scores))
            ]
            self.pareto_front.append((params.copy(), scores))


class ParameterEvaluator:
    """参数评估器"""

    def __init__(self, data_path: str = "data/raw/dlt_data.csv"):
        """
        初始化参数评估器

        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.data = None
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")
        self._load_data()

    def _load_data(self):
        """加载数据"""
        try:
            if os.path.exists(self.data_path):
                self.data = pd.read_csv(self.data_path)
                self.logger.info(f"加载数据: {len(self.data)} 条记录")
            else:
                self.logger.error(f"数据文件不存在: {self.data_path}")
        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")

    def evaluate_kill_system_params(self, params: Dict[str, Any]) -> float:
        """评估杀号系统参数"""
        if self.data is None:
            return 0.0

        try:
            # 模拟杀号系统评估
            # 这里应该集成实际的杀号系统
            from src.systems.main import LotteryPredictionSystem

            # 创建系统实例
            system = LotteryPredictionSystem()
            if not system.load_data():
                return 0.0

            # 应用参数
            self._apply_kill_system_params(system, params)

            # 运行回测
            backtest_periods = 10
            success_count = 0
            total_count = 0

            for i in range(len(self.data) - backtest_periods, len(self.data)):
                try:
                    period = str(self.data.iloc[i]["期号"])
                    kill_info = system.get_kill_numbers(period, red_count=5, blue_count=2)

                    # 获取实际开奖号码
                    actual_red, actual_blue = self._parse_winning_numbers(self.data.iloc[i])

                    # 检查杀号成功率
                    red_kills = kill_info.get('red_universal', [])
                    blue_kills = kill_info.get('blue_universal', [])

                    red_success = all(num not in actual_red for num in red_kills)
                    blue_success = all(num not in actual_blue for num in blue_kills)

                    if red_success and blue_success:
                        success_count += 1
                    total_count += 1

                except Exception as e:
                    self.logger.warning(f"评估期号 {period} 失败: {e}")
                    continue

            success_rate = success_count / total_count if total_count > 0 else 0.0
            self.logger.debug(f"杀号成功率: {success_rate:.4f} ({success_count}/{total_count})")

            return success_rate

        except Exception as e:
            self.logger.error(f"杀号系统参数评估失败: {e}")
            return 0.0

    def evaluate_prediction_params(self, params: Dict[str, Any]) -> float:
        """评估预测系统参数"""
        if self.data is None:
            return 0.0

        try:
            # 模拟预测系统评估
            # 这里应该集成实际的预测系统

            # 计算2+1命中率等指标
            hit_count = 0
            total_count = 0

            backtest_periods = 10
            for i in range(len(self.data) - backtest_periods, len(self.data)):
                try:
                    # 这里应该运行实际的预测算法
                    # 暂时返回随机分数作为示例
                    hit_count += np.random.choice([0, 1], p=[0.4, 0.6])
                    total_count += 1
                except Exception as e:
                    continue

            hit_rate = hit_count / total_count if total_count > 0 else 0.0
            return hit_rate

        except Exception as e:
            self.logger.error(f"预测系统参数评估失败: {e}")
            return 0.0

    def _apply_kill_system_params(self, system, params: Dict[str, Any]):
        """应用杀号系统参数"""
        # 这里应该将参数应用到实际系统中
        # 暂时作为示例实现
        pass

    def _parse_winning_numbers(self, row) -> Tuple[List[int], List[int]]:
        """解析开奖号码"""
        try:
            # 解析红球
            red_str = str(row.get('红球', ''))
            red_balls = [int(x.strip()) for x in red_str.split() if x.strip().isdigit()]

            # 解析蓝球
            blue_str = str(row.get('蓝球', ''))
            blue_balls = [int(x.strip()) for x in blue_str.split() if x.strip().isdigit()]

            return red_balls, blue_balls

        except Exception as e:
            self.logger.error(f"解析开奖号码失败: {e}")
            return [], []


class AutoParameterTuningManager:
    """自动参数调优管理器"""

    def __init__(self, config: OptimizationConfig = None):
        """
        初始化调优管理器

        Args:
            config: 优化配置
        """
        self.config = config or OptimizationConfig()
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")

        # 创建优化器和评估器
        self.optimizer = ParameterOptimizer(self.config)
        self.evaluator = ParameterEvaluator()

        # 优化结果
        self.optimization_results = {}

    def optimize_all_algorithms(self) -> Dict[str, Dict[str, Any]]:
        """优化所有算法参数"""
        self.logger.info("🎯 开始自动参数调优...")

        algorithms_to_optimize = [
            "kill_system",
            "markov",
            "bayesian",
            "ensemble"
        ]

        results = {}

        for algorithm in algorithms_to_optimize:
            self.logger.info(f"🔧 优化 {algorithm} 参数...")

            try:
                # 设置评估函数
                if algorithm == "kill_system":
                    self.optimizer.set_evaluation_function(self.evaluator.evaluate_kill_system_params)
                else:
                    self.optimizer.set_evaluation_function(self.evaluator.evaluate_prediction_params)

                # 执行优化
                result = self.optimizer.optimize(algorithm)
                results[algorithm] = result

                self.logger.info(f"✅ {algorithm} 优化完成，最佳分数: {result['best_score']:.4f}")

            except Exception as e:
                self.logger.error(f"❌ {algorithm} 优化失败: {e}")
                results[algorithm] = {'error': str(e)}

        self.optimization_results = results
        self._save_all_results()

        return results

    def get_best_parameters(self, algorithm: str) -> Dict[str, Any]:
        """获取算法的最佳参数"""
        if algorithm in self.optimization_results:
            return self.optimization_results[algorithm].get('best_params', {})
        return {}

    def apply_optimized_parameters(self, algorithm: str = None):
        """应用优化后的参数"""
        if algorithm:
            algorithms = [algorithm]
        else:
            algorithms = list(self.optimization_results.keys())

        for alg in algorithms:
            best_params = self.get_best_parameters(alg)
            if best_params:
                self.logger.info(f"应用 {alg} 优化参数: {best_params}")
                # 这里应该将参数应用到实际系统配置中
                self._apply_parameters_to_system(alg, best_params)

    def _apply_parameters_to_system(self, algorithm: str, params: Dict[str, Any]):
        """将参数应用到系统配置"""
        try:
            # 这里应该更新系统配置文件或运行时配置
            config_path = Path("config/optimized_params.json")
            config_path.parent.mkdir(exist_ok=True)

            # 读取现有配置
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}

            # 更新配置
            config[algorithm] = params

            # 保存配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.logger.info(f"参数已保存到配置文件: {config_path}")

        except Exception as e:
            self.logger.error(f"应用参数到系统失败: {e}")

    def _save_all_results(self):
        """保存所有优化结果"""
        try:
            results_dir = Path("optimization_results")
            results_dir.mkdir(exist_ok=True)

            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = results_dir / f"all_algorithms_optimization_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.optimization_results, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"所有优化结果已保存: {filename}")

        except Exception as e:
            self.logger.error(f"保存优化结果失败: {e}")

    def generate_optimization_report(self) -> str:
        """生成优化报告"""
        report = ["🎯 参数优化报告", "=" * 50]

        for algorithm, result in self.optimization_results.items():
            if 'error' in result:
                report.append(f"\n❌ {algorithm}: 优化失败 - {result['error']}")
            else:
                best_score = result.get('best_score', 0)
                n_evaluations = result.get('n_evaluations', 0)
                best_params = result.get('best_params', {})

                report.append(f"\n✅ {algorithm}:")
                report.append(f"   最佳分数: {best_score:.4f}")
                report.append(f"   评估次数: {n_evaluations}")
                report.append(f"   最佳参数: {best_params}")

        return "\n".join(report)
