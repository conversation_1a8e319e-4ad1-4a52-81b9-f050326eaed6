#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级概率系统 - 基于贝叶斯和马尔科夫链
目标：30期回测，平均5个杀号，97%全中率（提高精准度）
策略：使用高级概率模型捕捉复杂的依赖关系，减少杀号数量提高成功率
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from itertools import combinations
from collections import defaultdict, Counter
import math
import re
from datetime import datetime
import logging


class BayesianKillAlgorithm:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.prior_probs = {}
        self.conditional_probs = {}
        self._calculate_probabilities()

    def _calculate_probabilities(self):
        """计算先验概率和条件概率（增强版本，包含时间衰减）"""
        print("🔍 计算增强贝叶斯概率...")
        import math

        # 计算先验概率（基于历史频率，加入时间衰减）
        weighted_numbers = defaultdict(float)
        recent_patterns = []
        total_weight = 0

        for i, row in self.data.head(300).iterrows():  # 增加历史数据窗口到300期
            from src.utils.utils import parse_numbers

            red_balls, _ = parse_numbers(row)

            # 优化的时间衰减权重：基于回测结果调整衰减速度
            # 适度增加衰减速度，让近期数据有更高权重，提升预测准确性
            decay_factor = math.exp(-i * 0.003)  # 适度增加衰减速度
            weight = decay_factor
            total_weight += weight * len(red_balls)

            for num in red_balls:
                weighted_numbers[num] += weight

            # 记录模式：前一期的号码组合（也加入时间权重）
            if i < len(self.data) - 1:
                prev_row = self.data.iloc[i + 1]
                prev_red, _ = parse_numbers(prev_row)
                recent_patterns.append(
                    (tuple(sorted(prev_red)), tuple(sorted(red_balls)), weight)
                )

        # 先验概率（时间加权）
        for num in range(1, 36):
            self.prior_probs[num] = (
                weighted_numbers[num] / total_weight if total_weight > 0 else 1 / 35
            )

        # 条件概率：P(当期号码|前期号码组合)（时间加权）
        pattern_counts = defaultdict(lambda: defaultdict(float))
        pattern_totals = defaultdict(float)

        for prev_combo, curr_combo, weight in recent_patterns:
            pattern_totals[prev_combo] += weight
            for num in curr_combo:
                pattern_counts[prev_combo][num] += weight

        # 计算条件概率并应用拉普拉斯平滑
        for prev_combo in pattern_counts:
            total = pattern_totals[prev_combo]
            for num in range(1, 36):
                count = pattern_counts[prev_combo][num]
                # 优化拉普拉斯平滑参数，提高精准度
                self.conditional_probs[(prev_combo, num)] = (count + 0.05) / (
                    total + 2.0
                )

    def predict_kill_numbers(
        self, period_data: Dict, target_count: int = 5
    ) -> List[int]:
        """使用融合马尔可夫链的增强贝叶斯算法预测杀号"""
        # 获取前期号码
        from src.utils.utils import parse_numbers

        prev_red, _ = parse_numbers(period_data["last"])
        prev_combo = tuple(sorted(prev_red))

        # 1. 传统贝叶斯概率计算
        bayesian_probs = {}
        for num in range(1, 36):
            # 贝叶斯公式：P(号码|前期) ∝ P(前期|号码) * P(号码)
            prior = self.prior_probs.get(num, 1 / 35)
            conditional = self.conditional_probs.get((prev_combo, num), 1 / 35)

            # 后验概率（未归一化）
            bayesian_probs[num] = conditional * prior

        # 2. 马尔可夫链状态特征融合
        markov_features = self._extract_markov_features(prev_red)
        markov_weights = self._calculate_markov_weights(markov_features)

        # 3. 融合贝叶斯和马尔可夫链概率
        enhanced_probs = {}
        for num in range(1, 36):
            # 基础贝叶斯概率
            base_prob = bayesian_probs[num]

            # 马尔可夫链调整因子
            markov_factor = self._get_markov_adjustment(
                num, markov_features, markov_weights
            )

            # 融合概率：贝叶斯 * 马尔可夫调整因子
            enhanced_probs[num] = base_prob * markov_factor

        # 4. 选择概率最低的号码作为杀号
        sorted_nums = sorted(enhanced_probs.items(), key=lambda x: x[1])

        # 添加概率阈值过滤：只选择概率低于阈值的号码
        probability_threshold = 0.012  # 更严格的阈值，因为融合后概率更准确
        low_prob_numbers = [
            num for num, prob in sorted_nums if prob < probability_threshold
        ]

        # 如果低概率号码不够，选择概率最低的号码
        if len(low_prob_numbers) >= target_count:
            kill_numbers = low_prob_numbers[:target_count]
        else:
            kill_numbers = [num for num, prob in sorted_nums[:target_count]]

        return kill_numbers

    def _extract_markov_features(self, red_balls: List[int]) -> Tuple:
        """提取马尔可夫链状态特征（与马尔可夫链系统保持一致）"""
        # 使用与马尔可夫链相同的特征提取方法
        odd_count = sum(1 for num in red_balls if num % 2 == 1)
        large_count = sum(1 for num in red_balls if num > 17)
        sum_value = sum(red_balls)

        # 简化的离散化特征，只使用2个级别
        odd_ratio = "high" if odd_count >= 3 else "low"
        large_ratio = "high" if large_count >= 3 else "low"
        sum_range = "high" if sum_value > 100 else "low"

        return (odd_ratio, large_ratio, sum_range)

    def _calculate_markov_weights(self, features: Tuple) -> Dict:
        """计算马尔可夫链特征权重"""
        odd_ratio, large_ratio, sum_range = features

        # 基于特征计算权重
        weights = {
            "odd_weight": 0.8 if odd_ratio == "high" else 0.2,
            "large_weight": 0.8 if large_ratio == "high" else 0.2,
            "sum_weight": 0.8 if sum_range == "high" else 0.2,
        }

        return weights

    def _get_markov_adjustment(self, num: int, features: Tuple, weights: Dict) -> float:
        """获取马尔可夫链调整因子"""
        odd_ratio, large_ratio, sum_range = features

        # 基础调整因子
        adjustment = 1.0

        # 奇偶调整
        if num % 2 == 1:  # 奇数
            if odd_ratio == "high":
                adjustment *= 1.0 + weights["odd_weight"] * 0.3  # 增加奇数概率
            else:
                adjustment *= 1.0 - weights["odd_weight"] * 0.3  # 减少奇数概率
        else:  # 偶数
            if odd_ratio == "high":
                adjustment *= 1.0 - weights["odd_weight"] * 0.3  # 减少偶数概率
            else:
                adjustment *= 1.0 + weights["odd_weight"] * 0.3  # 增加偶数概率

        # 大小调整
        if num > 17:  # 大数
            if large_ratio == "high":
                adjustment *= 1.0 + weights["large_weight"] * 0.3
            else:
                adjustment *= 1.0 - weights["large_weight"] * 0.3
        else:  # 小数
            if large_ratio == "high":
                adjustment *= 1.0 - weights["large_weight"] * 0.3
            else:
                adjustment *= 1.0 + weights["large_weight"] * 0.3

        # 和值调整
        if num > 25:  # 高和值贡献号码
            if sum_range == "high":
                adjustment *= 1.0 + weights["sum_weight"] * 0.2
            else:
                adjustment *= 1.0 - weights["sum_weight"] * 0.2
        elif num < 10:  # 低和值贡献号码
            if sum_range == "high":
                adjustment *= 1.0 - weights["sum_weight"] * 0.2
            else:
                adjustment *= 1.0 + weights["sum_weight"] * 0.2

        # 确保调整因子在合理范围内
        return max(0.1, min(2.0, adjustment))


class MarkovChainKillAlgorithm:
    def __init__(self, data: pd.DataFrame, order: int = 1):
        self.data = data
        self.order = order  # 马尔科夫链的阶数
        self.transition_matrix = {}
        # 监控统计
        self.state_miss_count = 0  # 状态匹配失败次数
        self.total_predictions = 0  # 总预测次数
        self._build_transition_matrix()

    def _build_transition_matrix(self):
        """构建状态转移矩阵"""
        print(f"🔍 构建{self.order}阶马尔科夫链...")

        # 收集状态序列 - 使用优化后的窗口大小
        states = []
        max_data = min(200, len(self.data))  # 优化参数：200期窗口
        for i, row in self.data.head(max_data).iterrows():
            from src.utils.utils import parse_numbers

            red_balls, _ = parse_numbers(row)
            # 简化状态：使用号码的特征而不是完整组合
            state = self._extract_state_features(red_balls)
            states.append(state)

        # 构建转移计数
        transition_counts = defaultdict(lambda: defaultdict(int))
        state_counts = defaultdict(int)

        for i in range(len(states) - self.order):
            # 当前状态（前order个状态的组合）
            if self.order == 1:
                current_state = states[i]
            else:
                current_state = tuple(states[i : i + self.order])

            next_state = states[i + self.order]

            transition_counts[current_state][next_state] += 1
            state_counts[current_state] += 1

        # 计算转移概率（添加拉普拉斯平滑）- 使用优化后的平滑参数
        smoothing_factor = 0.01  # 优化参数：0.01平滑因子
        for current_state in transition_counts:
            total = state_counts[current_state]
            for next_state in transition_counts[current_state]:
                count = transition_counts[current_state][next_state]
                # 拉普拉斯平滑：(count + smoothing_factor) / (total + smoothing_factor * num_possible_states)
                smoothed_prob = (count + smoothing_factor) / (
                    total + smoothing_factor * len(transition_counts)
                )
                self.transition_matrix[(current_state, next_state)] = smoothed_prob

        # 输出统计信息
        unique_states = len(state_counts)
        total_transitions = len(self.transition_matrix)
        coverage_rate = (
            total_transitions / (unique_states * unique_states)
            if unique_states > 0
            else 0
        )
        print(
            f"  状态数量: {unique_states}, 转移数量: {total_transitions}, 覆盖率: {coverage_rate:.1%}"
        )

    def _extract_state_features(self, red_balls: List[int]) -> Tuple:
        """提取优化的状态特征（减少状态空间维度）"""
        # 简化特征，减少状态空间
        odd_count = sum(1 for num in red_balls if num % 2 == 1)
        large_count = sum(1 for num in red_balls if num > 17)
        sum_value = sum(red_balls)

        # 简化的离散化特征，只使用2个级别
        odd_ratio = "high" if odd_count >= 3 else "low"
        large_ratio = "high" if large_count >= 3 else "low"
        sum_range = "high" if sum_value > 100 else "low"

        # 只保留最重要的3个特征，大幅减少状态空间
        return (odd_ratio, large_ratio, sum_range)

    def predict_kill_numbers(
        self, period_data: Dict, target_count: int = 5
    ) -> List[int]:
        """使用马尔科夫链预测杀号"""
        self.total_predictions += 1  # 统计总预测次数

        # 获取当前状态
        from src.utils.utils import parse_numbers

        if self.order == 1:
            prev_red, _ = parse_numbers(period_data["last"])
            current_state = self._extract_state_features(prev_red)
        else:
            # 二阶马尔科夫链
            prev_red, _ = parse_numbers(period_data["last"])
            prev2_red, _ = parse_numbers(period_data["prev2"])
            state1 = self._extract_state_features(prev2_red)
            state2 = self._extract_state_features(prev_red)
            current_state = (state1, state2)

        # 预测下一个状态的概率分布
        next_state_probs = defaultdict(float)
        for (state, next_state), prob in self.transition_matrix.items():
            if state == current_state:
                next_state_probs[next_state] += prob

        # 基于预测的状态特征选择杀号
        if not next_state_probs:
            # 统计状态匹配失败
            self.state_miss_count += 1
            miss_rate = self.state_miss_count / self.total_predictions
            print(
                f"⚠️ 马尔可夫链{self.order}阶：状态转移失败 ({self.state_miss_count}/{self.total_predictions}, {miss_rate:.1%})"
            )

            # 使用默认状态调用杀号策略
            default_state = self._extract_state_features([1, 2, 3, 4, 5])
            return self._select_kills_by_state(default_state, target_count)

        # 选择概率最高的下一状态
        most_likely_state = max(next_state_probs.items(), key=lambda x: x[1])[0]

        # 根据预测状态选择杀号
        kill_numbers = self._select_kills_by_state(most_likely_state, target_count)

        return kill_numbers

    def _select_kills_by_state(
        self, predicted_state: Tuple, target_count: int
    ) -> List[int]:
        """根据预测状态动态选择杀号（优化版本）"""
        import random

        # 解包简化的状态特征
        odd_ratio, large_ratio, sum_range = predicted_state

        kill_candidates = []

        # 策略1：基于奇偶分布的动态杀号
        if odd_ratio == "high":
            # 预测奇数多，杀掉一些偶数
            even_candidates = [2, 4, 8, 14, 16, 24, 26, 28, 32, 34]
            kill_candidates.extend(
                random.sample(even_candidates, min(2, len(even_candidates)))
            )
        else:  # low
            # 预测奇数少，杀掉一些奇数
            odd_candidates = [1, 9, 13, 17, 19, 23, 25, 27, 33, 35]
            kill_candidates.extend(
                random.sample(odd_candidates, min(2, len(odd_candidates)))
            )

        # 策略2：基于大小分布的动态杀号
        if large_ratio == "high":
            # 预测大数多，杀掉一些小数
            small_candidates = [1, 4, 9, 13, 14, 17]
            kill_candidates.extend(
                random.sample(small_candidates, min(2, len(small_candidates)))
            )
        else:  # low
            # 预测大数少，杀掉一些大数
            large_candidates = [24, 25, 26, 32, 33, 35]
            kill_candidates.extend(
                random.sample(large_candidates, min(2, len(large_candidates)))
            )

        # 策略3：基于和值的杀号
        if sum_range == "high":
            # 预测和值高，杀掉一些小号
            low_sum_candidates = [1, 2, 4, 9, 13]
            kill_candidates.extend(
                random.sample(low_sum_candidates, min(1, len(low_sum_candidates)))
            )
        else:  # low
            # 预测和值低，杀掉一些大号
            high_sum_candidates = [32, 33, 34, 35]
            kill_candidates.extend(
                random.sample(high_sum_candidates, min(1, len(high_sum_candidates)))
            )

        # 去重并限制数量
        kill_numbers = list(set(kill_candidates))

        # 增强安全性检查
        kill_numbers = self._apply_safety_checks(kill_numbers)

        # 如果数量不够，智能补充
        if len(kill_numbers) < target_count:
            remaining_candidates = [
                num for num in range(1, 36) if num not in kill_numbers
            ]
            needed = target_count - len(kill_numbers)

            # 优先选择历史冷号
            cold_numbers = self._get_cold_numbers(remaining_candidates)
            additional = random.sample(cold_numbers, min(needed, len(cold_numbers)))
            kill_numbers.extend(additional)

            # 如果还不够，随机补充
            if len(kill_numbers) < target_count:
                still_remaining = [
                    num for num in remaining_candidates if num not in additional
                ]
                still_needed = target_count - len(kill_numbers)
                kill_numbers.extend(
                    random.sample(
                        still_remaining, min(still_needed, len(still_remaining))
                    )
                )

        return kill_numbers[:target_count]

    def _get_cold_numbers(self, candidates: List[int]) -> List[int]:
        """获取冷号（简化实现）"""
        # 简化实现：返回较大的号码作为冷号
        return [num for num in candidates if num > 20]

    def _apply_safety_checks(self, kill_numbers: List[int]) -> List[int]:
        """应用增强的安全性检查，包含热号保护机制（优化版）"""
        safe_kills = []

        # 获取最近1期出现的号码
        recent_1_numbers = set()
        try:
            if len(self.data) > 0:
                from src.utils.utils import parse_numbers

                red_balls, _ = parse_numbers(self.data.iloc[0])
                recent_1_numbers.update(red_balls)
        except:
            pass

        # 热号保护：获取最近3期、5期、10期的频率统计
        recent_3_freq = {}
        recent_5_freq = {}
        recent_10_freq = {}

        try:
            # 最近3期频率
            for i in range(min(3, len(self.data))):
                from src.utils.utils import parse_numbers

                red_balls, _ = parse_numbers(self.data.iloc[i])
                for ball in red_balls:
                    recent_3_freq[ball] = recent_3_freq.get(ball, 0) + 1

            # 最近5期频率
            for i in range(min(5, len(self.data))):
                red_balls, _ = parse_numbers(self.data.iloc[i])
                for ball in red_balls:
                    recent_5_freq[ball] = recent_5_freq.get(ball, 0) + 1

            # 最近10期频率
            for i in range(min(10, len(self.data))):
                red_balls, _ = parse_numbers(self.data.iloc[i])
                for ball in red_balls:
                    recent_10_freq[ball] = recent_10_freq.get(ball, 0) + 1
        except:
            pass

        # 计算号码的沉默期（连续未出现期数）
        silence_periods = {}
        try:
            for ball in range(1, 36):
                silence_count = 0
                for i in range(min(25, len(self.data))):  # 增加检查范围到25期
                    red_balls, _ = parse_numbers(self.data.iloc[i])
                    if ball in red_balls:
                        break
                    silence_count += 1
                silence_periods[ball] = silence_count
        except:
            silence_periods = {ball: 0 for ball in range(1, 36)}

        # 计算号码的历史出现频率（用于判断是否为长期冷号）
        historical_freq = {}
        try:
            for i in range(min(50, len(self.data))):  # 检查最近50期的历史频率
                red_balls, _ = parse_numbers(self.data.iloc[i])
                for ball in red_balls:
                    historical_freq[ball] = historical_freq.get(ball, 0) + 1
        except:
            historical_freq = {ball: 0 for ball in range(1, 36)}

        for num in kill_numbers:
            # 规则1：跳过最近1期出现的号码（基础保护）
            if num in recent_1_numbers:
                continue

            # 规则2：简化热号保护 - 跳过最近3期出现≥2次的热号
            if recent_3_freq.get(num, 0) >= 2:
                continue

            # 规则3：简化反弹保护 - 统一阈值
            historical_avg = (
                historical_freq.get(num, 0) / min(50, len(self.data))
                if len(self.data) > 0
                else 0
            )

            # 简化的反弹阈值（更宽松）
            if historical_avg > 0.12:  # 历史频率>12%的号码
                silence_threshold = 15  # 统一阈值
            else:  # 历史低频号码
                silence_threshold = 25

            if silence_periods.get(num, 0) >= silence_threshold:
                continue

            safe_kills.append(num)

        return safe_kills

    def _has_periodic_pattern(self, num: int) -> bool:
        """简化的周期性检查"""
        # 简化：不进行复杂的周期性检查，直接返回False
        return False

    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        miss_rate = (
            self.state_miss_count / self.total_predictions
            if self.total_predictions > 0
            else 0
        )
        return {
            "order": self.order,
            "total_predictions": self.total_predictions,
            "state_miss_count": self.state_miss_count,
            "state_miss_rate": miss_rate,
            "transition_matrix_size": len(self.transition_matrix),
        }


class FrequencyAnalysisKillAlgorithm:
    """基于频率分析的杀号算法"""

    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.frequency_cache = {}
        self._calculate_frequencies()

    def _calculate_frequencies(self):
        """计算号码出现频率"""
        from src.utils.utils import parse_numbers

        # 统计所有红球号码的出现频率
        red_freq = {}
        for _, row in self.data.iterrows():
            try:
                red_balls, _ = parse_numbers(row)
                for ball in red_balls:
                    red_freq[ball] = red_freq.get(ball, 0) + 1
            except:
                continue

        # 计算频率权重（逆频率）
        total_periods = len(self.data)
        self.frequency_weights = {}
        for ball in range(1, 36):
            freq = red_freq.get(ball, 0)
            # 逆频率权重：出现越少，权重越高
            self.frequency_weights[ball] = 1.0 / (freq + 1)

    def predict_kill_numbers(
        self, period_data: Dict, target_count: int = 5
    ) -> List[int]:
        """改进的频率分析预测杀号"""
        try:
            from src.utils.utils import parse_numbers

            # 获取最近2期的号码，避免杀掉刚出现的号码（减少保守程度）
            recent_numbers = set()
            for key in ["current", "last"]:  # 只看最近2期
                if key in period_data:
                    red_balls, _ = parse_numbers(period_data[key])
                    recent_numbers.update(red_balls)

            # 计算最近10期的频率分布
            recent_freq = {}
            recent_data = self.data.head(10)  # 最近10期
            for _, row in recent_data.iterrows():
                try:
                    red_balls, _ = parse_numbers(row)
                    for ball in red_balls:
                        recent_freq[ball] = recent_freq.get(ball, 0) + 1
                except:
                    continue

            # 基于综合频率权重选择杀号候选
            candidates = []
            for ball in range(1, 36):
                if ball not in recent_numbers:  # 避免杀掉最近出现的号码
                    # 综合权重：历史频率权重 + 最近频率权重
                    historical_weight = self.frequency_weights.get(ball, 0)
                    recent_weight = 1.0 / (
                        recent_freq.get(ball, 0) + 1
                    )  # 最近出现越少，权重越高

                    # 综合权重（70%历史 + 30%最近）
                    combined_weight = 0.7 * historical_weight + 0.3 * recent_weight
                    candidates.append((ball, combined_weight))

            # 按权重排序，选择权重最高的（最不常出现的）
            candidates.sort(key=lambda x: x[1], reverse=True)

            # 返回前target_count个，但要确保不全是边界号码
            result = []
            boundary_numbers = {1, 2, 34, 35}
            boundary_count = 0

            for ball, _ in candidates:
                if len(result) >= target_count:
                    break

                # 限制边界号码数量（最多1个）
                if ball in boundary_numbers:
                    if boundary_count >= 1:
                        continue
                    boundary_count += 1

                result.append(ball)

            return result if result else [32, 33, 34, 35, 31][:target_count]

        except Exception as e:
            print(f"频率分析算法预测失败: {e}")
            return [32, 33, 34, 35, 31][:target_count]


class TrendAnalysisKillAlgorithm:
    """基于趋势分析的杀号算法"""

    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.trend_window = 10  # 趋势分析窗口
        self._calculate_trends()

    def _calculate_trends(self):
        """计算号码出现趋势"""
        from src.utils.utils import parse_numbers

        # 计算每个号码在不同时间窗口的出现频率
        self.short_term_freq = {}  # 最近5期
        self.long_term_freq = {}  # 最近20期

        recent_data = self.data.head(20)  # 最近20期

        for ball in range(1, 36):
            # 短期频率（最近5期）
            short_count = 0
            long_count = 0

            for i, (_, row) in enumerate(recent_data.iterrows()):
                try:
                    red_balls, _ = parse_numbers(row)
                    if ball in red_balls:
                        long_count += 1
                        if i < 5:  # 最近5期
                            short_count += 1
                except:
                    continue

            self.short_term_freq[ball] = short_count / min(5, len(recent_data))
            self.long_term_freq[ball] = long_count / min(20, len(recent_data))

    def predict_kill_numbers(
        self, period_data: Dict, target_count: int = 5
    ) -> List[int]:
        """改进的趋势分析预测杀号"""
        try:
            from src.utils.utils import parse_numbers

            # 获取最近1期的号码（减少保守程度）
            recent_numbers = set()
            if "current" in period_data:
                red_balls, _ = parse_numbers(period_data["current"])
                recent_numbers.update(red_balls)

            # 计算更精确的趋势分数
            candidates = []
            for ball in range(1, 36):
                if ball not in recent_numbers:
                    short_freq = self.short_term_freq.get(ball, 0)
                    long_freq = self.long_term_freq.get(ball, 0)

                    # 改进的趋势分数计算
                    if long_freq > 0:
                        # 相对趋势：短期频率相对于长期频率的下降程度
                        relative_trend = (long_freq - short_freq) / long_freq
                        # 绝对趋势：短期频率的绝对值
                        absolute_trend = 1.0 / (short_freq + 0.1)  # 避免除零

                        # 综合趋势分数（60%相对 + 40%绝对）
                        trend_score = 0.6 * relative_trend + 0.4 * absolute_trend
                    else:
                        # 如果长期频率为0，使用绝对趋势
                        trend_score = 1.0 / (short_freq + 0.1)

                    candidates.append((ball, trend_score))

            # 选择趋势分数最高的（最可能继续不出现的）
            candidates.sort(key=lambda x: x[1], reverse=True)

            # 智能选择，避免全选边界号码
            result = []
            boundary_numbers = {1, 2, 34, 35}
            boundary_count = 0

            for ball, _ in candidates:
                if len(result) >= target_count:
                    break

                # 限制边界号码数量（最多1个）
                if ball in boundary_numbers:
                    if boundary_count >= 1:
                        continue
                    boundary_count += 1

                result.append(ball)

            return result if result else [32, 33, 34, 35, 31][:target_count]

        except Exception as e:
            print(f"趋势分析算法预测失败: {e}")
            return [32, 33, 34, 35, 31][:target_count]


class CorrelationKillAlgorithm:
    """基于相关性分析的杀号算法"""

    def __init__(self, data: pd.DataFrame):
        self.data = data
        self._calculate_correlations()

    def _calculate_correlations(self):
        """计算号码间的相关性"""
        from src.utils.utils import parse_numbers
        import numpy as np

        # 创建号码共现矩阵
        self.cooccurrence = {}
        self.ball_counts = {}

        for ball in range(1, 36):
            self.ball_counts[ball] = 0
            self.cooccurrence[ball] = {}
            for other_ball in range(1, 36):
                self.cooccurrence[ball][other_ball] = 0

        # 统计共现次数
        for _, row in self.data.iterrows():
            try:
                red_balls, _ = parse_numbers(row)
                for ball in red_balls:
                    self.ball_counts[ball] += 1
                    for other_ball in red_balls:
                        if ball != other_ball:
                            self.cooccurrence[ball][other_ball] += 1
            except:
                continue

        # 计算条件概率
        self.conditional_prob = {}
        for ball in range(1, 36):
            self.conditional_prob[ball] = {}
            for other_ball in range(1, 36):
                if self.ball_counts[ball] > 0:
                    self.conditional_prob[ball][other_ball] = (
                        self.cooccurrence[ball][other_ball] / self.ball_counts[ball]
                    )
                else:
                    self.conditional_prob[ball][other_ball] = 0

    def predict_kill_numbers(
        self, period_data: Dict, target_count: int = 5
    ) -> List[int]:
        """基于相关性分析预测杀号"""
        try:
            from src.utils.utils import parse_numbers

            # 获取当前期的号码作为条件
            if "current" in period_data:
                current_red, _ = parse_numbers(period_data["current"])
            else:
                return [32, 33, 34, 35, 31][:target_count]

            # 计算每个号码与当前期号码的相关性
            correlation_scores = {}
            for ball in range(1, 36):
                if ball not in current_red:
                    # 计算该号码与当前期号码共现的概率
                    total_prob = 0
                    for current_ball in current_red:
                        total_prob += self.conditional_prob.get(current_ball, {}).get(
                            ball, 0
                        )

                    # 平均相关性
                    avg_correlation = (
                        total_prob / len(current_red) if current_red else 0
                    )
                    correlation_scores[ball] = avg_correlation

            # 选择相关性最低的号码（最不可能与当前期号码一起出现）
            candidates = sorted(correlation_scores.items(), key=lambda x: x[1])

            result = [ball for ball, _ in candidates[:target_count]]
            return result if result else [32, 33, 34, 35, 31][:target_count]

        except Exception as e:
            print(f"相关性分析算法预测失败: {e}")
            return [32, 33, 34, 35, 31][:target_count]


class EnsembleKillSystem:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        # 原有算法
        self.bayesian_algo = BayesianKillAlgorithm(data)
        self.markov1_algo = MarkovChainKillAlgorithm(data, order=1)
        self.markov2_algo = MarkovChainKillAlgorithm(data, order=2)

        # 新增算法
        self.frequency_algo = FrequencyAnalysisKillAlgorithm(data)
        self.trend_algo = TrendAnalysisKillAlgorithm(data)
        self.correlation_algo = CorrelationKillAlgorithm(data)

        # 精细调整权重配置（6个算法）- 适度优化，避免过度调整
        # 基于回测反馈：适度调整权重，保持算法多样性
        self.weights = {
            "bayesian": 0.18,
            "markov1": 0.14,
            "markov2": 0.13,  # 适度调整贝叶斯和马尔可夫权重
            "frequency": 0.32,
            "trend": 0.20,
            "correlation": 0.03,  # 提升频率和趋势，保留少量相关性
        }

        # 增强的自适应权重调整相关
        self.performance_history = {
            "bayesian": [],
            "markov1": [],
            "markov2": [],
            "frequency": [],
            "trend": [],
            "correlation": [],
        }
        self.detailed_performance = {
            "bayesian": [],
            "markov1": [],
            "markov2": [],
            "frequency": [],
            "trend": [],
            "correlation": [],
        }
        self.adaptation_window = 3  # 每3期调整一次权重，快速响应
        self.period_count = 0

        # 权重记忆和稳定性机制
        self.best_weights = self.weights.copy()  # 历史最优权重
        self.best_performance = 0.0  # 历史最优性能
        self.weight_momentum = {
            "bayesian": 0.0,
            "markov1": 0.0,
            "markov2": 0.0,
            "frequency": 0.0,
            "trend": 0.0,
            "correlation": 0.0,
        }
        self.learning_rate = 0.1  # 动态学习率
        self.stability_threshold = 0.05  # 稳定性阈值

    def predict_ensemble_kills(
        self, period_data: Dict, target_count: int = 5
    ) -> List[int]:
        """集成多个模型的预测结果（支持6个算法）+ 区间平衡检查 + 多样性增强"""
        # 获取期号特征用于增加多样性
        period_number = str(period_data["current"]["期号"])
        period_seed = (
            int(period_number[-3:])
            if len(period_number) >= 3
            else int(period_number[-2:])
        )

        # 添加时间戳作为多样性因子（保持基本可重现性的同时增加变化）
        import time

        diversity_factor = int(time.time() * 1000) % 1000  # 毫秒级时间戳的后3位
        enhanced_seed = period_seed + diversity_factor

        # 动态调整权重以增加多样性
        dynamic_weights = self._get_dynamic_weights(enhanced_seed)

        # 获取各模型的预测
        predictions = {}
        try:
            predictions["bayesian"] = self.bayesian_algo.predict_kill_numbers(
                period_data, target_count
            )
            predictions["markov1"] = self.markov1_algo.predict_kill_numbers(
                period_data, target_count
            )
            predictions["markov2"] = self.markov2_algo.predict_kill_numbers(
                period_data, target_count
            )
            predictions["frequency"] = self.frequency_algo.predict_kill_numbers(
                period_data, target_count
            )
            predictions["trend"] = self.trend_algo.predict_kill_numbers(
                period_data, target_count
            )
            predictions["correlation"] = self.correlation_algo.predict_kill_numbers(
                period_data, target_count
            )
        except Exception as e:
            print(f"⚠️ 模型预测失败: {e}")
            return self._generate_diverse_fallback(enhanced_seed, target_count)

        # 加权投票机制（使用动态权重）
        vote_counts = defaultdict(float)

        # 所有算法投票
        for algo_name, kills in predictions.items():
            weight = dynamic_weights.get(algo_name, 0)
            for kill in kills:
                vote_counts[kill] += weight

        # 按得分排序
        sorted_votes = sorted(vote_counts.items(), key=lambda x: x[1], reverse=True)

        # 区间平衡选择 + 多样性检查
        ensemble_kills = self._diverse_balanced_selection(
            sorted_votes, target_count, enhanced_seed
        )

        # 如果投票结果不足，补充多样化候选
        if len(ensemble_kills) < target_count:
            ensemble_kills = self._supplement_diverse_kills(
                ensemble_kills, predictions, target_count, enhanced_seed
            )

        return ensemble_kills[:target_count]

    def _get_dynamic_weights(self, period_seed: int) -> dict:
        """根据期号特征动态调整权重以增加多样性"""
        import random

        # 使用期号作为随机种子确保可重现性
        random.seed(period_seed)

        # 基础权重
        base_weights = self.weights.copy()

        # 根据期号特征选择不同的权重策略
        strategy = period_seed % 4

        if strategy == 0:  # 强化贝叶斯和频率分析
            multipliers = {
                "bayesian": 1.3,
                "frequency": 1.4,
                "trend": 0.8,
                "markov1": 0.9,
                "markov2": 0.7,
                "correlation": 0.9,
            }
        elif strategy == 1:  # 强化马尔科夫链
            multipliers = {
                "markov1": 1.4,
                "markov2": 1.3,
                "bayesian": 0.8,
                "frequency": 0.9,
                "trend": 0.9,
                "correlation": 0.7,
            }
        elif strategy == 2:  # 强化趋势和相关性
            multipliers = {
                "trend": 1.4,
                "correlation": 1.3,
                "frequency": 0.8,
                "bayesian": 0.9,
                "markov1": 0.8,
                "markov2": 0.8,
            }
        else:  # 平衡策略
            multipliers = {
                "bayesian": 1.1,
                "markov1": 1.0,
                "markov2": 1.0,
                "frequency": 1.1,
                "trend": 1.0,
                "correlation": 0.8,
            }

        # 应用乘数并添加随机扰动
        dynamic_weights = {}
        for algo, base_weight in base_weights.items():
            multiplier = multipliers.get(algo, 1.0)
            # 添加±10%的随机扰动
            noise = random.uniform(0.9, 1.1)
            dynamic_weights[algo] = base_weight * multiplier * noise

        # 归一化权重
        total_weight = sum(dynamic_weights.values())
        if total_weight > 0:
            for algo in dynamic_weights:
                dynamic_weights[algo] /= total_weight

        return dynamic_weights

    def _generate_diverse_fallback(
        self, period_seed: int, target_count: int
    ) -> List[int]:
        """生成多样化的回退杀号"""
        import random

        random.seed(period_seed)

        # 根据期号选择不同的回退策略
        strategy = period_seed % 3

        if strategy == 0:  # 偏向小号
            candidates = list(range(1, 16)) + list(range(30, 36))
        elif strategy == 1:  # 偏向大号
            candidates = list(range(1, 6)) + list(range(20, 36))
        else:  # 中号为主
            candidates = list(range(8, 28))

        return random.sample(candidates, min(target_count, len(candidates)))

    def _diverse_balanced_selection(
        self, sorted_votes: List, target_count: int, period_seed: int
    ) -> List[int]:
        """多样化的区间平衡选择"""
        import random

        random.seed(period_seed)

        # 先进行基础的区间平衡选择
        basic_selection = self._balanced_zone_selection(sorted_votes, target_count)

        # 如果选择结果过于集中，进行多样化调整
        if len(basic_selection) >= 3:
            zone_distribution = self._analyze_zone_distribution(basic_selection)
            if (
                max(zone_distribution.values()) > target_count * 0.6
            ):  # 某个区间占比超过60%
                # 重新平衡选择
                return self._rebalance_selection(
                    sorted_votes, target_count, period_seed
                )

        return basic_selection

    def _analyze_zone_distribution(self, numbers: List[int]) -> dict:
        """分析号码的区间分布"""
        distribution = {"small": 0, "medium": 0, "large": 0}
        for num in numbers:
            if 1 <= num <= 12:
                distribution["small"] += 1
            elif 13 <= num <= 24:
                distribution["medium"] += 1
            else:
                distribution["large"] += 1
        return distribution

    def _rebalance_selection(
        self, sorted_votes: List, target_count: int, period_seed: int
    ) -> List[int]:
        """重新平衡选择以增加多样性"""
        import random

        random.seed(period_seed)

        # 按区间分组候选号码
        zones = {"small": [], "medium": [], "large": []}
        for num, votes in sorted_votes:
            if 1 <= num <= 12:
                zones["small"].append((num, votes))
            elif 13 <= num <= 24:
                zones["medium"].append((num, votes))
            else:
                zones["large"].append((num, votes))

        # 每个区间最多选择的数量
        max_per_zone = (target_count + 2) // 3  # 向上取整分配

        selected = []
        for zone_name, candidates in zones.items():
            if candidates:
                # 从每个区间选择最多max_per_zone个
                zone_count = min(
                    max_per_zone, len(candidates), target_count - len(selected)
                )
                # 添加随机性：70%选择得分最高的，30%随机选择
                if random.random() < 0.7:
                    selected.extend([num for num, _ in candidates[:zone_count]])
                else:
                    zone_selection = random.sample(
                        candidates[: min(zone_count * 2, len(candidates))], zone_count
                    )
                    selected.extend([num for num, _ in zone_selection])

        return selected[:target_count]

    def _supplement_diverse_kills(
        self,
        current_kills: List[int],
        predictions: dict,
        target_count: int,
        period_seed: int,
    ) -> List[int]:
        """补充多样化的杀号"""
        import random

        random.seed(period_seed)

        # 收集所有候选号码
        all_candidates = set()
        for kills in predictions.values():
            all_candidates.update(kills)

        # 移除已选择的号码
        remaining_candidates = [
            num for num in all_candidates if num not in current_kills
        ]

        # 如果候选不足，添加更多多样化选择
        if len(remaining_candidates) < (target_count - len(current_kills)):
            # 根据期号特征添加额外候选
            extra_candidates = self._generate_extra_candidates(
                period_seed, current_kills
            )
            remaining_candidates.extend(extra_candidates)

        # 随机选择补充
        needed = target_count - len(current_kills)
        if remaining_candidates and needed > 0:
            supplement = random.sample(
                remaining_candidates, min(needed, len(remaining_candidates))
            )
            current_kills.extend(supplement)

        return current_kills

    def _generate_extra_candidates(
        self, period_seed: int, exclude: List[int]
    ) -> List[int]:
        """生成额外的多样化候选号码"""
        import random

        random.seed(period_seed)

        # 根据期号特征选择不同的候选策略
        strategy = period_seed % 5

        if strategy == 0:  # 边界号码
            candidates = [1, 2, 34, 35]
        elif strategy == 1:  # 质数
            candidates = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31]
        elif strategy == 2:  # 连号
            candidates = [i for i in range(1, 35) if i + 1 in range(1, 36)]
        elif strategy == 3:  # 尾数相同
            tail = period_seed % 10
            candidates = [i for i in range(1, 36) if i % 10 == tail]
        else:  # 随机分散
            candidates = list(range(1, 36))
            random.shuffle(candidates)

        # 过滤已排除的号码
        return [num for num in candidates if num not in exclude][:10]

    def _balanced_zone_selection(
        self, sorted_votes: List, target_count: int
    ) -> List[int]:
        """区间平衡选择杀号，确保不过度集中在某个区间（增强版）"""
        small_zone = []  # 1-12
        medium_zone = []  # 13-24
        large_zone = []  # 25-35

        # 按区间分类候选杀号
        for num, votes in sorted_votes:
            if 1 <= num <= 12:
                small_zone.append((num, votes))
            elif 13 <= num <= 24:
                medium_zone.append((num, votes))
            elif 25 <= num <= 35:
                large_zone.append((num, votes))

        # 增强平衡选择策略：基于历史成功率动态调整区间权重
        selected_kills = []

        # 计算各区间的历史杀号成功率
        zone_success_rates = self._calculate_zone_success_rates()

        # 根据成功率调整各区间的选择优先级
        zones_with_priority = [
            (small_zone, zone_success_rates.get("small", 0.6), "small"),
            (medium_zone, zone_success_rates.get("medium", 0.6), "medium"),
            (large_zone, zone_success_rates.get("large", 0.6), "large"),
        ]

        # 按成功率排序区间
        zones_with_priority.sort(key=lambda x: x[1], reverse=True)

        # 第一轮：优先从成功率高的区间选择
        for zone, success_rate, zone_name in zones_with_priority:
            if zone and len(selected_kills) < target_count:
                # 根据成功率决定从该区间选择的数量
                if success_rate > 0.7:
                    select_count = min(2, len(zone), target_count - len(selected_kills))
                elif success_rate > 0.6:
                    select_count = min(1, len(zone), target_count - len(selected_kills))
                else:
                    select_count = (
                        min(1, len(zone), target_count - len(selected_kills))
                        if len(selected_kills) < target_count // 2
                        else 0
                    )

                for i in range(select_count):
                    selected_kills.append(zone[i][0])

        # 第二轮：按得分继续选择，但考虑区间平衡
        zone_counts = {"small": 0, "medium": 0, "large": 0}
        for num in selected_kills:
            if 1 <= num <= 12:
                zone_counts["small"] += 1
            elif 13 <= num <= 24:
                zone_counts["medium"] += 1
            else:
                zone_counts["large"] += 1

        for num, votes in sorted_votes:
            if len(selected_kills) >= target_count:
                break

            if num in selected_kills:
                continue

            # 确定号码所属区间
            if 1 <= num <= 12:
                zone_name = "small"
            elif 13 <= num <= 24:
                zone_name = "medium"
            else:
                zone_name = "large"

            # 动态限制：成功率高的区间可以选择更多
            max_per_zone = 3 if zone_success_rates.get(zone_name, 0.6) > 0.65 else 2

            if zone_counts[zone_name] < max_per_zone:
                selected_kills.append(num)
                zone_counts[zone_name] += 1

        return selected_kills

    def _calculate_zone_success_rates(self) -> dict:
        """计算各区间的历史杀号成功率"""
        try:
            # 简化实现：基于最近的杀号表现估算
            # 在实际应用中，这里应该基于历史回测数据

            # 获取最近期数据进行简单分析
            zone_stats = {"small": [], "medium": [], "large": []}

            for i in range(min(20, len(self.data))):
                try:
                    from src.utils.utils import parse_numbers

                    red_balls, _ = parse_numbers(self.data.iloc[i])

                    # 统计各区间出现的号码数量
                    small_count = sum(1 for ball in red_balls if 1 <= ball <= 12)
                    medium_count = sum(1 for ball in red_balls if 13 <= ball <= 24)
                    large_count = sum(1 for ball in red_balls if 25 <= ball <= 35)

                    total_balls = len(red_balls)
                    if total_balls > 0:
                        zone_stats["small"].append(small_count / total_balls)
                        zone_stats["medium"].append(medium_count / total_balls)
                        zone_stats["large"].append(large_count / total_balls)
                except:
                    continue

            # 计算各区间的平均占比，占比低的区间杀号成功率可能更高
            success_rates = {}
            for zone, ratios in zone_stats.items():
                if ratios:
                    avg_ratio = sum(ratios) / len(ratios)
                    # 占比越低，杀号成功率越高（反向关系）
                    success_rates[zone] = max(0.5, 1.0 - avg_ratio * 1.5)
                else:
                    success_rates[zone] = 0.6  # 默认值

            return success_rates

        except:
            # 默认成功率
            return {"small": 0.6, "medium": 0.6, "large": 0.6}

    def update_performance(self, actual_red: List[int], period_data: Dict):
        """更新各模型的性能表现（支持6个算法）"""
        try:
            # 获取各模型的预测
            predictions = {}
            predictions["bayesian"] = self.bayesian_algo.predict_kill_numbers(
                period_data, 5
            )
            predictions["markov1"] = self.markov1_algo.predict_kill_numbers(
                period_data, 5
            )
            predictions["markov2"] = self.markov2_algo.predict_kill_numbers(
                period_data, 5
            )
            predictions["frequency"] = self.frequency_algo.predict_kill_numbers(
                period_data, 5
            )
            predictions["trend"] = self.trend_algo.predict_kill_numbers(period_data, 5)
            predictions["correlation"] = self.correlation_algo.predict_kill_numbers(
                period_data, 5
            )

            # 计算各模型的成功率（全中）
            for algo_name, kills in predictions.items():
                success = all(k not in actual_red for k in kills)
                self.performance_history[algo_name].append(1.0 if success else 0.0)

            # 限制历史记录长度
            for key in self.performance_history:
                if len(self.performance_history[key]) > 20:
                    self.performance_history[key] = self.performance_history[key][-20:]

            self.period_count += 1

            # 计算精确的性能分数（部分成功率）
            for algo_name, kills in predictions.items():
                score = self._calculate_kill_score(kills, actual_red)
                self.detailed_performance[algo_name].append(score)

            # 限制历史记录长度
            for key in self.performance_history:
                if len(self.performance_history[key]) > 30:
                    self.performance_history[key] = self.performance_history[key][-30:]
                if len(self.detailed_performance[key]) > 30:
                    self.detailed_performance[key] = self.detailed_performance[key][
                        -30:
                    ]

            # 每adaptation_window期调整一次权重
            if self.period_count % self.adaptation_window == 0:
                self._enhanced_adaptive_weight_adjustment()

        except Exception as e:
            print(f"性能更新失败: {e}")

    def _calculate_kill_score(
        self, kill_numbers: List[int], actual_red: List[int]
    ) -> float:
        """计算杀号的精确分数"""
        if not kill_numbers:
            return 0.0

        # 计算成功杀号的比例
        successful_kills = sum(1 for k in kill_numbers if k not in actual_red)
        return successful_kills / len(kill_numbers)

    def _enhanced_adaptive_weight_adjustment(self):
        """增强的自适应权重调整（支持6个算法）"""
        try:
            all_models = [
                "bayesian",
                "markov1",
                "markov2",
                "frequency",
                "trend",
                "correlation",
            ]

            # 计算加权移动平均性能
            weighted_performance = {}
            for model in all_models:
                if self.detailed_performance[model]:
                    # 使用指数加权移动平均，近期表现权重更高
                    scores = self.detailed_performance[model]
                    weights = [0.9 ** (len(scores) - i - 1) for i in range(len(scores))]
                    weighted_avg = sum(s * w for s, w in zip(scores, weights)) / sum(
                        weights
                    )
                    weighted_performance[model] = weighted_avg
                else:
                    weighted_performance[model] = 1.0 / len(all_models)  # 默认平均值

            # 计算性能稳定性（标准差的倒数）
            stability_scores = {}
            for model in all_models:
                if len(self.detailed_performance[model]) >= 3:
                    import numpy as np

                    std_dev = np.std(
                        self.detailed_performance[model][-10:]
                    )  # 最近10期的稳定性
                    stability_scores[model] = 1.0 / (std_dev + 0.1)  # 避免除零
                else:
                    stability_scores[model] = 1.0

            # 综合评分：性能 * 稳定性
            combined_scores = {}
            for model in all_models:
                combined_scores[model] = weighted_performance[model] * (
                    1 + 0.3 * stability_scores[model]
                )

            # 检查是否需要更新最优权重
            current_overall_performance = sum(weighted_performance.values()) / len(
                all_models
            )
            if current_overall_performance > self.best_performance:
                self.best_performance = current_overall_performance
                self.best_weights = self.weights.copy()

            # 动态学习率调整
            performance_trend = self._calculate_performance_trend()
            if performance_trend > 0:
                self.learning_rate = min(
                    0.2, self.learning_rate * 1.1
                )  # 性能上升，增加学习率
            else:
                self.learning_rate = max(
                    0.05, self.learning_rate * 0.9
                )  # 性能下降，减少学习率

            # 计算新权重（使用动量机制）
            total_score = sum(combined_scores.values())
            if total_score > 0:
                new_weights = {}
                for model in all_models:
                    target_weight = combined_scores[model] / total_score

                    # 应用动量和学习率
                    weight_change = (
                        target_weight - self.weights[model]
                    ) * self.learning_rate
                    self.weight_momentum[model] = (
                        0.7 * self.weight_momentum[model] + 0.3 * weight_change
                    )
                    new_weights[model] = (
                        self.weights[model] + self.weight_momentum[model]
                    )

                # 归一化权重
                total_weight = sum(new_weights.values())
                if total_weight > 0:
                    for model in new_weights:
                        new_weights[model] /= total_weight

                # 检查权重变化是否过大（稳定性机制）
                max_change = max(
                    abs(new_weights[model] - self.weights[model])
                    for model in new_weights
                )
                if (
                    max_change < self.stability_threshold
                    or current_overall_performance > self.best_performance * 0.9
                ):
                    self.weights = new_weights
                    print(f"🔄 增强权重调整 (6算法): {self.weights}")
                    print(f"   性能: {weighted_performance}")
                    print(f"   稳定性: {stability_scores}")
                    print(f"   学习率: {self.learning_rate:.3f}")
                else:
                    print(f"⚠️ 权重变化过大，保持当前权重")

            # 如果性能持续下降，回退到历史最优权重
            if current_overall_performance < self.best_performance * 0.7:
                print(f"📉 性能下降严重，回退到历史最优权重")
                self.weights = self.best_weights.copy()

        except Exception as e:
            print(f"增强权重调整失败: {e}")

    def _calculate_performance_trend(self) -> float:
        """计算性能趋势（支持6个算法）"""
        try:
            all_models = [
                "bayesian",
                "markov1",
                "markov2",
                "frequency",
                "trend",
                "correlation",
            ]

            if len(self.detailed_performance["bayesian"]) < 4:
                return 0.0

            # 计算最近4期的平均性能趋势
            recent_scores = []
            for model in all_models:
                if self.detailed_performance[model]:
                    recent_scores.append(self.detailed_performance[model][-4:])

            if not recent_scores:
                return 0.0

            # 计算整体趋势
            overall_recent = [
                sum(scores[i] for scores in recent_scores) / len(recent_scores)
                for i in range(min(len(s) for s in recent_scores))
            ]

            if len(overall_recent) >= 2:
                return overall_recent[-1] - overall_recent[0]  # 最新 - 最旧

            return 0.0
        except:
            return 0.0


class AdvancedProbabilisticSystem:
    def __init__(self):
        self.data = None
        self.ensemble_system = None
        self.blue_kill_system = None  # 高级蓝球杀号系统
        self.red_kill_system = None  # 高级红球杀号系统

        # 默认权重配置
        self.bayesian_weight = 0.3  # 贝叶斯权重
        self.markov_weight = 0.7  # 马尔可夫权重

        # 初始化增强的多样性杀号系统
        self.enhanced_diversity_system = EnhancedDiversityKillSystem()

        # 初始化参数优化器
        self.parameter_optimizer = AdaptiveParameterOptimizer()  # 马尔可夫权重

    def _parse_period_data(self, period_item) -> Tuple[List[int], List[int]]:
        """统一解析期数据，支持字典和pandas Series格式"""
        try:
            if isinstance(period_item, dict):
                # 字典格式：{'红球': [1,2,3,4,5], '蓝球': [1,2]} 或 {'红球1': 1, '红球2': 2, ...}
                red_balls = period_item.get("红球", [])
                blue_balls = period_item.get("蓝球", [])

                if not red_balls and "红球1" in period_item:
                    red_balls = [int(period_item[f"红球{i}"]) for i in range(1, 6)]
                if not blue_balls and "蓝球1" in period_item:
                    blue_balls = [int(period_item[f"蓝球{i}"]) for i in range(1, 3)]

                return red_balls, blue_balls
            else:
                # pandas Series格式
                from src.utils.utils import parse_numbers

                return parse_numbers(period_item)
        except Exception as e:
            print(f"⚠️ 数据解析失败: {e}, 数据: {period_item}")
            return [1, 2, 3, 4, 5], [1, 2]  # 返回默认值

    def set_kill_weights(self, bayesian_weight: float, markov_weight: float):
        """设置杀号算法权重"""
        if abs(bayesian_weight + markov_weight - 1.0) > 0.01:
            raise ValueError("权重之和必须等于1.0")

        self.bayesian_weight = bayesian_weight
        self.markov_weight = markov_weight
        print(
            f"🔧 设置杀号权重: 贝叶斯{bayesian_weight:.1f}/马尔可夫{markov_weight:.1f}"
        )
        self.target_success_rate = 0.97
        self.test_periods = 10  # 回测期数改为10期

    def load_data(self) -> bool:
        """加载数据"""
        try:
            data_files = [
                "双色球历史数据.csv",
                "data/raw/dlt_data.csv",
                "dlt_data.csv",
                "data.csv",
            ]

            for file_name in data_files:
                try:
                    self.data = pd.read_csv(file_name)
                    print(f"成功加载数据文件 {file_name}: {len(self.data)} 期")
                    return True
                except FileNotFoundError:
                    continue

            print(f"未找到数据文件: {data_files}")
            return False
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False

    def initialize_system(self):
        """初始化概率系统"""
        print("🔧 初始化高级概率系统...")
        self.ensemble_system = EnsembleKillSystem(self.data)

        # 使用现有的集成杀号系统作为高级杀号系统
        self.blue_kill_system = self.ensemble_system  # 复用集成系统
        self.red_kill_system = self.ensemble_system  # 复用集成系统
        print("✅ 高级杀号系统初始化完成（使用集成系统）")

        print("✅ 系统初始化完成")

    def predict_kills_by_period(
        self, period_number: str, red_target_count: int = 13, blue_target_count: int = 5
    ) -> Dict:
        """根据期号预测杀号（主入口方法）"""
        print(f"开始预测期号 {period_number} 的杀号...")

        # 内部构建训练数据和period_data
        train_data, period_data = self._prepare_data_for_period(period_number)

        if train_data is None or period_data is None:
            print(f"期号 {period_number} 数据准备失败")
            return {
                "period_number": period_number,
                "red_kills": list(range(23, 23 + red_target_count)),
                "blue_kills": list(range(8, 8 + blue_target_count)),
                "success": False,
                "error": "数据准备失败",
            }

        # 更新系统数据并重新初始化
        self.data = train_data
        self.initialize_system()

        # 使用增强的多样性杀号系统
        try:
            # 转换数据格式为增强系统需要的格式
            enhanced_data = []
            for _, row in train_data.iterrows():
                enhanced_data.append(
                    [
                        row["期号"],
                        row["红球1"],
                        row["红球2"],
                        row["红球3"],
                        row["红球4"],
                        row["红球5"],
                        row["蓝球1"],
                        row["蓝球2"],
                    ]
                )

            # 使用增强杀号系统预测
            red_kills = self.enhanced_diversity_system.predict_enhanced_kills(
                enhanced_data, int(period_number), red_target_count, "red"
            )
            blue_kills = self.enhanced_diversity_system.predict_enhanced_kills(
                enhanced_data, int(period_number), blue_target_count, "blue"
            )

            # 如果增强系统失败，回退到原系统
            if not red_kills:
                red_kills = self.predict_red_kills(period_data, red_target_count)
            if not blue_kills:
                blue_kills = self.predict_blue_kills(period_data, blue_target_count)

        except Exception as e:
            print(f"增强杀号系统失败，回退到原系统: {e}")
            red_kills = self.predict_red_kills(period_data, red_target_count)
            blue_kills = self.predict_blue_kills(period_data, blue_target_count)

        result = {
            "period_number": period_number,
            "red_kills": red_kills,
            "blue_kills": blue_kills,
            "success": True,
            "train_data_periods": len(train_data),
            "train_data_range": f"{train_data.iloc[-1]['期号']} - {train_data.iloc[0]['期号']}",
            "enhanced_system_used": True,
        }

        print(f"期号 {period_number} 杀号预测完成")
        print(f"   红球杀号: {sorted(red_kills)}")
        print(f"   蓝球杀号: {blue_kills}")
        print(f"   训练数据: {result['train_data_range']} (共{len(train_data)}期)")

        return result

    def _prepare_data_for_period(self, target_period: str) -> Tuple:
        """为指定期号准备训练数据和period_data（从最老数据开始训练）"""
        try:
            from src.utils.utils import load_data, parse_numbers

            # 加载完整数据
            full_data = load_data("data/raw/dlt_data.csv")
            print(
                f"加载数据完成，共{len(full_data)}期，期号范围：{full_data.iloc[0]['期号']}-{full_data.iloc[-1]['期号']}"
            )

            # 找到目标期号的位置
            target_index = None
            for i, row in full_data.iterrows():
                if str(row["期号"]) == str(target_period):
                    target_index = i
                    break

            if target_index is None:
                print(f"未找到期号 {target_period}")
                print(
                    f"   可用期号范围：{full_data.iloc[0]['期号']}-{full_data.iloc[-1]['期号']}"
                )
                # 如果找不到确切期号，使用最接近的期号
                try:
                    target_num = int(target_period)
                    closest_index = None
                    min_diff = float("inf")
                    for i, row in full_data.iterrows():
                        diff = abs(int(row["期号"]) - target_num)
                        if diff < min_diff:
                            min_diff = diff
                            closest_index = i

                    if closest_index is not None:
                        target_index = closest_index
                        actual_period = full_data.iloc[target_index]["期号"]
                        print(f"使用最接近的期号 {actual_period} 替代 {target_period}")
                    else:
                        return None, None
                except:
                    return None, None

            # 构建训练数据：使用目标期号之前的所有历史数据
            # 注意：数据现在按期号升序排列（从旧到新），target_index之前的都是历史数据
            train_start = 0  # 从最老的数据开始
            train_end = target_index  # 到目标期号之前（不包含目标期号）

            if train_end - train_start < 50:  # 至少需要50期数据
                print(
                    f"期号 {target_period} 之前的历史数据不足（只有{train_end - train_start}期）"
                )
                return None, None

            train_data = full_data.iloc[train_start:train_end]
            start_period = train_data.iloc[0]["期号"]
            end_period = train_data.iloc[-1]["期号"]
            print(
                f"训练数据范围: 期号{start_period}-{end_period}，共{len(train_data)}期"
            )

            # 构建period_data：使用训练数据的最后5期（最接近目标期号的数据）
            # 数据现在从旧到新排列，所以最后几期是最接近目标期号的
            period_data = {
                "current": train_data.iloc[-1],  # 最新的一期（最接近目标期号）
                "last": (
                    train_data.iloc[-2] if len(train_data) > 1 else train_data.iloc[-1]
                ),
                "prev2": (
                    train_data.iloc[-3] if len(train_data) > 2 else train_data.iloc[-1]
                ),
                "prev3": (
                    train_data.iloc[-4] if len(train_data) > 3 else train_data.iloc[-1]
                ),
                "prev4": (
                    train_data.iloc[-5] if len(train_data) > 4 else train_data.iloc[-1]
                ),
            }

            return train_data, period_data

        except Exception as e:
            print(f"数据准备异常: {e}")
            import traceback

            traceback.print_exc()
            return None, None

    def predict_blue_kills(self, period_data: Dict, target_count: int = 2) -> List[int]:
        """预测蓝球杀号 - 使用增强多样性马尔可夫+贝叶斯算法"""
        try:
            print(f"🔵 使用增强多样性马尔可夫+贝叶斯算法计算蓝球杀号...")

            # 获取当前期号用于动态调整
            current_period = period_data.get("current", {}).get("期号", 25068)
            period_num = int(str(current_period)[-3:])  # 取期号后三位

            # 计算每个蓝球号码的综合出现概率（增强版）
            ball_probabilities = self._calculate_enhanced_blue_probabilities(
                period_data, period_num
            )

            # 应用蓝球多样性约束，避免重复杀号
            ball_probabilities = self._apply_blue_diversity_constraints(
                ball_probabilities, period_data, period_num
            )

            # 按概率从小到大排序，选择概率最小的号码作为杀号
            sorted_balls = sorted(ball_probabilities.items(), key=lambda x: x[1])

            # 动态选择候选数量
            candidate_count = min(target_count * 3, 8)  # 蓝球候选数量
            kill_candidates = [ball for ball, prob in sorted_balls[:candidate_count]]

            # 应用增强安全过滤规则
            final_kills = self._apply_enhanced_blue_safety_filters(
                kill_candidates, period_data, target_count, period_num
            )

            print(f"🔵 增强多样性蓝球杀号: {sorted(final_kills)}")
            print(
                f"  期号特征: {period_num}, 概率范围: {min(ball_probabilities.values()):.4f}-{max(ball_probabilities.values()):.4f}"
            )
            return final_kills

        except Exception as e:
            print(f"⚠️ 增强蓝球杀号算法失败，使用备选策略: {e}")
            # 备选策略：使用原有的简化策略
            return self._simple_blue_kill_strategy(period_data, target_count)

    def _simple_blue_kill_strategy(
        self, period_data: Dict, target_count: int = 1
    ) -> List[int]:
        """改进的蓝球杀号策略（多样化策略）"""
        try:
            from src.utils.utils import parse_numbers
            from collections import Counter
            import random

            # 获取最近5期蓝球数据（扩大观察窗口）
            recent_blues = []
            for key in ["current", "last", "prev2", "prev3", "prev4"]:
                if key in period_data:
                    _, blue_balls = parse_numbers(period_data[key])
                    recent_blues.extend(blue_balls)

            # 多策略融合杀号
            kill_candidates = set()
            all_blues = list(range(1, 13))

            # 策略1: 频率统计
            blue_freq = Counter(recent_blues)
            min_freq = min(blue_freq.get(b, 0) for b in all_blues)
            freq_candidates = [b for b in all_blues if blue_freq.get(b, 0) == min_freq]

            # 策略2: 基于期号特征的动态选择
            current_period_num = int(
                str(period_data["current"]["期号"])[-2:]
            )  # 取期号后两位

            # 根据期号特征选择不同策略
            if current_period_num % 3 == 0:  # 3的倍数期号
                # 偏向杀小号蓝球
                zone_candidates = [b for b in range(1, 5) if b not in recent_blues[-3:]]
            elif current_period_num % 3 == 1:  # 余1期号
                # 偏向杀中号蓝球
                zone_candidates = [b for b in range(5, 9) if b not in recent_blues[-3:]]
            else:  # 余2期号
                # 偏向杀大号蓝球
                zone_candidates = [
                    b for b in range(9, 13) if b not in recent_blues[-3:]
                ]

            # 策略3: 连续未出现
            never_appeared = [b for b in all_blues if b not in recent_blues]

            # 策略4: 基于奇偶性
            period_odd = current_period_num % 2
            if period_odd:
                # 奇数期号偏向杀偶数蓝球
                parity_candidates = [
                    b for b in all_blues if b % 2 == 0 and b not in recent_blues[-2:]
                ]
            else:
                # 偶数期号偏向杀奇数蓝球
                parity_candidates = [
                    b for b in all_blues if b % 2 == 1 and b not in recent_blues[-2:]
                ]

            # 综合选择
            if freq_candidates:
                kill_candidates.update(freq_candidates[:1])
            if zone_candidates:
                kill_candidates.update(zone_candidates[:1])
            if never_appeared:
                kill_candidates.update(never_appeared[:1])
            if parity_candidates:
                kill_candidates.update(parity_candidates[:1])

            # 如果候选不足，使用随机策略
            if len(kill_candidates) < target_count:
                remaining = [
                    b
                    for b in all_blues
                    if b not in kill_candidates and b not in recent_blues[-2:]
                ]
                if remaining:
                    random.seed(current_period_num)  # 确定性随机
                    additional = random.sample(
                        remaining,
                        min(target_count - len(kill_candidates), len(remaining)),
                    )
                    kill_candidates.update(additional)

            # 转换为列表并排序
            final_kills = sorted(list(kill_candidates))

            # 返回指定数量的杀号
            result = final_kills[:target_count] if final_kills else [12]

            print(
                f"🎯 蓝球杀号策略: 期号{current_period_num}, 频率{len(freq_candidates)}, 区间{len(zone_candidates) if 'zone_candidates' in locals() else 0}, 奇偶{len(parity_candidates) if 'parity_candidates' in locals() else 0}"
            )

            return result

        except Exception as e:
            print(f"⚠️ 简化蓝球杀号预测失败: {e}")
            # 基于期号的确定性回退
            try:
                period_num = int(str(period_data["current"]["期号"])[-1:])
                return [period_num % 12 + 1]
            except:
                return [9]

    def _calculate_enhanced_blue_probabilities(
        self, period_data: Dict, period_num: int
    ) -> Dict[int, float]:
        """计算增强的蓝球综合出现概率（马尔可夫+贝叶斯+动态调整）"""
        try:
            from src.utils.utils import parse_numbers
            from collections import defaultdict, Counter
            import math

            # 获取历史蓝球数据
            historical_blues = []
            recent_blues = []

            # 收集最近期数据用于马尔可夫链（增加时间衰减权重）
            time_weights = [1.0, 0.8, 0.6, 0.4, 0.2]  # 时间衰减权重
            for i, key in enumerate(["last", "prev2", "prev3", "prev4", "prev5"]):
                if key in period_data:
                    _, blue_balls = parse_numbers(period_data[key])
                    # 应用时间权重
                    weight = time_weights[i] if i < len(time_weights) else 0.1
                    weighted_blues = blue_balls * int(weight * 10)  # 转换为整数权重
                    recent_blues.extend(weighted_blues)
                    if len(historical_blues) < 50:
                        historical_blues.extend(blue_balls)

            # 如果数据不足，使用系统数据
            if (
                len(historical_blues) < 30
                and hasattr(self, "data")
                and self.data is not None
            ):
                for i in range(min(30, len(self.data))):
                    try:
                        _, blue_balls = parse_numbers(self.data.iloc[i])
                        historical_blues.extend(blue_balls)
                    except:
                        continue

            # 初始化概率字典
            ball_probabilities = {}

            # 使用类设置的权重（支持参数优化）
            bayesian_weight = self.bayesian_weight
            markov_weight = self.markov_weight

            for ball in range(1, 13):  # 蓝球范围1-12
                # 1. 增强贝叶斯概率计算
                bayesian_prob = self._calculate_enhanced_blue_bayesian_probability(
                    ball, period_data, historical_blues, period_num
                )

                # 2. 增强马尔可夫概率计算
                markov_prob = self._calculate_enhanced_blue_markov_probability(
                    ball, recent_blues, period_num
                )

                # 3. 动态权重综合概率
                comprehensive_prob = (
                    bayesian_weight * bayesian_prob + markov_weight * markov_prob
                )

                # 4. 添加随机扰动（基于期号的确定性随机）
                import random

                random.seed(period_num + ball + 100)  # 确定性随机（与红球区分）
                noise_factor = (
                    1.0 + (random.random() - 0.5) * 0.3
                )  # ±15%扰动（蓝球扰动更大）
                comprehensive_prob *= noise_factor

                ball_probabilities[ball] = comprehensive_prob

            print(
                f"  📊 蓝球增强概率计算完成，权重 贝叶斯{bayesian_weight:.2f}/马尔可夫{markov_weight:.2f}"
            )
            return ball_probabilities

        except Exception as e:
            print(f"⚠️ 蓝球增强概率计算失败: {e}")
            # 返回均匀分布作为回退
            return {ball: 1.0 / 12 for ball in range(1, 13)}

    def _apply_blue_diversity_constraints(
        self, ball_probabilities: Dict[int, float], period_data: Dict, period_num: int
    ) -> Dict[int, float]:
        """应用蓝球多样性约束，避免重复杀号"""
        try:
            from src.utils.utils import parse_numbers

            # 获取最近几期的蓝球杀号历史（模拟）
            recent_blue_kills = set()

            # 基于最近期蓝球号码推测可能的杀号模式
            for key in ["last", "prev2"]:
                if key in period_data:
                    _, blue_balls = parse_numbers(period_data[key])
                    # 推测这些期次可能的杀号（频率最低的号码）
                    all_blues = set(range(1, 13))
                    appeared_blues = set(blue_balls)
                    potential_kills = all_blues - appeared_blues
                    recent_blue_kills.update(
                        list(potential_kills)[:2]
                    )  # 取前2个作为可能的历史杀号

            # 对最近可能使用过的杀号降低优先级
            adjusted_probabilities = ball_probabilities.copy()

            for ball in recent_blue_kills:
                if ball in adjusted_probabilities:
                    # 增加概率，降低被选为杀号的可能性
                    adjusted_probabilities[ball] *= 1.8  # 蓝球惩罚更强

            # 区间平衡约束（蓝球分为小中大三个区间）
            zone_counts = {
                "small": len(
                    [b for b in range(1, 5) if ball_probabilities[b] < 0.08]
                ),  # 1-4
                "medium": len(
                    [b for b in range(5, 9) if ball_probabilities[b] < 0.08]
                ),  # 5-8
                "large": len(
                    [b for b in range(9, 13) if ball_probabilities[b] < 0.08]
                ),  # 9-12
            }

            # 如果某个区间候选过多，适当调整
            for zone, (start, end) in [
                ("small", (1, 5)),
                ("medium", (5, 9)),
                ("large", (9, 13)),
            ]:
                if zone_counts[zone] > 2:  # 蓝球每个区间最多2个候选
                    zone_balls = list(range(start, end))
                    zone_probs = [(b, adjusted_probabilities[b]) for b in zone_balls]
                    zone_probs.sort(key=lambda x: x[1])

                    # 对该区间概率最低的号码适当增加概率
                    for i in range(min(1, len(zone_probs))):
                        ball = zone_probs[i][0]
                        adjusted_probabilities[ball] *= 1.3

            return adjusted_probabilities

        except Exception as e:
            print(f"⚠️ 蓝球多样性约束应用失败: {e}")
            return ball_probabilities

    def _calculate_enhanced_blue_bayesian_probability(
        self, ball: int, period_data: Dict, historical_blues: List[int], period_num: int
    ) -> float:
        """计算增强蓝球贝叶斯概率"""
        try:
            from src.utils.utils import parse_numbers
            from collections import Counter
            import math

            # 先验概率：基于历史频率（加入时间衰减）
            if historical_blues:
                freq_counter = Counter(historical_blues)
                prior_prob = freq_counter.get(ball, 0) / len(historical_blues)

                # 时间衰减调整
                time_factor = 1.0 + 0.15 * math.sin(
                    period_num * 0.08
                )  # 蓝球时间因子更强
                prior_prob *= time_factor
            else:
                prior_prob = 1.0 / 12  # 均匀先验

            # 似然概率：基于当前条件（增强版）
            likelihood = 1.0

            if "last" in period_data:
                _, last_blue = parse_numbers(period_data["last"])

                # 连续出现概率调整（蓝球更强的惩罚）
                if ball in last_blue:
                    likelihood *= 0.1  # 蓝球连续出现概率极低

                # 基于号码间距离的似然调整
                if last_blue:
                    min_distance = min(abs(ball - last_ball) for last_ball in last_blue)
                    if min_distance <= 1:  # 紧邻号码
                        likelihood *= 0.3
                    elif min_distance <= 2:  # 相近号码
                        likelihood *= 0.6
                    elif min_distance >= 6:  # 距离很远
                        likelihood *= 1.5

                # 奇偶性调整（蓝球奇偶性更重要）
                ball_parity = ball % 2
                last_parity_count = sum(1 for b in last_blue if b % 2 == ball_parity)
                if last_parity_count >= 1:  # 蓝球只有1-2个，奇偶性影响更大
                    likelihood *= 0.6

            # 期号特征调整（蓝球特有）
            period_factor = 1.0 + 0.2 * math.cos(period_num * 0.2 + ball * 0.3)
            likelihood *= period_factor

            # 后验概率 ∝ 先验 × 似然
            posterior_prob = prior_prob * likelihood

            return posterior_prob

        except Exception as e:
            print(f"⚠️ 增强蓝球贝叶斯概率计算失败 (球{ball}): {e}")
            return 1.0 / 12

    def _calculate_enhanced_blue_markov_probability(
        self, ball: int, recent_blues: List[int], period_num: int
    ) -> float:
        """计算增强蓝球马尔可夫链概率"""
        try:
            from collections import defaultdict, Counter
            import math

            if len(recent_blues) < 6:  # 蓝球数据要求更低
                return 1.0 / 12

            # 1阶马尔可夫链（蓝球版）
            markov1_prob = self._calculate_enhanced_blue_first_order_markov(
                ball, recent_blues, period_num
            )

            # 简化的2阶马尔可夫链（蓝球数据较少）
            markov2_prob = self._calculate_enhanced_blue_second_order_markov(
                ball, recent_blues, period_num
            )

            # 动态权重组合（基于期号）
            weight1 = 0.7 + 0.2 * math.sin(period_num * 0.12)  # 0.5-0.9
            weight2 = 1.0 - weight1

            combined_markov_prob = weight1 * markov1_prob + weight2 * markov2_prob

            return combined_markov_prob

        except Exception as e:
            print(f"⚠️ 增强蓝球马尔可夫概率计算失败 (球{ball}): {e}")
            return 1.0 / 12

    def _calculate_enhanced_blue_first_order_markov(
        self, ball: int, recent_blues: List[int], period_num: int
    ) -> float:
        """计算增强蓝球1阶马尔可夫转移概率"""
        try:
            from collections import defaultdict
            import math

            # 构建转移计数矩阵（蓝球版）
            transitions = defaultdict(lambda: defaultdict(float))

            # 蓝球按期分组（每1-2个号码为一期）
            periods = []
            i = 0
            while i < len(recent_blues):
                period_blues = recent_blues[i : i + 2]  # 每期最多2个蓝球
                if period_blues:
                    periods.append(period_blues)
                i += 2

            for i in range(len(periods) - 1):
                current_period = set(periods[i])
                next_period = set(periods[i + 1])

                # 时间权重（越近的转移权重越高）
                time_weight = math.exp(-i * 0.2)

                # 记录从当前期到下一期的转移
                for current_ball in current_period:
                    for next_ball in next_period:
                        transitions[current_ball][next_ball] += time_weight

            # 计算目标球的转移概率
            total_transitions = 0.0
            ball_transitions = 0.0

            for from_ball in transitions:
                for to_ball in transitions[from_ball]:
                    weight = transitions[from_ball][to_ball]
                    total_transitions += weight
                    if to_ball == ball:
                        ball_transitions += weight

            if total_transitions > 0:
                base_prob = ball_transitions / total_transitions
                # 期号调整
                period_factor = 1.0 + 0.1 * math.sin(period_num * 0.18 + ball * 0.15)
                return base_prob * period_factor
            else:
                return 1.0 / 12

        except Exception as e:
            return 1.0 / 12

    def _calculate_enhanced_blue_second_order_markov(
        self, ball: int, recent_blues: List[int], period_num: int
    ) -> float:
        """计算增强蓝球2阶马尔可夫转移概率"""
        try:
            from collections import defaultdict
            import math

            # 构建2阶转移计数（蓝球版）
            transitions = defaultdict(lambda: defaultdict(float))

            # 蓝球按期分组
            periods = []
            i = 0
            while i < len(recent_blues):
                period_blues = recent_blues[i : i + 2]
                if period_blues:
                    periods.append(tuple(sorted(period_blues)))
                i += 2

            for i in range(len(periods) - 2):
                prev2_period = periods[i]
                prev1_period = periods[i + 1]
                current_period = set(recent_blues[(i + 2) * 2 : (i + 3) * 2])

                # 时间权重
                time_weight = math.exp(-i * 0.25)

                # 2阶状态：前两期的组合
                state = (prev2_period, prev1_period)

                for current_ball in current_period:
                    transitions[state][current_ball] += time_weight

            # 计算目标球的2阶转移概率
            total_transitions = 0.0
            ball_transitions = 0.0

            for state in transitions:
                for to_ball in transitions[state]:
                    weight = transitions[state][to_ball]
                    total_transitions += weight
                    if to_ball == ball:
                        ball_transitions += weight

            if total_transitions > 0:
                base_prob = ball_transitions / total_transitions
                # 期号调整
                period_factor = 1.0 + 0.05 * math.cos(period_num * 0.22 + ball * 0.12)
                return base_prob * period_factor
            else:
                return 1.0 / 12

        except Exception as e:
            return 1.0 / 12

    def predict_red_kills(self, period_data: Dict, target_count: int = 6) -> List[int]:
        """预测红球杀号 - 使用增强多样性马尔可夫+贝叶斯算法（无安全过滤）"""
        try:
            print(f"🎯 使用增强多样性马尔可夫+贝叶斯算法计算红球杀号...")

            # 获取当前期号用于动态调整
            current_period = period_data.get("current", {}).get("期号", 25068)
            period_num = int(str(current_period)[-3:])  # 取期号后三位

            # 计算每个红球号码的综合出现概率（增强版）
            ball_probabilities = self._calculate_enhanced_probabilities(
                period_data, period_num
            )

            # 应用多样性约束，避免重复杀号
            ball_probabilities = self._apply_diversity_constraints(
                ball_probabilities, period_data, period_num
            )

            # 按概率从小到大排序，直接选择概率最小的6个号码作为杀号
            sorted_balls = sorted(ball_probabilities.items(), key=lambda x: x[1])

            # 直接选择概率最小的6个号码，不进行安全过滤
            final_kills = [ball for ball, prob in sorted_balls[:target_count]]

            print(f"🎯 增强多样性红球杀号: {sorted(final_kills)}")
            print(
                f"  期号特征: {period_num}, 概率范围: {min(ball_probabilities.values()):.4f}-{max(ball_probabilities.values()):.4f}"
            )

            # 保存最后一次的概率详情用于分析
            self._last_probability_details = {
                "final_probabilities": ball_probabilities,
                "period_num": period_num,
                "weights": {
                    "bayesian": self.bayesian_weight,
                    "markov": self.markov_weight,
                },
            }

            return final_kills

        except Exception as e:
            print(f"⚠️ 增强杀号算法失败，使用备选策略: {e}")
            # 备选策略：使用原有的集成杀号系统
            return self.ensemble_system.predict_ensemble_kills(
                period_data, target_count
            )

    def get_probability_details(self) -> Dict:
        """获取最后一次概率计算的详细信息"""
        return getattr(self, "_last_probability_details", {})

    def _calculate_enhanced_probabilities(
        self, period_data: Dict, period_num: int
    ) -> Dict[int, float]:
        """计算增强的综合出现概率（马尔可夫+贝叶斯+动态调整）"""
        try:
            from src.utils.utils import parse_numbers
            from collections import defaultdict, Counter
            import math

            # 获取历史数据
            historical_reds = []
            recent_reds = []

            # 收集最近期数据用于马尔可夫链（增加时间衰减权重）
            time_weights = [1.0, 0.8, 0.6, 0.4, 0.2]  # 时间衰减权重
            for i, key in enumerate(["last", "prev2", "prev3", "prev4", "prev5"]):
                if key in period_data:
                    red_balls, _ = self._parse_period_data(period_data[key])

                    # 应用时间权重
                    weight = time_weights[i] if i < len(time_weights) else 0.1
                    weighted_reds = red_balls * int(weight * 10)  # 转换为整数权重
                    recent_reds.extend(weighted_reds)
                    if len(historical_reds) < 100:
                        historical_reds.extend(red_balls)

            # 如果数据不足，使用系统数据
            if (
                len(historical_reds) < 50
                and hasattr(self, "data")
                and self.data is not None
            ):
                for i in range(min(50, len(self.data))):
                    try:
                        red_balls, _ = parse_numbers(self.data.iloc[i])
                        historical_reds.extend(red_balls)
                    except:
                        continue

            # 初始化概率字典
            ball_probabilities = {}

            # 使用类设置的权重（支持参数优化）
            bayesian_weight = self.bayesian_weight
            markov_weight = self.markov_weight

            for ball in range(1, 36):
                # 1. 增强贝叶斯概率计算
                bayesian_prob = self._calculate_enhanced_bayesian_probability(
                    ball, period_data, historical_reds, period_num
                )

                # 2. 增强马尔可夫概率计算
                markov_prob = self._calculate_enhanced_markov_probability(
                    ball, recent_reds, period_num
                )

                # 3. 动态权重综合概率
                comprehensive_prob = (
                    bayesian_weight * bayesian_prob + markov_weight * markov_prob
                )

                # 4. 添加随机扰动（基于期号的确定性随机）
                import random

                random.seed(period_num + ball)  # 确定性随机
                noise_factor = 1.0 + (random.random() - 0.5) * 0.2  # ±10%扰动
                comprehensive_prob *= noise_factor

                ball_probabilities[ball] = comprehensive_prob

            print(
                f"  📊 增强概率计算完成，权重 贝叶斯{bayesian_weight:.2f}/马尔可夫{markov_weight:.2f}"
            )
            return ball_probabilities

        except Exception as e:
            print(f"⚠️ 增强概率计算失败: {e}")
            # 返回均匀分布作为回退
            return {ball: 1.0 / 35 for ball in range(1, 36)}

    def _calculate_comprehensive_probabilities(
        self, period_data: Dict
    ) -> Dict[int, float]:
        """计算每个红球号码的综合出现概率（马尔可夫+贝叶斯）"""
        try:
            from src.utils.utils import parse_numbers
            from collections import defaultdict, Counter

            # 获取历史数据
            historical_reds = []
            recent_reds = []

            # 收集最近期数据用于马尔可夫链
            for key in ["last", "prev2", "prev3", "prev4", "prev5"]:
                if key in period_data:
                    red_balls, _ = self._parse_period_data(period_data[key])
                    recent_reds.extend(red_balls)
                    if len(historical_reds) < 100:  # 收集更多历史数据
                        historical_reds.extend(red_balls)

            # 如果数据不足，使用系统数据
            if (
                len(historical_reds) < 50
                and hasattr(self, "data")
                and self.data is not None
            ):
                for i in range(min(50, len(self.data))):
                    try:
                        red_balls, _ = parse_numbers(self.data.iloc[i])
                        historical_reds.extend(red_balls)
                    except:
                        continue

            # 初始化概率字典
            ball_probabilities = {}

            for ball in range(1, 36):
                # 1. 贝叶斯概率计算
                bayesian_prob = self._calculate_bayesian_probability(
                    ball, period_data, historical_reds
                )

                # 2. 马尔可夫概率计算
                markov_prob = self._calculate_markov_probability(ball, recent_reds)

                # 3. 综合概率（加权平均）
                # 贝叶斯权重60%，马尔可夫权重40%
                comprehensive_prob = 0.6 * bayesian_prob + 0.4 * markov_prob

                ball_probabilities[ball] = comprehensive_prob

            print(
                f"  📊 概率计算完成，最低概率: {min(ball_probabilities.values()):.4f}"
            )
            return ball_probabilities

        except Exception as e:
            print(f"⚠️ 综合概率计算失败: {e}")
            # 返回均匀分布作为回退
            return {ball: 1.0 / 35 for ball in range(1, 36)}

    def _apply_diversity_constraints(
        self, ball_probabilities: Dict[int, float], period_data: Dict, period_num: int
    ) -> Dict[int, float]:
        """应用多样性约束，避免重复杀号"""
        try:
            from src.utils.utils import parse_numbers

            # 获取最近几期的杀号历史（模拟）
            recent_kills = set()

            # 基于最近期号码推测可能的杀号模式
            for key in ["last", "prev2"]:
                if key in period_data:
                    red_balls, _ = self._parse_period_data(period_data[key])
                    # 推测这些期次可能的杀号（频率最低的号码）
                    all_reds = set(range(1, 36))
                    appeared_reds = set(red_balls)
                    potential_kills = all_reds - appeared_reds
                    recent_kills.update(
                        list(potential_kills)[:3]
                    )  # 取前3个作为可能的历史杀号

            # 对最近可能使用过的杀号降低优先级
            adjusted_probabilities = ball_probabilities.copy()

            for ball in recent_kills:
                if ball in adjusted_probabilities:
                    # 增加概率，降低被选为杀号的可能性
                    adjusted_probabilities[ball] *= 1.5

            # 区间平衡约束
            # 确保杀号在不同区间有分布
            zone_counts = {
                "small": len([b for b in range(1, 13) if ball_probabilities[b] < 0.02]),
                "medium": len(
                    [b for b in range(13, 24) if ball_probabilities[b] < 0.02]
                ),
                "large": len(
                    [b for b in range(24, 36) if ball_probabilities[b] < 0.02]
                ),
            }

            # 如果某个区间候选过多，适当调整
            for zone, (start, end) in [
                ("small", (1, 13)),
                ("medium", (13, 24)),
                ("large", (24, 36)),
            ]:
                if zone_counts[zone] > 3:  # 如果某区间候选过多
                    zone_balls = list(range(start, end))
                    zone_probs = [(b, adjusted_probabilities[b]) for b in zone_balls]
                    zone_probs.sort(key=lambda x: x[1])

                    # 对该区间概率最低的几个号码适当增加概率
                    for i in range(min(2, len(zone_probs))):
                        ball = zone_probs[i][0]
                        adjusted_probabilities[ball] *= 1.2

            return adjusted_probabilities

        except Exception as e:
            print(f"⚠️ 多样性约束应用失败: {e}")
            return ball_probabilities

    def _calculate_enhanced_bayesian_probability(
        self, ball: int, period_data: Dict, historical_reds: List[int], period_num: int
    ) -> float:
        """计算增强贝叶斯概率"""
        try:
            from src.utils.utils import parse_numbers
            from collections import Counter
            import math

            # 先验概率：基于历史频率（加入时间衰减）
            if historical_reds:
                freq_counter = Counter(historical_reds)
                prior_prob = freq_counter.get(ball, 0) / len(historical_reds)

                # 时间衰减调整
                time_factor = 1.0 + 0.1 * math.sin(period_num * 0.05)
                prior_prob *= time_factor
            else:
                prior_prob = 1.0 / 35  # 均匀先验

            # 似然概率：基于当前条件（增强版）
            likelihood = 1.0

            if "last" in period_data:
                last_red, _ = self._parse_period_data(period_data["last"])

                # 连续出现概率调整
                if ball in last_red:
                    likelihood *= 0.2  # 更强的连续出现惩罚

                # 基于号码间距离的似然调整（增强版）
                min_distance = min(abs(ball - last_ball) for last_ball in last_red)
                if min_distance <= 1:  # 紧邻号码
                    likelihood *= 0.5
                elif min_distance <= 3:  # 相近号码
                    likelihood *= 0.7
                elif min_distance >= 15:  # 距离很远
                    likelihood *= 1.3

                # 奇偶性调整
                ball_parity = ball % 2
                last_parity_count = sum(1 for b in last_red if b % 2 == ball_parity)
                if last_parity_count >= 3:  # 如果前期同奇偶性号码过多
                    likelihood *= 0.8

            # 期号特征调整
            period_factor = 1.0 + 0.1 * math.cos(period_num * 0.1 + ball * 0.2)
            likelihood *= period_factor

            # 后验概率 ∝ 先验 × 似然
            posterior_prob = prior_prob * likelihood

            return posterior_prob

        except Exception as e:
            print(f"⚠️ 增强贝叶斯概率计算失败 (球{ball}): {e}")
            return 1.0 / 35

    def _calculate_enhanced_markov_probability(
        self, ball: int, recent_reds: List[int], period_num: int
    ) -> float:
        """计算增强马尔可夫链概率"""
        try:
            from collections import defaultdict, Counter
            import math

            if len(recent_reds) < 10:  # 数据不足
                return 1.0 / 35

            # 1阶马尔可夫链（增强版）
            markov1_prob = self._calculate_enhanced_first_order_markov(
                ball, recent_reds, period_num
            )

            # 2阶马尔可夫链（增强版）
            markov2_prob = self._calculate_enhanced_second_order_markov(
                ball, recent_reds, period_num
            )

            # 使用优化后的权重参数（基于参数优化结果）
            # 优化结果显示：一阶权重0.3效果最佳
            weight1 = 0.3  # 优化参数：一阶权重0.3
            weight2 = 0.7  # 二阶权重0.7

            combined_markov_prob = weight1 * markov1_prob + weight2 * markov2_prob

            return combined_markov_prob

        except Exception as e:
            print(f"⚠️ 增强马尔可夫概率计算失败 (球{ball}): {e}")
            return 1.0 / 35

    def _calculate_bayesian_probability(
        self, ball: int, period_data: Dict, historical_reds: List[int]
    ) -> float:
        """计算贝叶斯概率"""
        try:
            from src.utils.utils import parse_numbers
            from collections import Counter

            # 先验概率：基于历史频率
            if historical_reds:
                freq_counter = Counter(historical_reds)
                prior_prob = freq_counter.get(ball, 0) / len(historical_reds)
            else:
                prior_prob = 1.0 / 35  # 均匀先验

            # 似然概率：基于当前条件（前期号码）
            likelihood = 1.0

            if "last" in period_data:
                last_red, _ = self._parse_period_data(period_data["last"])

                # 计算与前期号码的条件概率
                # 如果该号码在前期出现过，降低其再次出现的概率
                if ball in last_red:
                    likelihood *= 0.3  # 连续出现概率较低

                # 基于号码间距离的似然调整
                min_distance = min(abs(ball - last_ball) for last_ball in last_red)
                if min_distance <= 2:  # 相邻号码
                    likelihood *= 0.7
                elif min_distance >= 10:  # 距离较远
                    likelihood *= 1.2

            # 后验概率 ∝ 先验 × 似然
            posterior_prob = prior_prob * likelihood

            return posterior_prob

        except Exception as e:
            print(f"⚠️ 贝叶斯概率计算失败 (球{ball}): {e}")
            return 1.0 / 35

    def _calculate_markov_probability(self, ball: int, recent_reds: List[int]) -> float:
        """计算马尔可夫链概率"""
        try:
            from collections import defaultdict, Counter

            if len(recent_reds) < 10:  # 数据不足
                return 1.0 / 35

            # 1阶马尔可夫链：基于前一期的转移概率
            markov1_prob = self._calculate_first_order_markov(ball, recent_reds)

            # 2阶马尔可夫链：基于前两期的转移概率
            markov2_prob = self._calculate_second_order_markov(ball, recent_reds)

            # 组合两种马尔可夫概率（1阶权重70%，2阶权重30%）
            combined_markov_prob = 0.7 * markov1_prob + 0.3 * markov2_prob

            return combined_markov_prob

        except Exception as e:
            print(f"⚠️ 马尔可夫概率计算失败 (球{ball}): {e}")
            return 1.0 / 35

    def _calculate_first_order_markov(self, ball: int, recent_reds: List[int]) -> float:
        """计算1阶马尔可夫转移概率"""
        try:
            from collections import defaultdict

            # 构建转移计数矩阵
            transitions = defaultdict(lambda: defaultdict(int))

            # 按期分组（每5个号码为一期）
            periods = [recent_reds[i : i + 5] for i in range(0, len(recent_reds), 5)]

            for i in range(len(periods) - 1):
                current_period = set(periods[i])
                next_period = set(periods[i + 1])

                # 记录从当前期到下一期的转移
                for current_ball in current_period:
                    for next_ball in next_period:
                        transitions[current_ball][next_ball] += 1

            # 计算目标球的转移概率
            total_transitions = 0
            ball_transitions = 0

            for from_ball in transitions:
                for to_ball in transitions[from_ball]:
                    total_transitions += transitions[from_ball][to_ball]
                    if to_ball == ball:
                        ball_transitions += transitions[from_ball][to_ball]

            if total_transitions > 0:
                return ball_transitions / total_transitions
            else:
                return 1.0 / 35

        except Exception as e:
            return 1.0 / 35

    def _calculate_second_order_markov(
        self, ball: int, recent_reds: List[int]
    ) -> float:
        """计算2阶马尔可夫转移概率"""
        try:
            from collections import defaultdict

            # 构建2阶转移计数
            transitions = defaultdict(lambda: defaultdict(int))

            # 按期分组
            periods = [recent_reds[i : i + 5] for i in range(0, len(recent_reds), 5)]

            for i in range(len(periods) - 2):
                prev2_period = tuple(sorted(periods[i]))
                prev1_period = tuple(sorted(periods[i + 1]))
                current_period = set(periods[i + 2])

                # 2阶状态：前两期的组合
                state = (prev2_period, prev1_period)

                for current_ball in current_period:
                    transitions[state][current_ball] += 1

            # 计算目标球的2阶转移概率
            total_transitions = 0
            ball_transitions = 0

            for state in transitions:
                for to_ball in transitions[state]:
                    total_transitions += transitions[state][to_ball]
                    if to_ball == ball:
                        ball_transitions += transitions[state][to_ball]

            if total_transitions > 0:
                return ball_transitions / total_transitions
            else:
                return 1.0 / 35

        except Exception as e:
            return 1.0 / 35

    def _calculate_enhanced_first_order_markov(
        self, ball: int, recent_reds: List[int], period_num: int
    ) -> float:
        """计算增强1阶马尔可夫转移概率"""
        try:
            from collections import defaultdict
            import math

            # 构建转移计数矩阵（加权）
            transitions = defaultdict(lambda: defaultdict(float))

            # 按期分组（每5个号码为一期）
            periods = [recent_reds[i : i + 5] for i in range(0, len(recent_reds), 5)]

            for i in range(len(periods) - 1):
                current_period = set(periods[i])
                next_period = set(periods[i + 1])

                # 时间权重（越近的转移权重越高）- 使用优化后的衰减参数
                time_weight = math.exp(-i * 0.15)  # 优化参数：0.15衰减

                # 记录从当前期到下一期的转移
                for current_ball in current_period:
                    for next_ball in next_period:
                        transitions[current_ball][next_ball] += time_weight

            # 计算目标球的转移概率
            total_transitions = 0.0
            ball_transitions = 0.0

            for from_ball in transitions:
                for to_ball in transitions[from_ball]:
                    weight = transitions[from_ball][to_ball]
                    total_transitions += weight
                    if to_ball == ball:
                        ball_transitions += weight

            if total_transitions > 0:
                base_prob = ball_transitions / total_transitions
                # 期号调整
                period_factor = 1.0 + 0.05 * math.sin(period_num * 0.12 + ball * 0.1)
                return base_prob * period_factor
            else:
                return 1.0 / 35

        except Exception as e:
            return 1.0 / 35

    def _calculate_enhanced_second_order_markov(
        self, ball: int, recent_reds: List[int], period_num: int
    ) -> float:
        """计算增强2阶马尔可夫转移概率"""
        try:
            from collections import defaultdict
            import math

            # 构建2阶转移计数（加权）
            transitions = defaultdict(lambda: defaultdict(float))

            # 按期分组
            periods = [recent_reds[i : i + 5] for i in range(0, len(recent_reds), 5)]

            for i in range(len(periods) - 2):
                prev2_period = tuple(sorted(periods[i]))
                prev1_period = tuple(sorted(periods[i + 1]))
                current_period = set(periods[i + 2])

                # 时间权重
                time_weight = math.exp(-i * 0.15)

                # 2阶状态：前两期的组合
                state = (prev2_period, prev1_period)

                for current_ball in current_period:
                    transitions[state][current_ball] += time_weight

            # 计算目标球的2阶转移概率
            total_transitions = 0.0
            ball_transitions = 0.0

            for state in transitions:
                for to_ball in transitions[state]:
                    weight = transitions[state][to_ball]
                    total_transitions += weight
                    if to_ball == ball:
                        ball_transitions += weight

            if total_transitions > 0:
                base_prob = ball_transitions / total_transitions
                # 期号调整
                period_factor = 1.0 + 0.03 * math.cos(period_num * 0.15 + ball * 0.08)
                return base_prob * period_factor
            else:
                return 1.0 / 35

        except Exception as e:
            return 1.0 / 35

    def _apply_enhanced_safety_filters(
        self,
        kill_candidates: List[int],
        period_data: Dict,
        target_count: int,
        period_num: int,
    ) -> List[int]:
        """应用增强安全过滤规则，确保杀号的合理性和多样性"""
        try:
            from src.utils.utils import parse_numbers
            import random

            # 获取最近期号码，避免杀掉刚出现的号码
            recent_appeared = set()
            for key in ["last", "prev2"]:
                if key in period_data:
                    red_balls, _ = parse_numbers(period_data[key])
                    recent_appeared.update(red_balls)

            # 过滤规则
            filtered_kills = []
            zone_counts = {"small": 0, "medium": 0, "large": 0}  # 区间计数

            # 确定性随机排序（基于期号）
            random.seed(period_num)
            random.shuffle(kill_candidates)

            for ball in kill_candidates:
                # 规则1：不杀最近2期出现的号码
                if ball in recent_appeared:
                    continue

                # 规则2：区间平衡控制
                if ball <= 12:
                    zone = "small"
                elif ball <= 24:
                    zone = "medium"
                else:
                    zone = "large"

                # 限制每个区间最多2个杀号
                if zone_counts[zone] >= 2:
                    continue

                # 规则3：避免连续号码过多
                consecutive_count = 0
                for existing_kill in filtered_kills:
                    if abs(ball - existing_kill) <= 2:
                        consecutive_count += 1

                if consecutive_count >= 2:  # 最多2个相近号码
                    continue

                # 规则4：奇偶平衡
                odd_count = len([k for k in filtered_kills if k % 2 == 1])
                even_count = len(filtered_kills) - odd_count

                if ball % 2 == 1 and odd_count >= 3:  # 最多3个奇数
                    continue
                if ball % 2 == 0 and even_count >= 3:  # 最多3个偶数
                    continue

                # 通过所有过滤规则
                filtered_kills.append(ball)
                zone_counts[zone] += 1

                if len(filtered_kills) >= target_count:
                    break

            # 如果过滤后数量不足，补充一些安全的候选
            if len(filtered_kills) < target_count:
                backup_candidates = [
                    b
                    for b in kill_candidates
                    if b not in filtered_kills and b not in recent_appeared
                ]
                needed = target_count - len(filtered_kills)

                # 优先选择不同区间的号码
                for zone, (start, end) in [
                    ("small", (1, 13)),
                    ("medium", (13, 25)),
                    ("large", (25, 36)),
                ]:
                    if zone_counts[zone] == 0 and needed > 0:  # 如果某区间还没有杀号
                        zone_candidates = [
                            b for b in backup_candidates if start <= b < end
                        ]
                        if zone_candidates:
                            filtered_kills.append(zone_candidates[0])
                            needed -= 1

                # 如果还是不足，随机补充
                if needed > 0:
                    remaining = [
                        b for b in backup_candidates if b not in filtered_kills
                    ]
                    random.seed(period_num + 100)
                    additional = (
                        random.sample(remaining, min(needed, len(remaining)))
                        if remaining
                        else []
                    )
                    filtered_kills.extend(additional)

            # 最终确保数量正确
            final_kills = filtered_kills[:target_count]

            # 如果还是不足，使用动态默认策略
            if len(final_kills) < target_count:
                # 基于期号的动态默认杀号
                base_kills = [30, 31, 32, 33, 34, 35, 1, 2, 3]
                period_offset = period_num % 5
                default_kills = base_kills[period_offset : period_offset + target_count]

                for ball in default_kills:
                    if ball not in final_kills and ball not in recent_appeared:
                        final_kills.append(ball)
                        if len(final_kills) >= target_count:
                            break

            return final_kills[:target_count]

        except Exception as e:
            print(f"⚠️ 增强安全过滤失败: {e}")
            return [30, 31, 32, 33, 34][:target_count]

    def _apply_enhanced_blue_safety_filters(
        self,
        kill_candidates: List[int],
        period_data: Dict,
        target_count: int,
        period_num: int,
    ) -> List[int]:
        """应用增强蓝球安全过滤规则，确保杀号的合理性和多样性"""
        try:
            from src.utils.utils import parse_numbers
            import random

            # 获取最近期蓝球号码，避免杀掉刚出现的号码
            recent_appeared = set()
            for key in ["last", "prev2"]:
                if key in period_data:
                    _, blue_balls = parse_numbers(period_data[key])
                    recent_appeared.update(blue_balls)

            # 过滤规则
            filtered_kills = []
            zone_counts = {"small": 0, "medium": 0, "large": 0}  # 区间计数

            # 确定性随机排序（基于期号）
            random.seed(period_num + 200)  # 与红球区分
            random.shuffle(kill_candidates)

            for ball in kill_candidates:
                # 规则1：不杀最近2期出现的蓝球号码
                if ball in recent_appeared:
                    continue

                # 规则2：区间平衡控制（蓝球分3个区间）
                if ball <= 4:
                    zone = "small"
                elif ball <= 8:
                    zone = "medium"
                else:
                    zone = "large"

                # 限制每个区间最多1个杀号（蓝球总共只杀2个）
                if zone_counts[zone] >= 1:
                    continue

                # 规则3：避免连续号码
                consecutive_count = 0
                for existing_kill in filtered_kills:
                    if abs(ball - existing_kill) <= 1:
                        consecutive_count += 1

                if consecutive_count >= 1:  # 蓝球不允许连续号码
                    continue

                # 规则4：奇偶平衡（蓝球杀2个，最好1奇1偶）
                odd_count = len([k for k in filtered_kills if k % 2 == 1])
                even_count = len(filtered_kills) - odd_count

                if ball % 2 == 1 and odd_count >= 1:  # 最多1个奇数
                    continue
                if ball % 2 == 0 and even_count >= 1:  # 最多1个偶数
                    continue

                # 通过所有过滤规则
                filtered_kills.append(ball)
                zone_counts[zone] += 1

                if len(filtered_kills) >= target_count:
                    break

            # 如果过滤后数量不足，补充一些安全的候选
            if len(filtered_kills) < target_count:
                backup_candidates = [
                    b
                    for b in kill_candidates
                    if b not in filtered_kills and b not in recent_appeared
                ]
                needed = target_count - len(filtered_kills)

                # 优先选择不同区间的号码
                for zone, (start, end) in [
                    ("small", (1, 5)),
                    ("medium", (5, 9)),
                    ("large", (9, 13)),
                ]:
                    if zone_counts[zone] == 0 and needed > 0:  # 如果某区间还没有杀号
                        zone_candidates = [
                            b for b in backup_candidates if start <= b < end
                        ]
                        if zone_candidates:
                            filtered_kills.append(zone_candidates[0])
                            needed -= 1

                # 如果还是不足，随机补充
                if needed > 0:
                    remaining = [
                        b for b in backup_candidates if b not in filtered_kills
                    ]
                    random.seed(period_num + 300)
                    additional = (
                        random.sample(remaining, min(needed, len(remaining)))
                        if remaining
                        else []
                    )
                    filtered_kills.extend(additional)

            # 最终确保数量正确
            final_kills = filtered_kills[:target_count]

            # 如果还是不足，使用动态默认策略
            if len(final_kills) < target_count:
                # 基于期号的动态默认蓝球杀号
                base_kills = [11, 12, 1, 2, 9, 10, 3, 4]
                period_offset = period_num % 4
                default_kills = base_kills[period_offset : period_offset + target_count]

                for ball in default_kills:
                    if ball not in final_kills and ball not in recent_appeared:
                        final_kills.append(ball)
                        if len(final_kills) >= target_count:
                            break

            return final_kills[:target_count]

        except Exception as e:
            print(f"⚠️ 增强蓝球安全过滤失败: {e}")
            return [11, 12][:target_count]

    def _apply_safety_filters(
        self, kill_candidates: List[int], period_data: Dict, target_count: int
    ) -> List[int]:
        """应用安全过滤规则，确保杀号的合理性"""
        try:
            from src.utils.utils import parse_numbers

            # 获取最近期号码，避免杀掉刚出现的号码
            recent_appeared = set()
            for key in ["last", "prev2"]:
                if key in period_data:
                    red_balls, _ = parse_numbers(period_data[key])
                    recent_appeared.update(red_balls)

            # 过滤规则
            filtered_kills = []

            for ball in kill_candidates:
                # 规则1：不杀最近2期出现的号码
                if ball in recent_appeared:
                    continue

                # 规则2：避免过度集中在边界号码
                boundary_count = len([k for k in filtered_kills if k <= 3 or k >= 33])
                if (ball <= 3 or ball >= 33) and boundary_count >= 2:
                    continue

                # 规则3：确保号码范围分布
                if len(filtered_kills) >= target_count:
                    break

                filtered_kills.append(ball)

            # 如果过滤后数量不足，补充一些安全的候选
            if len(filtered_kills) < target_count:
                safe_candidates = [
                    b for b in kill_candidates if b not in filtered_kills
                ]
                needed = target_count - len(filtered_kills)
                filtered_kills.extend(safe_candidates[:needed])

            # 最终确保数量正确
            final_kills = filtered_kills[:target_count]

            # 如果还是不足，使用默认策略
            if len(final_kills) < target_count:
                default_kills = [30, 31, 32, 33, 34, 35]
                for ball in default_kills:
                    if ball not in final_kills and ball not in recent_appeared:
                        final_kills.append(ball)
                        if len(final_kills) >= target_count:
                            break

            return final_kills[:target_count]

        except Exception as e:
            print(f"⚠️ 安全过滤失败: {e}")
            return [30, 31, 32, 33, 34][:target_count]

    def _simple_red_kill_strategy(
        self, period_data: Dict, target_count: int = 5
    ) -> List[int]:
        """改进的红球杀号策略（多样化策略）"""
        try:
            from src.utils.utils import parse_numbers
            from collections import Counter
            import random

            # 获取最近5期红球数据
            recent_reds = []
            for key in ["current", "last", "prev2", "prev3", "prev4"]:
                if key in period_data:
                    red_balls, _ = parse_numbers(period_data[key])
                    recent_reds.extend(red_balls)

            # 多策略融合杀号
            kill_candidates = set()

            # 策略1: 频率最低的号码
            red_freq = Counter(recent_reds)
            all_reds = list(range(1, 36))
            min_freq = min(red_freq.get(r, 0) for r in all_reds)
            freq_candidates = [r for r in all_reds if red_freq.get(r, 0) == min_freq]
            kill_candidates.update(freq_candidates[:3])

            # 策略2: 连续未出现的号码
            if len(recent_reds) >= 5:
                never_appeared = [r for r in all_reds if r not in recent_reds]
                kill_candidates.update(never_appeared[:2])

            # 策略3: 基于期号特征的动态杀号
            current_period_num = int(
                str(period_data["current"]["期号"])[-2:]
            )  # 取期号后两位

            # 根据期号奇偶性选择不同区间
            if current_period_num % 2 == 0:  # 偶数期号
                # 偏向杀小号
                zone_candidates = [
                    r for r in range(1, 13) if r not in recent_reds[-10:]
                ]
            else:  # 奇数期号
                # 偏向杀大号
                zone_candidates = [
                    r for r in range(24, 36) if r not in recent_reds[-10:]
                ]

            kill_candidates.update(zone_candidates[:2])

            # 策略4: 随机多样化（避免过度集中）
            remaining_numbers = [
                r
                for r in all_reds
                if r not in kill_candidates and r not in recent_reds[-5:]
            ]
            if remaining_numbers:
                random.seed(current_period_num)  # 基于期号的确定性随机
                random_candidates = random.sample(
                    remaining_numbers, min(2, len(remaining_numbers))
                )
                kill_candidates.update(random_candidates)

            # 转换为列表并排序
            final_kills = sorted(list(kill_candidates))

            # 确保数量足够
            if len(final_kills) < target_count:
                # 补充更多候选
                backup_candidates = [
                    r
                    for r in all_reds
                    if r not in final_kills and red_freq.get(r, 0) <= 1
                ]
                final_kills.extend(backup_candidates[: target_count - len(final_kills)])

            # 返回指定数量的杀号
            result = (
                final_kills[:target_count]
                if final_kills
                else [32, 33, 34, 35, 31][:target_count]
            )

            print(
                f"🎯 红球杀号策略: 频率{len(freq_candidates)}, 未出现{len(never_appeared) if 'never_appeared' in locals() else 0}, 区间{len(zone_candidates) if 'zone_candidates' in locals() else 0}, 随机{len(random_candidates) if 'random_candidates' in locals() else 0}"
            )

            return result

        except Exception as e:
            print(f"⚠️ 简化红球杀号预测失败: {e}")
            return [32, 33, 34, 35, 31][:target_count]

    def test_30_periods(self) -> Dict:
        """30期回测 - 使用统一框架"""
        print(f"\n🔍 开始30期回测 - 使用统一框架...")

        try:
            # 导入统一框架
            from src.framework import BacktestFramework, BacktestConfig
            from src.framework.predictor_adapter import create_predictor_adapter

            # 创建适配器
            adapter = create_predictor_adapter("advanced", self)

            # 创建框架
            framework = BacktestFramework(self.data)

            # 配置回测（专注于杀号测试）
            config = BacktestConfig(
                num_periods=self.test_periods,
                min_train_periods=0,
                display_periods=10,
                metrics=["red_kill_success", "blue_kill_success"],  # 只关注杀号
                enable_detailed_output=False,
                enable_statistics=True,
            )

            # 运行回测
            result = framework.run_backtest(adapter, config)

            # 转换为原始格式的统计信息
            stats = {
                "total_periods": result.statistics.total_periods,
                "perfect_periods": result.statistics.red_kill_success_count,  # 使用杀号成功期数
                "perfect_rate": result.statistics.red_kill_success_rate,
                "total_kills": result.statistics.total_periods * 5,  # 假设每期5个杀号
                "successful_kills": result.statistics.red_kill_success_count,
                "kill_success_rate": result.statistics.red_kill_success_rate,
                "avg_kills": 5.0,  # 固定5个杀号
                "period_details": [],
            }

            # 提取期次详情
            for period_result in result.period_results:
                if period_result.prediction.kill_numbers:
                    red_kills = period_result.prediction.kill_numbers.get("red", [])
                    if red_kills:
                        # 展平红球杀号列表
                        flat_kills = []
                        for kill_list in red_kills:
                            flat_kills.extend(kill_list)

                        actual_red = period_result.actual_result.red_balls
                        all_kills_successful = all(
                            k not in actual_red for k in flat_kills
                        )
                        successful_count = len(
                            [k for k in flat_kills if k not in actual_red]
                        )

                        stats["period_details"].append(
                            {
                                "period": period_result.period_number,
                                "kills": flat_kills,
                                "successful": successful_count,
                                "total": len(flat_kills),
                                "perfect": all_kills_successful,
                                "actual_red": actual_red,
                            }
                        )

            return stats

        except ImportError as e:
            print(f"⚠️ 统一框架导入失败，使用原始回测方法: {e}")
            return self._test_30_periods_legacy()
        except Exception as e:
            print(f"⚠️ 统一框架回测失败，使用原始回测方法: {e}")
            return self._test_30_periods_legacy()

    def _test_30_periods_legacy(self) -> Dict:
        """30期回测（原始方法）"""
        print(f"\n🔍 开始30期回测（原始方法）...")

        stats = {
            "total_periods": 0,
            "perfect_periods": 0,
            "perfect_rate": 0.0,
            "total_kills": 0,
            "successful_kills": 0,
            "kill_success_rate": 0.0,
            "avg_kills": 0.0,
            "period_details": [],
        }

        for i in range(self.test_periods):
            if i + 5 >= len(self.data):
                break

            # 获取历史数据
            current_period = self.data.iloc[i]
            period_data = {
                "current": current_period,
                "last": self.data.iloc[i + 1],
                "prev2": self.data.iloc[i + 2],
                "prev3": self.data.iloc[i + 3],
                "prev4": self.data.iloc[i + 4],
                "prev5": self.data.iloc[i + 5],
            }

            # 解析号码
            from src.utils.utils import parse_numbers

            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period_data["last"])
            period2_red, _ = parse_numbers(period_data["prev2"])

            # 使用集成系统预测杀号
            try:
                predicted_kills = self.ensemble_system.predict_ensemble_kills(
                    period_data, target_count=5
                )

                # 过滤掉前两期出现的号码
                valid_kills = [
                    k
                    for k in predicted_kills
                    if k not in (period1_red + period2_red) and 1 <= k <= 35
                ]

                if valid_kills:
                    stats["total_periods"] += 1
                    stats["total_kills"] += len(valid_kills)

                    # 检查杀号成功情况 - 修正：每一期杀号都不在下一期开奖数字中则为成功
                    # 检查是否所有杀号都不在开奖号码中
                    all_kills_successful = all(
                        k not in current_red for k in valid_kills
                    )

                    if all_kills_successful:
                        stats["successful_kills"] += 1  # 按期计算，不是按个数
                        stats["perfect_periods"] += 1

                    # 更新集成系统的性能跟踪
                    self.ensemble_system.update_performance(current_red, period_data)

                    # 记录详情
                    stats["period_details"].append(
                        {
                            "period": current_period["期号"],
                            "kills": valid_kills,
                            "successful": len(
                                [k for k in valid_kills if k not in current_red]
                            ),  # 个别杀号成功数（用于显示）
                            "total": len(valid_kills),
                            "perfect": all_kills_successful,  # 该期杀号是否全部成功
                            "actual_red": current_red,
                        }
                    )

            except Exception as e:
                print(f"期号 {current_period['期号']} 预测失败: {e}")
                continue

        # 计算统计数据
        if stats["total_periods"] > 0:
            stats["perfect_rate"] = stats["perfect_periods"] / stats["total_periods"]
            stats["avg_kills"] = stats["total_kills"] / stats["total_periods"]
            # 修正：杀号成功率应该按期数计算，不是按杀号总数
            stats["kill_success_rate"] = (
                stats["successful_kills"] / stats["total_periods"]
            )

        return stats

    def print_results(self, stats: Dict):
        """打印测试结果"""
        print(f"\n📊 高级概率系统30期回测结果")
        print("=" * 60)

        print(f"🎯 核心指标:")
        print(
            f"  全中率: {stats['perfect_rate']:.1%} ({stats['perfect_periods']}/{stats['total_periods']})"
        )
        print(
            f"  杀号成功率: {stats['kill_success_rate']:.1%} ({stats['successful_kills']}/{stats['total_periods']})"
        )
        print(f"  平均杀号数: {stats['avg_kills']:.1f}")

        # 显示失败期数
        failed_periods = [p for p in stats["period_details"] if not p["perfect"]]
        if failed_periods:
            print(f"\n❌ 失败期数 ({len(failed_periods)}期):")
            for detail in failed_periods:
                kills_str = ",".join(map(str, detail["kills"]))
                actual_str = ",".join(map(str, detail["actual_red"]))
                print(
                    f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] 成功{detail['successful']}/{detail['total']}"
                )

        # 显示成功期数示例
        success_periods = [p for p in stats["period_details"] if p["perfect"]]
        if success_periods:
            print(f"\n✅ 成功期数示例 (前5期):")
            for detail in success_periods[:5]:
                kills_str = ",".join(map(str, detail["kills"]))
                actual_str = ",".join(map(str, detail["actual_red"]))
                print(
                    f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] 全中 ✅"
                )

        # 分析结果
        print(f"\n🔍 结果分析:")
        if stats["perfect_rate"] >= self.target_success_rate:
            print(
                f"🎉 达到目标！全中率{stats['perfect_rate']:.1%} ≥ 目标{self.target_success_rate:.0%}"
            )
        else:
            print(
                f"⚠️ 未达到目标。全中率{stats['perfect_rate']:.1%} < 目标{self.target_success_rate:.0%}"
            )
            improvement_needed = self.target_success_rate - stats["perfect_rate"]
            print(f"   需要提升: {improvement_needed:.1%}")

        if 4.0 <= stats["avg_kills"] <= 6.0:
            print(f"✅ 杀号数量合适: {stats['avg_kills']:.1f}个")
        else:
            print(f"⚠️ 杀号数量需调整: {stats['avg_kills']:.1f}个 (目标: 4-6个)")

    def optimize_weights(self):
        """智能权重优化系统（多阶段自适应优化）"""
        print(f"\n🔧 启动智能权重优化系统...")

        # 第一阶段：快速筛选优秀权重组合
        print("📊 第一阶段：快速评估基础权重组合...")

        initial_combinations = [
            # 贝叶斯主导型
            {"bayesian": 0.6, "markov1": 0.2, "markov2": 0.2},
            {"bayesian": 0.7, "markov1": 0.15, "markov2": 0.15},
            {"bayesian": 0.65, "markov1": 0.2, "markov2": 0.15},
            {"bayesian": 0.8, "markov1": 0.1, "markov2": 0.1},
            # 平衡型
            {"bayesian": 0.5, "markov1": 0.25, "markov2": 0.25},
            {"bayesian": 0.55, "markov1": 0.25, "markov2": 0.2},
            {"bayesian": 0.45, "markov1": 0.3, "markov2": 0.25},
            # 马尔科夫主导型
            {"bayesian": 0.4, "markov1": 0.35, "markov2": 0.25},
            {"bayesian": 0.35, "markov1": 0.4, "markov2": 0.25},
        ]

        stage1_results = []

        for i, weights in enumerate(initial_combinations):
            self.ensemble_system.weights = weights
            # 快速评估：只测试15期
            stats = self._quick_test(15)

            # 评分策略：更注重杀号成功率
            score = (
                stats["kill_success_rate"] * 0.85 + stats.get("perfect_rate", 0) * 0.15
            )
            stage1_results.append((weights, score, stats))

            print(
                f"  组合{i+1}: 评分{score:.3f} (杀号: {stats['kill_success_rate']:.1%})"
            )

        # 选择前3个最佳组合
        stage1_results.sort(key=lambda x: x[1], reverse=True)
        top3_combinations = [result[0] for result in stage1_results[:3]]

        print(f"\n🎯 第二阶段：精细优化前3个组合...")

        # 第二阶段：对最佳组合进行微调
        best_score = 0
        best_weights = None

        for base_weights in top3_combinations:
            # 微调优化
            optimized_weights = self._fine_tune_weights(base_weights)

            self.ensemble_system.weights = optimized_weights
            stats = self.test_30_periods()  # 完整测试

            # 最终评分
            score = (
                stats["kill_success_rate"] * 0.8 + stats.get("perfect_rate", 0) * 0.2
            )

            print(
                f"  优化权重: 评分{score:.3f} (杀号: {stats['kill_success_rate']:.1%}, 完美: {stats.get('perfect_rate', 0):.1%})"
            )

            if score > best_score:
                best_score = score
                best_weights = optimized_weights

        # 第三阶段：自适应调整
        if best_weights:
            print(f"\n🚀 第三阶段：自适应微调...")
            final_weights = self._adaptive_adjustment(best_weights)

            self.ensemble_system.weights = final_weights
            print(f"✅ 最终优化权重: {final_weights}")
            print(f"✅ 最佳评分: {best_score:.3f}")

            return final_weights
        else:
            # 回退策略
            fallback_weights = stage1_results[0][0]
            self.ensemble_system.weights = fallback_weights
            print(f"⚠️ 使用回退权重: {fallback_weights}")
            return fallback_weights

    def _quick_test(self, period_count: int) -> dict:
        """快速测试指定期数"""
        try:
            if len(self.data) < period_count + 10:
                return {"kill_success_rate": 0.0, "perfect_rate": 0.0}

            success_count = 0
            perfect_count = 0

            for i in range(period_count):
                try:
                    period_data = self._prepare_period_data(i)
                    red_kills = self.predict_red_kills(period_data, 5)

                    actual_row = self.data.iloc[i]
                    from src.utils.utils import parse_numbers

                    actual_red, _ = parse_numbers(actual_row)

                    kill_success = not any(kill in actual_red for kill in red_kills)
                    if kill_success:
                        success_count += 1
                        if len(set(red_kills) & set(actual_red)) == 0:
                            perfect_count += 1

                except:
                    continue

            return {
                "kill_success_rate": (
                    success_count / period_count if period_count > 0 else 0.0
                ),
                "perfect_rate": (
                    perfect_count / period_count if period_count > 0 else 0.0
                ),
            }

        except:
            return {"kill_success_rate": 0.0, "perfect_rate": 0.0}

    def _fine_tune_weights(self, base_weights: dict) -> dict:
        """对基础权重进行微调优化"""
        import copy

        best_weights = copy.deepcopy(base_weights)
        best_score = 0

        # 微调步长
        adjustment_steps = [-0.05, -0.03, 0.03, 0.05]

        for main_algo in base_weights.keys():
            for step in adjustment_steps:
                test_weights = copy.deepcopy(base_weights)

                # 调整主算法权重
                new_weight = test_weights[main_algo] + step
                if new_weight < 0.05 or new_weight > 0.85:  # 权重范围限制
                    continue

                test_weights[main_algo] = new_weight

                # 重新分配其他权重
                remaining_weight = 1.0 - new_weight
                other_algos = [k for k in test_weights.keys() if k != main_algo]
                total_other_weight = sum(test_weights[k] for k in other_algos)

                if total_other_weight > 0:
                    for algo in other_algos:
                        test_weights[algo] = (
                            test_weights[algo] * remaining_weight / total_other_weight
                        )

                # 快速测试
                self.ensemble_system.weights = test_weights
                stats = self._quick_test(10)
                score = stats["kill_success_rate"]

                if score > best_score:
                    best_score = score
                    best_weights = copy.deepcopy(test_weights)

        return best_weights

    def _adaptive_adjustment(self, base_weights: dict) -> dict:
        """自适应权重调整"""
        import copy

        # 基于当前性能进行最后的微调
        current_weights = copy.deepcopy(base_weights)

        # 测试当前权重的性能
        self.ensemble_system.weights = current_weights
        current_stats = self._quick_test(20)
        current_success_rate = current_stats["kill_success_rate"]

        # 如果成功率低于70%，增加贝叶斯权重
        if current_success_rate < 0.7:
            if "bayesian" in current_weights:
                # 增加贝叶斯权重，减少其他权重
                bayesian_boost = min(0.1, 0.8 - current_weights["bayesian"])
                current_weights["bayesian"] += bayesian_boost

                # 按比例减少其他权重
                other_algos = [k for k in current_weights.keys() if k != "bayesian"]
                reduction_per_algo = bayesian_boost / len(other_algos)

                for algo in other_algos:
                    current_weights[algo] = max(
                        0.05, current_weights[algo] - reduction_per_algo
                    )

                # 重新归一化
                total_weight = sum(current_weights.values())
                for algo in current_weights:
                    current_weights[algo] /= total_weight

        return current_weights

    def run_performance_optimization(self, optimization_periods: int = 50) -> Dict:
        """运行完整的性能优化"""
        try:
            print("🚀 启动性能优化流程...")

            if self.data is None or len(self.data) < optimization_periods + 20:
                return {"success": False, "error": "数据不足，无法进行优化"}

            # 转换数据格式
            enhanced_data = []
            for _, row in self.data.iterrows():
                enhanced_data.append(
                    [
                        row["期号"],
                        row["红球1"],
                        row["红球2"],
                        row["红球3"],
                        row["红球4"],
                        row["红球5"],
                        row["蓝球1"],
                        row["蓝球2"],
                    ]
                )

            # 1. 参数优化
            print("🔧 第一阶段: 参数优化")
            optimization_result = (
                self.parameter_optimizer.optimize_kill_system_parameters(
                    self.enhanced_diversity_system, enhanced_data, optimization_periods
                )
            )

            # 2. 性能验证
            print("📊 第二阶段: 性能验证")
            validation_result = self._validate_optimized_performance(enhanced_data, 30)

            # 3. 生成优化报告
            print("📋 第三阶段: 生成优化报告")
            optimization_report = self._generate_optimization_report(
                optimization_result, validation_result
            )

            print("✅ 性能优化流程完成!")
            print(optimization_report)

            return {
                "success": True,
                "optimization_result": optimization_result,
                "validation_result": validation_result,
                "report": optimization_report,
            }

        except Exception as e:
            print(f"❌ 性能优化失败: {e}")
            import traceback

            traceback.print_exc()
            return {"success": False, "error": str(e)}

    def _validate_optimized_performance(self, data: List, test_periods: int) -> Dict:
        """验证优化后的性能"""
        try:
            if len(data) < test_periods + 10:
                return {"success": False, "error": "验证数据不足"}

            correct_red = 0
            correct_blue = 0
            total_tests = 0

            diversity_scores = []

            print(f"   验证最近 {test_periods} 期的性能...")

            for i in range(len(data) - test_periods, len(data)):
                if i <= 0:
                    continue

                train_data = data[:i]
                actual_data = data[i]

                try:
                    # 预测杀号
                    red_kills = self.enhanced_diversity_system.predict_enhanced_kills(
                        train_data, actual_data[0], 13, "red"
                    )
                    blue_kills = self.enhanced_diversity_system.predict_enhanced_kills(
                        train_data, actual_data[0], 5, "blue"
                    )

                    # 检查杀号成功率
                    actual_red = set(actual_data[1:6])
                    actual_blue = set(actual_data[6:8])

                    if not (set(red_kills) & actual_red):
                        correct_red += 1
                    if not (set(blue_kills) & actual_blue):
                        correct_blue += 1

                    # 计算多样性得分
                    diversity_score = self._calculate_diversity_score(
                        red_kills, blue_kills
                    )
                    diversity_scores.append(diversity_score)

                    total_tests += 1

                except Exception as e:
                    print(f"   验证期号 {actual_data[0]} 失败: {e}")
                    continue

            if total_tests > 0:
                red_success_rate = correct_red / total_tests
                blue_success_rate = correct_blue / total_tests
                avg_diversity = (
                    sum(diversity_scores) / len(diversity_scores)
                    if diversity_scores
                    else 0
                )

                return {
                    "success": True,
                    "red_success_rate": red_success_rate,
                    "blue_success_rate": blue_success_rate,
                    "combined_success_rate": (red_success_rate + blue_success_rate) / 2,
                    "average_diversity": avg_diversity,
                    "total_tests": total_tests,
                }
            else:
                return {"success": False, "error": "无有效验证数据"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def _calculate_diversity_score(
        self, red_kills: List[int], blue_kills: List[int]
    ) -> float:
        """计算杀号多样性得分"""
        try:
            # 红球区间分布
            red_zones = [0, 0, 0]  # 小中大三个区间
            for ball in red_kills:
                if 1 <= ball <= 12:
                    red_zones[0] += 1
                elif 13 <= ball <= 24:
                    red_zones[1] += 1
                elif 25 <= ball <= 35:
                    red_zones[2] += 1

            # 蓝球区间分布
            blue_zones = [0, 0, 0]  # 小中大三个区间
            for ball in blue_kills:
                if 1 <= ball <= 4:
                    blue_zones[0] += 1
                elif 5 <= ball <= 8:
                    blue_zones[1] += 1
                elif 9 <= ball <= 12:
                    blue_zones[2] += 1

            # 计算分布均匀性（越均匀得分越高）
            red_uniformity = (
                1.0 - (max(red_zones) - min(red_zones)) / len(red_kills)
                if red_kills
                else 0
            )
            blue_uniformity = (
                1.0 - (max(blue_zones) - min(blue_zones)) / len(blue_kills)
                if blue_kills
                else 0
            )

            return red_uniformity * 0.7 + blue_uniformity * 0.3

        except Exception:
            return 0.0

    def _generate_optimization_report(
        self, optimization_result: Dict, validation_result: Dict
    ) -> str:
        """生成优化报告"""
        try:
            report = f"""
🎯 性能优化完成报告
==================

📈 参数优化结果:
{self.parameter_optimizer.get_optimization_report()}

📊 性能验证结果:
"""

            if validation_result.get("success"):
                report += f"""   ✅ 红球杀号成功率: {validation_result['red_success_rate']:.1%}
   ✅ 蓝球杀号成功率: {validation_result['blue_success_rate']:.1%}
   ✅ 综合成功率: {validation_result['combined_success_rate']:.1%}
   ✅ 平均多样性得分: {validation_result['average_diversity']:.3f}
   ✅ 验证期数: {validation_result['total_tests']}

🎉 优化效果评估:
"""
                combined_rate = validation_result["combined_success_rate"]
                if combined_rate >= 0.85:
                    report += "   🌟 优秀! 杀号性能显著提升"
                elif combined_rate >= 0.75:
                    report += "   👍 良好! 杀号性能有所改善"
                elif combined_rate >= 0.65:
                    report += "   ⚠️  一般! 杀号性能基本稳定"
                else:
                    report += "   ❌ 需要进一步优化"

                diversity_score = validation_result["average_diversity"]
                if diversity_score >= 0.8:
                    report += "\n   🎨 多样性: 优秀"
                elif diversity_score >= 0.6:
                    report += "\n   🎨 多样性: 良好"
                else:
                    report += "\n   🎨 多样性: 需要改进"
            else:
                report += (
                    f"   ❌ 验证失败: {validation_result.get('error', '未知错误')}"
                )

            report += f"""

💡 使用建议:
   - 优化后的参数已自动应用到系统中
   - 建议定期监控杀号效果并重新优化
   - 如发现性能下降，可重新运行优化流程
   - 保持数据更新以确保算法适应性

⚙️  技术细节:
   - 使用了增强的马尔可夫+贝叶斯算法
   - 集成了多样性约束和模式识别
   - 采用自适应权重调整机制
   - 支持实时性能监控和反馈
"""

            return report

        except Exception as e:
            return f"❌ 报告生成失败: {e}"


class EnhancedDiversityKillSystem:
    """增强的多样性杀号系统"""

    def __init__(self):
        self.kill_history = []  # 杀号历史记录
        self.pattern_memory = {}  # 模式记忆
        self.adaptive_weights = {
            "markov_weight": 0.6,
            "bayesian_weight": 0.4,
            "diversity_penalty": 0.3,
            "pattern_penalty": 0.2,
        }

    def predict_enhanced_kills(
        self, data: List, period_num: int, kill_count: int = 13, ball_type: str = "red"
    ) -> List[int]:
        """预测增强的杀号"""
        try:
            if ball_type == "red":
                return self._predict_enhanced_red_kills(data, period_num, kill_count)
            else:
                return self._predict_enhanced_blue_kills(
                    data, period_num, min(kill_count, 5)
                )
        except Exception as e:
            print(f"⚠️ 增强杀号预测失败: {e}")
            return []

    def _predict_enhanced_red_kills(
        self, data: List, period_num: int, kill_count: int
    ) -> List[int]:
        """预测增强的红球杀号 - 集成多维度特征"""
        try:
            # 转换数据格式用于特征提取
            df_data = pd.DataFrame(data)

            # 初始化特征提取器
            feature_extractor = AdvancedFeatureExtractor(df_data)
            seasonal_analyzer = SeasonalPatternAnalyzer(df_data)

            # 提取多维度特征
            interval_features = feature_extractor.extract_interval_features(
                str(period_num), "red"
            )
            combination_features = feature_extractor.extract_combination_features(
                str(period_num), "red"
            )
            statistical_features = feature_extractor.extract_statistical_features(
                str(period_num)
            )
            seasonal_weights = seasonal_analyzer.get_seasonal_weights(
                str(period_num), "red"
            )

            # 计算每个号码的综合概率
            ball_scores = {}

            for ball in range(1, 36):
                # 基础马尔科夫概率
                markov_prob = self._calculate_adaptive_markov_probability(
                    ball, data, period_num
                )

                # 贝叶斯概率
                bayesian_prob = self._calculate_adaptive_bayesian_probability(
                    ball, data, period_num
                )

                # 间隔特征权重 (间隔越长，被杀概率越高)
                interval_weight = 1.0
                if ball in interval_features:
                    interval_days = interval_features[ball]
                    # 间隔超过10期的数字更容易被杀
                    if interval_days > 10:
                        interval_weight = 1.0 + (interval_days - 10) * 0.1
                    elif interval_days < 3:
                        interval_weight = 0.5  # 最近出现的数字不太容易被杀

                # 组合特征权重 (与其他数字组合频率低的更容易被杀)
                combination_weight = 1.0
                ball_combinations = [
                    combo for combo in combination_features.keys() if ball in combo
                ]
                if ball_combinations:
                    avg_combo_prob = np.mean(
                        [combination_features[combo] for combo in ball_combinations]
                    )
                    combination_weight = (
                        1.0 + (0.5 - avg_combo_prob) * 2.0
                    )  # 组合频率低的权重更高

                # 季节性权重
                seasonal_weight = seasonal_weights.get(ball, 1.0)

                # 统计特征调整
                stat_weight = 1.0
                # 根据当前数字的奇偶性和大小调整权重
                is_odd = ball % 2 == 1
                is_large = ball >= 18

                # 如果历史奇数比例高，偶数更容易被杀
                if not is_odd and statistical_features["avg_odd_ratio"] > 0.6:
                    stat_weight *= 1.2
                elif is_odd and statistical_features["avg_odd_ratio"] < 0.4:
                    stat_weight *= 1.2

                # 如果历史大号比例高，小号更容易被杀
                if not is_large and statistical_features["avg_large_ratio"] > 0.6:
                    stat_weight *= 1.2
                elif is_large and statistical_features["avg_large_ratio"] < 0.4:
                    stat_weight *= 1.2

                # 综合计算杀号概率 (概率越高越容易被杀)
                kill_probability = (
                    (
                        markov_prob * self.adaptive_weights["markov_weight"]
                        + bayesian_prob * self.adaptive_weights["bayesian_weight"]
                    )
                    * interval_weight
                    * combination_weight
                    * seasonal_weight
                    * stat_weight
                )

                # 应用多样性惩罚
                diversity_penalty = self._calculate_diversity_penalty(
                    ball, period_num, "red"
                )
                pattern_penalty = self._calculate_pattern_penalty(
                    ball, period_num, "red"
                )

                final_score = (
                    kill_probability
                    * (
                        1
                        + diversity_penalty * self.adaptive_weights["diversity_penalty"]
                    )
                    * (1 + pattern_penalty * self.adaptive_weights["pattern_penalty"])
                )

                ball_scores[ball] = final_score

            # 选择得分最高的数字作为杀号
            sorted_balls = sorted(ball_scores.items(), key=lambda x: x[1], reverse=True)
            kill_numbers = [ball for ball, _ in sorted_balls[:kill_count]]

            # 记录杀号历史
            self.kill_history.append(
                {
                    "period": period_num,
                    "kills": kill_numbers,
                    "ball_type": "red",
                    "features_used": {
                        "interval": True,
                        "combination": True,
                        "statistical": True,
                        "seasonal": True,
                    },
                }
            )

            print(f"🎯 使用增强多样性马尔可夫+贝叶斯算法计算红球杀号...")
            print(
                f"  📊 增强概率计算完成，权重 贝叶斯{self.adaptive_weights['bayesian_weight']:.2f}/马尔可夫{self.adaptive_weights['markov_weight']:.2f}"
            )
            print(f"🎯 增强多样性红球杀号: {kill_numbers}")
            print(
                f"  期号特征: {period_num % 100}, 概率范围: {min(ball_scores.values()):.4f}-{max(ball_scores.values()):.4f}"
            )

            return kill_numbers

        except Exception as e:
            print(f"⚠️ 红球增强杀号失败: {e}")
            return list(range(1, kill_count + 1))  # 降级方案

    def _predict_enhanced_blue_kills(
        self, data: List, period_num: int, kill_count: int
    ) -> List[int]:
        """预测增强的蓝球杀号"""
        try:
            ball_scores = {}

            for ball in range(1, 13):
                # 马尔科夫概率
                markov_prob = self._calculate_adaptive_markov_probability(
                    ball, data, period_num, ball_type="blue"
                )

                # 贝叶斯概率
                bayesian_prob = self._calculate_adaptive_bayesian_probability(
                    ball, data, period_num, ball_type="blue"
                )

                # 多样性惩罚
                diversity_penalty = self._calculate_diversity_penalty(
                    ball, period_num, "blue"
                )

                # 模式惩罚
                pattern_penalty = self._calculate_pattern_penalty(
                    ball, period_num, "blue"
                )

                # 综合评分
                combined_score = (
                    self.adaptive_weights["markov_weight"] * markov_prob
                    + self.adaptive_weights["bayesian_weight"] * bayesian_prob
                    + self.adaptive_weights["diversity_penalty"] * diversity_penalty
                    + self.adaptive_weights["pattern_penalty"] * pattern_penalty
                )

                ball_scores[ball] = combined_score

            # 应用区间平衡约束
            balanced_scores = self._apply_zone_balance_constraints(
                ball_scores, kill_count, "blue"
            )

            # 选择评分最低的号码作为杀号
            sorted_balls = sorted(balanced_scores.items(), key=lambda x: x[1])
            kill_numbers = [ball for ball, _ in sorted_balls[:kill_count]]

            # 记录杀号历史
            self._record_kill_history(period_num, kill_numbers, "blue")

            return kill_numbers

        except Exception as e:
            print(f"⚠️ 蓝球增强杀号失败: {e}")
            return list(range(1, kill_count + 1))  # 降级方案

    def _calculate_adaptive_markov_probability(
        self, ball: int, data: List, period_num: int, ball_type: str = "red"
    ) -> float:
        """计算自适应马尔科夫概率"""
        try:
            from collections import defaultdict
            import math

            if len(data) < 20:
                return 1.0 / (35 if ball_type == "red" else 12)

            # 提取历史数据
            recent_data = data[-50:]  # 使用更多历史数据
            transitions = defaultdict(lambda: defaultdict(int))

            # 构建转移矩阵
            for i in range(len(recent_data) - 1):
                # 解析当前期和下一期的数据
                from src.utils.utils import parse_numbers

                # 转换为DataFrame行格式进行解析
                current_row = pd.Series(recent_data[i])
                next_row = pd.Series(recent_data[i + 1])

                current_red, current_blue = parse_numbers(current_row)
                next_red, next_blue = parse_numbers(next_row)

                if ball_type == "red":
                    current_balls = set(current_red)
                    next_balls = set(next_red)
                else:
                    current_balls = set(current_blue)
                    next_balls = set(next_blue)

                # 记录转移
                for curr_ball in current_balls:
                    for next_ball in next_balls:
                        transitions[curr_ball][next_ball] += 1

            # 计算条件概率
            if ball_type == "red":
                last_row = pd.Series(data[-1])
                last_red, _ = parse_numbers(last_row)
                last_balls = set(last_red)
            else:
                last_row = pd.Series(data[-1])
                _, last_blue = parse_numbers(last_row)
                last_balls = set(last_blue)

            total_prob = 0.0
            count = 0

            for last_ball in last_balls:
                if last_ball in transitions:
                    total_transitions = sum(transitions[last_ball].values())
                    if total_transitions > 0:
                        prob = transitions[last_ball][ball] / total_transitions
                        total_prob += prob
                        count += 1

            if count > 0:
                return total_prob / count
            else:
                return 1.0 / (35 if ball_type == "red" else 12)

        except Exception as e:
            print(f"⚠️ 自适应马尔科夫概率计算失败: {e}")
            return 1.0 / (35 if ball_type == "red" else 12)

    def _calculate_adaptive_bayesian_probability(
        self, ball: int, data: List, period_num: int, ball_type: str = "red"
    ) -> float:
        """计算自适应贝叶斯概率"""
        try:
            from collections import Counter
            import math
            from src.utils.utils import parse_numbers

            if len(data) < 10:
                return 1.0 / (35 if ball_type == "red" else 12)

            # 使用更长的历史窗口
            window_sizes = [10, 20, 50]
            probabilities = []

            for window_size in window_sizes:
                if len(data) >= window_size:
                    recent_data = data[-window_size:]

                    # 解析数据获取球号
                    all_balls = []
                    for row_dict in recent_data:
                        row = pd.Series(row_dict)
                        red_balls, blue_balls = parse_numbers(row)

                        if ball_type == "red":
                            all_balls.extend(red_balls)
                        else:
                            all_balls.extend(blue_balls)

                    counter = Counter(all_balls)
                    total_count = len(all_balls)

                    # 贝叶斯更新
                    prior = 1.0 / (35 if ball_type == "red" else 12)
                    likelihood = counter[ball] / total_count if total_count > 0 else 0

                    # 使用拉普拉斯平滑
                    smoothed_likelihood = (counter[ball] + 1) / (
                        total_count + (35 if ball_type == "red" else 12)
                    )

                    probabilities.append(smoothed_likelihood)

            # 加权平均（近期权重更高）
            if probabilities:
                weights = [0.5, 0.3, 0.2][: len(probabilities)]
                weighted_prob = sum(
                    p * w for p, w in zip(probabilities, weights)
                ) / sum(weights)
                return weighted_prob
            else:
                return 1.0 / (35 if ball_type == "red" else 12)

        except Exception as e:
            print(f"⚠️ 自适应贝叶斯概率计算失败: {e}")
            return 1.0 / (35 if ball_type == "red" else 12)

    def _calculate_diversity_penalty(
        self, ball: int, period_num: int, ball_type: str
    ) -> float:
        """计算多样性惩罚"""
        try:
            penalty = 0.0

            # 检查最近的杀号历史
            recent_kills = [
                kill_record["kills"]
                for kill_record in self.kill_history[-10:]
                if kill_record["ball_type"] == ball_type
            ]

            # 计算该号码在最近杀号中的出现频率
            total_appearances = sum(1 for kills in recent_kills if ball in kills)

            if len(recent_kills) > 0:
                frequency = total_appearances / len(recent_kills)
                # 频率越高，惩罚越大
                penalty = frequency * 0.5

            return penalty

        except Exception as e:
            print(f"⚠️ 多样性惩罚计算失败: {e}")
            return 0.0

    def _calculate_pattern_penalty(
        self, ball: int, period_num: int, ball_type: str
    ) -> float:
        """计算模式惩罚"""
        try:
            penalty = 0.0

            # 检查是否存在重复模式
            pattern_key = f"{ball_type}_{ball}"

            if pattern_key in self.pattern_memory:
                last_periods = self.pattern_memory[pattern_key]

                # 检查周期性模式
                if len(last_periods) >= 3:
                    intervals = [
                        last_periods[i] - last_periods[i - 1]
                        for i in range(1, len(last_periods))
                    ]

                    # 如果间隔显示规律性，增加惩罚
                    if len(set(intervals)) <= 2:  # 间隔过于规律
                        penalty = 0.3

            return penalty

        except Exception as e:
            print(f"⚠️ 模式惩罚计算失败: {e}")
            return 0.0

    def _apply_zone_balance_constraints(
        self, ball_scores: dict, kill_count: int, ball_type: str
    ) -> dict:
        """应用区间平衡约束"""
        try:
            adjusted_scores = ball_scores.copy()

            if ball_type == "red":
                zones = [(1, 12), (13, 24), (25, 35)]
                max_per_zone = max(1, kill_count // 3)
            else:
                zones = [(1, 4), (5, 8), (9, 12)]
                max_per_zone = max(1, kill_count // 3)

            # 统计每个区间当前的候选数量
            for zone_start, zone_end in zones:
                zone_candidates = [
                    (ball, score)
                    for ball, score in ball_scores.items()
                    if zone_start <= ball <= zone_end
                ]
                zone_candidates.sort(key=lambda x: x[1])

                # 如果某个区间候选过多，对后续候选增加惩罚
                if len(zone_candidates) > max_per_zone:
                    for i in range(max_per_zone, len(zone_candidates)):
                        ball = zone_candidates[i][0]
                        adjusted_scores[ball] *= 1.5  # 增加惩罚

            return adjusted_scores

        except Exception as e:
            print(f"⚠️ 区间平衡约束失败: {e}")
            return ball_scores

    def _record_kill_history(
        self, period_num: int, kill_numbers: List[int], ball_type: str
    ):
        """记录杀号历史"""
        try:
            # 记录杀号历史
            self.kill_history.append(
                {"period": period_num, "kills": kill_numbers, "ball_type": ball_type}
            )

            # 保持历史记录在合理范围内
            if len(self.kill_history) > 100:
                self.kill_history = self.kill_history[-100:]

            # 更新模式记忆
            for ball in kill_numbers:
                pattern_key = f"{ball_type}_{ball}"
                if pattern_key not in self.pattern_memory:
                    self.pattern_memory[pattern_key] = []

                self.pattern_memory[pattern_key].append(period_num)

                # 保持模式记忆在合理范围内
                if len(self.pattern_memory[pattern_key]) > 20:
                    self.pattern_memory[pattern_key] = self.pattern_memory[pattern_key][
                        -20:
                    ]

        except Exception as e:
            print(f"⚠️ 杀号历史记录失败: {e}")

    def update_adaptive_weights(self, success_rate: float, target_rate: float = 0.9):
        """根据成功率自适应调整权重"""
        try:
            if success_rate < target_rate:
                # 成功率不足，增加多样性和模式惩罚
                self.adaptive_weights["diversity_penalty"] = min(
                    0.5, self.adaptive_weights["diversity_penalty"] * 1.1
                )
                self.adaptive_weights["pattern_penalty"] = min(
                    0.4, self.adaptive_weights["pattern_penalty"] * 1.1
                )

                # 相应调整其他权重
                total_penalty = (
                    self.adaptive_weights["diversity_penalty"]
                    + self.adaptive_weights["pattern_penalty"]
                )
                remaining = 1.0 - total_penalty

                self.adaptive_weights["markov_weight"] = remaining * 0.6
                self.adaptive_weights["bayesian_weight"] = remaining * 0.4
            else:
                # 成功率良好，保持当前权重或微调
                pass

        except Exception as e:
            print(f"⚠️ 自适应权重更新失败: {e}")


class AdaptiveParameterOptimizer:
    """自适应参数优化器"""

    def __init__(self):
        self.optimization_history = []
        self.best_parameters = None
        self.best_score = 0.0

    def optimize_kill_system_parameters(
        self,
        kill_system: EnhancedDiversityKillSystem,
        data: List,
        test_periods: int = 30,
    ) -> Dict:
        """优化杀号系统参数"""
        try:
            print("🔧 开始自适应参数优化...")

            # 定义参数搜索空间
            parameter_space = {
                "markov_weight": [0.4, 0.5, 0.6, 0.7, 0.8],
                "bayesian_weight": [0.2, 0.3, 0.4, 0.5, 0.6],
                "diversity_penalty": [0.1, 0.2, 0.3, 0.4, 0.5],
                "pattern_penalty": [0.1, 0.15, 0.2, 0.25, 0.3],
            }

            best_score = 0.0
            best_params = None

            # 网格搜索（简化版）
            for markov_w in parameter_space["markov_weight"]:
                for bayesian_w in parameter_space["bayesian_weight"]:
                    for diversity_p in parameter_space["diversity_penalty"]:
                        for pattern_p in parameter_space["pattern_penalty"]:

                            # 确保权重归一化
                            total_weight = markov_w + bayesian_w
                            if total_weight > 0:
                                normalized_markov = markov_w / total_weight
                                normalized_bayesian = bayesian_w / total_weight
                            else:
                                continue

                            # 设置参数
                            test_params = {
                                "markov_weight": normalized_markov,
                                "bayesian_weight": normalized_bayesian,
                                "diversity_penalty": diversity_p,
                                "pattern_penalty": pattern_p,
                            }

                            # 评估参数
                            score = self._evaluate_parameters(
                                kill_system, data, test_params, test_periods
                            )

                            if score > best_score:
                                best_score = score
                                best_params = test_params.copy()

                            print(f"   参数测试: {test_params} -> 得分: {score:.3f}")

            # 应用最佳参数
            if best_params:
                kill_system.adaptive_weights.update(best_params)
                self.best_parameters = best_params
                self.best_score = best_score

                print(f"✅ 参数优化完成!")
                print(f"   最佳参数: {best_params}")
                print(f"   最佳得分: {best_score:.3f}")

            return {
                "best_parameters": best_params,
                "best_score": best_score,
                "optimization_completed": True,
            }

        except Exception as e:
            print(f"⚠️ 参数优化失败: {e}")
            return {"optimization_completed": False, "error": str(e)}

    def _evaluate_parameters(
        self,
        kill_system: EnhancedDiversityKillSystem,
        data: List,
        params: Dict,
        test_periods: int,
    ) -> float:
        """评估参数性能"""
        try:
            # 备份原始权重
            original_weights = kill_system.adaptive_weights.copy()

            # 应用测试参数
            kill_system.adaptive_weights.update(params)

            # 进行回测
            if len(data) < test_periods + 10:
                return 0.0

            correct_red = 0
            correct_blue = 0
            total_tests = 0

            # 测试最近的periods
            for i in range(len(data) - test_periods, len(data)):
                if i <= 0:
                    continue

                # 使用前i期数据预测第i+1期
                train_data = data[:i]
                actual_data = data[i]

                try:
                    # 预测杀号
                    red_kills = kill_system._predict_enhanced_red_kills(
                        train_data, actual_data[0], 13
                    )
                    blue_kills = kill_system._predict_enhanced_blue_kills(
                        train_data, actual_data[0], 5
                    )

                    # 检查杀号成功率
                    actual_red = set(actual_data[1:6])
                    actual_blue = set(actual_data[6:8])

                    # 杀号成功：杀号中的数字都不在实际开奖号码中
                    if not (set(red_kills) & actual_red):
                        correct_red += 1
                    if not (set(blue_kills) & actual_blue):
                        correct_blue += 1

                    total_tests += 1

                except Exception:
                    continue

            # 恢复原始权重
            kill_system.adaptive_weights = original_weights

            # 计算综合得分
            if total_tests > 0:
                red_success_rate = correct_red / total_tests
                blue_success_rate = correct_blue / total_tests
                combined_score = red_success_rate * 0.7 + blue_success_rate * 0.3
                return combined_score
            else:
                return 0.0

        except Exception as e:
            # 恢复原始权重
            kill_system.adaptive_weights = original_weights
            return 0.0

    def get_optimization_report(self) -> str:
        """获取优化报告"""
        if not self.best_parameters:
            return "❌ 尚未进行参数优化"

        report = f"""
📊 参数优化报告
================
✅ 最佳参数配置:
   - 马尔可夫权重: {self.best_parameters['markov_weight']:.3f}
   - 贝叶斯权重: {self.best_parameters['bayesian_weight']:.3f}
   - 多样性惩罚: {self.best_parameters['diversity_penalty']:.3f}
   - 模式惩罚: {self.best_parameters['pattern_penalty']:.3f}

🎯 最佳性能得分: {self.best_score:.3f}

💡 优化建议:
   - 当前参数已针对历史数据进行优化
   - 建议定期重新优化以适应数据变化
   - 可根据实际使用效果进一步微调
"""
        return report


def main():
    """主函数"""
    print("🎯 高级概率系统 - 贝叶斯 + 马尔科夫链")
    print("目标: 30期回测，平均5个杀号，97%全中率（精准模式）")
    print("=" * 60)

    system = AdvancedProbabilisticSystem()

    if not system.load_data():
        return

    system.initialize_system()

    # 优化模型权重
    best_weights = system.optimize_weights()

    # 使用最佳权重进行最终测试
    print(f"\n🎯 使用最佳权重进行最终30期回测...")
    final_stats = system.test_30_periods()

    # 打印最终结果
    system.print_results(final_stats)

    # 总结
    print(f"\n🎉 高级概率系统测试完成！")

    if final_stats["perfect_rate"] >= system.target_success_rate:
        print(
            f"🏆 成功达到目标！贝叶斯+马尔科夫链方法实现了{final_stats['perfect_rate']:.1%}全中率"
        )
    else:
        print(
            f"📈 虽未完全达到97%目标，但{final_stats['perfect_rate']:.1%}的全中率已经是很大的进步"
        )
        print(f"💡 建议进一步优化：")
        print(f"   1. 增加更多历史数据训练")
        print(f"   2. 尝试更复杂的贝叶斯网络")
        print(f"   3. 使用隐马尔科夫模型")
        print(f"   4. 添加更多特征工程")


class AdvancedFeatureExtractor:
    """增强特征工程模块 - 提取多维度历史特征"""

    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.red_range = range(1, 36)  # 红球范围1-35
        self.blue_range = range(1, 13)  # 蓝球范围1-12

    def extract_interval_features(
        self, target_period: str, ball_type: str = "red"
    ) -> Dict[int, int]:
        """提取数字间隔特征 - 每个数字连续未出现的期数"""
        from src.utils.utils import parse_numbers

        intervals = {}
        ball_range = self.red_range if ball_type == "red" else self.blue_range

        # 初始化所有数字的间隔
        for num in ball_range:
            intervals[num] = 0

        # 从目标期号往前查找
        target_idx = None
        for idx, row in self.data.iterrows():
            if str(row["期号"]) == str(target_period):
                target_idx = idx
                break

        if target_idx is None:
            return intervals

        # 从目标期号的前一期开始往前查找
        search_data = self.data.iloc[:target_idx]

        for num in ball_range:
            interval = 0
            found = False

            # 从最近的期号开始往前查找
            for _, row in search_data.iloc[::-1].iterrows():
                red_balls, blue_balls = parse_numbers(row)
                current_balls = red_balls if ball_type == "red" else blue_balls

                if num in current_balls:
                    found = True
                    break
                else:
                    interval += 1

            intervals[num] = interval if found else len(search_data)

        return intervals

    def extract_combination_features(
        self, target_period: str, ball_type: str = "red", window_size: int = 100
    ) -> Dict[Tuple[int, int], float]:
        """提取数字组合特征 - 分析经常一起出现的数字对"""
        from src.utils.utils import parse_numbers

        combination_counts = defaultdict(int)
        total_combinations = 0

        # 获取目标期号之前的数据
        target_idx = None
        for idx, row in self.data.iterrows():
            if str(row["期号"]) == str(target_period):
                target_idx = idx
                break

        if target_idx is None:
            return {}

        # 分析最近window_size期的数据
        search_data = self.data.iloc[:target_idx].tail(window_size)

        for _, row in search_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            current_balls = red_balls if ball_type == "red" else blue_balls

            # 计算所有数字对的组合
            for combo in combinations(current_balls, 2):
                combination_counts[combo] += 1
                total_combinations += 1

        # 计算组合概率
        combination_probs = {}
        for combo, count in combination_counts.items():
            combination_probs[combo] = count / max(total_combinations, 1)

        return combination_probs

    def extract_statistical_features(
        self, target_period: str, window_size: int = 50
    ) -> Dict[str, float]:
        """提取统计特征 - 奇偶比、大小比、和值、跨度等的历史分布"""
        from src.utils.utils import parse_numbers

        features = {
            "avg_odd_ratio": 0.0,
            "avg_large_ratio": 0.0,
            "avg_sum": 0.0,
            "avg_span": 0.0,
            "sum_variance": 0.0,
            "span_variance": 0.0,
        }

        # 获取目标期号之前的数据
        target_idx = None
        for idx, row in self.data.iterrows():
            if str(row["期号"]) == str(target_period):
                target_idx = idx
                break

        if target_idx is None:
            return features

        # 分析最近window_size期的数据
        search_data = self.data.iloc[:target_idx].tail(window_size)

        odd_ratios = []
        large_ratios = []
        sums = []
        spans = []

        for _, row in search_data.iterrows():
            red_balls, _ = parse_numbers(row)

            if len(red_balls) > 0:
                # 奇偶比例
                odd_count = sum(1 for num in red_balls if num % 2 == 1)
                odd_ratio = odd_count / len(red_balls)
                odd_ratios.append(odd_ratio)

                # 大小比例 (大号定义为18-35)
                large_count = sum(1 for num in red_balls if num >= 18)
                large_ratio = large_count / len(red_balls)
                large_ratios.append(large_ratio)

                # 和值
                sum_value = sum(red_balls)
                sums.append(sum_value)

                # 跨度
                span = max(red_balls) - min(red_balls)
                spans.append(span)

        # 计算统计特征
        if odd_ratios:
            features["avg_odd_ratio"] = np.mean(odd_ratios)
        if large_ratios:
            features["avg_large_ratio"] = np.mean(large_ratios)
        if sums:
            features["avg_sum"] = np.mean(sums)
            features["sum_variance"] = np.var(sums)
        if spans:
            features["avg_span"] = np.mean(spans)
            features["span_variance"] = np.var(spans)

        return features


class SeasonalPatternAnalyzer:
    """季节性模式分析模块"""

    def __init__(self, data: pd.DataFrame):
        self.data = data

    def extract_period_features(self, period: str) -> Dict[str, float]:
        """从期号中提取时间特征"""
        features = {
            "year_cycle": 0.0,
            "month_cycle": 0.0,
            "season_cycle": 0.0,
            "week_cycle": 0.0,
        }

        try:
            # 假设期号格式为YYDDD (如25068表示2025年第68期)
            if len(str(period)) >= 5:
                year_part = int(str(period)[:2])
                day_part = int(str(period)[2:])

                # 年度周期特征 (0-1)
                features["year_cycle"] = (year_part % 10) / 10.0

                # 月度周期特征 (基于期号在年内的位置)
                # 假设一年约104期 (每周2期)
                month_position = (day_part % 104) / 104.0
                features["month_cycle"] = month_position

                # 季节周期特征
                season_position = (day_part % 26) / 26.0  # 每季度约26期
                features["season_cycle"] = season_position

                # 周期特征
                week_position = (day_part % 2) / 2.0  # 每周2期
                features["week_cycle"] = week_position

        except (ValueError, IndexError):
            pass

        return features

    def analyze_seasonal_patterns(
        self, ball_type: str = "red", window_size: int = 200
    ) -> Dict[str, Dict[int, float]]:
        """分析季节性出号模式"""
        from src.utils.utils import parse_numbers

        patterns = {
            "month_patterns": defaultdict(lambda: defaultdict(int)),
            "season_patterns": defaultdict(lambda: defaultdict(int)),
            "week_patterns": defaultdict(lambda: defaultdict(int)),
        }

        # 分析最近window_size期的数据
        recent_data = self.data.tail(window_size)

        for _, row in recent_data.iterrows():
            period = str(row["期号"])
            red_balls, blue_balls = parse_numbers(row)
            current_balls = red_balls if ball_type == "red" else blue_balls

            # 提取时间特征
            time_features = self.extract_period_features(period)

            # 计算月份、季节、周期模式
            month_key = int(time_features["month_cycle"] * 12)  # 0-11
            season_key = int(time_features["season_cycle"] * 4)  # 0-3
            week_key = int(time_features["week_cycle"] * 2)  # 0-1

            for ball in current_balls:
                patterns["month_patterns"][month_key][ball] += 1
                patterns["season_patterns"][season_key][ball] += 1
                patterns["week_patterns"][week_key][ball] += 1

        # 转换为概率
        result_patterns = {}
        for pattern_type, pattern_data in patterns.items():
            result_patterns[pattern_type] = {}
            for time_key, ball_counts in pattern_data.items():
                total_count = sum(ball_counts.values())
                if total_count > 0:
                    result_patterns[pattern_type][time_key] = {
                        ball: count / total_count for ball, count in ball_counts.items()
                    }

        return result_patterns

    def get_seasonal_weights(
        self, period: str, ball_type: str = "red"
    ) -> Dict[int, float]:
        """获取基于季节性模式的数字权重"""
        time_features = self.extract_period_features(period)
        seasonal_patterns = self.analyze_seasonal_patterns(ball_type)

        weights = {}
        ball_range = range(1, 36) if ball_type == "red" else range(1, 13)

        # 初始化权重
        for ball in ball_range:
            weights[ball] = 1.0

        # 应用季节性权重
        month_key = int(time_features["month_cycle"] * 12)
        season_key = int(time_features["season_cycle"] * 4)
        week_key = int(time_features["week_cycle"] * 2)

        # 月份权重
        if month_key in seasonal_patterns.get("month_patterns", {}):
            month_probs = seasonal_patterns["month_patterns"][month_key]
            for ball in ball_range:
                if ball in month_probs:
                    weights[ball] *= 1.0 + month_probs[ball]

        # 季节权重
        if season_key in seasonal_patterns.get("season_patterns", {}):
            season_probs = seasonal_patterns["season_patterns"][season_key]
            for ball in ball_range:
                if ball in season_probs:
                    weights[ball] *= 1.0 + season_probs[ball]

        # 周期权重
        if week_key in seasonal_patterns.get("week_patterns", {}):
            week_probs = seasonal_patterns["week_patterns"][week_key]
            for ball in ball_range:
                if ball in week_probs:
                    weights[ball] *= 1.0 + week_probs[ball]

        return weights


if __name__ == "__main__":
    main()
