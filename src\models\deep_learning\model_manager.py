#!/usr/bin/env python3
"""
深度学习模型管理器
提供模型保存、加载、性能监控和超参数优化功能
"""

import os
import json
import pickle
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
import pandas as pd
import numpy as np
from datetime import datetime

try:
    import tensorflow as tf

    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

from .lstm_predictor import LSTMPredictor, LSTMConfig
from .transformer_predictor import TransformerPredictor, TransformerConfig
from .multi_task_neural import MultiTaskNeuralPredictor, MultiTaskConfig
from .ensemble_deep_learning import EnsembleDeepLearning, EnsembleConfig


@dataclass
class ModelPerformance:
    """模型性能记录"""

    model_name: str
    timestamp: str
    training_loss: float
    validation_loss: float
    training_time: float
    prediction_accuracy: float
    model_params: int
    config: Dict[str, Any]
    metadata: Dict[str, Any] = None


class DeepLearningModelManager:
    """深度学习模型管理器"""

    def __init__(self, models_dir: str = "models/saved"):
        """
        初始化模型管理器

        Args:
            models_dir: 模型保存目录
        """
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)

        self.performance_log = []
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")

        # 性能日志文件
        self.performance_file = self.models_dir / "performance_log.json"
        self._load_performance_log()

    def _load_performance_log(self):
        """加载性能日志"""
        if self.performance_file.exists():
            try:
                with open(self.performance_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.performance_log = [ModelPerformance(**item) for item in data]
                self.logger.info(f"加载性能日志: {len(self.performance_log)} 条记录")
            except Exception as e:
                self.logger.error(f"加载性能日志失败: {e}")
                self.performance_log = []

    def _save_performance_log(self):
        """保存性能日志"""
        try:
            data = [asdict(perf) for perf in self.performance_log]
            with open(self.performance_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"保存性能日志: {len(self.performance_log)} 条记录")
        except Exception as e:
            self.logger.error(f"保存性能日志失败: {e}")

    def save_model(self, model, model_name: str, config: Dict[str, Any] = None) -> str:
        """
        保存模型

        Args:
            model: 要保存的模型
            model_name: 模型名称
            config: 模型配置

        Returns:
            保存路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = self.models_dir / f"{model_name}_{timestamp}"
        model_dir.mkdir(exist_ok=True)

        try:
            # 保存TensorFlow模型
            tf_model_path = None
            if TENSORFLOW_AVAILABLE and hasattr(model, "model") and model.model:
                tf_model_path = model_dir / "tensorflow_model.keras"
                model.model.save(tf_model_path)
                self.logger.info(f"TensorFlow模型已保存: {tf_model_path}")

            # 保存完整预测器对象
            predictor_path = model_dir / "predictor.pkl"
            with open(predictor_path, "wb") as f:
                pickle.dump(model, f)
            self.logger.info(f"预测器对象已保存: {predictor_path}")

            # 保存配置
            if config:
                config_path = model_dir / "config.json"
                with open(config_path, "w", encoding="utf-8") as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                self.logger.info(f"配置已保存: {config_path}")

            # 保存元数据
            metadata = {
                "model_name": model_name,
                "timestamp": timestamp,
                "model_type": type(model).__name__,
                "save_path": str(model_dir),
                "files": {
                    "tensorflow_model": (
                        str(tf_model_path)
                        if TENSORFLOW_AVAILABLE
                        and hasattr(model, "model")
                        and model.model
                        else None
                    ),
                    "predictor": str(predictor_path),
                    "config": str(config_path) if config else None,
                },
            }

            metadata_path = model_dir / "metadata.json"
            with open(metadata_path, "w", encoding="utf-8") as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)

            self.logger.info(f"模型保存完成: {model_dir}")
            return str(model_dir)

        except Exception as e:
            self.logger.error(f"模型保存失败: {e}")
            raise

    def load_model(self, model_path: str):
        """
        加载模型

        Args:
            model_path: 模型路径

        Returns:
            加载的模型
        """
        model_dir = Path(model_path)

        try:
            # 加载预测器对象
            predictor_path = model_dir / "predictor.pkl"
            if predictor_path.exists():
                with open(predictor_path, "rb") as f:
                    model = pickle.load(f)
                self.logger.info(f"预测器对象已加载: {predictor_path}")

                # 如果有TensorFlow模型，尝试加载
                tf_model_path = model_dir / "tensorflow_model"
                if TENSORFLOW_AVAILABLE and tf_model_path.exists():
                    try:
                        tf_model = tf.keras.models.load_model(tf_model_path)
                        model.model = tf_model
                        self.logger.info(f"TensorFlow模型已加载: {tf_model_path}")
                    except Exception as e:
                        self.logger.warning(f"TensorFlow模型加载失败: {e}")

                return model
            else:
                raise FileNotFoundError(f"预测器文件不存在: {predictor_path}")

        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise

    def record_performance(
        self,
        model_name: str,
        training_result: Dict[str, Any],
        prediction_accuracy: float = 0.0,
        config: Dict[str, Any] = None,
    ):
        """
        记录模型性能

        Args:
            model_name: 模型名称
            training_result: 训练结果
            prediction_accuracy: 预测准确率
            config: 模型配置
        """
        performance = ModelPerformance(
            model_name=model_name,
            timestamp=datetime.now().isoformat(),
            training_loss=training_result.get("final_loss", 0.0),
            validation_loss=training_result.get("final_val_loss", 0.0),
            training_time=training_result.get("training_time", 0.0),
            prediction_accuracy=prediction_accuracy,
            model_params=training_result.get("model_params", 0),
            config=config or {},
            metadata=training_result,
        )

        self.performance_log.append(performance)
        self._save_performance_log()

        self.logger.info(f"性能记录已保存: {model_name}")

    def get_best_models(
        self, metric: str = "validation_loss", top_k: int = 5
    ) -> List[ModelPerformance]:
        """
        获取最佳模型

        Args:
            metric: 评估指标
            top_k: 返回前k个模型

        Returns:
            最佳模型列表
        """
        if not self.performance_log:
            return []

        # 根据指标排序
        reverse = metric in ["prediction_accuracy"]  # 准确率越高越好
        sorted_models = sorted(
            self.performance_log, key=lambda x: getattr(x, metric, 0), reverse=reverse
        )

        return sorted_models[:top_k]

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        if not self.performance_log:
            return {"message": "暂无性能记录"}

        df = pd.DataFrame([asdict(perf) for perf in self.performance_log])

        summary = {
            "total_models": len(self.performance_log),
            "model_types": df["model_name"].value_counts().to_dict(),
            "average_metrics": {
                "training_loss": df["training_loss"].mean(),
                "validation_loss": df["validation_loss"].mean(),
                "prediction_accuracy": df["prediction_accuracy"].mean(),
                "training_time": df["training_time"].mean(),
                "model_params": df["model_params"].mean(),
            },
            "best_models": {
                "lowest_val_loss": (
                    df.loc[df["validation_loss"].idxmin()]["model_name"]
                    if "validation_loss" in df.columns
                    else None
                ),
                "highest_accuracy": (
                    df.loc[df["prediction_accuracy"].idxmax()]["model_name"]
                    if "prediction_accuracy" in df.columns
                    else None
                ),
                "fastest_training": (
                    df.loc[df["training_time"].idxmin()]["model_name"]
                    if "training_time" in df.columns
                    else None
                ),
            },
        }

        return summary

    def optimize_hyperparameters(
        self,
        model_type: str,
        data: pd.DataFrame,
        target_col: str,
        param_grid: Dict[str, List],
    ) -> Dict[str, Any]:
        """
        超参数优化

        Args:
            model_type: 模型类型
            data: 训练数据
            target_col: 目标列
            param_grid: 参数网格

        Returns:
            最佳参数和结果
        """
        self.logger.info(f"开始{model_type}超参数优化...")

        best_score = float("inf")
        best_params = None
        best_result = None

        # 生成参数组合
        param_combinations = self._generate_param_combinations(param_grid)

        for i, params in enumerate(param_combinations):
            self.logger.info(f"测试参数组合 {i+1}/{len(param_combinations)}: {params}")

            try:
                # 创建模型
                model = self._create_model_with_params(model_type, params)

                # 训练模型
                result = model.train(data, target_col)

                # 评估性能
                score = result.get("final_val_loss", float("inf"))

                if score < best_score:
                    best_score = score
                    best_params = params
                    best_result = result
                    self.logger.info(f"发现更好的参数: {params}, 分数: {score}")

            except Exception as e:
                self.logger.warning(f"参数组合失败: {params}, 错误: {e}")
                continue

        optimization_result = {
            "model_type": model_type,
            "best_params": best_params,
            "best_score": best_score,
            "best_result": best_result,
            "total_combinations": len(param_combinations),
        }

        self.logger.info(f"超参数优化完成: {optimization_result}")
        return optimization_result

    def _generate_param_combinations(
        self, param_grid: Dict[str, List]
    ) -> List[Dict[str, Any]]:
        """生成参数组合"""
        import itertools

        keys = param_grid.keys()
        values = param_grid.values()
        combinations = []

        for combination in itertools.product(*values):
            combinations.append(dict(zip(keys, combination)))

        return combinations

    def _create_model_with_params(self, model_type: str, params: Dict[str, Any]):
        """根据参数创建模型"""
        if model_type == "lstm":
            config = LSTMConfig(**params)
            return LSTMPredictor(config)
        elif model_type == "transformer":
            config = TransformerConfig(**params)
            return TransformerPredictor(config)
        elif model_type == "multi_task":
            config = MultiTaskConfig(**params)
            return MultiTaskNeuralPredictor(config)
        elif model_type == "ensemble":
            config = EnsembleConfig(**params)
            return EnsembleDeepLearning(config)
        else:
            raise ValueError(f"未知的模型类型: {model_type}")
