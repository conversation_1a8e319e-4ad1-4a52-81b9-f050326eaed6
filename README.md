# 大乐透预测系统开发文档（Python）

## 一、项目概述

本项目旨在通过数据分析和概率模型，对大乐透历史开奖数据进行特征归类、马尔科夫建模、贝叶斯推理、杀号处理、最终号码生成，并进行50期回测（仅输出最后10期）来验证模型效果。数据读取自 `dlt_data.csv` 文件，预测逻辑需严格遵守数据隔离，避免未来数据泄漏。

---

## 二、项目结构设计

```
rebuileCP/
├── src/                     # 源代码目录
│   ├── core/               # 核心模块
│   │   ├── config_manager.py      # 统一配置管理器
│   │   ├── exceptions.py          # 异常定义
│   │   ├── interfaces.py          # 核心接口
│   │   ├── base.py               # 基础类
│   │   └── predictor_factory.py  # 预测器工厂
│   ├── algorithms/         # 算法模块
│   │   ├── enhanced_bayesian.py   # 增强贝叶斯算法
│   │   ├── enhanced_markov_bayes.py # 马尔科夫-贝叶斯融合
│   │   └── data_driven_predictors.py # 数据驱动预测器
│   ├── models/             # 模型模块
│   │   ├── unified_odd_even_predictor.py # 统一奇偶预测器
│   │   ├── enhanced_size_ratio_predictor.py # 增强大小比预测器
│   │   ├── deep_learning/  # 深度学习模型
│   │   ├── neural/         # 神经网络模型
│   │   └── ensemble/       # 集成模型
│   ├── decision_tree/      # 决策树模块
│   │   ├── improved_odd_even_tree.py # 改进奇偶决策树
│   │   └── tree_ensemble.py       # 决策树集成
│   ├── systems/            # 系统模块
│   │   ├── main.py                # 主预测系统
│   │   └── unified_predictor.py   # 统一预测器
│   └── utils/              # 工具模块
├── tests/                  # 测试目录
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   └── performance/       # 性能测试
├── docs/                   # 文档目录
├── scripts/               # 脚本目录
├── config/                # 配置目录
├── main.py                # 主入口文件
├── pyproject.toml         # 项目配置
└── README.md              # 项目说明
```

---

## 三、规则与算法说明

### 1. 数据读取与格式

- 文件名：`dlt_data.csv`
- 格式示例：
  ```
  期号,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
  24050,01,03,07,22,34,04,11
  ```

---

### 2. 回测准则

- **目标**：预测第 i+1 期，仅使用 ≤ i 期数据。
- **输出**：仅展示最后 10 期回测结果。
- **限制**：不得泄露未来数据；马尔科夫链与贝叶斯预测不能使用随机机制。

---

### 3. 特征归类（比值）

#### 红球特征：

- **奇偶比分类**：
  - 状态：0:5, 1:4, 2:3, 3:2, 4:1, 5:0 共 6 种

- **大小比分类**：
  - 小号范围：01–18，大号范围：19–35
  - 状态与奇偶比相同，共 6 种

#### 蓝球特征：

- **奇偶比与大小比**（等价）：
  - 小号范围：01–06，大号范围：07–12
  - 状态：0:2, 1:1, 2:0 共 3 种

---

### 4. 状态预测方法

#### 马尔科夫链

- 对比值状态（如红球奇偶比）建立转移概率矩阵
- 输出预测状态的最大转移概率，确保稳定性

#### 贝叶斯选择

- 输入多个状态概率，结合历史先验概率
- 选择最大后验概率状态，禁用随机选择，确保稳定性

---

### 5. 杀号策略

- 每一位红球预测两个最不可能出现的号码
- 蓝球每位也预测两个最不可能的号码
- **回测胜率需 ≥ 90%**
- 杀号影响最终号码生成逻辑

---

### 6. 号码生成

- 利用统计分析与概率模型随机生成符合状态的号码组合
- 过滤掉所有落入杀号集的号码
- 生成的号码组合需满足：
  - **50期回测中命中 2+1 的概率 ≥ 60%**
  - 满足所有比值状态预测

---

## 四、控制台日志输出格式

> 注意：禁止写入文件，仅控制台输出，格式如下：

```
基于第25157期预测第25158期:

红球
奇偶比: 预测[3:2(0.709)] -> 实际[2:3] (未中)
大小比: 预测[2:3(0.695)] -> 实际[3:2] (未中)

蓝球
大小比: 预测[1:1(0.708)] -> 实际[0:2] (未中)

杀号：1：（05,19），2：（07,23），3：（08,22），4：（14,31），5：（11,20），6：（04,33），7：（01,09）——成功率：92%

预测号码：05,06,08,18,32——01,02

实际开奖号码：05,06,08,18,32——01,02
```

> 最新预测（待开奖）格式如下：

```
预测下一期:

红球
奇偶比: 预测[3:2(0.709)] -> 实际[待开奖] (待验证)
大小比: 预测[2:3(0.695)] -> 实际[待开奖] (待验证)

蓝球
大小比: 预测[1:1(0.759)] -> 实际[待开奖] (待验证)

杀号：1：（03,25），2：（06,26），3：（08,22），4：（17,30），5：（11,24），6：（04,33），7：（02,12）——成功率：91%

预测号码：06,09,10,19,28——01,10

实际开奖号码：待开奖
```

---

## 五、依赖说明

本项目依赖以下第三方库：

```bash
pip install numpy pandas scikit-learn
```

---

## 六、运行说明

- 执行主脚本：

```bash
python main.py
```

- 控制台将打印出最近10期回测情况和预测结果。
- 确保 `dlt_data.csv` 位于同级目录，并保证数据从新到旧排序。

---

## 七、模型验证标准

| 模块       | 验证标准              |
|------------|-----------------------|
| 比值预测   | 三项命中率 ≥ 80%      |
| 杀号逻辑   | 回测成功率 ≥ 90%      |
| 号码生成器 | 50期内命中2+1 ≥ 60%   |

---

## 八、注意事项

- 所有预测不能依赖随机性生成，必须保证确定性输出。
- 严格遵守数据隔离规则，杜绝未来数据参与模型训练。
- 回测结果应实时打印，格式规范，确保可读性。

---

## 九、项目重构说明

### 重构目标
本次重构旨在清理项目结构，消除代码冗余，提高系统的可维护性和可扩展性。

### 重构内容

#### 1. 目录结构优化
- **标准化布局**：采用标准的Python项目结构，将所有源代码移至`src/`目录
- **测试分离**：所有测试文件统一移至`tests/`目录，按类型分为unit、integration、performance
- **文档整理**：将所有markdown文档移至`docs/`目录，按类型组织
- **脚本整理**：将工具脚本移至`scripts/`目录

#### 2. 代码去重
- **配置管理器整合**：删除重复的配置管理器，保留功能最完整的`src/core/config_manager.py`
- **预测器去重**：
  - 删除旧版本奇偶比预测器，保留改进版本
  - 删除基础神经网络预测器，保留增强版本
  - 删除重复的统一预测器实现
- **V4系列清理**：删除测试和临时文件，保留核心功能模块

#### 3. 数据一致性修复
- **球号分类标准统一**：
  - 红球：1-17为小号，18-35为大号
  - 蓝球：1-6为小号，7-12为大号
- **修复范围错误**：纠正了球池范围定义错误

#### 4. 架构改进
- **接口标准化**：统一预测器接口，提高系统一致性
- **依赖注入**：使用依赖容器管理组件依赖关系
- **工厂模式**：通过工厂类创建预测器实例
- **适配器模式**：为旧系统提供兼容性适配器

### 重构成果

#### 文件清理统计
- **删除重复文件**：10个
- **保留核心文件**：确保系统完整性
- **目录结构清晰**：符合Python项目最佳实践

#### 代码质量提升
- **消除冗余**：删除重复的预测器实现
- **统一标准**：修复数据分类标准不一致问题
- **提高可维护性**：清晰的模块划分和接口定义

#### 系统稳定性
- **保持功能完整**：重构过程中保持所有核心功能
- **向后兼容**：通过适配器模式保持旧接口兼容性
- **测试验证**：通过基础验证确保系统结构正确

### 后续计划
1. **依赖安装**：配置完整的开发环境
2. **功能测试**：运行完整的测试套件验证系统功能
3. **性能优化**：基于重构后的清晰结构进行性能优化
4. **文档完善**：更新API文档和使用指南

---

## 十、开发环境配置

### 依赖安装
```bash
# 安装基础依赖
pip install -e .

# 安装开发依赖
pip install -e ".[dev]"

# 安装深度学习依赖（可选）
pip install -e ".[deep-learning]"
```

### 运行测试
```bash
# 运行基础验证
python tests/unit/minimal_validation.py

# 运行完整测试套件（需要安装依赖）
pytest tests/
```

### 代码质量检查
```bash
# 代码格式化
black src/ tests/

# 代码检查
flake8 src/ tests/

# 类型检查
mypy src/
```
