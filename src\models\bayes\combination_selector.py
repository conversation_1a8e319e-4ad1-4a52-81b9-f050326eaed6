"""
贝叶斯号码组合选择器
使用贝叶斯方法对多组预测号码进行评估和排序
"""

import numpy as np
from typing import List, Tuple, Dict
from collections import Counter


class BayesCombinationSelector:
    """贝叶斯号码组合选择器"""

    def __init__(self, config: Dict = None):
        self.is_initialized = False
        self.historical_data = None

        # 增强多样性的配置（降低频率权重，增加随机性）
        default_config = {
            "frequency_weight": 0.20,  # 20% - 频率分析权重（降低）
            "pattern_weight": 0.15,  # 15% - 模式识别权重（降低）
            "balance_weight": 0.25,  # 25% - 平衡性权重（提高）
            "trend_weight": 0.10,  # 10% - 趋势分析权重（提高）
            "kill_avoidance_weight": 0.15,  # 15% - 杀号避免权重
            "diversity_weight": 0.15,  # 15% - 多样性权重（新增）
            "historical_window": 50,  # 历史数据窗口
            "recent_trend_window": 5,  # 近期趋势窗口
            "overlap_target_red": 0.2,  # 红球重叠目标（降低）
            "overlap_target_blue": 0.3,  # 蓝球重叠目标（降低）
        }

        # 合并用户配置
        if config:
            default_config.update(config)

        self.config = default_config

        # 设置特征权重
        self.feature_weights = {
            "frequency_score": self.config["frequency_weight"],
            "pattern_score": self.config["pattern_weight"],
            "balance_score": self.config["balance_weight"],
            "trend_score": self.config["trend_weight"],
            "kill_avoidance_score": self.config["kill_avoidance_weight"],
            "diversity_score": self.config["diversity_weight"],
        }

    def initialize(
        self,
        historical_data: List[Tuple[List[int], List[int]]],
        kill_numbers: Dict[str, List[List[int]]] = None,
    ):
        """
        初始化选择器（增强异常处理版）

        Args:
            historical_data: 历史开奖数据 [(红球, 蓝球), ...]
            kill_numbers: 杀号数据
        """
        try:
            # 输入验证
            if not isinstance(historical_data, list) or len(historical_data) == 0:
                raise ValueError("历史数据为空或格式错误")

            # 验证历史数据格式
            validated_data = []
            for i, item in enumerate(historical_data):
                try:
                    if (
                        isinstance(item, tuple)
                        and len(item) == 2
                        and isinstance(item[0], list)
                        and len(item[0]) == 5
                        and isinstance(item[1], list)
                        and len(item[1]) == 2
                    ):
                        # 验证号码范围
                        red_valid = all(
                            isinstance(n, int) and 1 <= n <= 35 for n in item[0]
                        )
                        blue_valid = all(
                            isinstance(n, int) and 1 <= n <= 12 for n in item[1]
                        )
                        if red_valid and blue_valid:
                            validated_data.append(item)
                except Exception:
                    continue  # 跳过无效数据

            if len(validated_data) < 3:  # 降低要求：至少需要3期数据
                raise ValueError(f"有效历史数据不足: {len(validated_data)}期")

            self.historical_data = validated_data

            # 验证杀号信息
            try:
                if kill_numbers and isinstance(kill_numbers, dict):
                    validated_kills = {}

                    # 验证红球杀号
                    red_kills = kill_numbers.get("red", [])
                    if isinstance(red_kills, list):
                        validated_red = []
                        for kill_list in red_kills:
                            if isinstance(kill_list, list):
                                valid_kills = [
                                    n
                                    for n in kill_list
                                    if isinstance(n, int) and 1 <= n <= 35
                                ]
                                if valid_kills:
                                    validated_red.append(valid_kills)
                        validated_kills["red"] = validated_red

                    # 验证蓝球杀号
                    blue_kills = kill_numbers.get("blue", [])
                    if isinstance(blue_kills, list):
                        validated_blue = []
                        for kill_list in blue_kills:
                            if isinstance(kill_list, list):
                                valid_kills = [
                                    n
                                    for n in kill_list
                                    if isinstance(n, int) and 1 <= n <= 12
                                ]
                                if valid_kills:
                                    validated_blue.append(valid_kills)
                        validated_kills["blue"] = validated_blue

                    self.kill_numbers = validated_kills
                else:
                    self.kill_numbers = {"red": [], "blue": []}
            except Exception as e:
                print(f"    ⚠️ 杀号验证失败: {e}")
                self.kill_numbers = {"red": [], "blue": []}

            # 计算历史统计（增强异常处理）
            try:
                self._calculate_historical_stats()
            except Exception as e:
                print(f"    ⚠️ 历史统计计算失败: {e}")
                # 设置默认统计值
                self.red_frequency = Counter()
                self.blue_frequency = Counter()
                self.red_odd_even_patterns = []
                self.red_size_patterns = []
                self.blue_size_patterns = []

            # 设置特征权重（使用优化后的配置）
            self.feature_weights = {
                "frequency_score": self.config["frequency_weight"],
                "pattern_score": self.config["pattern_weight"],
                "trend_score": self.config["trend_weight"],
                "balance_score": self.config["balance_weight"],
                "kill_avoidance_score": self.config["kill_avoidance_weight"],
            }

            self.is_initialized = True

        except Exception as e:
            print(f"    ❌ 选择器初始化失败: {e}")
            # 设置最小可用状态
            self.historical_data = []
            self.kill_numbers = {"red": [], "blue": []}
            self.red_frequency = Counter()
            self.blue_frequency = Counter()
            self.red_odd_even_patterns = []
            self.red_size_patterns = []
            self.blue_size_patterns = []
            self.feature_weights = {
                "frequency_score": 0.3157894736842105,  # 优化后的频率权重
                "pattern_score": 0.2631578947368421,  # 优化后的模式权重
                "trend_score": 0.05263157894736842,  # 优化后的趋势权重
                "balance_score": 0.10526315789473684,  # 优化后的平衡权重
                "kill_avoidance_score": 0.2631578947368421,  # 优化后的杀号权重
            }
            self.is_initialized = False
            raise  # 重新抛出异常让调用者处理

    def _calculate_historical_stats(self):
        """计算历史统计信息"""
        if not self.historical_data:
            return

        # 使用配置的历史窗口大小
        window_size = min(self.config["historical_window"], len(self.historical_data))
        data_window = self.historical_data[-window_size:]  # 使用最近的数据

        # 红球频率统计
        self.red_frequency = Counter()
        self.blue_frequency = Counter()

        # 奇偶比、大小比统计
        self.red_odd_even_patterns = []
        self.red_size_patterns = []
        self.blue_size_patterns = []

        for red_balls, blue_balls in data_window:
            # 频率统计
            self.red_frequency.update(red_balls)
            self.blue_frequency.update(blue_balls)

            # 模式统计
            red_odd = sum(1 for x in red_balls if x % 2 == 1)
            red_even = len(red_balls) - red_odd
            self.red_odd_even_patterns.append((red_odd, red_even))

            red_small = sum(1 for x in red_balls if x <= 17)
            red_big = len(red_balls) - red_small
            self.red_size_patterns.append((red_small, red_big))

            blue_small = sum(1 for x in blue_balls if x <= 6)
            blue_big = len(blue_balls) - blue_small
            self.blue_size_patterns.append((blue_small, blue_big))

    def evaluate_combinations(
        self, combinations: List[Tuple[List[int], List[int]]]
    ) -> List[Tuple[int, float, Dict[str, float]]]:
        """
        使用贝叶斯方法评估号码组合

        Args:
            combinations: 号码组合列表 [(红球, 蓝球), ...]

        Returns:
            List[Tuple[int, float, Dict[str, float]]]: [(组合索引, 总分, 各项得分), ...]
        """
        if not self.is_initialized:
            raise ValueError("选择器尚未初始化")

        results = []

        for i, (red_balls, blue_balls) in enumerate(combinations):
            # 计算各项得分
            scores = self._calculate_combination_scores(red_balls, blue_balls)

            # 贝叶斯加权融合
            total_score = self._bayesian_fusion(scores)

            results.append((i, total_score, scores))

        # 按总分排序
        results.sort(key=lambda x: x[1], reverse=True)

        return results

    def _calculate_combination_scores(
        self, red_balls: List[int], blue_balls: List[int]
    ) -> Dict[str, float]:
        """计算单个组合的各项得分"""
        scores = {}

        # 1. 频率得分 - 基于历史出现频率
        scores["frequency_score"] = self._calculate_frequency_score(
            red_balls, blue_balls
        )

        # 2. 模式得分 - 基于奇偶比、大小比的历史分布
        scores["pattern_score"] = self._calculate_pattern_score(red_balls, blue_balls)

        # 3. 平衡性得分 - 号码分布的均匀性
        scores["balance_score"] = self._calculate_balance_score(red_balls, blue_balls)

        # 4. 趋势得分 - 与最近期号码的关联性
        scores["trend_score"] = self._calculate_trend_score(red_balls, blue_balls)

        # 5. 杀号规避得分 - 避开杀号的程度
        scores["kill_avoidance_score"] = self._calculate_kill_avoidance_score(
            red_balls, blue_balls
        )

        return scores

    def _calculate_frequency_score(
        self, red_balls: List[int], blue_balls: List[int]
    ) -> float:
        """计算频率得分"""
        if not self.historical_data:
            return 0.5

        total_periods = len(self.historical_data)

        # 红球频率得分
        red_score = 0
        for num in red_balls:
            frequency = self.red_frequency.get(num, 0)
            # 使用对数平滑，避免极端值
            normalized_freq = (frequency + 1) / (total_periods + 35)
            red_score += np.log(normalized_freq + 0.001)

        # 蓝球频率得分
        blue_score = 0
        for num in blue_balls:
            frequency = self.blue_frequency.get(num, 0)
            normalized_freq = (frequency + 1) / (total_periods + 12)
            blue_score += np.log(normalized_freq + 0.001)

        # 归一化到0-1范围
        combined_score = (red_score + blue_score) / (len(red_balls) + len(blue_balls))
        return max(0, min(1, (combined_score + 6) / 6))  # 调整范围

    def _calculate_pattern_score(
        self, red_balls: List[int], blue_balls: List[int]
    ) -> float:
        """计算模式得分"""
        if not self.red_odd_even_patterns:
            return 0.5

        # 当前组合的模式
        red_odd = sum(1 for x in red_balls if x % 2 == 1)
        red_even = len(red_balls) - red_odd

        red_small = sum(1 for x in red_balls if x <= 17)
        red_big = len(red_balls) - red_small

        blue_small = sum(1 for x in blue_balls if x <= 6)
        blue_big = len(blue_balls) - blue_small

        # 计算与历史模式的匹配度
        odd_even_matches = sum(
            1
            for pattern in self.red_odd_even_patterns
            if pattern == (red_odd, red_even)
        )
        size_matches = sum(
            1 for pattern in self.red_size_patterns if pattern == (red_small, red_big)
        )
        blue_size_matches = sum(
            1
            for pattern in self.blue_size_patterns
            if pattern == (blue_small, blue_big)
        )

        total_periods = len(self.historical_data)

        # 计算概率
        odd_even_prob = (odd_even_matches + 1) / (total_periods + 6)  # 平滑
        size_prob = (size_matches + 1) / (total_periods + 6)
        blue_size_prob = (blue_size_matches + 1) / (total_periods + 3)

        # 综合得分
        return (odd_even_prob + size_prob + blue_size_prob) / 3

    def _calculate_balance_score(
        self, red_balls: List[int], blue_balls: List[int]
    ) -> float:
        """计算平衡性得分"""
        # 红球分布平衡性
        red_ranges = [0, 0, 0, 0, 0]  # 1-7, 8-14, 15-21, 22-28, 29-35
        for num in red_balls:
            range_idx = min(4, (num - 1) // 7)
            red_ranges[range_idx] += 1

        # 计算红球分布的标准差（越小越平衡）
        red_std = np.std(red_ranges)
        red_balance = max(0, 1 - red_std / 2.5)  # 归一化

        # 蓝球分布平衡性
        blue_ranges = [0, 0]  # 1-6, 7-12
        for num in blue_balls:
            range_idx = 0 if num <= 6 else 1
            blue_ranges[range_idx] += 1

        blue_balance = 1 - abs(blue_ranges[0] - blue_ranges[1]) / len(blue_balls)

        return (red_balance + blue_balance) / 2

    def _calculate_trend_score(
        self, red_balls: List[int], blue_balls: List[int]
    ) -> float:
        """计算趋势得分"""
        trend_window = self.config["recent_trend_window"]
        if len(self.historical_data) < trend_window:
            return 0.5

        # 最近N期的号码
        recent_red = set()
        recent_blue = set()

        for red, blue in self.historical_data[-trend_window:]:
            recent_red.update(red)
            recent_blue.update(blue)

        # 计算与最近号码的重叠度
        red_overlap = len(set(red_balls) & recent_red) / len(red_balls)
        blue_overlap = len(set(blue_balls) & recent_blue) / len(blue_balls)

        # 使用配置的期望重叠度
        red_trend = 1 - abs(red_overlap - self.config["overlap_target_red"])
        blue_trend = 1 - abs(blue_overlap - self.config["overlap_target_blue"])

        return (red_trend + blue_trend) / 2

    def _calculate_kill_avoidance_score(
        self, red_balls: List[int], blue_balls: List[int]
    ) -> float:
        """计算杀号规避得分（增强异常处理版）"""
        try:
            # 输入验证
            if not isinstance(red_balls, list) or not isinstance(blue_balls, list):
                return 1.0

            if not self.kill_numbers or not isinstance(self.kill_numbers, dict):
                return 1.0

            total_penalty = 0
            total_kills = 0

            # 检查红球杀号（增强异常处理）
            try:
                red_kill_lists = self.kill_numbers.get("red", [])
                if isinstance(red_kill_lists, list):
                    for red_kill_list in red_kill_lists:
                        if isinstance(red_kill_list, list) and red_kill_list:
                            # 检查红球列表中是否包含任何杀号
                            for kill_num in red_kill_list:
                                if isinstance(kill_num, int) and kill_num in red_balls:
                                    total_penalty += 1
                            total_kills += len(red_kill_list)
            except Exception as e:
                print(f"    ⚠️ 红球杀号检查异常: {e}")

            # 检查蓝球杀号（增强异常处理）
            try:
                blue_kill_lists = self.kill_numbers.get("blue", [])
                if isinstance(blue_kill_lists, list):
                    for blue_kill_list in blue_kill_lists:
                        if isinstance(blue_kill_list, list) and blue_kill_list:
                            # 检查蓝球列表中是否包含任何杀号
                            for kill_num in blue_kill_list:
                                if isinstance(kill_num, int) and kill_num in blue_balls:
                                    total_penalty += 1
                            total_kills += len(blue_kill_list)
            except Exception as e:
                print(f"    ⚠️ 蓝球杀号检查异常: {e}")

            # 计算得分
            if total_kills == 0:
                return 1.0

            # 如果包含杀号，严重降低得分
            if total_penalty > 0:
                print(
                    f"    ⚠️ 发现杀号冲突: 惩罚{total_penalty}个, 总杀号{total_kills}个"
                )
                return 0.1  # 包含杀号的组合得分极低

            return 1.0  # 不包含杀号的组合得满分

        except Exception as e:
            print(f"    ❌ 杀号规避计算异常: {e}")
            return 1.0  # 异常时返回中性得分

    def _bayesian_fusion(self, scores: Dict[str, float]) -> float:
        """贝叶斯融合各项得分"""
        # 使用对数概率进行贝叶斯融合
        log_likelihood = 0

        for feature, score in scores.items():
            weight = self.feature_weights.get(feature, 0.2)
            # 将得分转换为对数似然
            likelihood = max(0.001, min(0.999, score))  # 避免极端值
            log_likelihood += weight * np.log(likelihood / (1 - likelihood))

        # 转换回概率
        exp_log = np.exp(log_likelihood)
        probability = exp_log / (1 + exp_log)

        return probability

    def _enhance_diversity_selection(
        self,
        evaluation_results: List[Tuple[int, float, Dict[str, float]]],
        period_number: int = None,
    ) -> List[Tuple[int, float, Dict[str, float]]]:
        """
        增强多样性选择机制

        Args:
            evaluation_results: 原始评估结果
            period_number: 期号，用于随机种子

        Returns:
            增强多样性后的评估结果
        """
        try:
            import random

            # 使用期号作为随机种子的一部分，确保不同期号有不同的随机性
            if period_number is not None:
                random.seed(period_number * 7 + 42)  # 使用质数增加随机性
            else:
                import time

                random.seed(int(time.time() * 1000) % 10000)

            # 为每个组合添加随机扰动，增加多样性（增强版）
            enhanced_results = []
            for combo_idx, total_score, scores in evaluation_results:
                # 增强随机扰动幅度（-0.2 到 +0.2）
                diversity_factor = (random.random() - 0.5) * 0.4

                # 根据期号特征调整扰动强度
                if period_number is not None:
                    period_mod = period_number % 10
                    if period_mod in [1, 3, 7, 9]:  # 奇数期号增加更多随机性
                        diversity_factor *= 2.0
                    elif period_mod in [2, 4, 6, 8]:  # 偶数期号适度随机性
                        diversity_factor *= 1.5
                    else:  # 0和5增加最大随机性
                        diversity_factor *= 2.5

                # 添加基于组合索引的多样性调整
                index_diversity = (combo_idx % 7) * 0.02  # 基于索引的小幅调整
                diversity_factor += index_diversity

                # 应用多样性调整
                enhanced_score = total_score + diversity_factor
                enhanced_score = max(0.0, min(1.0, enhanced_score))  # 限制在合理范围内

                enhanced_results.append((combo_idx, enhanced_score, scores))

            # 重新排序
            enhanced_results.sort(key=lambda x: x[1], reverse=True)

            # 进一步增强多样性：避免连续选择过于相似的组合
            final_results = self._apply_similarity_penalty(enhanced_results)

            return final_results

        except Exception as e:
            print(f"    ⚠️ 多样性增强失败: {e}")
            return evaluation_results

    def _apply_similarity_penalty(
        self, results: List[Tuple[int, float, Dict[str, float]]]
    ) -> List[Tuple[int, float, Dict[str, float]]]:
        """
        应用相似性惩罚，避免选择过于相似的组合（增强版）
        """
        try:
            if len(results) <= 1:
                return results

            penalized_results = []
            selected_combinations = []
            selected_combo_data = []  # 存储已选择组合的实际号码数据

            for combo_idx, score, scores in results:
                # 获取当前组合的号码数据
                if hasattr(self, "_current_combinations") and combo_idx < len(
                    self._current_combinations
                ):
                    current_red, current_blue = self._current_combinations[combo_idx]

                    # 计算与已选择组合的内容相似性
                    similarity_penalty = 0.0

                    for selected_red, selected_blue in selected_combo_data:
                        # 计算红球重叠度
                        red_overlap = len(set(current_red) & set(selected_red)) / 5.0
                        # 计算蓝球重叠度
                        blue_overlap = len(set(current_blue) & set(selected_blue)) / 2.0

                        # 综合相似度（红球权重0.7，蓝球权重0.3）
                        total_similarity = red_overlap * 0.7 + blue_overlap * 0.3

                        # 相似度惩罚（相似度越高惩罚越大）
                        if total_similarity > 0.6:  # 相似度超过60%进行重惩罚
                            similarity_penalty += total_similarity * 0.3
                        elif total_similarity > 0.4:  # 相似度超过40%进行轻惩罚
                            similarity_penalty += total_similarity * 0.15

                    # 应用惩罚
                    penalized_score = score - similarity_penalty
                    penalized_score = max(0.0, penalized_score)

                    penalized_results.append((combo_idx, penalized_score, scores))
                    selected_combinations.append(combo_idx)
                    selected_combo_data.append((current_red, current_blue))
                else:
                    # 回退到原始方法
                    similarity_penalty = 0.0
                    for selected_idx in selected_combinations:
                        if abs(combo_idx - selected_idx) < 5:
                            similarity_penalty += 0.05

                    penalized_score = score - similarity_penalty
                    penalized_score = max(0.0, penalized_score)
                    penalized_results.append((combo_idx, penalized_score, scores))
                    selected_combinations.append(combo_idx)

                # 限制选择数量，避免过度处理
                if len(selected_combinations) >= 20:
                    break

            # 重新排序
            penalized_results.sort(key=lambda x: x[1], reverse=True)

            return penalized_results

        except Exception as e:
            print(f"    ⚠️ 相似性惩罚失败: {e}")
            return results

    def generate_and_select_combinations(
        self,
        target_ratios: Dict,
        kill_numbers: Dict,
        top_k: int = 10,
        period_number: int = None,
    ) -> List[Dict]:
        """
        独立生成并选择最优组合（不依赖外部生成器）

        Args:
            target_ratios: 目标比例 {'red_odd_even': (ratio, prob), 'red_size': (ratio, prob), 'blue_size': (ratio, prob)}
            kill_numbers: 杀号信息 {'red': [kill_list], 'blue': [kill_list]}
            top_k: 返回的组合数量
            period_number: 期号，用于多样性增强

        Returns:
            List[Dict]: 排序后的组合信息
        """
        try:
            if not self.is_initialized:
                raise ValueError("选择器尚未初始化")

            # 生成候选组合
            combinations = self._generate_bayes_combinations(
                target_ratios, kill_numbers, count=50
            )

            if not combinations:
                raise ValueError("无法生成有效组合")

            # 评估组合
            evaluation_results = self.evaluate_combinations(combinations)

            # 增强多样性选择
            evaluation_results = self._enhance_diversity_selection(
                evaluation_results, period_number
            )

            # 返回前K个组合
            top_results = evaluation_results[:top_k]

            formatted_results = []
            for rank, (combo_idx, score, scores) in enumerate(top_results, 1):
                red_balls, blue_balls = combinations[combo_idx]

                formatted_results.append(
                    {
                        "rank": rank,
                        "original_index": combo_idx,
                        "red_balls": red_balls,
                        "blue_balls": blue_balls,
                        "total_score": score,
                        "confidence": min(100, int(score * 100)),
                        "scores": scores,
                        "recommendation": self._get_recommendation_level(score),
                    }
                )

            return formatted_results

        except Exception as e:
            print(f"    ⚠️ 贝叶斯组合生成失败: {e}")
            return self._generate_fallback_combinations(
                target_ratios, kill_numbers, top_k
            )

    def select_top_combinations(
        self,
        combinations: List[Tuple[List[int], List[int]]],
        top_k: int = 5,
        period_number: int = None,
    ) -> List[Dict]:
        """
        选择前K个最优组合（增强多样性版）

        Args:
            combinations: 号码组合列表
            top_k: 返回前K个组合
            period_number: 期号，用于增加随机性

        Returns:
            List[Dict]: 排序后的组合信息
        """
        try:
            # 输入验证
            if not isinstance(combinations, list) or len(combinations) == 0:
                raise ValueError("组合列表为空或格式错误")

            if not self.is_initialized:
                raise ValueError("选择器尚未初始化")

            top_k = max(1, min(len(combinations), int(top_k)))  # 限制范围

            # 存储组合数据供相似性检测使用
            self._current_combinations = combinations

            # 评估组合（增强异常处理）
            try:
                evaluation_results = self.evaluate_combinations(combinations)
                if not evaluation_results:
                    raise ValueError("评估结果为空")
            except Exception as e:
                print(f"    ⚠️ 组合评估失败: {e}")
                # 使用简单排序作为回退
                evaluation_results = [(i, 0.5, {}) for i in range(len(combinations))]

            # 🎯 增强多样性选择机制
            evaluation_results = self._enhance_diversity_selection(
                evaluation_results, period_number
            )

            top_combinations = []
            try:
                for i, (combo_idx, total_score, scores) in enumerate(
                    evaluation_results[:top_k]
                ):
                    try:
                        # 验证组合索引
                        if combo_idx >= len(combinations):
                            continue

                        red_balls, blue_balls = combinations[combo_idx]

                        # 验证组合数据
                        if (
                            not isinstance(red_balls, list)
                            or len(red_balls) != 5
                            or not isinstance(blue_balls, list)
                            or len(blue_balls) != 2
                        ):
                            continue

                        combination_info = {
                            "rank": i + 1,
                            "original_index": combo_idx + 1,
                            "red_balls": sorted(red_balls),
                            "blue_balls": sorted(blue_balls),
                            "total_score": max(0.0, min(1.0, float(total_score))),
                            "confidence": min(100, max(0, int(total_score * 100))),
                            "scores": scores if isinstance(scores, dict) else {},
                            "recommendation": self._get_recommendation_level(
                                total_score
                            ),
                        }

                        top_combinations.append(combination_info)

                    except Exception as e:
                        print(f"    ⚠️ 处理组合{combo_idx}失败: {e}")
                        continue

                if not top_combinations:
                    # 最终回退：返回第一个有效组合
                    for i, (red, blue) in enumerate(combinations[:top_k]):
                        if (
                            isinstance(red, list)
                            and len(red) == 5
                            and isinstance(blue, list)
                            and len(blue) == 2
                        ):
                            top_combinations.append(
                                {
                                    "rank": len(top_combinations) + 1,
                                    "original_index": i + 1,
                                    "red_balls": sorted(red),
                                    "blue_balls": sorted(blue),
                                    "total_score": 0.3,
                                    "confidence": 30,
                                    "scores": {"fallback": 0.3},
                                    "recommendation": "⚠️回退",
                                }
                            )
                            break

                return top_combinations

            except Exception as e:
                print(f"    ⚠️ 组合处理失败: {e}")
                return self._get_emergency_combinations(combinations, top_k)

        except Exception as e:
            print(f"    ❌ 选择组合失败: {e}")
            return self._get_emergency_combinations(combinations, top_k)

    def _get_emergency_combinations(
        self, combinations: List[Tuple[List[int], List[int]]], top_k: int
    ) -> List[Dict]:
        """紧急回退组合生成"""
        try:
            emergency_combinations = []
            for i, (red, blue) in enumerate(combinations[:top_k]):
                try:
                    if (
                        isinstance(red, list)
                        and isinstance(blue, list)
                        and len(red) == 5
                        and len(blue) == 2
                    ):
                        emergency_combinations.append(
                            {
                                "rank": i + 1,
                                "original_index": i + 1,
                                "red_balls": sorted(red),
                                "blue_balls": sorted(blue),
                                "total_score": 0.2,
                                "confidence": 20,
                                "scores": {"emergency": 0.2},
                                "recommendation": "❌紧急",
                            }
                        )
                except Exception:
                    continue

            if not emergency_combinations:
                # 最终兜底
                emergency_combinations = [
                    {
                        "rank": 1,
                        "original_index": 1,
                        "red_balls": [1, 2, 3, 4, 5],
                        "blue_balls": [1, 2],
                        "total_score": 0.1,
                        "confidence": 10,
                        "scores": {"critical": 0.1},
                        "recommendation": "❌严重",
                    }
                ]

            return emergency_combinations
        except Exception:
            return [
                {
                    "rank": 1,
                    "original_index": 1,
                    "red_balls": [1, 2, 3, 4, 5],
                    "blue_balls": [1, 2],
                    "total_score": 0.1,
                    "confidence": 10,
                    "scores": {"fatal": 0.1},
                    "recommendation": "❌致命",
                }
            ]

    def _get_recommendation_level(self, score: float) -> str:
        """根据得分获取推荐等级"""
        if score >= 0.8:
            return "🌟强烈推荐"
        elif score >= 0.7:
            return "⭐推荐"
        elif score >= 0.6:
            return "✅可选"
        elif score >= 0.5:
            return "⚠️谨慎"
        else:
            return "❌不推荐"

    def _generate_bayes_combinations(
        self, target_ratios: Dict, kill_numbers: Dict, count: int = 50
    ) -> List[Tuple[List[int], List[int]]]:
        """
        使用贝叶斯方法独立生成号码组合

        Args:
            target_ratios: 目标比例
            kill_numbers: 杀号信息
            count: 生成组合数量

        Returns:
            List[Tuple[List[int], List[int]]]: 生成的组合列表
        """
        combinations = []
        max_attempts = count * 5
        attempts = 0

        # 解析目标比例
        red_odd_even_ratio = target_ratios.get("red_odd_even", ("3:2", 0.5))[0]
        red_size_ratio = target_ratios.get("red_size", ("2:3", 0.5))[0]
        blue_size_ratio = target_ratios.get("blue_size", ("1:1", 0.5))[0]

        # 解析杀号
        red_kills = []
        blue_kills = []
        if kill_numbers:
            if "red" in kill_numbers and kill_numbers["red"]:
                red_kills = (
                    kill_numbers["red"][0]
                    if isinstance(kill_numbers["red"][0], list)
                    else kill_numbers["red"]
                )
            if "blue" in kill_numbers and kill_numbers["blue"]:
                blue_kills = (
                    kill_numbers["blue"][0]
                    if isinstance(kill_numbers["blue"][0], list)
                    else kill_numbers["blue"]
                )

        while len(combinations) < count and attempts < max_attempts:
            attempts += 1

            try:
                # 生成红球
                red_balls = self._generate_red_balls_bayes(
                    red_odd_even_ratio, red_size_ratio, red_kills
                )

                # 生成蓝球
                blue_balls = self._generate_blue_balls_bayes(
                    blue_size_ratio, blue_kills
                )

                # 验证组合
                if self._validate_combination(red_balls, blue_balls, target_ratios):
                    combinations.append((red_balls, blue_balls))

            except Exception as e:
                continue

        return combinations

    def _generate_red_balls_bayes(
        self, odd_even_ratio: str, size_ratio: str, kill_numbers: List[int]
    ) -> List[int]:
        """使用贝叶斯方法生成红球"""
        # 解析比例
        odd_count, even_count = map(int, odd_even_ratio.split(":"))
        small_count, large_count = map(int, size_ratio.split(":"))

        # 候选号码
        all_reds = list(range(1, 36))
        valid_reds = [num for num in all_reds if num not in kill_numbers]

        if len(valid_reds) < 5:
            valid_reds = all_reds  # 如果杀号过多，忽略杀号约束

        # 分类号码
        odd_nums = [num for num in valid_reds if num % 2 == 1]
        even_nums = [num for num in valid_reds if num % 2 == 0]
        small_nums = [num for num in valid_reds if num <= 17]
        large_nums = [num for num in valid_reds if num >= 18]

        # 使用贝叶斯概率选择
        selected = []

        # 按奇偶比例选择
        selected_odds = self._bayes_select_numbers(odd_nums, odd_count)
        selected_evens = self._bayes_select_numbers(even_nums, even_count)

        # 合并并检查大小比例
        temp_selected = selected_odds + selected_evens

        # 调整以满足大小比例
        final_selected = self._adjust_for_size_ratio(
            temp_selected, small_count, large_count, valid_reds
        )

        return sorted(final_selected)

    def _generate_blue_balls_bayes(
        self, size_ratio: str, kill_numbers: List[int]
    ) -> List[int]:
        """使用贝叶斯方法生成蓝球"""
        # 解析比例
        small_count, large_count = map(int, size_ratio.split(":"))

        # 候选号码
        all_blues = list(range(1, 13))
        valid_blues = [num for num in all_blues if num not in kill_numbers]

        if len(valid_blues) < 2:
            valid_blues = all_blues  # 如果杀号过多，忽略杀号约束

        # 分类号码
        small_nums = [num for num in valid_blues if num <= 6]
        large_nums = [num for num in valid_blues if num >= 7]

        # 按比例选择
        selected = []
        if small_count > 0:
            selected.extend(self._bayes_select_numbers(small_nums, small_count))
        if large_count > 0:
            selected.extend(self._bayes_select_numbers(large_nums, large_count))

        # 如果数量不足，随机补充
        while len(selected) < 2:
            remaining = [num for num in valid_blues if num not in selected]
            if remaining:
                selected.append(np.random.choice(remaining))
            else:
                break

        return sorted(selected[:2])

    def _bayes_select_numbers(self, candidates: List[int], count: int) -> List[int]:
        """使用贝叶斯概率选择号码"""
        if not candidates or count <= 0:
            return []

        if count >= len(candidates):
            return candidates[:]

        # 计算每个号码的贝叶斯概率
        probabilities = {}
        for num in candidates:
            # 基于历史频率的先验概率
            freq = (
                self.red_frequency.get(num, 0)
                if num <= 35
                else self.blue_frequency.get(num, 0)
            )
            total_periods = len(self.historical_data) if self.historical_data else 1

            # 贝叶斯更新
            prior = (freq + 1) / (
                total_periods + 35 if num <= 35 else total_periods + 12
            )

            # 添加随机性避免过度集中
            random_factor = np.random.uniform(0.8, 1.2)
            probabilities[num] = prior * random_factor

        # 归一化概率
        total_prob = sum(probabilities.values())
        if total_prob > 0:
            normalized_probs = [probabilities[num] / total_prob for num in candidates]
        else:
            normalized_probs = [1.0 / len(candidates)] * len(candidates)

        # 基于概率选择
        selected = np.random.choice(
            candidates, size=count, replace=False, p=normalized_probs
        )
        return selected.tolist()

    def _adjust_for_size_ratio(
        self,
        numbers: List[int],
        small_count: int,
        large_count: int,
        valid_nums: List[int],
    ) -> List[int]:
        """调整号码以满足大小比例"""
        small_nums = [num for num in numbers if num <= 17]
        large_nums = [num for num in numbers if num >= 18]

        # 如果比例已经正确，直接返回
        if len(small_nums) == small_count and len(large_nums) == large_count:
            return numbers

        # 调整比例
        result = []

        # 添加小号
        if small_count > 0:
            available_small = [num for num in valid_nums if num <= 17]
            if len(small_nums) >= small_count:
                result.extend(small_nums[:small_count])
            else:
                result.extend(small_nums)
                need_more = small_count - len(small_nums)
                remaining_small = [num for num in available_small if num not in result]
                if len(remaining_small) >= need_more:
                    result.extend(
                        np.random.choice(remaining_small, need_more, replace=False)
                    )

        # 添加大号
        if large_count > 0:
            available_large = [num for num in valid_nums if num >= 18]
            if len(large_nums) >= large_count:
                result.extend(large_nums[:large_count])
            else:
                result.extend(large_nums)
                need_more = large_count - len(large_nums)
                remaining_large = [num for num in available_large if num not in result]
                if len(remaining_large) >= need_more:
                    result.extend(
                        np.random.choice(remaining_large, need_more, replace=False)
                    )

        # 确保总数为5
        while len(result) < 5:
            remaining = [num for num in valid_nums if num not in result]
            if remaining:
                result.append(np.random.choice(remaining))
            else:
                break

        return result[:5]

    def _validate_combination(
        self, red_balls: List[int], blue_balls: List[int], target_ratios: Dict
    ) -> bool:
        """验证组合是否符合目标比例"""
        if len(red_balls) != 5 or len(blue_balls) != 2:
            return False

        # 检查红球奇偶比例
        red_odd_count = sum(1 for num in red_balls if num % 2 == 1)
        red_even_count = 5 - red_odd_count
        target_odd_even = target_ratios.get("red_odd_even", ("3:2", 0.5))[0]
        expected_odd, expected_even = map(int, target_odd_even.split(":"))

        if red_odd_count != expected_odd or red_even_count != expected_even:
            return False

        # 检查红球大小比例 - 使用统一标准 (1-18小号, 19-35大号)
        red_small_count = sum(1 for num in red_balls if num <= 18)
        red_large_count = 5 - red_small_count
        target_size = target_ratios.get("red_size", ("2:3", 0.5))[0]
        expected_small, expected_large = map(int, target_size.split(":"))

        if red_small_count != expected_small or red_large_count != expected_large:
            return False

        # 检查蓝球大小比例
        blue_small_count = sum(1 for num in blue_balls if num <= 6)
        blue_large_count = 2 - blue_small_count
        target_blue_size = target_ratios.get("blue_size", ("1:1", 0.5))[0]
        expected_blue_small, expected_blue_large = map(int, target_blue_size.split(":"))

        if (
            blue_small_count != expected_blue_small
            or blue_large_count != expected_blue_large
        ):
            return False

        return True

    def _generate_fallback_combinations(
        self, target_ratios: Dict, kill_numbers: Dict, count: int
    ) -> List[Dict]:
        """生成回退组合"""
        fallback_combinations = []
        for i in range(count):
            fallback_combinations.append(
                {
                    "rank": i + 1,
                    "original_index": i,
                    "red_balls": [1, 2, 3, 4, 5],
                    "blue_balls": [1, 2],
                    "total_score": 0.1,
                    "confidence": 10,
                    "scores": {},
                    "recommendation": "❌不推荐",
                }
            )
        return fallback_combinations
