#!/usr/bin/env python3
"""
使用自动优化配置进行实际训练测试
对比优化前后的训练效果
"""

import sys
import os
import time
import json
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入配置
from src.config.auto_optimized_config import AutoOptimizedConfig
from src.config.advanced_tuning_config import AdvancedTuningConfig

# 导入训练组件
try:
    from src.systems.number_selection_system import NumberSelectionSystem, SelectionConfig
    SELECTION_SYSTEM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 导入警告: {e}")
    SELECTION_SYSTEM_AVAILABLE = False

try:
    from src.systems.deep_learning_selector import DeepLearningSelector
    from src.systems.selection_data_processor import DataProcessor
    DEEP_LEARNING_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 深度学习组件导入警告: {e}")
    DEEP_LEARNING_AVAILABLE = False

try:
    from src.utils.data_utils import load_data
    DATA_UTILS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 数据工具导入警告: {e}")
    DATA_UTILS_AVAILABLE = False


def convert_to_selection_config(advanced_config: AdvancedTuningConfig) -> SelectionConfig:
    """将AdvancedTuningConfig转换为SelectionConfig"""
    return SelectionConfig(
        sequence_length=advanced_config.sequence_length,
        train_ratio=0.85,
        validation_ratio=0.1,
        dl_epochs=advanced_config.epochs,
        dl_batch_size=advanced_config.batch_size,
        dl_learning_rate=advanced_config.initial_learning_rate,
        use_lr_scheduler=True,
        lr_scheduler_factor=0.5,
        lr_scheduler_patience=8,
        lr_scheduler_min_lr=1e-6,
        early_stopping_patience=advanced_config.early_stopping_patience,
        ml_n_estimators=100,
        ml_max_depth=10,
        ml_random_state=42,
        backtest_periods=50,
        cross_validation_folds=5,
        save_models=True,
        generate_report=True,
        verbose=True
    )


class OptimizedTrainingRunner:
    """优化配置训练运行器"""
    
    def __init__(self):
        # 结果目录
        self.results_dir = Path("results/training_comparison")
        self.results_dir.mkdir(parents=True, exist_ok=True)

        self.logger = logging.getLogger(__name__)
        self.setup_logging()
        
        # 训练配置
        self.auto_config = None
        self.base_config = None
        
        # 数据
        self.data = None
        self.train_data = None
        self.test_data = None
        
        # 训练结果
        self.training_results = {}
    
    def setup_logging(self):
        """设置日志"""
        log_file = self.results_dir / f"training_log_{int(time.time())}.log"
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        self.logger.info(f"训练日志文件: {log_file}")
    
    def load_configurations(self) -> bool:
        """加载配置"""
        print("📋 加载训练配置")
        print("=" * 50)
        
        try:
            # 加载自动优化配置
            print("🔧 加载自动优化配置...")
            self.auto_config = AutoOptimizedConfig()
            print("✅ 自动优化配置加载成功")
            
            # 加载基础配置
            print("🔧 加载基础配置...")
            self.base_config = AdvancedTuningConfig()
            print("✅ 基础配置加载成功")
            
            # 显示配置对比
            self.display_config_comparison()
            
            return True
            
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            self.logger.error(f"配置加载失败: {e}")
            return False
    
    def display_config_comparison(self):
        """显示配置对比"""
        print("\n📊 配置对比:")
        print("-" * 60)
        
        comparisons = [
            ("学习率", self.auto_config.initial_learning_rate, self.base_config.initial_learning_rate),
            ("序列长度", self.auto_config.sequence_length, self.base_config.sequence_length),
            ("训练轮数", self.auto_config.epochs, self.base_config.epochs),
            ("批次大小", self.auto_config.batch_size, self.base_config.batch_size),
            ("早停耐心", self.auto_config.early_stopping_patience, self.base_config.early_stopping_patience),
        ]
        
        print(f"{'参数':<12} {'优化配置':<12} {'基础配置':<12} {'变化':<12}")
        print("-" * 60)
        
        for param, auto_val, base_val in comparisons:
            if isinstance(auto_val, float):
                change = f"{((auto_val/base_val-1)*100):+.1f}%" if base_val != 0 else "N/A"
                print(f"{param:<12} {auto_val:<12.4f} {base_val:<12.4f} {change:<12}")
            else:
                change = f"{((auto_val/base_val-1)*100):+.1f}%" if base_val != 0 else "N/A"
                print(f"{param:<12} {auto_val:<12} {base_val:<12} {change:<12}")
    
    def load_training_data(self) -> bool:
        """加载训练数据"""
        print("\n📊 加载训练数据")
        print("=" * 50)

        try:
            # 尝试加载数据
            data_file = "data/raw/dlt_data.csv"
            if Path(data_file).exists():
                print(f"📁 从文件加载数据: {data_file}")
                if DATA_UTILS_AVAILABLE:
                    self.data = load_data(data_file)
                else:
                    # 备用CSV加载方法
                    self.data = pd.read_csv(data_file, encoding='utf-8')
            else:
                print("📁 数据文件不存在，生成模拟数据...")
                self.data = self.generate_sample_data()

            print(f"✅ 数据加载成功，共 {len(self.data)} 条记录")

            # 数据分割
            self.split_training_data()

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            self.logger.error(f"数据加载失败: {e}")
            return False
    
    def generate_sample_data(self) -> pd.DataFrame:
        """生成模拟数据"""
        print("🔄 生成模拟训练数据...")
        
        np.random.seed(42)  # 确保可重复性
        
        data = []
        for i in range(500):  # 生成500期数据
            # 生成红球（5个，1-35）
            red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
            
            # 生成蓝球（2个，1-12）
            blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
            
            period = f"2024{i+1:03d}"
            
            data.append({
                "期号": period,
                "红球1": red_balls[0],
                "红球2": red_balls[1],
                "红球3": red_balls[2],
                "红球4": red_balls[3],
                "红球5": red_balls[4],
                "蓝球1": blue_balls[0],
                "蓝球2": blue_balls[1],
            })
        
        return pd.DataFrame(data)
    
    def split_training_data(self):
        """分割训练数据"""
        print("🔄 分割训练测试数据...")
        
        # 使用优化配置的分割比例
        train_ratio = self.auto_config.train_ratio
        test_ratio = self.auto_config.test_ratio
        
        total_size = len(self.data)
        train_size = int(total_size * train_ratio)
        test_size = int(total_size * test_ratio)
        
        # 按时间顺序分割（最新的数据作为测试集）
        self.train_data = self.data.iloc[:train_size].copy()
        self.test_data = self.data.iloc[-test_size:].copy()
        
        print(f"  训练集: {len(self.train_data)} 条记录")
        print(f"  测试集: {len(self.test_data)} 条记录")
        
        self.logger.info(f"数据分割完成 - 训练:{len(self.train_data)}, 测试:{len(self.test_data)}")
    
    def convert_auto_config_to_system_config(self, auto_config: AutoOptimizedConfig) -> AdvancedTuningConfig:
        """将AutoOptimizedConfig转换为系统兼容的AdvancedTuningConfig"""
        system_config = AdvancedTuningConfig(
            initial_learning_rate=auto_config.initial_learning_rate,
            sequence_length=auto_config.sequence_length,
            epochs=auto_config.epochs,
            batch_size=auto_config.batch_size,
            early_stopping_patience=auto_config.early_stopping_patience,
            weight_decay=auto_config.weight_decay,
            dropout_rate=auto_config.dropout_rate,
            train_ratio=auto_config.train_ratio,
            validation_ratio=auto_config.validation_ratio,
            test_ratio=auto_config.test_ratio,
        )
        
        # 更新嵌套配置
        if auto_config.lstm_config:
            system_config.lstm_config.update(auto_config.lstm_config)
        if auto_config.transformer_config:
            system_config.transformer_config.update(auto_config.transformer_config)
        if auto_config.multi_task_config:
            system_config.multi_task_config.update(auto_config.multi_task_config)
        
        return system_config
    
    def run_training_with_config(self, config: AdvancedTuningConfig, config_name: str) -> Dict[str, Any]:
        """使用指定配置运行训练"""
        print(f"\n🚀 开始训练: {config_name}")
        print("=" * 60)

        start_time = time.time()

        try:
            # 转换配置格式
            selection_config = convert_to_selection_config(config)

            # 创建号码选择系统
            print("🏗️ 创建号码选择系统...")
            selection_system = NumberSelectionSystem(config=selection_config)

            # 数据已准备好，无需设置data属性
            print("🔄 训练数据已准备完成...")

            print(f"✅ {config_name} 系统初始化完成")

            # 评估模型性能（通过预测测试）
            print("📊 评估模型性能...")
            evaluation_results = self.evaluate_model_performance(selection_system, config_name)

            training_time = time.time() - start_time

            results = {
                "success": True,
                "config_name": config_name,
                "training_time": training_time,
                "evaluation": evaluation_results,
                "config_params": {
                    "learning_rate": config.initial_learning_rate,
                    "sequence_length": config.sequence_length,
                    "epochs": config.epochs,
                    "batch_size": config.batch_size,
                    "early_stopping_patience": config.early_stopping_patience,
                }
            }

            print(f"⏱️ 处理耗时: {training_time:.2f} 秒")

            return results

        except Exception as e:
            print(f"❌ {config_name} 处理出错: {e}")
            self.logger.error(f"{config_name} 处理异常: {e}")

            return {
                "success": False,
                "config_name": config_name,
                "error": str(e),
                "training_time": time.time() - start_time,
            }
    
    def evaluate_model_performance(self, selection_system: NumberSelectionSystem, config_name: str) -> Dict[str, Any]:
        """评估模型性能"""
        print(f"🔍 评估 {config_name} 性能...")

        evaluation_results = {
            "predictions_made": 0,
            "accuracy_metrics": {},
            "prediction_quality": {},
            "detailed_results": [],
        }

        try:
            # 在测试集上进行预测
            correct_predictions = 0
            total_predictions = 0
            prediction_scores = []

            # 取测试集的前10期进行评估
            test_sample = self.test_data.head(10)

            for idx, row in test_sample.iterrows():
                try:
                    # 使用历史数据进行预测
                    historical_data = self.train_data.iloc[:idx] if idx < len(self.train_data) else self.train_data

                    # 进行选号预测
                    from src.systems.number_selection_system import SelectionMethod
                    selection_result = selection_system.select_numbers(
                        method=SelectionMethod.ENSEMBLE,
                        data=historical_data
                    )

                    if selection_result and hasattr(selection_result, 'red_balls') and hasattr(selection_result, 'blue_balls'):
                        # 计算预测准确性
                        actual_red = [row[f'红球{i}'] for i in range(1, 6)]
                        actual_blue = [row[f'蓝球{i}'] for i in range(1, 3)]

                        predicted_red = selection_result.red_balls
                        predicted_blue = selection_result.blue_balls

                        # 计算红球命中数
                        red_hits = len(set(actual_red) & set(predicted_red))
                        blue_hits = len(set(actual_blue) & set(predicted_blue))

                        # 计算得分
                        score = (red_hits / 5) * 0.7 + (blue_hits / 2) * 0.3
                        prediction_scores.append(score)

                        evaluation_results["detailed_results"].append({
                            "period": row["期号"],
                            "red_hits": red_hits,
                            "blue_hits": blue_hits,
                            "score": score,
                            "predicted_red": predicted_red,
                            "predicted_blue": predicted_blue,
                            "actual_red": actual_red,
                            "actual_blue": actual_blue,
                        })

                        total_predictions += 1
                        if score > 0.3:  # 定义一个阈值作为"正确"预测
                            correct_predictions += 1

                except Exception as e:
                    self.logger.warning(f"预测第{idx}期时出错: {e}")
                    continue

            # 计算总体指标
            if total_predictions > 0:
                accuracy = correct_predictions / total_predictions
                avg_score = np.mean(prediction_scores) if prediction_scores else 0

                evaluation_results.update({
                    "predictions_made": total_predictions,
                    "accuracy_metrics": {
                        "accuracy": accuracy,
                        "average_score": avg_score,
                        "max_score": max(prediction_scores) if prediction_scores else 0,
                        "min_score": min(prediction_scores) if prediction_scores else 0,
                    },
                    "prediction_quality": {
                        "total_correct": correct_predictions,
                        "total_predictions": total_predictions,
                        "success_rate": accuracy,
                    }
                })

                print(f"  预测准确率: {accuracy:.2%}")
                print(f"  平均得分: {avg_score:.4f}")
                print(f"  成功预测: {correct_predictions}/{total_predictions}")

        except Exception as e:
            print(f"⚠️ 评估过程出错: {e}")
            self.logger.error(f"评估异常: {e}")
            evaluation_results["error"] = str(e)

        return evaluation_results

    def run_comparison_training(self) -> Dict[str, Any]:
        """运行对比训练"""
        print("\n🎯 开始对比训练测试")
        print("=" * 70)

        comparison_results = {
            "start_time": datetime.now().isoformat(),
            "configurations": {},
            "comparison_summary": {},
        }

        # 1. 使用基础配置训练
        print("\n1️⃣ 基础配置训练")
        base_results = self.run_training_with_config(self.base_config, "基础配置")
        comparison_results["configurations"]["base_config"] = base_results

        # 2. 使用优化配置训练
        print("\n2️⃣ 优化配置训练")
        auto_system_config = self.convert_auto_config_to_system_config(self.auto_config)
        auto_results = self.run_training_with_config(auto_system_config, "优化配置")
        comparison_results["configurations"]["auto_config"] = auto_results

        # 3. 生成对比分析
        comparison_results["comparison_summary"] = self.generate_comparison_summary(
            base_results, auto_results
        )

        # 4. 保存结果
        self.save_training_results(comparison_results)

        # 5. 显示总结
        self.display_training_summary(comparison_results)

        return comparison_results

    def generate_comparison_summary(self, base_results: Dict[str, Any], auto_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成对比分析总结"""
        summary = {
            "training_time_comparison": {},
            "performance_comparison": {},
            "improvement_analysis": {},
        }

        try:
            # 训练时间对比
            if base_results.get("success") and auto_results.get("success"):
                base_time = base_results.get("training_time", 0)
                auto_time = auto_results.get("training_time", 0)

                time_change = ((auto_time / base_time) - 1) * 100 if base_time > 0 else 0

                summary["training_time_comparison"] = {
                    "base_time": base_time,
                    "auto_time": auto_time,
                    "time_change_percent": time_change,
                }

                # 性能对比
                base_eval = base_results.get("evaluation", {})
                auto_eval = auto_results.get("evaluation", {})

                base_accuracy = base_eval.get("accuracy_metrics", {}).get("accuracy", 0)
                auto_accuracy = auto_eval.get("accuracy_metrics", {}).get("accuracy", 0)

                base_score = base_eval.get("accuracy_metrics", {}).get("average_score", 0)
                auto_score = auto_eval.get("accuracy_metrics", {}).get("average_score", 0)

                accuracy_improvement = ((auto_accuracy / base_accuracy) - 1) * 100 if base_accuracy > 0 else 0
                score_improvement = ((auto_score / base_score) - 1) * 100 if base_score > 0 else 0

                summary["performance_comparison"] = {
                    "base_accuracy": base_accuracy,
                    "auto_accuracy": auto_accuracy,
                    "accuracy_improvement_percent": accuracy_improvement,
                    "base_score": base_score,
                    "auto_score": auto_score,
                    "score_improvement_percent": score_improvement,
                }

                # 改进分析
                summary["improvement_analysis"] = {
                    "overall_improvement": accuracy_improvement > 0 or score_improvement > 0,
                    "accuracy_better": auto_accuracy > base_accuracy,
                    "score_better": auto_score > base_score,
                    "time_efficiency": time_change < 50,  # 训练时间增加不超过50%认为是可接受的
                    "recommendation": self.generate_recommendation(accuracy_improvement, score_improvement, time_change),
                }

        except Exception as e:
            self.logger.error(f"生成对比分析时出错: {e}")
            summary["error"] = str(e)

        return summary

    def generate_recommendation(self, accuracy_improvement: float, score_improvement: float, time_change: float) -> str:
        """生成使用建议"""
        if accuracy_improvement > 5 and score_improvement > 5:
            if time_change < 100:
                return "强烈推荐使用优化配置：性能显著提升且训练时间可接受"
            else:
                return "推荐使用优化配置：性能显著提升，但需要更多训练时间"
        elif accuracy_improvement > 0 and score_improvement > 0:
            return "建议使用优化配置：性能有所提升"
        elif accuracy_improvement < -5 or score_improvement < -5:
            return "不推荐使用优化配置：性能下降明显"
        else:
            return "优化配置效果不明显，可根据具体需求选择"

    def save_training_results(self, results: Dict[str, Any]):
        """保存训练结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = self.results_dir / f"training_comparison_{timestamp}.json"

        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

            print(f"💾 训练结果已保存: {results_file}")
            self.logger.info(f"训练结果保存至: {results_file}")

        except Exception as e:
            print(f"⚠️ 保存结果失败: {e}")
            self.logger.error(f"保存结果失败: {e}")

    def display_training_summary(self, results: Dict[str, Any]):
        """显示训练总结"""
        print("\n" + "=" * 70)
        print("🎯 训练对比总结")
        print("=" * 70)

        summary = results.get("comparison_summary", {})

        # 训练时间对比
        time_comp = summary.get("training_time_comparison", {})
        if time_comp:
            print(f"\n⏱️ 训练时间对比:")
            print(f"  基础配置: {time_comp.get('base_time', 0):.2f} 秒")
            print(f"  优化配置: {time_comp.get('auto_time', 0):.2f} 秒")
            print(f"  时间变化: {time_comp.get('time_change_percent', 0):+.1f}%")

        # 性能对比
        perf_comp = summary.get("performance_comparison", {})
        if perf_comp:
            print(f"\n📊 性能对比:")
            print(f"  基础配置准确率: {perf_comp.get('base_accuracy', 0):.2%}")
            print(f"  优化配置准确率: {perf_comp.get('auto_accuracy', 0):.2%}")
            print(f"  准确率改进: {perf_comp.get('accuracy_improvement_percent', 0):+.1f}%")
            print(f"  基础配置得分: {perf_comp.get('base_score', 0):.4f}")
            print(f"  优化配置得分: {perf_comp.get('auto_score', 0):.4f}")
            print(f"  得分改进: {perf_comp.get('score_improvement_percent', 0):+.1f}%")

        # 改进分析
        improvement = summary.get("improvement_analysis", {})
        if improvement:
            print(f"\n🔍 改进分析:")
            print(f"  整体改进: {'✅ 是' if improvement.get('overall_improvement') else '❌ 否'}")
            print(f"  准确率提升: {'✅ 是' if improvement.get('accuracy_better') else '❌ 否'}")
            print(f"  得分提升: {'✅ 是' if improvement.get('score_better') else '❌ 否'}")
            print(f"  时间效率: {'✅ 可接受' if improvement.get('time_efficiency') else '⚠️ 较慢'}")
            print(f"\n💡 使用建议: {improvement.get('recommendation', '无建议')}")

        # 配置状态
        base_success = results.get("configurations", {}).get("base_config", {}).get("success", False)
        auto_success = results.get("configurations", {}).get("auto_config", {}).get("success", False)

        print(f"\n✅ 训练状态:")
        print(f"  基础配置: {'成功' if base_success else '失败'}")
        print(f"  优化配置: {'成功' if auto_success else '失败'}")

        if base_success and auto_success:
            print(f"\n🎉 对比训练完成！优化配置已准备就绪。")
        else:
            print(f"\n⚠️ 部分训练失败，请检查日志了解详情。")


def main():
    """主函数"""
    print("🚀 优化配置实际训练测试")
    print("=" * 70)

    # 创建训练运行器
    runner = OptimizedTrainingRunner()

    try:
        # 1. 加载配置
        if not runner.load_configurations():
            print("❌ 配置加载失败，退出")
            return False

        # 2. 加载训练数据
        if not runner.load_training_data():
            print("❌ 数据加载失败，退出")
            return False

        # 3. 运行对比训练
        results = runner.run_comparison_training()

        # 4. 检查结果
        if results:
            print("\n🎯 训练测试完成！")
            return True
        else:
            print("\n❌ 训练测试失败！")
            return False

    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        runner.logger.error(f"程序异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
