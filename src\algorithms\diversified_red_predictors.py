"""
多样化红球预测算法
专门用于增加红球预测的多样性，避免所有算法都预测相同结果
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from collections import Counter, deque
import random
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers,
    calculate_odd_even_ratio,
    calculate_size_ratio_red,
    ratio_to_state,
    get_all_red_odd_even_states,
    get_all_red_size_states,
)


class AntiTrendRedPredictor:
    """反向趋势红球预测器 - 预测与近期趋势相反的结果"""

    def __init__(self, lookback_periods: int = 5):
        self.lookback_periods = lookback_periods
        self.name = "anti_trend_red"

    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """
        预测红球比例（反向趋势）

        Args:
            data: 历史数据
            ratio_type: 'odd_even' 或 'size'

        Returns:
            Dict: 预测结果
        """
        if len(data) < self.lookback_periods:
            return self._get_default_prediction(ratio_type)

        # 分析近期趋势
        recent_states = []
        for i in range(min(self.lookback_periods, len(data))):
            row = data.iloc[-(i + 1)]
            red_balls, _ = parse_numbers(row)

            if ratio_type == "odd_even":
                red_odd, red_even = calculate_odd_even_ratio(red_balls)
                state = ratio_to_state((red_odd, red_even))
            else:  # size
                red_big, red_small = calculate_size_ratio_red(red_balls)
                state = ratio_to_state((red_big, red_small))

            recent_states.append(state)

        # 统计近期最常见的状态
        state_counts = Counter(recent_states)
        most_common_states = [state for state, _ in state_counts.most_common(2)]

        # 预测相反的状态
        if ratio_type == "odd_even":
            all_states = get_all_red_odd_even_states()
        else:  # size
            all_states = get_all_red_size_states()

        anti_states = [state for state in all_states if state not in most_common_states]

        if not anti_states:
            anti_states = all_states

        # 随机选择一个反向状态
        predicted_state = random.choice(anti_states)
        confidence = 0.6 + random.random() * 0.2  # 0.6-0.8之间的随机置信度

        return {
            "prediction": predicted_state,
            "confidence": confidence,
            "method": "anti_trend",
        }

    def _get_default_prediction(self, ratio_type: str) -> Dict[str, Any]:
        """默认预测"""
        if ratio_type == "odd_even":
            return {"prediction": "2:3", "confidence": 0.5, "method": "default"}
        else:
            return {"prediction": "2:3", "confidence": 0.5, "method": "default"}


class CyclicRedPredictor:
    """周期性红球预测器 - 基于历史周期模式"""

    def __init__(self, cycle_length: int = 7):
        self.cycle_length = cycle_length
        self.name = "cyclic_red"

    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """
        基于周期性模式预测红球比例

        Args:
            data: 历史数据
            ratio_type: 'odd_even' 或 'size'

        Returns:
            Dict: 预测结果
        """
        if len(data) < self.cycle_length * 2:
            return self._get_default_prediction(ratio_type)

        # 提取历史状态序列
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)

            if ratio_type == "odd_even":
                red_odd, red_even = calculate_odd_even_ratio(red_balls)
                state = ratio_to_state((red_odd, red_even))
            else:  # size
                red_big, red_small = calculate_size_ratio_red(red_balls)
                state = ratio_to_state((red_big, red_small))

            states.append(state)

        # 寻找周期性模式
        current_position = len(states) % self.cycle_length
        cycle_predictions = []

        # 查看历史上相同位置的状态
        for i in range(current_position, len(states), self.cycle_length):
            if i < len(states):
                cycle_predictions.append(states[i])

        if not cycle_predictions:
            return self._get_default_prediction(ratio_type)

        # 选择最常见的周期性状态
        state_counts = Counter(cycle_predictions)
        predicted_state = state_counts.most_common(1)[0][0]
        confidence = state_counts[predicted_state] / len(cycle_predictions)

        return {
            "prediction": predicted_state,
            "confidence": confidence,
            "method": "cyclic",
        }

    def _get_default_prediction(self, ratio_type: str) -> Dict[str, Any]:
        """默认预测"""
        if ratio_type == "odd_even":
            return {"prediction": "3:2", "confidence": 0.4, "method": "default"}
        else:
            return {"prediction": "3:2", "confidence": 0.4, "method": "default"}


class RandomizedRedPredictor:
    """随机化红球预测器 - 在合理范围内引入随机性"""

    def __init__(self, randomness_factor: float = 0.3):
        self.randomness_factor = randomness_factor
        self.name = "randomized_red"

    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """
        基于历史概率分布进行随机化预测

        Args:
            data: 历史数据
            ratio_type: 'odd_even' 或 'size'

        Returns:
            Dict: 预测结果
        """
        if len(data) < 10:
            return self._get_random_prediction(ratio_type)

        # 统计历史状态分布
        state_counts = Counter()
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)

            if ratio_type == "odd_even":
                red_odd, red_even = calculate_odd_even_ratio(red_balls)
                state = ratio_to_state((red_odd, red_even))
            else:  # size
                red_big, red_small = calculate_size_ratio_red(red_balls)
                state = ratio_to_state((red_big, red_small))

            state_counts[state] += 1

        # 计算概率分布
        total_count = sum(state_counts.values())
        state_probs = {
            state: count / total_count for state, count in state_counts.items()
        }

        # 添加随机性 - 平滑概率分布
        if ratio_type == "odd_even":
            all_states = get_all_red_odd_even_states()
        else:  # size
            all_states = get_all_red_size_states()

        uniform_prob = 1.0 / len(all_states)

        adjusted_probs = {}
        for state in all_states:
            historical_prob = state_probs.get(state, 0)
            # 混合历史概率和均匀分布
            adjusted_prob = (
                1 - self.randomness_factor
            ) * historical_prob + self.randomness_factor * uniform_prob
            adjusted_probs[state] = adjusted_prob

        # 基于调整后的概率进行采样
        states = list(adjusted_probs.keys())
        probs = list(adjusted_probs.values())
        predicted_state = np.random.choice(states, p=probs)
        confidence = adjusted_probs[predicted_state]

        return {
            "prediction": predicted_state,
            "confidence": confidence,
            "method": "randomized",
        }

    def _get_random_prediction(self, ratio_type: str) -> Dict[str, Any]:
        """完全随机预测"""
        if ratio_type == "odd_even":
            all_states = get_all_red_odd_even_states()
        else:  # size
            all_states = get_all_red_size_states()

        predicted_state = random.choice(all_states)
        return {
            "prediction": predicted_state,
            "confidence": 0.5,
            "method": "pure_random",
        }


class TrendFollowingRedPredictor:
    """趋势跟踪红球预测器 - 跟随近期趋势"""

    def __init__(self, trend_periods: int = 3):
        self.trend_periods = trend_periods
        self.name = "trend_following_red"

    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """
        基于近期趋势预测红球比例

        Args:
            data: 历史数据
            ratio_type: 'odd_even' 或 'size'

        Returns:
            Dict: 预测结果
        """
        if len(data) < self.trend_periods:
            return self._get_default_prediction(ratio_type)

        # 分析近期趋势
        recent_states = []
        for i in range(min(self.trend_periods, len(data))):
            row = data.iloc[-(i + 1)]
            red_balls, _ = parse_numbers(row)

            if ratio_type == "odd_even":
                red_odd, red_even = calculate_odd_even_ratio(red_balls)
                state = ratio_to_state((red_odd, red_even))
            else:  # size
                red_big, red_small = calculate_size_ratio_red(red_balls)
                state = ratio_to_state((red_big, red_small))

            recent_states.append(state)

        # 找出最近的趋势状态
        if len(set(recent_states)) == 1:
            # 如果最近几期都是同一状态，继续预测这个状态
            predicted_state = recent_states[0]
            confidence = 0.7
        else:
            # 选择最近期最常见的状态
            state_counts = Counter(recent_states)
            predicted_state = state_counts.most_common(1)[0][0]
            confidence = state_counts[predicted_state] / len(recent_states)

        return {
            "prediction": predicted_state,
            "confidence": confidence,
            "method": "trend_following",
        }

    def _get_default_prediction(self, ratio_type: str) -> Dict[str, Any]:
        """默认预测"""
        if ratio_type == "odd_even":
            return {"prediction": "3:2", "confidence": 0.5, "method": "default"}
        else:
            return {"prediction": "3:2", "confidence": 0.5, "method": "default"}
