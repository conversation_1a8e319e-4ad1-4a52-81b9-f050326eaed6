"""
优化的训练配置文件
基于深度学习最佳实践和彩票预测特点优化的参数配置
"""

from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional


@dataclass
class OptimizedTrainingConfig:
    """优化的训练配置类"""
    
    # ==================== 数据配置 ====================
    # 序列长度优化：增加到30以捕捉更长期的模式
    sequence_length: int = 30
    
    # 数据分割优化：增加训练数据比例
    train_ratio: float = 0.85
    validation_ratio: float = 0.1
    test_ratio: float = 0.05
    
    # 特征工程
    enable_feature_engineering: bool = True
    feature_selection_threshold: float = 0.01
    
    # ==================== 深度学习通用配置 ====================
    # 训练轮数：适度增加
    epochs: int = 150
    
    # 批次大小：减小以提高训练精度
    batch_size: int = 16
    
    # 学习率：提高初始学习率
    initial_learning_rate: float = 0.002
    
    # 权重衰减：添加L2正则化
    weight_decay: float = 0.01
    
    # 梯度裁剪：防止梯度爆炸
    gradient_clip_norm: float = 1.0
    
    # ==================== 学习率调度器配置 ====================
    use_lr_scheduler: bool = True
    lr_scheduler_type: str = "ReduceLROnPlateau"  # "ReduceLROnPlateau", "CosineAnnealingLR", "StepLR"
    lr_scheduler_factor: float = 0.5
    lr_scheduler_patience: int = 10
    lr_scheduler_min_lr: float = 1e-6
    
    # 余弦退火参数（如果使用CosineAnnealingLR）
    cosine_t_max: int = 50
    cosine_eta_min: float = 1e-6
    
    # ==================== 早停配置 ====================
    use_early_stopping: bool = True
    early_stopping_patience: int = 20
    early_stopping_min_delta: float = 1e-4
    early_stopping_monitor: str = "val_loss"
    
    # ==================== LSTM特定配置 ====================
    lstm_config: Dict = field(default_factory=lambda: {
        "hidden_size": 256,
        "num_layers": 3,
        "dropout": 0.4,
        "bidirectional": False,
        "batch_first": True,
    })
    
    # ==================== Transformer特定配置 ====================
    transformer_config: Dict = field(default_factory=lambda: {
        "d_model": 384,
        "num_heads": 12,
        "num_layers": 6,
        "dim_feedforward": 1536,  # 通常是d_model的4倍
        "dropout": 0.15,
        "activation": "gelu",
        "layer_norm_eps": 1e-5,
        "warmup_steps": 1000,
    })
    
    # ==================== 多任务网络配置 ====================
    multi_task_config: Dict = field(default_factory=lambda: {
        "shared_hidden_size": 384,
        "task_hidden_size": 128,
        "num_shared_layers": 4,
        "num_task_layers": 2,
        "dropout": 0.25,
        "task_weights": {
            "red_balls": 1.5,
            "blue_balls": 1.0,
            "red_patterns": 1.2,
            "blue_patterns": 1.0,
        },
    })
    
    # ==================== 优化器配置 ====================
    optimizer_type: str = "AdamW"  # "Adam", "AdamW", "RMSprop"
    
    # Adam/AdamW参数
    adam_beta1: float = 0.9
    adam_beta2: float = 0.999
    adam_eps: float = 1e-8
    
    # ==================== 损失函数配置 ====================
    loss_function: str = "BCEWithLogitsLoss"
    label_smoothing: float = 0.1
    
    # 类别权重（处理不平衡数据）
    use_class_weights: bool = True
    red_ball_weights: Optional[List[float]] = None
    blue_ball_weights: Optional[List[float]] = None
    
    # ==================== 数据增强配置 ====================
    use_data_augmentation: bool = True
    augmentation_methods: List[str] = field(default_factory=lambda: [
        "noise_injection",
        "sequence_permutation", 
        "temporal_masking"
    ])
    noise_std: float = 0.01
    mask_ratio: float = 0.1
    
    # ==================== 混合精度训练 ====================
    use_mixed_precision: bool = True
    
    # ==================== 模型集成配置 ====================
    ensemble_weights: Dict[str, float] = field(default_factory=lambda: {
        "lstm": 0.35,
        "transformer": 0.40,
        "multi_task": 0.25,
    })
    
    # 动态权重调整
    use_dynamic_weights: bool = True
    weight_update_frequency: int = 10
    
    # ==================== 验证和测试配置 ====================
    validation_frequency: int = 1  # 每几个epoch验证一次
    save_best_model: bool = True
    save_checkpoint_frequency: int = 10
    
    # ==================== 日志和监控配置 ====================
    log_frequency: int = 10  # 每几个epoch记录一次
    tensorboard_logging: bool = True
    wandb_logging: bool = False
    
    # ==================== 硬件配置 ====================
    use_gpu: bool = True
    gpu_memory_fraction: float = 0.8
    num_workers: int = 0  # DataLoader的工作进程数
    pin_memory: bool = False
    
    # ==================== 随机种子 ====================
    random_seed: int = 42
    deterministic: bool = True
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        try:
            # 检查基本参数
            assert 0 < self.train_ratio < 1, "训练比例必须在0-1之间"
            assert 0 < self.validation_ratio < 1, "验证比例必须在0-1之间"
            assert self.train_ratio + self.validation_ratio + self.test_ratio <= 1, "数据分割比例总和不能超过1"
            
            # 检查学习率
            assert self.initial_learning_rate > 0, "学习率必须大于0"
            assert self.lr_scheduler_min_lr >= 0, "最小学习率不能为负"
            
            # 检查网络参数
            assert self.lstm_config["hidden_size"] > 0, "LSTM隐藏层大小必须大于0"
            assert self.transformer_config["d_model"] > 0, "Transformer模型维度必须大于0"
            assert self.transformer_config["d_model"] % self.transformer_config["num_heads"] == 0, \
                "Transformer模型维度必须能被注意力头数整除"
            
            # 检查集成权重
            weight_sum = sum(self.ensemble_weights.values())
            assert abs(weight_sum - 1.0) < 1e-6, f"集成权重总和必须为1，当前为{weight_sum}"
            
            return True
            
        except AssertionError as e:
            print(f"配置验证失败: {e}")
            return False
    
    def get_model_config(self, model_type: str) -> Dict:
        """获取特定模型的配置"""
        if model_type == "lstm":
            return self.lstm_config
        elif model_type == "transformer":
            return self.transformer_config
        elif model_type == "multi_task":
            return self.multi_task_config
        else:
            raise ValueError(f"未知的模型类型: {model_type}")
    
    def get_optimizer_config(self) -> Dict:
        """获取优化器配置"""
        config = {
            "lr": self.initial_learning_rate,
            "weight_decay": self.weight_decay,
        }
        
        if self.optimizer_type in ["Adam", "AdamW"]:
            config.update({
                "betas": (self.adam_beta1, self.adam_beta2),
                "eps": self.adam_eps,
            })
        
        return config
    
    def get_scheduler_config(self) -> Dict:
        """获取学习率调度器配置"""
        if self.lr_scheduler_type == "ReduceLROnPlateau":
            return {
                "mode": "min",
                "factor": self.lr_scheduler_factor,
                "patience": self.lr_scheduler_patience,
                "min_lr": self.lr_scheduler_min_lr,
                "verbose": True,
            }
        elif self.lr_scheduler_type == "CosineAnnealingLR":
            return {
                "T_max": self.cosine_t_max,
                "eta_min": self.cosine_eta_min,
            }
        else:
            return {}


# 预定义的配置模板
class OptimizedConfigTemplates:
    """优化配置模板"""
    
    @staticmethod
    def get_high_performance_config() -> OptimizedTrainingConfig:
        """高性能配置（追求最佳准确率）"""
        config = OptimizedTrainingConfig()
        config.epochs = 200
        config.sequence_length = 35
        config.lstm_config["hidden_size"] = 512
        config.transformer_config["d_model"] = 512
        config.transformer_config["num_heads"] = 16
        config.early_stopping_patience = 30
        return config
    
    @staticmethod
    def get_fast_training_config() -> OptimizedTrainingConfig:
        """快速训练配置（平衡速度和准确率）"""
        config = OptimizedTrainingConfig()
        config.epochs = 100
        config.batch_size = 32
        config.sequence_length = 25
        config.lstm_config["hidden_size"] = 128
        config.transformer_config["d_model"] = 256
        config.transformer_config["num_heads"] = 8
        return config
    
    @staticmethod
    def get_lightweight_config() -> OptimizedTrainingConfig:
        """轻量级配置（资源受限环境）"""
        config = OptimizedTrainingConfig()
        config.epochs = 80
        config.batch_size = 64
        config.sequence_length = 20
        config.lstm_config["hidden_size"] = 64
        config.lstm_config["num_layers"] = 2
        config.transformer_config["d_model"] = 128
        config.transformer_config["num_heads"] = 4
        config.transformer_config["num_layers"] = 3
        config.use_mixed_precision = False
        return config


if __name__ == "__main__":
    # 测试配置
    config = OptimizedTrainingConfig()
    print("配置验证结果:", config.validate_config())
    
    # 测试模板
    high_perf = OptimizedConfigTemplates.get_high_performance_config()
    print("高性能配置验证:", high_perf.validate_config())
