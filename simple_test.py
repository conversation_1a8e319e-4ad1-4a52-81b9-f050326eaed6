#!/usr/bin/env python3
"""
简单测试修正后的系统
"""

import sys
from pathlib import Path
from collections import Counter

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.lottery_config import LotteryConfig
from src.utils.utils import load_data, parse_numbers, calculate_size_ratio_red, ratio_to_state


def test_improved_predictor():
    """测试改进预测器"""
    print("🎯 测试改进预测器...")
    
    try:
        from src.models.improved_predictor import ImprovedPredictor
        
        predictor = ImprovedPredictor()
        print("✅ 改进预测器加载成功")
        
        # 运行几次简单预测
        data = load_data()
        prediction_counts = Counter()
        
        for i in range(10):  # 只测试10次
            try:
                result = predictor.predict_with_insights(len(data) - 1 - i)
                
                # 提取红球大小比预测
                if 'red_size_prediction' in result:
                    red_size_pred = result['red_size_prediction']
                    if isinstance(red_size_pred, dict) and 'prediction' in red_size_pred:
                        state = red_size_pred['prediction']
                        prediction_counts[state] += 1
                        print(f"   预测 {i+1}: {state}")
                
            except Exception as e:
                print(f"   预测 {i+1} 失败：{e}")
                continue
        
        # 分析结果
        total_predictions = sum(prediction_counts.values())
        if total_predictions > 0:
            print(f"\n📊 预测分布（共{total_predictions}次有效预测）：")
            
            for state in ["3:2", "2:3", "4:1", "1:4", "5:0", "0:5"]:
                count = prediction_counts.get(state, 0)
                percentage = (count / total_predictions) * 100 if total_predictions > 0 else 0
                ideal_percentage = LotteryConfig.IDEAL_RED_SIZE_DISTRIBUTION.get(state, 0) * 100
                print(f"   {state}: {count}次 ({percentage:5.1f}%) [理想: {ideal_percentage:5.1f}%]")
            
            # 检查2:3比例
            ratio_2_3_count = prediction_counts.get("2:3", 0)
            ratio_2_3_percentage = (ratio_2_3_count / total_predictions) * 100
            ideal_2_3_percentage = LotteryConfig.IDEAL_RED_SIZE_DISTRIBUTION.get("2:3", 0) * 100
            
            print(f"\n🎯 2:3比例检查：")
            print(f"   预测频率：{ratio_2_3_percentage:.1f}%")
            print(f"   理想频率：{ideal_2_3_percentage:.1f}%")
            
            if ratio_2_3_percentage > ideal_2_3_percentage * 1.5:
                print("   ❌ 仍存在2:3过度预测问题")
                return False
            else:
                print("   ✅ 2:3比例预测正常")
                return True
        else:
            print("❌ 没有获得有效预测结果")
            return False
            
    except Exception as e:
        print(f"❌ 预测器测试失败：{e}")
        return False


def test_historical_distribution():
    """测试历史数据分布"""
    print("📊 分析历史数据分布...")
    
    try:
        data = load_data()
        state_counts = Counter()
        
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_big, red_small = calculate_size_ratio_red(red_balls)
            state = ratio_to_state((red_big, red_small))
            state_counts[state] += 1
        
        total = sum(state_counts.values())
        print(f"📈 历史数据分布（共{total}期）：")
        
        for state in ["3:2", "2:3", "4:1", "1:4", "5:0", "0:5"]:
            count = state_counts.get(state, 0)
            percentage = (count / total) * 100 if total > 0 else 0
            ideal_percentage = LotteryConfig.IDEAL_RED_SIZE_DISTRIBUTION.get(state, 0) * 100
            print(f"   {state}: {count:4d}期 ({percentage:5.1f}%) [理想: {ideal_percentage:5.1f}%]")
        
        return True
        
    except Exception as e:
        print(f"❌ 历史分布分析失败：{e}")
        return False


def main():
    """主测试函数"""
    print("🚀 简单测试修正后的系统...")
    print("=" * 50)
    
    # 1. 历史分布分析
    historical_ok = test_historical_distribution()
    
    print()
    
    # 2. 预测器测试
    prediction_ok = test_improved_predictor()
    
    # 3. 总结
    print("\n" + "=" * 50)
    print("📋 测试总结：")
    
    if historical_ok and prediction_ok:
        print("✅ 系统修正成功")
    else:
        print("❌ 系统仍存在问题")


if __name__ == "__main__":
    main()
