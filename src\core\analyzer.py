"""
统计分析模块
实现各种统计分析方法：奇偶比、大小比等特征分析
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers,
    calculate_odd_even_ratio,
    calculate_size_ratio_red,
    calculate_size_ratio_blue,
    ratio_to_state,
    get_all_red_states,
    get_all_blue_states,
)


class LotteryAnalyzer:
    """大乐透数据分析器"""

    def __init__(self, data: pd.DataFrame):
        """
        初始化分析器

        Args:
            data: 历史开奖数据DataFrame
        """
        self.data = data
        self.features = self._extract_features()

    def _extract_features(self) -> Dict[str, List[str]]:
        """
        提取所有期次的特征（增强版）

        Returns:
            Dict: 包含各种特征的字典
        """
        features = {
            "red_odd_even": [],  # 红球奇偶比
            "red_size": [],  # 红球大小比
            "blue_odd_even": [],  # 蓝球奇偶比
            "blue_size": [],  # 蓝球大小比
            "red_sum_range": [],  # 红球和值范围
            "red_span": [],  # 红球跨度范围
            "red_consecutive": [],  # 红球连号情况
            "blue_sum_range": [],  # 蓝球和值范围
            "red_ac_value": [],  # 红球AC值（算术复杂性）
            "red_zone_distribution": [],  # 红球区间分布
            "red_tail_pattern": [],  # 红球尾数模式
            "blue_gap": [],  # 蓝球间距
            "red_prime_count": [],  # 红球质数个数
            "red_fibonacci": [],  # 红球斐波那契数个数
            "red_repeat_pattern": [],  # 红球重复模式
            "period_cycle": [],  # 期号周期特征
        }

        for _, row in self.data.iterrows():
            red_balls, blue_balls = parse_numbers(row)

            # 原有特征
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            features["red_odd_even"].append(ratio_to_state((red_odd, red_even)))

            red_big, red_small = calculate_size_ratio_red(red_balls)
            features["red_size"].append(ratio_to_state((red_big, red_small)))

            blue_odd, blue_even = calculate_odd_even_ratio(blue_balls)
            features["blue_odd_even"].append(ratio_to_state((blue_odd, blue_even)))

            blue_big, blue_small = calculate_size_ratio_blue(blue_balls)
            features["blue_size"].append(ratio_to_state((blue_big, blue_small)))

            # 新增特征
            # 红球和值范围
            red_sum = sum(red_balls)
            if red_sum <= 70:
                red_sum_state = "low"
            elif red_sum <= 110:
                red_sum_state = "mid"
            else:
                red_sum_state = "high"
            features["red_sum_range"].append(red_sum_state)

            # 红球跨度
            red_span = max(red_balls) - min(red_balls)
            if red_span <= 15:
                red_span_state = "small"
            elif red_span <= 25:
                red_span_state = "medium"
            else:
                red_span_state = "large"
            features["red_span"].append(red_span_state)

            # 红球连号情况
            sorted_red = sorted(red_balls)
            consecutive_count = 0
            for i in range(len(sorted_red) - 1):
                if sorted_red[i + 1] - sorted_red[i] == 1:
                    consecutive_count += 1

            if consecutive_count == 0:
                consecutive_state = "none"
            elif consecutive_count <= 2:
                consecutive_state = "few"
            else:
                consecutive_state = "many"
            features["red_consecutive"].append(consecutive_state)

            # 蓝球和值范围
            blue_sum = sum(blue_balls)
            if blue_sum <= 8:
                blue_sum_state = "low"
            elif blue_sum <= 15:
                blue_sum_state = "mid"
            else:
                blue_sum_state = "high"
            features["blue_sum_range"].append(blue_sum_state)

            # 新增高级特征
            # AC值（算术复杂性）
            ac_value = self._calculate_ac_value(red_balls)
            if ac_value <= 8:
                ac_state = "low"
            elif ac_value <= 12:
                ac_state = "medium"
            else:
                ac_state = "high"
            features["red_ac_value"].append(ac_state)

            # 区间分布（将35个号码分为7个区间）
            zone_dist = self._calculate_zone_distribution(red_balls)
            features["red_zone_distribution"].append(zone_dist)

            # 尾数模式
            tail_pattern = self._calculate_tail_pattern(red_balls)
            features["red_tail_pattern"].append(tail_pattern)

            # 蓝球间距
            blue_gap = abs(blue_balls[1] - blue_balls[0]) if len(blue_balls) == 2 else 0
            if blue_gap <= 3:
                gap_state = "small"
            elif blue_gap <= 6:
                gap_state = "medium"
            else:
                gap_state = "large"
            features["blue_gap"].append(gap_state)

            # 质数个数
            prime_count = sum(1 for num in red_balls if self._is_prime(num))
            features["red_prime_count"].append(str(prime_count))

            # 斐波那契数个数
            fib_count = sum(1 for num in red_balls if self._is_fibonacci(num))
            features["red_fibonacci"].append(str(fib_count))

            # 重复模式（与前一期的重复号码数）
            if len(features["red_odd_even"]) > 1:  # 不是第一期
                prev_index = len(features["red_odd_even"]) - 2
                if prev_index >= 0:
                    prev_row = self.data.iloc[prev_index]
                    prev_red, _ = parse_numbers(prev_row)
                    repeat_count = len(set(red_balls) & set(prev_red))
                    features["red_repeat_pattern"].append(str(repeat_count))
                else:
                    features["red_repeat_pattern"].append("0")
            else:
                features["red_repeat_pattern"].append("0")

            # 期号周期特征
            period_num = int(str(row["期号"])[-1])  # 期号末位数字
            if period_num in [1, 2, 3]:
                cycle_state = "early"
            elif period_num in [4, 5, 6, 7]:
                cycle_state = "middle"
            else:
                cycle_state = "late"
            features["period_cycle"].append(cycle_state)

        return features

    def get_feature_sequence(self, feature_name: str) -> List[str]:
        """
        获取指定特征的序列

        Args:
            feature_name: 特征名称

        Returns:
            List[str]: 特征状态序列
        """
        return self.features.get(feature_name, [])

    def calculate_state_frequencies(self, feature_name: str) -> Dict[str, float]:
        """
        计算状态频率分布

        Args:
            feature_name: 特征名称

        Returns:
            Dict[str, float]: 状态频率字典
        """
        sequence = self.get_feature_sequence(feature_name)
        if not sequence:
            return {}

        # 获取所有可能的状态
        if feature_name == "red_odd_even":
            from src.utils.utils import get_all_red_odd_even_states
            all_states = get_all_red_odd_even_states()
        elif feature_name == "red_size":
            from src.utils.utils import get_all_red_size_states
            all_states = get_all_red_size_states()
        elif feature_name == "blue_odd_even":
            from src.utils.utils import get_all_blue_odd_even_states
            all_states = get_all_blue_odd_even_states()
        else:  # blue_size
            from src.utils.utils import get_all_blue_size_states
            all_states = get_all_blue_size_states()

        # 计算频率
        frequencies = {}
        total_count = len(sequence)

        for state in all_states:
            count = sequence.count(state)
            frequencies[state] = count / total_count if total_count > 0 else 0.0

        return frequencies

    def get_recent_states(self, feature_name: str, n: int = 10) -> List[str]:
        """
        获取最近n期的状态

        Args:
            feature_name: 特征名称
            n: 期数

        Returns:
            List[str]: 最近n期的状态列表
        """
        sequence = self.get_feature_sequence(feature_name)
        return sequence[:n] if len(sequence) >= n else sequence

    def analyze_state_trends(
        self, feature_name: str, window: int = 20
    ) -> Dict[str, float]:
        """
        分析状态趋势（最近window期的频率）

        Args:
            feature_name: 特征名称
            window: 分析窗口大小

        Returns:
            Dict[str, float]: 最近window期的状态频率
        """
        sequence = self.get_feature_sequence(feature_name)
        recent_sequence = sequence[:window] if len(sequence) >= window else sequence

        if not recent_sequence:
            return {}

        # 获取所有可能的状态
        if feature_name == "red_odd_even":
            from src.utils.utils import get_all_red_odd_even_states
            all_states = get_all_red_odd_even_states()
        elif feature_name == "red_size":
            from src.utils.utils import get_all_red_size_states
            all_states = get_all_red_size_states()
        elif feature_name == "blue_odd_even":
            from src.utils.utils import get_all_blue_odd_even_states
            all_states = get_all_blue_odd_even_states()
        else:  # blue_size
            from src.utils.utils import get_all_blue_size_states
            all_states = get_all_blue_size_states()

        # 计算最近期数的频率
        frequencies = {}
        total_count = len(recent_sequence)

        for state in all_states:
            count = recent_sequence.count(state)
            frequencies[state] = count / total_count if total_count > 0 else 0.0

        return frequencies

    def get_state_at_period(self, feature_name: str, period_index: int) -> str:
        """
        获取指定期次的状态

        Args:
            feature_name: 特征名称
            period_index: 期次索引（0为最新期）

        Returns:
            str: 状态字符串
        """
        sequence = self.get_feature_sequence(feature_name)
        if 0 <= period_index < len(sequence):
            return sequence[period_index]
        return ""

    def calculate_consecutive_patterns(self, feature_name: str) -> Dict[str, List[int]]:
        """
        计算连续出现模式

        Args:
            feature_name: 特征名称

        Returns:
            Dict[str, List[int]]: 每个状态的连续出现次数列表
        """
        sequence = self.get_feature_sequence(feature_name)
        if not sequence:
            return {}

        patterns = {}
        current_state = sequence[0]
        current_count = 1

        for i in range(1, len(sequence)):
            if sequence[i] == current_state:
                current_count += 1
            else:
                if current_state not in patterns:
                    patterns[current_state] = []
                patterns[current_state].append(current_count)
                current_state = sequence[i]
                current_count = 1

        # 添加最后一个状态的计数
        if current_state not in patterns:
            patterns[current_state] = []
        patterns[current_state].append(current_count)

        return patterns

    def _calculate_ac_value(self, numbers: List[int]) -> int:
        """
        计算AC值（算术复杂性）
        AC值 = 不同差值的个数 - (n-1)，其中n是号码个数
        """
        if len(numbers) < 2:
            return 0

        differences = set()
        sorted_nums = sorted(numbers)

        for i in range(len(sorted_nums)):
            for j in range(i + 1, len(sorted_nums)):
                differences.add(abs(sorted_nums[j] - sorted_nums[i]))

        return len(differences) - (len(numbers) - 1)

    def _calculate_zone_distribution(self, numbers: List[int]) -> str:
        """
        计算区间分布模式
        将1-35分为7个区间：1-5, 6-10, 11-15, 16-20, 21-25, 26-30, 31-35
        """
        zones = [0] * 7
        for num in numbers:
            zone_index = min((num - 1) // 5, 6)
            zones[zone_index] += 1

        # 找出最多号码的区间模式
        max_count = max(zones)
        if max_count >= 3:
            return "concentrated"
        elif max_count == 2:
            return "moderate"
        else:
            return "dispersed"

    def _calculate_tail_pattern(self, numbers: List[int]) -> str:
        """
        计算尾数模式
        """
        tails = [num % 10 for num in numbers]
        unique_tails = len(set(tails))

        if unique_tails == 5:
            return "all_different"
        elif unique_tails >= 4:
            return "mostly_different"
        elif unique_tails >= 3:
            return "some_repeat"
        else:
            return "many_repeat"

    def _is_prime(self, n: int) -> bool:
        """判断是否为质数"""
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False

        for i in range(3, int(n**0.5) + 1, 2):
            if n % i == 0:
                return False
        return True

    def _is_fibonacci(self, n: int) -> bool:
        """判断是否为斐波那契数"""
        fib_numbers = {1, 1, 2, 3, 5, 8, 13, 21, 34}  # 35以内的斐波那契数
        return n in fib_numbers

    def get_feature_correlation(self, feature1: str, feature2: str) -> float:
        """
        计算两个特征之间的相关性
        """
        seq1 = self.get_feature_sequence(feature1)
        seq2 = self.get_feature_sequence(feature2)

        if len(seq1) != len(seq2) or len(seq1) == 0:
            return 0.0

        # 简单的相关性计算（基于状态匹配度）
        matches = sum(1 for s1, s2 in zip(seq1, seq2) if s1 == s2)
        return matches / len(seq1)

    def get_feature_importance_scores(self) -> Dict[str, float]:
        """
        计算各特征的重要性分数
        """
        importance_scores = {}
        all_features = list(self.features.keys())

        for feature in all_features:
            sequence = self.get_feature_sequence(feature)
            if not sequence:
                importance_scores[feature] = 0.0
                continue

            # 基于状态变化频率计算重要性
            state_changes = sum(
                1 for i in range(1, len(sequence)) if sequence[i] != sequence[i - 1]
            )

            # 基于状态分布均匀性
            from collections import Counter

            state_counts = Counter(sequence)
            entropy = -sum(
                (count / len(sequence)) * np.log2(count / len(sequence))
                for count in state_counts.values()
                if count > 0
            )

            # 综合评分
            change_score = state_changes / len(sequence) if len(sequence) > 1 else 0
            entropy_score = entropy / 3.0  # 归一化

            importance_scores[feature] = (change_score + entropy_score) / 2

        return importance_scores
