#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法性能监控器

主要功能:
1. 实时性能监控
2. 性能指标计算
3. 异常检测
4. 性能报告生成
5. 自动预警系统
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import logging
from datetime import datetime, timedelta
import json
import threading
import time
from scipy import stats
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
import warnings

warnings.filterwarnings("ignore")


@dataclass
class PerformanceMetrics:
    """性能指标"""

    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0

    # 预测相关指标
    hit_rate: float = 0.0  # 命中率
    kill_success_rate: float = 0.0  # 杀号成功率
    prediction_confidence: float = 0.0  # 预测置信度

    # 性能指标
    response_time: float = 0.0  # 响应时间(ms)
    throughput: float = 0.0  # 吞吐量(predictions/sec)
    memory_usage: float = 0.0  # 内存使用(MB)
    cpu_usage: float = 0.0  # CPU使用率(%)

    # 稳定性指标
    error_rate: float = 0.0  # 错误率
    availability: float = 1.0  # 可用性
    consistency: float = 1.0  # 一致性

    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class AlertConfig:
    """预警配置"""

    metric_name: str
    threshold_type: str  # 'upper', 'lower', 'range'
    threshold_value: float
    threshold_range: Tuple[float, float] = None
    severity: str = "warning"  # 'info', 'warning', 'error', 'critical'
    enabled: bool = True
    consecutive_violations: int = 3  # 连续违规次数触发预警


@dataclass
class PerformanceAlert:
    """性能预警"""

    alert_id: str
    metric_name: str
    current_value: float
    threshold_value: float
    severity: str
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolution_time: Optional[datetime] = None


@dataclass
class MonitorConfig:
    """监控配置"""

    # 监控间隔
    monitoring_interval: float = 1.0  # 秒
    metrics_retention_days: int = 30

    # 性能基线
    baseline_window_size: int = 100
    baseline_update_interval: int = 50

    # 异常检测
    anomaly_detection_enabled: bool = True
    anomaly_threshold: float = 0.1  # 异常分数阈值

    # 报告生成
    report_generation_enabled: bool = True
    report_interval_hours: int = 24

    # 自动优化
    auto_optimization_enabled: bool = False
    optimization_trigger_threshold: float = 0.8  # 性能下降阈值


class PerformanceMonitor:
    """算法性能监控器"""

    def __init__(self, config: MonitorConfig = None):
        """
        初始化性能监控器

        Args:
            config: 监控配置
        """
        self.config = config or MonitorConfig()

        # 性能数据存储
        self.metrics_history = deque(maxlen=10000)
        self.algorithm_metrics = defaultdict(lambda: deque(maxlen=1000))

        # 基线性能
        self.performance_baseline = {}
        self.baseline_updated_count = 0

        # 预警系统
        self.alert_configs = {}
        self.active_alerts = {}
        self.alert_history = deque(maxlen=1000)

        # 异常检测
        self.anomaly_detector = None
        self.anomaly_history = deque(maxlen=500)

        # 监控状态
        self.monitoring_active = False
        self.monitoring_thread = None
        self.last_report_time = datetime.now()

        # 性能统计
        self.performance_stats = defaultdict(dict)

        # 锁
        self.metrics_lock = threading.Lock()
        self.alerts_lock = threading.Lock()

        self.logger = logging.getLogger(__name__)

    def start_monitoring(self) -> None:
        """开始监控"""
        if self.monitoring_active:
            self.logger.warning("监控已经在运行中")
            return

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True
        )
        self.monitoring_thread.start()

        self.logger.info("性能监控已启动")

    def stop_monitoring(self) -> None:
        """停止监控"""
        self.monitoring_active = False

        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5.0)

        self.logger.info("性能监控已停止")

    def record_metrics(self, algorithm_name: str, metrics: PerformanceMetrics) -> None:
        """记录性能指标"""
        with self.metrics_lock:
            # 添加算法名称
            metrics_dict = {
                "algorithm_name": algorithm_name,
                "timestamp": metrics.timestamp,
                **metrics.__dict__,
            }

            self.metrics_history.append(metrics_dict)
            self.algorithm_metrics[algorithm_name].append(metrics)

            # 更新基线
            self._update_baseline(algorithm_name, metrics)

            # 检查预警
            self._check_alerts(algorithm_name, metrics)

            # 异常检测
            if self.config.anomaly_detection_enabled:
                self._detect_anomalies(algorithm_name, metrics)

    def record_prediction_result(
        self,
        algorithm_name: str,
        predicted: List[int],
        actual: List[int],
        prediction_time: float = 0.0,
        confidence: float = 0.0,
    ) -> None:
        """记录预测结果"""
        try:
            # 计算性能指标
            metrics = self._calculate_prediction_metrics(
                predicted, actual, prediction_time, confidence
            )

            # 记录指标
            self.record_metrics(algorithm_name, metrics)

        except Exception as e:
            self.logger.error(f"记录预测结果失败: {e}")

    def record_kill_result(
        self,
        algorithm_name: str,
        kill_numbers: List[int],
        actual_numbers: List[int],
        kill_time: float = 0.0,
    ) -> None:
        """记录杀号结果"""
        try:
            # 计算杀号成功率
            kill_success = len(set(kill_numbers) & set(actual_numbers)) == 0
            kill_success_rate = 1.0 if kill_success else 0.0

            # 创建性能指标
            metrics = PerformanceMetrics(
                kill_success_rate=kill_success_rate,
                response_time=kill_time * 1000,  # 转换为毫秒
                timestamp=datetime.now(),
            )

            self.record_metrics(algorithm_name, metrics)

        except Exception as e:
            self.logger.error(f"记录杀号结果失败: {e}")

    def _calculate_prediction_metrics(
        self,
        predicted: List[int],
        actual: List[int],
        prediction_time: float,
        confidence: float,
    ) -> PerformanceMetrics:
        """计算预测指标"""
        # 计算命中率
        hit_count = len(set(predicted) & set(actual))
        hit_rate = hit_count / len(actual) if actual else 0.0

        # 计算准确率（简化版）
        accuracy = hit_rate

        # 创建性能指标
        metrics = PerformanceMetrics(
            accuracy=accuracy,
            hit_rate=hit_rate,
            prediction_confidence=confidence,
            response_time=prediction_time * 1000,  # 转换为毫秒
            timestamp=datetime.now(),
        )

        return metrics

    def _update_baseline(
        self, algorithm_name: str, metrics: PerformanceMetrics
    ) -> None:
        """更新性能基线"""
        if algorithm_name not in self.performance_baseline:
            self.performance_baseline[algorithm_name] = {
                "accuracy": [],
                "hit_rate": [],
                "response_time": [],
                "kill_success_rate": [],
            }

        baseline = self.performance_baseline[algorithm_name]

        # 添加新数据
        baseline["accuracy"].append(metrics.accuracy)
        baseline["hit_rate"].append(metrics.hit_rate)
        baseline["response_time"].append(metrics.response_time)
        baseline["kill_success_rate"].append(metrics.kill_success_rate)

        # 保持窗口大小
        window_size = self.config.baseline_window_size
        for key in baseline:
            if len(baseline[key]) > window_size:
                baseline[key] = baseline[key][-window_size:]

        self.baseline_updated_count += 1

    def get_baseline_metrics(self, algorithm_name: str) -> Dict[str, float]:
        """获取基线指标"""
        if algorithm_name not in self.performance_baseline:
            return {}

        baseline = self.performance_baseline[algorithm_name]
        baseline_metrics = {}

        for metric_name, values in baseline.items():
            if values:
                baseline_metrics[f"{metric_name}_mean"] = np.mean(values)
                baseline_metrics[f"{metric_name}_std"] = np.std(values)
                baseline_metrics[f"{metric_name}_median"] = np.median(values)
                baseline_metrics[f"{metric_name}_p95"] = np.percentile(values, 95)
                baseline_metrics[f"{metric_name}_p99"] = np.percentile(values, 99)

        return baseline_metrics

    def configure_alert(self, alert_config: AlertConfig) -> None:
        """配置预警"""
        with self.alerts_lock:
            self.alert_configs[alert_config.metric_name] = alert_config

        self.logger.info(f"预警配置已更新: {alert_config.metric_name}")

    def _check_alerts(self, algorithm_name: str, metrics: PerformanceMetrics) -> None:
        """检查预警"""
        with self.alerts_lock:
            for metric_name, alert_config in self.alert_configs.items():
                if not alert_config.enabled:
                    continue

                # 获取当前指标值
                current_value = getattr(metrics, metric_name, None)
                if current_value is None:
                    continue

                # 检查阈值
                violation = False

                if alert_config.threshold_type == "upper":
                    violation = current_value > alert_config.threshold_value
                elif alert_config.threshold_type == "lower":
                    violation = current_value < alert_config.threshold_value
                elif alert_config.threshold_type == "range":
                    if alert_config.threshold_range:
                        violation = not (
                            alert_config.threshold_range[0]
                            <= current_value
                            <= alert_config.threshold_range[1]
                        )

                if violation:
                    self._trigger_alert(
                        algorithm_name, metric_name, current_value, alert_config
                    )
                else:
                    self._resolve_alert(algorithm_name, metric_name)

    def _trigger_alert(
        self,
        algorithm_name: str,
        metric_name: str,
        current_value: float,
        alert_config: AlertConfig,
    ) -> None:
        """触发预警"""
        alert_key = f"{algorithm_name}_{metric_name}"

        if alert_key in self.active_alerts:
            # 更新现有预警
            alert = self.active_alerts[alert_key]
            alert.current_value = current_value
            alert.timestamp = datetime.now()
        else:
            # 创建新预警
            alert_id = f"alert_{len(self.alert_history)}_{int(time.time())}"

            alert = PerformanceAlert(
                alert_id=alert_id,
                metric_name=metric_name,
                current_value=current_value,
                threshold_value=alert_config.threshold_value,
                severity=alert_config.severity,
                message=f"{algorithm_name} 的 {metric_name} 指标异常: {current_value:.4f}",
            )

            self.active_alerts[alert_key] = alert
            self.alert_history.append(alert)

            self.logger.warning(f"预警触发: {alert.message}")

    def _resolve_alert(self, algorithm_name: str, metric_name: str) -> None:
        """解决预警"""
        alert_key = f"{algorithm_name}_{metric_name}"

        if alert_key in self.active_alerts:
            alert = self.active_alerts[alert_key]
            alert.resolved = True
            alert.resolution_time = datetime.now()

            del self.active_alerts[alert_key]

            self.logger.info(f"预警已解决: {alert.message}")

    def _detect_anomalies(
        self, algorithm_name: str, metrics: PerformanceMetrics
    ) -> None:
        """检测异常"""
        try:
            # 获取历史数据
            history = list(self.algorithm_metrics[algorithm_name])

            if len(history) < 20:  # 数据不足
                return

            # 准备特征数据
            features = []
            for hist_metrics in history[-50:]:  # 使用最近50个数据点
                feature_vector = [
                    hist_metrics.accuracy,
                    hist_metrics.hit_rate,
                    hist_metrics.response_time,
                    hist_metrics.kill_success_rate,
                    hist_metrics.prediction_confidence,
                ]
                features.append(feature_vector)

            # 标准化
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)

            # 训练异常检测器
            if self.anomaly_detector is None or len(history) % 20 == 0:
                self.anomaly_detector = IsolationForest(
                    contamination=0.1, random_state=42
                )
                self.anomaly_detector.fit(features_scaled)

            # 检测当前指标
            current_features = [
                [
                    metrics.accuracy,
                    metrics.hit_rate,
                    metrics.response_time,
                    metrics.kill_success_rate,
                    metrics.prediction_confidence,
                ]
            ]

            current_scaled = scaler.transform(current_features)
            anomaly_score = self.anomaly_detector.decision_function(current_scaled)[0]
            is_anomaly = self.anomaly_detector.predict(current_scaled)[0] == -1

            if is_anomaly:
                self.anomaly_history.append(
                    {
                        "algorithm_name": algorithm_name,
                        "timestamp": metrics.timestamp,
                        "anomaly_score": anomaly_score,
                        "metrics": metrics.__dict__.copy(),
                    }
                )

                self.logger.warning(
                    f"检测到异常: {algorithm_name}, 异常分数: {anomaly_score:.4f}"
                )

        except Exception as e:
            self.logger.debug(f"异常检测失败: {e}")

    def _monitoring_loop(self) -> None:
        """监控主循环"""
        while self.monitoring_active:
            try:
                # 更新性能统计
                self._update_performance_stats()

                # 生成报告
                if self.config.report_generation_enabled:
                    self._check_report_generation()

                # 清理过期数据
                self._cleanup_expired_data()

                time.sleep(self.config.monitoring_interval)

            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                time.sleep(1.0)

    def _update_performance_stats(self) -> None:
        """更新性能统计"""
        with self.metrics_lock:
            for algorithm_name, metrics_list in self.algorithm_metrics.items():
                if not metrics_list:
                    continue

                # 计算统计指标
                recent_metrics = list(metrics_list)[-20:]  # 最近20个数据点

                stats = {
                    "count": len(recent_metrics),
                    "avg_accuracy": np.mean([m.accuracy for m in recent_metrics]),
                    "avg_hit_rate": np.mean([m.hit_rate for m in recent_metrics]),
                    "avg_response_time": np.mean(
                        [m.response_time for m in recent_metrics]
                    ),
                    "avg_kill_success_rate": np.mean(
                        [m.kill_success_rate for m in recent_metrics]
                    ),
                    "last_update": datetime.now(),
                }

                self.performance_stats[algorithm_name] = stats

    def _check_report_generation(self) -> None:
        """检查是否需要生成报告"""
        now = datetime.now()
        time_since_last_report = (now - self.last_report_time).total_seconds() / 3600

        if time_since_last_report >= self.config.report_interval_hours:
            self._generate_performance_report()
            self.last_report_time = now

    def _cleanup_expired_data(self) -> None:
        """清理过期数据"""
        cutoff_time = datetime.now() - timedelta(
            days=self.config.metrics_retention_days
        )

        with self.metrics_lock:
            # 清理指标历史
            self.metrics_history = deque(
                [m for m in self.metrics_history if m["timestamp"] > cutoff_time],
                maxlen=self.metrics_history.maxlen,
            )

            # 清理算法指标
            for algorithm_name in self.algorithm_metrics:
                self.algorithm_metrics[algorithm_name] = deque(
                    [
                        m
                        for m in self.algorithm_metrics[algorithm_name]
                        if m.timestamp > cutoff_time
                    ],
                    maxlen=self.algorithm_metrics[algorithm_name].maxlen,
                )

        with self.alerts_lock:
            # 清理预警历史
            self.alert_history = deque(
                [a for a in self.alert_history if a.timestamp > cutoff_time],
                maxlen=self.alert_history.maxlen,
            )

    def get_performance_summary(
        self, algorithm_name: str = None, time_range_hours: int = 24
    ) -> Dict[str, Any]:
        """获取性能摘要"""
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)

        with self.metrics_lock:
            if algorithm_name:
                # 单个算法摘要
                metrics_list = [
                    m
                    for m in self.algorithm_metrics.get(algorithm_name, [])
                    if m.timestamp > cutoff_time
                ]

                if not metrics_list:
                    return {"algorithm_name": algorithm_name, "status": "no_data"}

                summary = self._calculate_algorithm_summary(
                    algorithm_name, metrics_list
                )

            else:
                # 全局摘要
                summary = {
                    "total_algorithms": len(self.algorithm_metrics),
                    "total_metrics": len(self.metrics_history),
                    "active_alerts": len(self.active_alerts),
                    "anomalies_detected": len(self.anomaly_history),
                    "algorithms": {},
                }

                for alg_name in self.algorithm_metrics:
                    alg_metrics = [
                        m
                        for m in self.algorithm_metrics[alg_name]
                        if m.timestamp > cutoff_time
                    ]

                    if alg_metrics:
                        summary["algorithms"][alg_name] = (
                            self._calculate_algorithm_summary(alg_name, alg_metrics)
                        )

        return summary

    def _calculate_algorithm_summary(
        self, algorithm_name: str, metrics_list: List[PerformanceMetrics]
    ) -> Dict[str, Any]:
        """计算算法摘要"""
        if not metrics_list:
            return {"status": "no_data"}

        # 基本统计
        accuracies = [m.accuracy for m in metrics_list]
        hit_rates = [m.hit_rate for m in metrics_list]
        response_times = [m.response_time for m in metrics_list]
        kill_success_rates = [m.kill_success_rate for m in metrics_list]

        summary = {
            "algorithm_name": algorithm_name,
            "data_points": len(metrics_list),
            "time_range": {
                "start": min(m.timestamp for m in metrics_list),
                "end": max(m.timestamp for m in metrics_list),
            },
            "accuracy": {
                "mean": np.mean(accuracies),
                "std": np.std(accuracies),
                "min": np.min(accuracies),
                "max": np.max(accuracies),
                "median": np.median(accuracies),
            },
            "hit_rate": {
                "mean": np.mean(hit_rates),
                "std": np.std(hit_rates),
                "min": np.min(hit_rates),
                "max": np.max(hit_rates),
                "median": np.median(hit_rates),
            },
            "response_time": {
                "mean": np.mean(response_times),
                "std": np.std(response_times),
                "min": np.min(response_times),
                "max": np.max(response_times),
                "median": np.median(response_times),
                "p95": np.percentile(response_times, 95),
                "p99": np.percentile(response_times, 99),
            },
            "kill_success_rate": {
                "mean": np.mean(kill_success_rates),
                "std": np.std(kill_success_rates),
                "min": np.min(kill_success_rates),
                "max": np.max(kill_success_rates),
                "median": np.median(kill_success_rates),
            },
        }

        # 趋势分析
        if len(metrics_list) >= 10:
            summary["trends"] = self._analyze_trends(metrics_list)

        # 基线比较
        baseline = self.get_baseline_metrics(algorithm_name)
        if baseline:
            summary["baseline_comparison"] = self._compare_with_baseline(
                summary, baseline
            )

        return summary

    def _analyze_trends(self, metrics_list: List[PerformanceMetrics]) -> Dict[str, Any]:
        """分析趋势"""
        # 时间序列分析
        timestamps = [m.timestamp for m in metrics_list]
        time_numeric = [(t - timestamps[0]).total_seconds() for t in timestamps]

        trends = {}

        for metric_name in [
            "accuracy",
            "hit_rate",
            "response_time",
            "kill_success_rate",
        ]:
            values = [getattr(m, metric_name) for m in metrics_list]

            if len(values) >= 5:
                # 线性回归分析趋势
                slope, intercept, r_value, p_value, std_err = stats.linregress(
                    time_numeric, values
                )

                trends[metric_name] = {
                    "slope": slope,
                    "correlation": r_value,
                    "p_value": p_value,
                    "trend_direction": (
                        "increasing"
                        if slope > 0
                        else "decreasing" if slope < 0 else "stable"
                    ),
                    "trend_strength": abs(r_value),
                }

        return trends

    def _compare_with_baseline(
        self, current_summary: Dict[str, Any], baseline: Dict[str, float]
    ) -> Dict[str, Any]:
        """与基线比较"""
        comparison = {}

        for metric_name in [
            "accuracy",
            "hit_rate",
            "response_time",
            "kill_success_rate",
        ]:
            if metric_name in current_summary and f"{metric_name}_mean" in baseline:
                current_mean = current_summary[metric_name]["mean"]
                baseline_mean = baseline[f"{metric_name}_mean"]
                baseline_std = baseline.get(f"{metric_name}_std", 0.1)

                # 计算偏差
                deviation = (
                    (current_mean - baseline_mean) / baseline_mean
                    if baseline_mean != 0
                    else 0
                )
                z_score = (
                    (current_mean - baseline_mean) / baseline_std
                    if baseline_std != 0
                    else 0
                )

                comparison[metric_name] = {
                    "current": current_mean,
                    "baseline": baseline_mean,
                    "deviation_percent": deviation * 100,
                    "z_score": z_score,
                    "status": self._get_performance_status(
                        metric_name, deviation, z_score
                    ),
                }

        return comparison

    def _get_performance_status(
        self, metric_name: str, deviation: float, z_score: float
    ) -> str:
        """获取性能状态"""
        # 对于响应时间，负偏差是好的
        if metric_name == "response_time":
            if deviation < -0.1:  # 响应时间减少10%以上
                return "improved"
            elif deviation > 0.2:  # 响应时间增加20%以上
                return "degraded"
            else:
                return "stable"

        # 对于其他指标，正偏差是好的
        else:
            if deviation > 0.1:  # 提升10%以上
                return "improved"
            elif deviation < -0.1:  # 下降10%以上
                return "degraded"
            else:
                return "stable"

    def _generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        report = {
            "report_id": f"report_{int(time.time())}",
            "generation_time": datetime.now(),
            "time_range_hours": self.config.report_interval_hours,
            "summary": self.get_performance_summary(),
            "alerts": {
                "active_count": len(self.active_alerts),
                "total_triggered": len(self.alert_history),
                "active_alerts": list(self.active_alerts.values()),
            },
            "anomalies": {
                "detected_count": len(self.anomaly_history),
                "recent_anomalies": list(self.anomaly_history)[-10:],
            },
            "recommendations": self._generate_recommendations(),
        }

        self.logger.info(f"性能报告已生成: {report['report_id']}")

        return report

    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []

        # 分析性能统计
        for algorithm_name, stats in self.performance_stats.items():
            if stats["avg_response_time"] > 1000:  # 响应时间超过1秒
                recommendations.append(
                    f"{algorithm_name}: 响应时间过长({stats['avg_response_time']:.1f}ms)，建议优化算法性能"
                )

            if stats["avg_accuracy"] < 0.5:  # 准确率低于50%
                recommendations.append(
                    f"{algorithm_name}: 准确率较低({stats['avg_accuracy']:.2f})，建议调整算法参数"
                )

            if stats["avg_hit_rate"] < 0.3:  # 命中率低于30%
                recommendations.append(
                    f"{algorithm_name}: 命中率较低({stats['avg_hit_rate']:.2f})，建议重新训练模型"
                )

        # 分析活跃预警
        if len(self.active_alerts) > 5:
            recommendations.append("活跃预警过多，建议检查系统状态并调整预警阈值")

        # 分析异常检测
        if len(self.anomaly_history) > 10:
            recommendations.append("检测到较多异常，建议进行系统诊断")

        return recommendations

    def export_metrics(
        self, filepath: str, algorithm_name: str = None, time_range_hours: int = 24
    ) -> None:
        """导出指标数据"""
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)

        with self.metrics_lock:
            if algorithm_name:
                # 导出单个算法数据
                metrics_data = [
                    m.__dict__
                    for m in self.algorithm_metrics.get(algorithm_name, [])
                    if m.timestamp > cutoff_time
                ]
            else:
                # 导出所有数据
                metrics_data = [
                    m for m in self.metrics_history if m["timestamp"] > cutoff_time
                ]

        # 转换为DataFrame并导出
        if metrics_data:
            df = pd.DataFrame(metrics_data)

            if filepath.endswith(".csv"):
                df.to_csv(filepath, index=False)
            elif filepath.endswith(".json"):
                df.to_json(filepath, orient="records", date_format="iso")
            else:
                # 默认JSON格式
                with open(filepath, "w", encoding="utf-8") as f:
                    json.dump(
                        metrics_data, f, ensure_ascii=False, indent=2, default=str
                    )

            self.logger.info(f"指标数据已导出到: {filepath}")
        else:
            self.logger.warning("没有找到符合条件的指标数据")

    def get_real_time_metrics(
        self, algorithm_name: str
    ) -> Optional[PerformanceMetrics]:
        """获取实时指标"""
        with self.metrics_lock:
            metrics_list = self.algorithm_metrics.get(algorithm_name, [])
            if metrics_list:
                return metrics_list[-1]  # 返回最新指标
            return None

    def get_alert_status(self) -> Dict[str, Any]:
        """获取预警状态"""
        with self.alerts_lock:
            return {
                "active_alerts_count": len(self.active_alerts),
                "total_alerts_count": len(self.alert_history),
                "active_alerts": {
                    key: {
                        "metric_name": alert.metric_name,
                        "current_value": alert.current_value,
                        "threshold_value": alert.threshold_value,
                        "severity": alert.severity,
                        "duration": (datetime.now() - alert.timestamp).total_seconds(),
                    }
                    for key, alert in self.active_alerts.items()
                },
                "recent_alerts": [
                    {
                        "alert_id": alert.alert_id,
                        "metric_name": alert.metric_name,
                        "severity": alert.severity,
                        "timestamp": alert.timestamp,
                        "resolved": alert.resolved,
                    }
                    for alert in list(self.alert_history)[-10:]
                ],
            }

    def reset_metrics(self, algorithm_name: str = None) -> None:
        """重置指标数据"""
        with self.metrics_lock:
            if algorithm_name:
                if algorithm_name in self.algorithm_metrics:
                    self.algorithm_metrics[algorithm_name].clear()
                    self.logger.info(f"已重置 {algorithm_name} 的指标数据")
            else:
                self.metrics_history.clear()
                self.algorithm_metrics.clear()
                self.performance_baseline.clear()
                self.logger.info("已重置所有指标数据")

        with self.alerts_lock:
            if not algorithm_name:  # 全局重置时清理预警
                self.active_alerts.clear()
                self.alert_history.clear()

    def __del__(self):
        """析构函数"""
        self.stop_monitoring()
