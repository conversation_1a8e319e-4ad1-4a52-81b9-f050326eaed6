#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phase 4优化验证
"""

def test_confidence_fix():
    """测试confidence字段修复"""
    print("测试confidence字段修复...")
    
    try:
        # 模拟深度学习结果（字典格式confidence）
        dl_results = {
            'confidence': {
                'red': 0.7,
                'blue': 0.6,
                'overall': 0.65
            }
        }
        
        # 模拟传统ML结果（浮点数格式confidence）
        ml_results = {
            'confidence': 0.55
        }
        
        # 测试confidence处理逻辑
        def process_confidence(results, name):
            confidence_raw = results.get("confidence", 0.5)
            if isinstance(confidence_raw, dict):
                confidence = float(confidence_raw.get("overall", 0.5))
                print(f"   {name}: 字典格式 -> {confidence:.3f}")
            else:
                confidence = float(confidence_raw)
                print(f"   {name}: 浮点格式 -> {confidence:.3f}")
            return confidence
        
        dl_confidence = process_confidence(dl_results, "深度学习")
        ml_confidence = process_confidence(ml_results, "传统ML")
        
        print("confidence字段处理成功")
        return True
        
    except Exception as e:
        print(f"confidence测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("Phase 4优化验证")
    print("=" * 50)
    
    result = test_confidence_fix()
    
    if result:
        print("验证通过！Phase 4优化成功！")
    else:
        print("验证失败！")
    
    return result

if __name__ == "__main__":
    success = main()