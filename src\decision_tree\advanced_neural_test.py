#!/usr/bin/env python3
"""
高级神经网络预测器测试脚本
验证是否能达到80%以上的命中率
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
sys.path.insert(0, project_root)

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from pathlib import Path

# 导入框架组件
from src.framework.backtest_framework import BacktestFramework
from src.framework.data_models import BacktestConfig
from src.framework.result_display import ResultDisplayer
from src.utils.utils import load_data
from src.utils.logger import setup_logger

# 导入高级预测器
from src.decision_tree.advanced_neural_predictor import (
    AdvancedNeuralPredictor,
    AdvancedConfig,
)


class AdvancedNeuralTest:
    """高级神经网络预测器测试类"""

    def __init__(self):
        self.logger = setup_logger("AdvancedNeuralTest")

    def run_test(self):
        """运行测试"""
        try:
            self.logger.info("=== 高级神经网络预测器测试开始 ===")

            # 1. 加载数据
            self.logger.info("正在加载数据...")
            data = load_data("dlt_data.csv")
            self.logger.info(f"数据加载完成，共 {len(data)} 期数据")

            # 2. 创建高级配置
            config = AdvancedConfig(
                lstm_units=64,  # 减少参数以加快训练
                dense_units=32,
                dropout_rate=0.2,
                learning_rate=0.001,
                batch_size=16,
                epochs=50,  # 减少训练轮数
                sequence_length=15,
                feature_windows=[3, 5, 10, 15],
                use_fft=True,
                use_attention=True,
                n_models=3,  # 减少集成模型数量
                ensemble_method="voting",
                augment_data=True,
                noise_level=0.01,
            )

            # 3. 创建预测器
            self.logger.info("创建高级神经网络预测器...")
            predictor = AdvancedNeuralPredictor(config)

            # 4. 配置回测参数
            backtest_config = BacktestConfig(
                num_periods=min(100, len(data) - 100),  # 使用100期进行回测
                min_train_periods=100,  # 最少需要100期训练数据
                display_periods=10,  # 显示最后10期结果
                enable_detailed_output=True,
                enable_statistics=True,
                reverse_display=True,
            )

            self.logger.info(
                f"回测配置: 回测{backtest_config.num_periods}期，最少训练{backtest_config.min_train_periods}期"
            )

            # 5. 创建回测框架
            framework = BacktestFramework(data)

            # 6. 运行回测
            self.logger.info("开始运行高级神经网络回测...")
            start_time = datetime.now()

            results = framework.run_backtest(predictor, backtest_config)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 7. 显示结果
            self.logger.info("\n=== 回测结果 ===")
            displayer = ResultDisplayer()
            displayer.display_backtest_result(results)

            # 8. 详细分析命中率
            self._analyze_hit_rates(results)

            # 9. 保存模型
            model_save_path = "models/advanced_neural"
            if predictor.save_models(model_save_path):
                self.logger.info(f"模型已保存到: {model_save_path}")

            # 10. 生成优化报告
            self._generate_optimization_report(results, duration)

            self.logger.info("高级神经网络预测器测试完成")

        except Exception as e:
            self.logger.error(f"测试过程中发生错误: {e}")
            import traceback

            traceback.print_exc()

    def _analyze_hit_rates(self, results):
        """分析命中率"""
        self.logger.info("\n=== 命中率检查 ===")

        # 计算各项命中率
        total_periods = len(results.period_results)
        if total_periods == 0:
            self.logger.warning("没有回测结果")
            return

        # 奇偶比命中率
        odd_even_hits = sum(
            1
            for result in results.period_results
            if result.evaluation.hits.get("red_odd_even_hit", False)
        )
        odd_even_rate = (odd_even_hits / total_periods) * 100

        # 红球大小比命中率
        red_size_hits = sum(
            1
            for result in results.period_results
            if result.evaluation.hits.get("red_size_hit", False)
        )
        red_size_rate = (red_size_hits / total_periods) * 100

        # 蓝球大小命中率
        blue_size_hits = sum(
            1
            for result in results.period_results
            if result.evaluation.hits.get("blue_size_hit", False)
        )
        blue_size_rate = (blue_size_hits / total_periods) * 100

        self.logger.info(f"奇偶比命中率: {odd_even_rate:.2f}%")
        self.logger.info(f"红球大小比命中率: {red_size_rate:.2f}%")
        self.logger.info(f"蓝球大小命中率: {blue_size_rate:.2f}%")

        # 检查是否达标
        target_rate = 80.0
        passed_count = 0

        if odd_even_rate >= target_rate:
            self.logger.info(
                f"✓ 奇偶比命中率达标: {odd_even_rate:.2f}% >= {target_rate}%"
            )
            passed_count += 1
        else:
            self.logger.warning(
                f"✗ 奇偶比命中率未达标: {odd_even_rate:.2f}% < {target_rate}%"
            )

        if red_size_rate >= target_rate:
            self.logger.info(
                f"✓ 红球大小比命中率达标: {red_size_rate:.2f}% >= {target_rate}%"
            )
            passed_count += 1
        else:
            self.logger.warning(
                f"✗ 红球大小比命中率未达标: {red_size_rate:.2f}% < {target_rate}%"
            )

        if blue_size_rate >= target_rate:
            self.logger.info(
                f"✓ 蓝球大小命中率达标: {blue_size_rate:.2f}% >= {target_rate}%"
            )
            passed_count += 1
        else:
            self.logger.warning(
                f"✗ 蓝球大小命中率未达标: {blue_size_rate:.2f}% < {target_rate}%"
            )

        self.logger.info(f"\n总体达标情况: {passed_count}/3 项达到{target_rate}%目标")

        if passed_count == 3:
            self.logger.info("🎉 恭喜！所有指标都达到了80%的目标！")
        elif passed_count >= 2:
            self.logger.info("👍 大部分指标达标，模型表现良好")
        elif passed_count >= 1:
            self.logger.warning("⚠️ 部分指标达标，建议进一步优化")
        else:
            self.logger.warning("❌ 模型效果未达到预期，需要重新设计")

    def _generate_optimization_report(self, results, duration):
        """生成优化报告"""
        self.logger.info("\n=== 优化报告 ===")

        # 性能统计
        total_periods = len(results.period_results)
        if total_periods > 0:
            # 计算平均红球命中数
            avg_red_hits = np.mean(
                [result.evaluation.red_hits for result in results.period_results]
            )

            self.logger.info(f"回测期数: {total_periods}")
            self.logger.info(f"平均红球命中数: {avg_red_hits:.2f}")
            self.logger.info(f"回测耗时: {duration:.2f}秒")
            self.logger.info(f"平均每期耗时: {duration/total_periods:.2f}秒")

        # 改进建议
        self.logger.info("\n=== 改进建议 ===")
        self.logger.info("1. 数据质量改进:")
        self.logger.info("   - 增加更多历史数据")
        self.logger.info("   - 考虑外部因素（节假日、特殊事件）")
        self.logger.info("   - 数据清洗和异常值处理")

        self.logger.info("\n2. 特征工程改进:")
        self.logger.info("   - 增加更多时序特征")
        self.logger.info("   - 尝试非线性特征组合")
        self.logger.info("   - 引入领域知识特征")

        self.logger.info("\n3. 模型架构改进:")
        self.logger.info("   - 尝试更深的网络结构")
        self.logger.info("   - 使用Transformer架构")
        self.logger.info("   - 多任务学习优化")

        self.logger.info("\n4. 训练策略改进:")
        self.logger.info("   - 使用更多的数据增强技术")
        self.logger.info("   - 实施课程学习")
        self.logger.info("   - 优化超参数搜索")

        self.logger.info("\n5. 集成策略改进:")
        self.logger.info("   - 增加模型多样性")
        self.logger.info("   - 使用动态权重集成")
        self.logger.info("   - 实施在线学习")


def main():
    """主函数"""
    # 设置随机种子以确保可重现性
    np.random.seed(42)

    # 运行测试
    test = AdvancedNeuralTest()
    test.run_test()


if __name__ == "__main__":
    main()
