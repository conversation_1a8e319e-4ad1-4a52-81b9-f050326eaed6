#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test for Phase 4 optimization
"""

import sys
import os

sys.path.append(os.path.join(os.getcwd(), "src"))

from systems.number_selection_system import (
    NumberSelectionSystem,
    SelectionMethod,
    SelectionConfig,
)
from systems.optimized_selection_config import OptimizedSelectionConfig
import pandas as pd


def test_phase4_integration():
    """测试Phase 4优化集成"""
    print("🧪 测试Phase 4优化集成...")

    # 创建测试数据
    test_data = pd.DataFrame(
        {
            "期号": ["25001", "25002", "25003"],
            "红球1": [1, 2, 3],
            "红球2": [5, 6, 7],
            "红球3": [10, 11, 12],
            "红球4": [15, 16, 17],
            "红球5": [20, 21, 22],
            "蓝球1": [1, 2, 3],
            "蓝球2": [5, 6, 7],
        }
    )

    try:
        # 测试基础选号系统
        print("测试基础选号系统...")
        config = SelectionConfig()
        system = NumberSelectionSystem(config)

        # 测试传统ML方法
        result = system.select_numbers(SelectionMethod.TRADITIONAL_ML, test_data)
        print(
            f'✅ 传统ML成功！红球: {result["red_balls"]}, 蓝球: {result["blue_balls"]}'
        )

        # 测试优化配置
        print("测试优化配置...")
        opt_config = OptimizedSelectionConfig()
        opt_system = NumberSelectionSystem(opt_config)

        # 测试集成方法
        result = opt_system.select_numbers(SelectionMethod.ENSEMBLE, test_data)
        print(
            f'✅ 集成方法成功！红球: {result["red_balls"]}, 蓝球: {result["blue_balls"]}'
        )

        print("🎉 Phase 4优化集成测试通过！")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_phase4_integration()
    if success:
        print("\n✅ 所有测试通过！Phase 4优化已成功集成。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
