"""决策树回测示例

演示如何使用决策树模块与统一回测框架进行历史数据回测
生成用户要求的日志格式
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import pandas as pd
from typing import Dict, Any

from src.decision_tree import (
    OddEvenTreePredictor,
    SizeRatioTreePredictor,
    TreeEnsemblePredictor,
)
from src.framework.backtest_framework import BacktestFramework
from src.framework.data_models import BacktestConfig
from src.utils.utils import load_data


class DecisionTreeBacktester:
    """决策树回测器"""

    def __init__(self, data_file: str = None):
        """初始化回测器"""
        self.data = self._load_data(data_file)
        self.framework = BacktestFramework(self.data)

    def _load_data(self, data_file: str = None) -> pd.DataFrame:
        """加载数据"""
        if data_file is None:
            # 使用项目中的默认数据文件
            data_file = project_root / "data" / "raw" / "dlt_data.csv"

        if not os.path.exists(data_file):
            # 如果默认文件不存在，创建模拟数据
            print(f"⚠️ 数据文件不存在: {data_file}")
            print("📊 创建模拟数据进行演示...")
            return self._create_mock_data()

        try:
            return load_data(str(data_file))
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            print("📊 创建模拟数据进行演示...")
            return self._create_mock_data()

    def _create_mock_data(self) -> pd.DataFrame:
        """创建模拟数据用于演示"""
        import numpy as np

        np.random.seed(42)

        # 创建1500期模拟数据
        periods = 1500
        data = []

        for i in range(periods):
            period_num = f"25{i+1:03d}" if i < 999 else f"2{5000+i:04d}"

            # 生成红球（5个不重复的1-35号码）
            red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))

            # 生成蓝球（2个不重复的1-12号码）
            blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))

            # 计算比例
            red_odd_count = sum(1 for x in red_balls if x % 2 == 1)
            red_even_count = 5 - red_odd_count
            red_odd_even = f"{red_odd_count}:{red_even_count}"

            red_small_count = sum(1 for x in red_balls if x <= 17)  # 红球分界线17
            red_big_count = 5 - red_small_count
            red_size = f"{red_small_count}:{red_big_count}"

            blue_small_count = sum(1 for x in blue_balls if x <= 6)
            blue_big_count = 2 - blue_small_count
            blue_size = "小" if blue_small_count > blue_big_count else "大"

            data.append(
                {
                    "期号": period_num,
                    "红球1": red_balls[0],
                    "红球2": red_balls[1],
                    "红球3": red_balls[2],
                    "红球4": red_balls[3],
                    "红球5": red_balls[4],
                    "蓝球1": blue_balls[0],
                    "蓝球2": blue_balls[1],
                    "红球奇偶比": red_odd_even,
                    "红球大小比": red_size,
                    "蓝球大小": blue_size,
                }
            )

        return pd.DataFrame(data)

    def run_backtest(
        self, predictor_type: str = "odd_even", num_periods: int = 20
    ) -> None:
        """运行回测并输出用户要求的日志格式"""
        print(f"🧪 开始决策树回测 - {predictor_type}")
        print("=" * 60)

        # 创建预测器
        if predictor_type == "odd_even":
            predictor = OddEvenTreePredictor(
                max_depth=10, min_samples_split=5, random_state=42
            )
        elif predictor_type == "size_ratio":
            predictor = SizeRatioTreePredictor(
                max_depth=10, min_samples_split=5, random_state=42
            )
        elif predictor_type == "ensemble":
            predictor = TreeEnsemblePredictor(
                ensemble_size=3, voting_strategy="weighted", random_state=42
            )
        else:
            raise ValueError(f"不支持的预测器类型: {predictor_type}")

        # 配置回测
        config = BacktestConfig(
            num_periods=num_periods,
            min_train_periods=50,
            metrics=["red_odd_even_hit", "red_size_hit", "blue_size_hit"],
        )

        # 运行回测
        result = self.framework.run_backtest(predictor, config)

        print("\n" + "=" * 60)
        print("📊 回测结果详情（用户要求的日志格式）")
        print("=" * 60)

        # 输出用户要求的日志格式
        self._print_detailed_results(result)

        # 输出统计摘要
        self._print_summary(result)

    def _print_detailed_results(self, result) -> None:
        """输出详细的回测结果（用户要求的日志格式）"""
        for period_result in result.period_results:
            if not period_result.success:
                continue

            prediction = period_result.prediction
            evaluation = period_result.evaluation

            # 获取预测期号和目标期号
            current_period = prediction.period_number
            target_period = evaluation.period_number
            data_index = evaluation.data_index

            print(
                f"\n基于期号 {current_period} 预测{target_period}(索引 {data_index}):"
            )
            print("红球")

            # 奇偶比预测结果
            if prediction.red_odd_even_predictions:
                pred_odd_even = prediction.red_odd_even_predictions[0]
                pred_ratio = pred_odd_even[0]
                pred_prob = pred_odd_even[1]
                actual_ratio = evaluation.actual_red_odd_even

                hit_symbol = (
                    "✅" if evaluation.hits.get("red_odd_even_hit", False) else "❌"
                )
                print(
                    f"奇偶比: 预测[{pred_ratio}({pred_prob:.3f})] -> 实际[{actual_ratio}] {hit_symbol}"
                )

            # 大小比预测结果
            if prediction.red_size_predictions:
                pred_size = prediction.red_size_predictions[0]
                pred_ratio = pred_size[0]
                pred_prob = pred_size[1]
                actual_ratio = evaluation.actual_red_size

                hit_symbol = (
                    "✅" if evaluation.hits.get("red_size_hit", False) else "❌"
                )
                print(
                    f"大小比: 预测[{pred_ratio}({pred_prob:.3f})] -> 实际[{actual_ratio}] {hit_symbol}"
                )

            # 蓝球大小预测结果
            if prediction.blue_size_predictions:
                pred_blue = prediction.blue_size_predictions[0]
                pred_size = pred_blue[0]
                pred_prob = pred_blue[1]
                actual_size = evaluation.actual_blue_size

                hit_symbol = (
                    "✅" if evaluation.hits.get("blue_size_hit", False) else "❌"
                )
                print(
                    f"蓝球大小: 预测[{pred_size}({pred_prob:.3f})] -> 实际[{actual_size}] {hit_symbol}"
                )

            # 号码命中情况
            pred_red, pred_blue = prediction.generated_numbers
            actual_red = evaluation.actual_red
            actual_blue = evaluation.actual_blue

            red_hits = len(set(pred_red) & set(actual_red))
            blue_hits = len(set(pred_blue) & set(actual_blue))

            print(f"号码命中: 红球{red_hits}/5, 蓝球{blue_hits}/2")
            print(f"预测红球: {sorted(pred_red)}")
            print(f"实际红球: {sorted(actual_red)}")
            print(f"预测蓝球: {sorted(pred_blue)}")
            print(f"实际蓝球: {sorted(actual_blue)}")

    def _print_summary(self, result) -> None:
        """输出统计摘要"""
        stats = result.statistics

        print("\n" + "=" * 60)
        print("📈 回测统计摘要")
        print("=" * 60)

        print(f"总回测期数: {stats.total_periods}")
        print(f"成功期数: {stats.successful_periods}")

        if stats.hit_rates:
            print("\n命中率统计:")
            for metric, rate in stats.hit_rates.items():
                count = stats.hit_counts.get(metric, 0)
                metric_name = {
                    "red_odd_even_hit": "红球奇偶比",
                    "red_size_hit": "红球大小比",
                    "blue_size_hit": "蓝球大小",
                }.get(metric, metric)
                print(f"  {metric_name}: {count}/{stats.total_periods} ({rate:.1%})")

        print(f"\n平均命中数:")
        print(f"  红球: {stats.avg_red_hits:.2f}/5")
        print(f"  蓝球: {stats.avg_blue_hits:.2f}/2")
        print(f"  总计: {stats.avg_total_hits:.2f}/7")

        print(f"\n预测器: {result.predictor_name}")
        print(f"回测耗时: {result.total_duration:.2f}秒")

    def compare_predictors(self, num_periods: int = 10) -> None:
        """比较不同决策树预测器的性能"""
        print("🔍 决策树预测器性能比较")
        print("=" * 60)

        predictors = {
            "奇偶比决策树": "odd_even",
            "大小比决策树": "size_ratio",
            "集成决策树": "ensemble",
        }

        results = {}

        for name, pred_type in predictors.items():
            print(f"\n🧪 测试 {name}...")

            # 创建预测器
            if pred_type == "odd_even":
                predictor = OddEvenTreePredictor(random_state=42)
            elif pred_type == "size_ratio":
                predictor = SizeRatioTreePredictor(random_state=42)
            elif pred_type == "ensemble":
                predictor = TreeEnsemblePredictor(
                    ensemble_size=3, voting_strategy="weighted", random_state=42
                )

            # 配置回测
            config = BacktestConfig(
                num_periods=num_periods,
                min_train_periods=50,
                metrics=["red_odd_even_hit", "red_size_hit", "blue_size_hit"],
            )

            # 运行回测
            result = self.framework.run_backtest(predictor, config)
            results[name] = result

        # 输出比较结果
        print("\n" + "=" * 60)
        print("📊 性能比较结果")
        print("=" * 60)

        print(
            f"{'预测器':<15} {'奇偶比命中率':<12} {'大小比命中率':<12} {'蓝球命中率':<12} {'平均红球命中':<12}"
        )
        print("-" * 70)

        for name, result in results.items():
            stats = result.statistics
            odd_even_rate = stats.hit_rates.get("red_odd_even_hit", 0)
            size_rate = stats.hit_rates.get("red_size_hit", 0)
            blue_rate = stats.hit_rates.get("blue_size_hit", 0)
            avg_red = stats.avg_red_hits

            print(
                f"{name:<15} {odd_even_rate:<12.1%} {size_rate:<12.1%} {blue_rate:<12.1%} {avg_red:<12.2f}"
            )


def main():
    """主函数"""
    print("🎯 决策树回测系统")
    print("=" * 60)

    # 创建回测器
    backtester = DecisionTreeBacktester()

    # 运行单个预测器回测
    print("\n1️⃣ 奇偶比决策树回测")
    backtester.run_backtest("odd_even", num_periods=5)

    print("\n\n2️⃣ 大小比决策树回测")
    backtester.run_backtest("size_ratio", num_periods=5)

    print("\n\n3️⃣ 集成决策树回测")
    backtester.run_backtest("ensemble", num_periods=5)

    # 比较不同预测器
    print("\n\n4️⃣ 预测器性能比较")
    backtester.compare_predictors(num_periods=10)


if __name__ == "__main__":
    main()
