#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phase 3 ASCII验证脚本 - 权重系统重构验证
验证智能权重管理系统的基本功能（使用ASCII字符）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_weight_manager_basic():
    """测试权重管理器基本功能"""
    print("=" * 60)
    print("Test 1: Weight Manager Basic Functions")
    print("=" * 60)
    
    try:
        from src.integrations.weight_manager_adapter import WeightManagerAdapter, PerformanceMetrics
        
        # 创建权重管理器
        manager = WeightManagerAdapter()
        
        # 测试获取权重
        ensemble_weights = manager.get_ensemble_weights()
        model_weights = manager.get_model_weights()
        feature_weights = manager.get_feature_weights()
        loss_weights = manager.get_loss_weights()
        
        print("[PASS] Weight manager created successfully")
        print(f"   Ensemble weights count: {len(ensemble_weights)}")
        print(f"   Model weights count: {len(model_weights)}")
        print(f"   Feature weights count: {len(feature_weights)}")
        print(f"   Loss weights count: {len(loss_weights)}")
        
        # 验证权重值
        assert len(ensemble_weights) > 0, "Ensemble weights cannot be empty"
        assert len(feature_weights) > 0, "Feature weights cannot be empty"
        assert 'red_odd_even' in feature_weights, "Missing red ball odd/even weight"
        assert 'red_size' in feature_weights, "Missing red ball size weight"
        
        print("[PASS] Weight configuration validation passed")
        
        # 测试权重更新
        manager.update_weight_performance('red_odd_even', 0.35, 0.8)
        print("[PASS] Weight performance update successful")
        
        # 获取状态
        status = manager.get_status()
        print(f"[PASS] Status retrieved: {status['total_weights']} weights")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Weight manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_config_structure():
    """测试权重配置结构"""
    print("\n" + "=" * 60)
    print("Test 2: Weight Configuration Structure")
    print("=" * 60)
    
    try:
        from src.integrations.weight_manager_adapter import WeightConfig, PerformanceMetrics
        
        # 测试权重配置类
        config = WeightConfig(
            name="test_weight",
            category="feature",
            base_weight=0.5,
            min_weight=0.1,
            max_weight=1.0
        )
        
        print("[PASS] WeightConfig class created successfully")
        print(f"   Weight name: {config.name}")
        print(f"   Weight category: {config.category}")
        print(f"   Base weight: {config.base_weight}")
        
        # 测试性能指标类
        metrics = PerformanceMetrics(
            accuracy=0.8,
            hit_rate_2_plus_1=0.35,
            stability=0.9,
            confidence=0.75
        )
        
        print("[PASS] PerformanceMetrics class created successfully")
        print(f"   Accuracy: {metrics.accuracy}")
        print(f"   2+1 hit rate: {metrics.hit_rate_2_plus_1}")
        print(f"   Stability: {metrics.stability}")
        print(f"   Confidence: {metrics.confidence}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Weight configuration structure test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_integration_basic():
    """测试权重集成基本功能"""
    print("\n" + "=" * 60)
    print("Test 3: Weight Integration Basic Functions")
    print("=" * 60)
    
    try:
        from src.integrations.weight_integration_adapter import WeightIntegrationAdapter
        
        # 创建集成适配器（禁用学习和优化以避免numpy依赖）
        adapter = WeightIntegrationAdapter(enable_learning=False, enable_optimization=False)
        
        print("[PASS] Weight integration adapter created successfully")
        
        # 获取集成权重
        weights = adapter.get_integrated_weights()
        total_weights = sum(len(category_weights) for category_weights in weights.values())
        print(f"[PASS] Integrated weights retrieved: {total_weights} weights")
        
        for category, category_weights in weights.items():
            print(f"   {category}: {len(category_weights)} weights")
        
        # 验证权重结构
        assert 'ensemble' in weights, "Missing ensemble weights"
        assert 'feature' in weights, "Missing feature weights"
        assert 'model' in weights, "Missing model weights"
        assert 'loss' in weights, "Missing loss weights"
        
        print("[PASS] Weight structure validation passed")
        
        # 模拟性能反馈
        adapter.update_performance_feedback(0.35, accuracy=0.8)
        print("[PASS] Performance feedback update successful")
        
        # 获取状态
        status = adapter.get_integration_status()
        print("[PASS] Integration status retrieved successfully")
        print(f"   Prediction count: {status['performance_tracking']['prediction_count']}")
        print(f"   Performance buffer size: {status['performance_tracking']['performance_buffer_size']}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Weight integration basic functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_categories():
    """测试权重分类功能"""
    print("\n" + "=" * 60)
    print("Test 4: Weight Categories Functions")
    print("=" * 60)
    
    try:
        from src.integrations.weight_integration_adapter import WeightIntegrationAdapter
        
        adapter = WeightIntegrationAdapter(enable_learning=False, enable_optimization=False)
        
        # 验证权重分类映射
        expected_categories = {
            'ensemble': ['bayesian', 'markov1', 'markov2', 'frequency', 'trend', 'correlation'],
            'model': ['markov', 'bayes', 'neural'],
            'feature': ['red_odd_even', 'red_size', 'blue_size'],
            'loss': ['red_odd_even_loss', 'red_size_loss', 'blue_size_loss', 'red_numbers_loss', 'blue_numbers_loss']
        }
        
        weights = adapter.get_integrated_weights()
        
        for category, expected_weights in expected_categories.items():
            actual_weights = weights.get(category, {})
            print(f"   {category} category:")
            print(f"     Expected weights: {len(expected_weights)}")
            print(f"     Actual weights: {len(actual_weights)}")
            
            # 检查关键权重是否存在
            for weight_name in expected_weights:
                if weight_name in actual_weights:
                    print(f"     [OK] {weight_name}: {actual_weights[weight_name]:.3f}")
                else:
                    print(f"     [WARN] {weight_name}: not found")
        
        print("[PASS] Weight categories functions validation completed")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Weight categories functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_tracking():
    """测试性能跟踪功能"""
    print("\n" + "=" * 60)
    print("Test 5: Performance Tracking Functions")
    print("=" * 60)
    
    try:
        from src.integrations.weight_integration_adapter import WeightIntegrationAdapter
        
        adapter = WeightIntegrationAdapter(enable_learning=False, enable_optimization=False)
        
        print("Simulating multiple performance feedbacks...")
        
        # 模拟多次性能反馈
        test_performances = [0.25, 0.30, 0.28, 0.35, 0.32, 0.38, 0.36, 0.40]
        
        for i, performance in enumerate(test_performances):
            adapter.update_performance_feedback(
                hit_rate_2_plus_1=performance,
                accuracy=performance * 0.9,
                additional_metrics={
                    'prediction_consistency': 0.8,
                    'model_agreement': 0.75
                }
            )
            print(f"   Round {i+1}: 2+1 hit rate={performance:.3f}")
        
        # 获取最终状态
        status = adapter.get_integration_status()
        
        print("[PASS] Performance tracking validation successful")
        print(f"   Total predictions: {status['performance_tracking']['prediction_count']}")
        print(f"   Current performance: {status['performance_tracking'].get('current_performance', 0):.3f}")
        print(f"   Average performance: {status['performance_tracking'].get('average_performance', 0):.3f}")
        print(f"   Performance trend: {status['performance_tracking'].get('performance_trend', 'unknown')}")
        
        # 验证性能数据
        assert status['performance_tracking']['prediction_count'] == len(test_performances)
        assert status['performance_tracking']['performance_buffer_size'] > 0
        
        print("[PASS] Performance data validation passed")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Performance tracking functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_weights():
    """测试后备权重功能"""
    print("\n" + "=" * 60)
    print("Test 6: Fallback Weights Functions")
    print("=" * 60)
    
    try:
        from src.integrations.weight_integration_adapter import WeightIntegrationAdapter
        
        adapter = WeightIntegrationAdapter(enable_learning=False, enable_optimization=False)
        
        # 获取后备权重
        fallback_weights = adapter._get_fallback_weights()
        
        print("[PASS] Fallback weights retrieved successfully")
        
        # 验证后备权重结构
        required_categories = ['ensemble', 'model', 'feature', 'loss']
        for category in required_categories:
            assert category in fallback_weights, f"Missing {category} fallback weights"
            assert len(fallback_weights[category]) > 0, f"{category} fallback weights are empty"
            print(f"   {category}: {len(fallback_weights[category])} weights")
        
        # 验证关键权重
        feature_weights = fallback_weights['feature']
        assert 'red_odd_even' in feature_weights, "Missing red ball odd/even fallback weight"
        assert 'red_size' in feature_weights, "Missing red ball size fallback weight"
        assert 'blue_size' in feature_weights, "Missing blue ball size fallback weight"
        
        print("[PASS] Fallback weights structure validation passed")
        print(f"   Red ball odd/even weight: {feature_weights['red_odd_even']}")
        print(f"   Red ball size weight: {feature_weights['red_size']}")
        print(f"   Blue ball size weight: {feature_weights['blue_size']}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Fallback weights functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主验证函数"""
    print("Phase 3 Weight System Reconstruction Simplified Validation")
    print("=" * 80)
    print("Validating core functions of intelligent weight management system (no numpy):")
    print("1. Weight Manager Basic Functions")
    print("2. Weight Configuration Structure")
    print("3. Weight Integration Basic Functions")
    print("4. Weight Categories Functions")
    print("5. Performance Tracking Functions")
    print("6. Fallback Weights Functions")
    
    # 执行所有测试
    tests = [
        ("Weight Manager Basic", test_weight_manager_basic),
        ("Weight Config Structure", test_weight_config_structure),
        ("Weight Integration Basic", test_weight_integration_basic),
        ("Weight Categories", test_weight_categories),
        ("Performance Tracking", test_performance_tracking),
        ("Fallback Weights", test_fallback_weights)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"[ERROR] {test_name} test exception: {e}")
            results.append((test_name, False))
    
    # 总结报告
    print("\n" + "=" * 80)
    print("Phase 3 Simplified Validation Summary Report")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{test_name:25} : {status}")
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n[SUCCESS] Phase 3 Weight System Reconstruction Basic Functions Validated!")
        print("\nCore Functions Validated:")
        print("[PASS] Intelligent Weight Management - Unified weight configuration management")
        print("[PASS] Weight Categories - Organized weights by categories")
        print("[PASS] Performance Tracking - Record and analyze performance data")
        print("[PASS] System Integration - Basic integration functions working")
        print("[PASS] Fallback Mechanism - Reliable fallback weights provided")
        print("\nPhase 3 basic functions validated, ready for next integration!")
        print("\nNote: Advanced functions (optimizer and adaptive learning) require numpy support")
    else:
        print(f"\n[WARNING] Phase 3 validation partially failed ({passed}/{total})")
        print("Need to fix failed components before integration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)