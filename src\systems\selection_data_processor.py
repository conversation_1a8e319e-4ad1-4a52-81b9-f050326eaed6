#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selection Data Processor - 选号系统数据处理器
为深度学习和传统机器学习提供统一的数据预处理
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import logging
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')


class DataProcessor:
    """选号系统数据处理器"""
    
    def __init__(self, config):
        """
        初始化数据处理器
        
        Args:
            config: 系统配置
        """
        self.config = config
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")
        
        # 缩放器
        self.feature_scaler = StandardScaler()
        self.target_scaler = MinMaxScaler()
        
        # 编码器
        self.label_encoders = {}
        
        # 特征名称
        self.feature_names = []
        
        # 处理后的数据缓存
        self.processed_cache = {}
        
        self.logger.info("数据处理器初始化完成")
    
    def process(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        处理数据
        
        Args:
            data: 原始数据
            
        Returns:
            Dict: 处理后的数据字典
        """
        self.logger.info("开始数据处理...")
        
        # 数据清洗
        cleaned_data = self._clean_data(data)
        
        # 特征工程
        features = self._extract_features(cleaned_data)
        
        # 目标变量构建
        targets = self._build_targets(cleaned_data)
        
        # 序列数据构建（用于深度学习）
        sequences = self._build_sequences(features, targets)
        
        # 数据分割
        train_data, val_data, test_data = self._split_data(features, targets, sequences)
        
        # 特征缩放
        scaled_data = self._scale_features(train_data, val_data, test_data)
        
        processed_data = {
            "raw_data": cleaned_data,
            "features": features,
            "targets": targets,
            "sequences": sequences,
            "train_data": scaled_data["train"],
            "val_data": scaled_data["val"],
            "test_data": scaled_data["test"],
            "feature_names": self.feature_names,
            "scalers": {
                "feature_scaler": self.feature_scaler,
                "target_scaler": self.target_scaler
            }
        }
        
        self.processed_cache = processed_data
        self.logger.info("数据处理完成")
        
        return processed_data
    
    def _clean_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据清洗"""
        self.logger.info("开始数据清洗...")
        
        # 复制数据
        cleaned = data.copy()
        
        # 排序（确保时间顺序）
        if '期号' in cleaned.columns:
            cleaned = cleaned.sort_values('期号', ascending=False)
        
        # 移除重复行
        cleaned = cleaned.drop_duplicates()
        
        # 检查必要列
        required_cols = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        missing_cols = [col for col in required_cols if col not in cleaned.columns]
        if missing_cols:
            raise ValueError(f"缺少必要列: {missing_cols}")
        
        # 数据类型转换
        for col in ['红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']:
            cleaned[col] = pd.to_numeric(cleaned[col], errors='coerce')
        
        # 移除无效行
        cleaned = cleaned.dropna(subset=required_cols)
        
        # 数据验证
        red_cols = ['红球1', '红球2', '红球3', '红球4', '红球5']
        blue_cols = ['蓝球1', '蓝球2']
        
        # 红球范围检查 (1-35)
        for col in red_cols:
            cleaned = cleaned[(cleaned[col] >= 1) & (cleaned[col] <= 35)]
        
        # 蓝球范围检查 (1-12)
        for col in blue_cols:
            cleaned = cleaned[(cleaned[col] >= 1) & (cleaned[col] <= 12)]
        
        self.logger.info(f"数据清洗完成，保留 {len(cleaned)} 条记录")
        return cleaned
    
    def _extract_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """特征工程"""
        self.logger.info("开始特征提取...")
        
        features = pd.DataFrame()
        
        # 基础特征
        red_cols = ['红球1', '红球2', '红球3', '红球4', '红球5']
        blue_cols = ['蓝球1', '蓝球2']
        
        # 1. 号码特征
        for i, col in enumerate(red_cols):
            features[f'red_{i+1}'] = data[col]
        
        for i, col in enumerate(blue_cols):
            features[f'blue_{i+1}'] = data[col]
        
        # 2. 统计特征
        features['red_sum'] = data[red_cols].sum(axis=1)
        features['red_mean'] = data[red_cols].mean(axis=1)
        features['red_std'] = data[red_cols].std(axis=1)
        features['red_range'] = data[red_cols].max(axis=1) - data[red_cols].min(axis=1)
        
        features['blue_sum'] = data[blue_cols].sum(axis=1)
        features['blue_mean'] = data[blue_cols].mean(axis=1)
        
        # 3. 比例特征
        # 奇偶比
        red_odd_count = data[red_cols].apply(lambda x: (x % 2 == 1).sum(), axis=1)
        features['red_odd_ratio'] = red_odd_count / 5
        
        blue_odd_count = data[blue_cols].apply(lambda x: (x % 2 == 1).sum(), axis=1)
        features['blue_odd_ratio'] = blue_odd_count / 2
        
        # 大小比 (红球: >17为大, 蓝球: >6为大)
        red_large_count = data[red_cols].apply(lambda x: (x > 17).sum(), axis=1)
        features['red_large_ratio'] = red_large_count / 5
        
        blue_large_count = data[blue_cols].apply(lambda x: (x > 6).sum(), axis=1)
        features['blue_large_ratio'] = blue_large_count / 2
        
        # 4. 区间分布特征
        # 红球区间 (1-7, 8-14, 15-21, 22-28, 29-35)
        for i in range(5):
            start = i * 7 + 1
            end = (i + 1) * 7
            if i == 4:  # 最后一个区间
                end = 35
            
            zone_count = data[red_cols].apply(
                lambda x: ((x >= start) & (x <= end)).sum(), axis=1
            )
            features[f'red_zone_{i+1}_count'] = zone_count
        
        # 5. 连号特征
        features['red_consecutive'] = self._count_consecutive(data[red_cols])
        
        # 6. 历史统计特征（滑动窗口）
        window_sizes = [3, 5, 10]
        for window in window_sizes:
            # 历史平均
            features[f'red_sum_ma_{window}'] = features['red_sum'].rolling(window).mean()
            features[f'red_odd_ratio_ma_{window}'] = features['red_odd_ratio'].rolling(window).mean()
            features[f'red_large_ratio_ma_{window}'] = features['red_large_ratio'].rolling(window).mean()
            
            # 历史标准差
            features[f'red_sum_std_{window}'] = features['red_sum'].rolling(window).std()
        
        # 7. 时间特征（如果有日期信息）
        if '开奖日期' in data.columns:
            try:
                dates = pd.to_datetime(data['开奖日期'])
                features['day_of_week'] = dates.dt.dayofweek
                features['month'] = dates.dt.month
                features['quarter'] = dates.dt.quarter
            except:
                pass
        
        # 填充缺失值
        features = features.fillna(method='bfill').fillna(0)
        
        # 保存特征名称
        self.feature_names = features.columns.tolist()
        
        self.logger.info(f"特征提取完成，共 {len(self.feature_names)} 个特征")
        return features
    
    def _count_consecutive(self, red_data: pd.DataFrame) -> pd.Series:
        """计算连号数量"""
        def count_consecutive_in_row(row):
            sorted_nums = sorted(row)
            consecutive_count = 0
            current_consecutive = 1
            
            for i in range(1, len(sorted_nums)):
                if sorted_nums[i] == sorted_nums[i-1] + 1:
                    current_consecutive += 1
                else:
                    if current_consecutive >= 2:
                        consecutive_count += current_consecutive
                    current_consecutive = 1
            
            if current_consecutive >= 2:
                consecutive_count += current_consecutive
            
            return consecutive_count
        
        return red_data.apply(count_consecutive_in_row, axis=1)
    
    def _build_targets(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """构建目标变量（多标签分类格式）"""
        self.logger.info("构建目标变量...")
        
        targets = {}
        
        # 红球目标 - 转换为35维多标签格式
        red_cols = ['红球1', '红球2', '红球3', '红球4', '红球5']
        red_targets = np.zeros((len(data), 35))  # 35个红球位置
        
        for i, row in data.iterrows():
            for col in red_cols:
                ball_num = int(row[col]) - 1  # 转换为0-based索引
                if 0 <= ball_num < 35:
                    red_targets[i, ball_num] = 1.0
        
        targets['red_balls'] = red_targets
        
        # 蓝球目标 - 转换为12维多标签格式
        blue_cols = ['蓝球1', '蓝球2']
        blue_targets = np.zeros((len(data), 12))  # 12个蓝球位置
        
        for i, row in data.iterrows():
            for col in blue_cols:
                ball_num = int(row[col]) - 1  # 转换为0-based索引
                if 0 <= ball_num < 12:
                    blue_targets[i, ball_num] = 1.0
        
        targets['blue_balls'] = blue_targets
        
        # 比例目标（保持原有格式）
        red_odd_count = data[red_cols].apply(lambda x: (x % 2 == 1).sum(), axis=1)
        targets['red_odd_even_ratio'] = red_odd_count.values
        
        red_large_count = data[red_cols].apply(lambda x: (x > 17).sum(), axis=1)
        targets['red_size_ratio'] = red_large_count.values
        
        blue_large_count = data[blue_cols].apply(lambda x: (x > 6).sum(), axis=1)
        targets['blue_size_ratio'] = blue_large_count.values
        
        self.logger.info(f"目标构建完成 - 红球目标形状: {targets['red_balls'].shape}, 蓝球目标形状: {targets['blue_balls'].shape}")
        
        return targets
    
    def _build_sequences(self, features: pd.DataFrame, targets: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """构建序列数据（用于深度学习）"""
        self.logger.info("构建序列数据...")
        
        seq_length = self.config.sequence_length
        
        # 特征序列
        feature_sequences = []
        target_sequences = {}
        
        for key in targets.keys():
            target_sequences[key] = []
        
        for i in range(seq_length, len(features)):
            # 特征序列
            feature_seq = features.iloc[i-seq_length:i].values
            feature_sequences.append(feature_seq)
            
            # 目标序列
            for key, target_data in targets.items():
                target_sequences[key].append(target_data[i])
        
        sequences = {
            'features': np.array(feature_sequences),
            'targets': {key: np.array(vals) for key, vals in target_sequences.items()}
        }
        
        self.logger.info(f"序列数据构建完成，序列长度: {seq_length}")
        return sequences
    
    def _split_data(self, features: pd.DataFrame, targets: Dict[str, np.ndarray], 
                   sequences: Dict[str, Any]) -> Tuple[Dict, Dict, Dict]:
        """数据分割"""
        self.logger.info("开始数据分割...")
        
        n_samples = len(features)
        train_size = int(n_samples * self.config.train_ratio)
        val_size = int(n_samples * self.config.validation_ratio)
        
        # 传统ML数据分割
        train_features = features.iloc[:train_size]
        val_features = features.iloc[train_size:train_size+val_size]
        test_features = features.iloc[train_size+val_size:]
        
        train_targets = {key: vals[:train_size] for key, vals in targets.items()}
        val_targets = {key: vals[train_size:train_size+val_size] for key, vals in targets.items()}
        test_targets = {key: vals[train_size+val_size:] for key, vals in targets.items()}
        
        # 深度学习序列数据分割
        seq_train_size = int(len(sequences['features']) * self.config.train_ratio)
        seq_val_size = int(len(sequences['features']) * self.config.validation_ratio)
        
        train_sequences = {
            'features': sequences['features'][:seq_train_size],
            'targets': {key: vals[:seq_train_size] for key, vals in sequences['targets'].items()}
        }
        
        val_sequences = {
            'features': sequences['features'][seq_train_size:seq_train_size+seq_val_size],
            'targets': {key: vals[seq_train_size:seq_train_size+seq_val_size] 
                       for key, vals in sequences['targets'].items()}
        }
        
        test_sequences = {
            'features': sequences['features'][seq_train_size+seq_val_size:],
            'targets': {key: vals[seq_train_size+seq_val_size:] 
                       for key, vals in sequences['targets'].items()}
        }
        
        train_data = {
            'features': train_features,
            'targets': train_targets,
            'sequences': train_sequences
        }
        
        val_data = {
            'features': val_features,
            'targets': val_targets,
            'sequences': val_sequences
        }
        
        test_data = {
            'features': test_features,
            'targets': test_targets,
            'sequences': test_sequences
        }
        
        self.logger.info(f"数据分割完成 - 训练集: {len(train_features)}, 验证集: {len(val_features)}, 测试集: {len(test_features)}")
        
        return train_data, val_data, test_data
    
    def _scale_features(self, train_data: Dict, val_data: Dict, test_data: Dict) -> Dict[str, Dict]:
        """特征缩放"""
        self.logger.info("开始特征缩放...")
        
        # 拟合缩放器
        self.feature_scaler.fit(train_data['features'])
        
        # 缩放特征
        train_data['features_scaled'] = pd.DataFrame(
            self.feature_scaler.transform(train_data['features']),
            columns=train_data['features'].columns,
            index=train_data['features'].index
        )
        
        val_data['features_scaled'] = pd.DataFrame(
            self.feature_scaler.transform(val_data['features']),
            columns=val_data['features'].columns,
            index=val_data['features'].index
        )
        
        test_data['features_scaled'] = pd.DataFrame(
            self.feature_scaler.transform(test_data['features']),
            columns=test_data['features'].columns,
            index=test_data['features'].index
        )
        
        # 缩放序列特征
        for dataset in [train_data, val_data, test_data]:
            if 'sequences' in dataset and len(dataset['sequences']['features']) > 0:
                original_shape = dataset['sequences']['features'].shape
                flattened = dataset['sequences']['features'].reshape(-1, original_shape[-1])
                scaled_flattened = self.feature_scaler.transform(flattened)
                dataset['sequences']['features_scaled'] = scaled_flattened.reshape(original_shape)
        
        self.logger.info("特征缩放完成")
        
        return {
            'train': train_data,
            'val': val_data,
            'test': test_data
        }
