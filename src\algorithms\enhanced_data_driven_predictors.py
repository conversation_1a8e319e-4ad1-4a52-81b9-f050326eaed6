#!/usr/bin/env python3
"""
增强版数据驱动预测器
专门为红球奇偶比预测优化的高级算法
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import Counter
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers,
    calculate_odd_even_ratio,
    ratio_to_state,
    get_all_red_odd_even_states,
)
from src.analysis.advanced_feature_engineer import AdvancedFeatureEngineer


class EnhancedHistoricalFrequencyPredictor:
    """增强版历史频率预测器"""

    def __init__(self):
        """初始化预测器"""
        self.logger = logging.getLogger(__name__)
        self.name = "enhanced_historical_frequency_predictor"
        self.feature_engineer = AdvancedFeatureEngineer()

    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """增强版频率预测"""
        if ratio_type != "odd_even":
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "unsupported_type",
            }

        if len(data) < 20:
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "insufficient_data",
            }

        # 提取高级特征
        features = self.feature_engineer.extract_all_features(data)

        # 准备状态序列
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)

        # 时间衰减权重分析
        decay_scores = self._calculate_decay_weighted_scores(states)

        # 多时间窗口分析
        window_scores = self._calculate_multi_window_scores(states)

        # 趋势强度分析
        trend_scores = self._calculate_trend_scores(features, states)

        # 稳定性分析
        stability_scores = self._calculate_stability_scores(states)

        # 综合评分
        final_scores = {}
        for state in get_all_red_odd_even_states():
            # 加权组合各种分析结果
            final_scores[state] = (
                decay_scores.get(state, 0) * 0.35  # 时间衰减权重
                + window_scores.get(state, 0) * 0.25  # 多窗口分析
                + trend_scores.get(state, 0) * 0.25  # 趋势分析
                + stability_scores.get(state, 0) * 0.15  # 稳定性分析
            )

        # 选择最佳预测
        if final_scores:
            best_state = max(final_scores.items(), key=lambda x: x[1])[0]
            confidence = min(final_scores[best_state], 0.9)

            return {
                "prediction": best_state,
                "confidence": confidence,
                "method": "enhanced_frequency_multi_factor",
            }

        return {"prediction": "3:2", "confidence": 0.4, "method": "fallback"}

    def _calculate_decay_weighted_scores(self, states: List[str]) -> Dict[str, float]:
        """计算时间衰减权重分数"""
        if not states:
            return {}

        # 指数衰减权重
        weights = np.exp(-0.03 * np.arange(len(states))[::-1])

        weighted_counts = {}
        total_weight = 0

        for state, weight in zip(states, weights):
            weighted_counts[state] = weighted_counts.get(state, 0) + weight
            total_weight += weight

        # 归一化
        return {state: count / total_weight for state, count in weighted_counts.items()}

    def _calculate_multi_window_scores(self, states: List[str]) -> Dict[str, float]:
        """计算多时间窗口分数"""
        windows = [5, 10, 20, 50]
        window_weights = [0.4, 0.3, 0.2, 0.1]  # 近期窗口权重更高

        combined_scores = {}

        for window, weight in zip(windows, window_weights):
            if len(states) >= window:
                window_states = states[-window:]
                window_counts = Counter(window_states)
                window_total = len(window_states)

                for state in get_all_red_states():
                    freq = window_counts.get(state, 0) / window_total
                    combined_scores[state] = (
                        combined_scores.get(state, 0) + freq * weight
                    )

        return combined_scores

    def _calculate_trend_scores(
        self, features: Dict[str, Any], states: List[str]
    ) -> Dict[str, float]:
        """计算趋势分数"""
        trend_scores = {}

        # 基于趋势方向和强度
        trend_direction = features.get("trend_direction", "stable")
        trend_strength = features.get("trend_strength", 0.0)

        if trend_direction == "increasing" and trend_strength > 0.2:
            # 上升趋势，偏向高奇数比
            trend_scores = {
                "5:0": 0.3,
                "4:1": 0.25,
                "3:2": 0.2,
                "2:3": 0.15,
                "1:4": 0.08,
                "0:5": 0.02,
            }
        elif trend_direction == "decreasing" and trend_strength > 0.2:
            # 下降趋势，偏向低奇数比
            trend_scores = {
                "0:5": 0.3,
                "1:4": 0.25,
                "2:3": 0.2,
                "3:2": 0.15,
                "4:1": 0.08,
                "5:0": 0.02,
            }
        else:
            # 稳定趋势，基于历史分布
            recent_states = states[-20:] if len(states) >= 20 else states
            recent_counts = Counter(recent_states)
            total = len(recent_states)
            trend_scores = {
                state: count / total for state, count in recent_counts.items()
            }

        # 应用趋势强度
        for state in trend_scores:
            trend_scores[state] *= 1 + trend_strength

        return trend_scores

    def _calculate_stability_scores(self, states: List[str]) -> Dict[str, float]:
        """计算稳定性分数"""
        if len(states) < 10:
            return {}

        stability_scores = {}

        # 计算每个状态的稳定性
        for state in get_all_red_odd_even_states():
            # 分段分析稳定性
            segments = []
            segment_size = max(5, len(states) // 5)

            for i in range(0, len(states), segment_size):
                segment = states[i : i + segment_size]
                if len(segment) >= 3:
                    freq = segment.count(state) / len(segment)
                    segments.append(freq)

            if len(segments) >= 2:
                # 稳定性 = 1 - 归一化方差
                variance = np.var(segments)
                max_variance = 0.25  # 理论最大方差
                stability = max(0, 1 - variance / max_variance)

                # 结合当前频率
                current_freq = states.count(state) / len(states)
                stability_scores[state] = stability * current_freq
            else:
                stability_scores[state] = 0.0

        return stability_scores


class EnhancedTransitionPatternPredictor:
    """增强版状态转移预测器"""

    def __init__(self):
        """初始化预测器"""
        self.logger = logging.getLogger(__name__)
        self.name = "enhanced_transition_pattern_predictor"
        self.feature_engineer = AdvancedFeatureEngineer()

    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """增强版转移预测"""
        if ratio_type != "odd_even":
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "unsupported_type",
            }

        if len(data) < 10:
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "insufficient_data",
            }

        # 提取高级特征
        features = self.feature_engineer.extract_all_features(data)

        # 准备状态序列
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)

        # 多阶马尔科夫链预测
        predictions = []

        # 2阶马尔科夫链
        if len(states) >= 3:
            markov_2nd = features.get("markov_2nd_order", {})
            if markov_2nd:
                current_context = tuple(states[-2:])
                if current_context in markov_2nd:
                    transitions = markov_2nd[current_context]
                    best_next = max(transitions.items(), key=lambda x: x[1])
                    predictions.append(
                        {
                            "state": best_next[0],
                            "confidence": best_next[1],
                            "method": "2nd_order_markov",
                        }
                    )

        # 1阶马尔科夫链
        if len(states) >= 2:
            markov_1st = features.get("markov_1st_order", {})
            if markov_1st:
                current_state = states[-1]
                if current_state in markov_1st:
                    transitions = markov_1st[current_state]
                    best_next = max(transitions.items(), key=lambda x: x[1])
                    predictions.append(
                        {
                            "state": best_next[0],
                            "confidence": best_next[1] * 0.8,  # 降低置信度
                            "method": "1st_order_markov",
                        }
                    )

        # 模式识别预测
        pattern_prediction = self._pattern_based_prediction(states, features)
        if pattern_prediction:
            predictions.append(pattern_prediction)

        # 选择最佳预测
        if predictions:
            best_prediction = max(predictions, key=lambda x: x["confidence"])
            return {
                "prediction": best_prediction["state"],
                "confidence": min(best_prediction["confidence"], 0.85),
                "method": f"enhanced_transition_{best_prediction['method']}",
            }

        return {"prediction": "3:2", "confidence": 0.4, "method": "fallback"}

    def _pattern_based_prediction(
        self, states: List[str], features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """基于模式的预测"""
        if len(states) < 4:
            return None

        recent_states = states[-4:]

        # 检测反转模式
        reversal_patterns = features.get("reversal_patterns", {})
        if len(recent_states) >= 3:
            if (
                recent_states[-3] == recent_states[-1]
                and recent_states[-3] != recent_states[-2]
            ):
                predicted_state = recent_states[-2]
                pattern_key = (
                    f"{recent_states[-3]}->{recent_states[-2]}->{recent_states[-1]}"
                )
                confidence = reversal_patterns.get(pattern_key, 0.1) * 2

                return {
                    "state": predicted_state,
                    "confidence": min(confidence, 0.7),
                    "method": "reversal_pattern",
                }

        # 检测震荡模式
        oscillation_patterns = features.get("oscillation_patterns", {})
        if len(recent_states) >= 4:
            if (
                recent_states[-4] == recent_states[-2]
                and recent_states[-3] == recent_states[-1]
                and recent_states[-4] != recent_states[-3]
            ):
                predicted_state = recent_states[-4]  # 继续震荡
                confidence = oscillation_patterns.get("ABAB", 0.1) * 3

                return {
                    "state": predicted_state,
                    "confidence": min(confidence, 0.6),
                    "method": "oscillation_pattern",
                }

        return None


class EnhancedCorrelationPredictor:
    """增强版关联特征预测器"""

    def __init__(self):
        """初始化预测器"""
        self.logger = logging.getLogger(__name__)
        self.name = "enhanced_correlation_predictor"
        self.feature_engineer = AdvancedFeatureEngineer()

    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """增强版关联预测"""
        if ratio_type != "odd_even":
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "unsupported_type",
            }

        if len(data) < 20:
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "insufficient_data",
            }

        # 提取高级特征
        features = self.feature_engineer.extract_all_features(data)

        # 多维度关联分析
        predictions = []

        # 与大小比的关联
        size_correlation_pred = self._predict_by_size_correlation(data, features)
        if size_correlation_pred:
            predictions.append(size_correlation_pred)

        # 与和值的关联
        sum_correlation_pred = self._predict_by_sum_correlation(data, features)
        if sum_correlation_pred:
            predictions.append(sum_correlation_pred)

        # 与跨度的关联
        span_correlation_pred = self._predict_by_span_correlation(data, features)
        if span_correlation_pred:
            predictions.append(span_correlation_pred)

        # 条件概率预测
        conditional_pred = self._predict_by_conditional_probability(data, features)
        if conditional_pred:
            predictions.append(conditional_pred)

        # 选择最佳预测
        if predictions:
            best_prediction = max(predictions, key=lambda x: x["confidence"])
            return {
                "prediction": best_prediction["state"],
                "confidence": min(best_prediction["confidence"], 0.8),
                "method": f"enhanced_correlation_{best_prediction['method']}",
            }

        return {"prediction": "3:2", "confidence": 0.4, "method": "fallback"}

    def _predict_by_size_correlation(
        self, data: pd.DataFrame, features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """基于大小比关联预测"""
        correlation = features.get("odd_even_size_correlation", 0.0)

        if abs(correlation) < 0.2:  # 关联度太低
            return None

        # 获取最新的大小比状态
        last_row = data.iloc[-1]
        red_balls, _ = parse_numbers(last_row)
        red_small = sum(1 for x in red_balls if x <= 17)
        red_large = sum(1 for x in red_balls if x >= 18)
        current_size_state = ratio_to_state((red_small, red_large))

        # 基于关联度预测
        state_order = {"0:5": 0, "1:4": 1, "2:3": 2, "3:2": 3, "4:1": 4, "5:0": 5}
        size_level = state_order.get(current_size_state, 2)

        if correlation > 0:  # 正相关
            predicted_level = size_level
        else:  # 负相关
            predicted_level = 5 - size_level

        predicted_level = max(0, min(5, predicted_level))
        odd_even_states = ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]
        predicted_state = odd_even_states[predicted_level]

        return {
            "state": predicted_state,
            "confidence": min(abs(correlation), 0.7),
            "method": "size_correlation",
        }

    def _predict_by_sum_correlation(
        self, data: pd.DataFrame, features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """基于和值关联预测"""
        correlation = features.get("odd_even_sum_correlation", 0.0)

        if abs(correlation) < 0.15:  # 关联度太低
            return None

        # 获取最新的和值
        last_row = data.iloc[-1]
        red_balls, _ = parse_numbers(last_row)
        current_sum = sum(red_balls)

        # 和值范围分级 (15-175)
        sum_ranges = [(15, 60), (61, 90), (91, 120), (121, 150), (151, 175)]
        sum_level = 2  # 默认中等

        for i, (min_sum, max_sum) in enumerate(sum_ranges):
            if min_sum <= current_sum <= max_sum:
                sum_level = i
                break

        # 基于关联度预测
        if correlation > 0:  # 正相关：和值高 -> 奇数比高
            predicted_level = sum_level
        else:  # 负相关：和值高 -> 奇数比低
            predicted_level = 4 - sum_level

        predicted_level = max(0, min(5, predicted_level))
        odd_even_states = ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]
        predicted_state = odd_even_states[predicted_level]

        return {
            "state": predicted_state,
            "confidence": min(abs(correlation) * 1.5, 0.6),
            "method": "sum_correlation",
        }

    def _predict_by_span_correlation(
        self, data: pd.DataFrame, features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """基于跨度关联预测"""
        correlation = features.get("odd_even_span_correlation", 0.0)

        if abs(correlation) < 0.15:  # 关联度太低
            return None

        # 获取最新的跨度
        last_row = data.iloc[-1]
        red_balls, _ = parse_numbers(last_row)
        current_span = max(red_balls) - min(red_balls)

        # 跨度范围分级 (0-34)
        span_ranges = [(0, 10), (11, 18), (19, 25), (26, 30), (31, 34)]
        span_level = 2  # 默认中等

        for i, (min_span, max_span) in enumerate(span_ranges):
            if min_span <= current_span <= max_span:
                span_level = i
                break

        # 基于关联度预测
        if correlation > 0:  # 正相关：跨度大 -> 奇数比高
            predicted_level = span_level
        else:  # 负相关：跨度大 -> 奇数比低
            predicted_level = 4 - span_level

        predicted_level = max(0, min(5, predicted_level))
        odd_even_states = ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]
        predicted_state = odd_even_states[predicted_level]

        return {
            "state": predicted_state,
            "confidence": min(abs(correlation) * 1.5, 0.6),
            "method": "span_correlation",
        }

    def _predict_by_conditional_probability(
        self, data: pd.DataFrame, features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """基于条件概率预测"""
        conditional_probs = features.get("conditional_probs", {})

        if not conditional_probs:
            return None

        # 获取最新的大小比状态作为条件
        last_row = data.iloc[-1]
        red_balls, _ = parse_numbers(last_row)
        red_small = sum(1 for x in red_balls if x <= 17)
        red_large = sum(1 for x in red_balls if x >= 18)
        current_size_state = ratio_to_state((red_small, red_large))

        if current_size_state in conditional_probs:
            size_to_odd_even = conditional_probs[current_size_state]
            if size_to_odd_even:
                best_odd_even = max(size_to_odd_even.items(), key=lambda x: x[1])
                return {
                    "state": best_odd_even[0],
                    "confidence": min(best_odd_even[1] * 1.2, 0.75),
                    "method": "conditional_probability",
                }

        return None


class EnhancedMultiTimeWindowPredictor:
    """增强版多时间窗口预测器"""

    def __init__(self):
        """初始化预测器"""
        self.logger = logging.getLogger(__name__)
        self.name = "enhanced_multi_time_window_predictor"
        self.feature_engineer = AdvancedFeatureEngineer()

    def predict_ratio(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, Any]:
        """增强版多时间窗口预测"""
        if ratio_type != "odd_even":
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "unsupported_type",
            }

        if len(data) < 20:
            return {
                "prediction": "3:2",
                "confidence": 0.3,
                "method": "insufficient_data",
            }

        # 提取高级特征
        features = self.feature_engineer.extract_all_features(data)

        # 准备状态序列
        states = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            state = ratio_to_state((red_odd, red_even))
            states.append(state)

        # 多时间窗口分析
        windows = [5, 10, 20, 50, 100]
        window_predictions = {}

        for window in windows:
            if len(states) >= window:
                window_pred = self._predict_for_window(states, window, features)
                if window_pred:
                    window_predictions[window] = window_pred

        # 智能权重分配
        window_weights = self._calculate_adaptive_weights(window_predictions, features)

        # 加权投票
        final_scores = {}
        total_weight = 0

        for window, prediction in window_predictions.items():
            weight = window_weights.get(window, 0)
            if weight > 0:
                state = prediction["state"]
                confidence = prediction["confidence"]
                score = weight * confidence

                final_scores[state] = final_scores.get(state, 0) + score
                total_weight += weight

        # 选择最佳预测
        if final_scores and total_weight > 0:
            best_state = max(final_scores.items(), key=lambda x: x[1])[0]
            final_confidence = min(final_scores[best_state] / total_weight, 0.85)

            return {
                "prediction": best_state,
                "confidence": final_confidence,
                "method": "enhanced_multi_window_adaptive",
            }

        return {"prediction": "3:2", "confidence": 0.4, "method": "fallback"}

    def _predict_for_window(
        self, states: List[str], window: int, features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """为特定时间窗口进行预测"""
        window_states = states[-window:]

        # 频率分析
        state_counts = Counter(window_states)
        total = len(window_states)

        # 趋势分析
        if window >= 10:
            mid_point = window // 2
            first_half = window_states[:mid_point]
            second_half = window_states[mid_point:]

            first_counts = Counter(first_half)
            second_counts = Counter(second_half)

            # 计算趋势调整
            trend_adjustments = {}
            for state in get_all_red_states():
                first_freq = first_counts.get(state, 0) / len(first_half)
                second_freq = second_counts.get(state, 0) / len(second_half)
                trend_adjustments[state] = second_freq - first_freq
        else:
            trend_adjustments = {}

        # 组合频率和趋势
        combined_scores = {}
        for state in get_all_red_odd_even_states():
            base_freq = state_counts.get(state, 0) / total
            trend_adj = trend_adjustments.get(state, 0)

            # 趋势权重根据窗口大小调整
            trend_weight = 0.3 if window >= 20 else 0.1
            combined_scores[state] = (
                base_freq * (1 - trend_weight) + trend_adj * trend_weight
            )

        # 选择最佳状态
        if combined_scores:
            best_state = max(combined_scores.items(), key=lambda x: x[1])[0]
            confidence = combined_scores[best_state]

            return {
                "state": best_state,
                "confidence": max(0.1, confidence),
                "method": f"window_{window}",
            }

        return None

    def _calculate_adaptive_weights(
        self, window_predictions: Dict[int, Dict[str, Any]], features: Dict[str, Any]
    ) -> Dict[int, float]:
        """计算自适应权重"""
        if not window_predictions:
            return {}

        weights = {}

        # 基础权重：近期窗口权重更高
        base_weights = {5: 0.3, 10: 0.25, 20: 0.2, 50: 0.15, 100: 0.1}

        # 根据数据特征调整权重
        trend_strength = features.get("trend_strength", 0.0)
        periodicity_strength = features.get("periodicity_strength", 0.0)

        for window in window_predictions.keys():
            base_weight = base_weights.get(window, 0.1)

            # 趋势强度调整
            if trend_strength > 0.3:
                if window <= 20:  # 强趋势时，更重视短期窗口
                    base_weight *= 1.2
                else:
                    base_weight *= 0.8

            # 周期性调整
            if periodicity_strength > 0.3:
                if window >= 20:  # 强周期性时，更重视长期窗口
                    base_weight *= 1.2
                else:
                    base_weight *= 0.8

            # 置信度调整
            prediction_confidence = window_predictions[window]["confidence"]
            base_weight *= 0.5 + prediction_confidence

            weights[window] = base_weight

        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {
                window: weight / total_weight for window, weight in weights.items()
            }

        return weights
