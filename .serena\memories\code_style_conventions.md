# 代码风格和约定

## Python编程规范
- 遵循PEP 8 Python代码风格指南
- 使用Python 3.10+的语法特性和最佳实践
- 合理使用面向对象编程(OOP)和函数式编程范式
- 使用类型提示(Type Hints)进行类型检查

## 项目结构规范
- **src布局**: 所有核心源代码放在`src/`目录内
- **测试分离**: 测试代码放在`tests/`目录，结构与`src`镜像
- **配置外部化**: 禁止硬编码配置，使用环境变量或配置文件
- **依赖声明**: 在`pyproject.toml`中明确声明所有依赖

## 命名约定
- 类名使用PascalCase（如`LotteryAnalyzer`）
- 函数和变量使用snake_case（如`calculate_odd_even_ratio`）
- 常量使用UPPER_CASE（如`DEFAULT_CONFIG`）
- 私有成员使用下划线前缀（如`_private_method`）

## 文档要求
- 编写详细的文档字符串(docstring)
- 重要函数和类必须有类型提示
- 复杂逻辑需要添加注释说明
- 保持README.md和项目文档更新

## 设计原则
- 高内聚、低耦合
- 确定性预测（避免随机性）
- 可扩展性（支持算法替换）
- 测试友好（每个组件可独立测试）
- 遵循DRY原则