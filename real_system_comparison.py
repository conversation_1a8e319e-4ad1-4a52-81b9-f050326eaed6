#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实选号系统对比分析
使用项目中现有的选号系统进行性能对比
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置路径
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/real_system_comparison.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RealSystemComparator:
    """真实系统对比器"""
    
    def __init__(self):
        """初始化对比器"""
        self.systems = {}
        self.results = {}
        self.data = None
        
        logger.info("真实系统对比器初始化")
    
    def load_data(self):
        """加载真实数据"""
        try:
            data_path = "data/raw/dlt_data.csv"
            if os.path.exists(data_path):
                self.data = pd.read_csv(data_path, encoding='utf-8')
                logger.info(f"成功加载数据: {len(self.data)}期")
            else:
                logger.warning("未找到真实数据文件，使用模拟数据")
                self.data = self._create_mock_data()
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            self.data = self._create_mock_data()
    
    def _create_mock_data(self, num_periods=200):
        """创建模拟数据"""
        data = []
        for i in range(num_periods):
            period = f"250{i:02d}"
            
            # 生成符合规律的开奖号码
            red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
            blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
            
            row = {
                '期号': period,
                '红球1': red_balls[0], '红球2': red_balls[1], '红球3': red_balls[2],
                '红球4': red_balls[3], '红球5': red_balls[4],
                '蓝球1': blue_balls[0], '蓝球2': blue_balls[1],
                '开奖日期': f"2025-{(i%12)+1:02d}-{(i%28)+1:02d}"
            }
            data.append(row)
        
        return pd.DataFrame(data)
    
    def initialize_systems(self):
        """初始化各个选号系统"""
        logger.info("初始化选号系统...")
        
        try:
            # 系统1: NumberSelectionAdapter (集成系统)
            from systems.number_selection_adapter import NumberSelectionAdapter, create_selection_adapter
            from systems.number_selection_system import SelectionMethod
            
            self.systems['ensemble'] = create_selection_adapter('optimized', SelectionMethod.ENSEMBLE)
            self.systems['markov'] = create_selection_adapter('basic', SelectionMethod.MARKOV)
            self.systems['frequency'] = create_selection_adapter('basic', SelectionMethod.FREQUENCY)
            self.systems['ml'] = create_selection_adapter('basic', SelectionMethod.ML)
            
            logger.info("成功初始化项目选号系统")
            
        except Exception as e:
            logger.error(f"初始化项目系统失败: {e}")
            # 使用简化的测试系统
            self._initialize_simple_systems()
    
    def _initialize_simple_systems(self):
        """初始化简化测试系统"""
        logger.info("使用简化测试系统...")
        
        self.systems = {
            'random': SimpleRandomSelector(),
            'frequency': SimpleFrequencySelector(),
            'markov': SimpleMarkovSelector(),
            'trend': SimpleTrendSelector()
        }
    
    def run_comparison(self, test_periods=30):
        """运行对比测试"""
        logger.info(f"开始对比测试，测试{test_periods}期...")
        
        if self.data is None:
            self.load_data()
        
        if len(self.data) < test_periods + 50:
            logger.warning("数据不足，重新生成更多数据")
            self.data = self._create_mock_data(test_periods + 100)
        
        # 为每个系统运行测试
        for system_name, system in self.systems.items():
            logger.info(f"测试系统: {system_name}")
            
            system_results = {
                'predictions': [],
                'actual_results': [],
                'hit_rates': [],
                'red_hit_rates': [],
                'blue_hit_rates': [],
                'periods': []
            }
            
            # 使用前面的数据作为训练，后面的数据作为测试
            train_end = len(self.data) - test_periods
            
            for i in range(test_periods):
                test_index = train_end + i
                if test_index >= len(self.data):
                    break
                
                # 获取训练数据
                train_data = self.data.iloc[:train_end + i]
                
                # 获取当期实际结果
                actual_row = self.data.iloc[test_index]
                actual_red = [actual_row[f'红球{j}'] for j in range(1, 6)]
                actual_blue = [actual_row[f'蓝球{j}'] for j in range(1, 3)]
                
                # 进行预测
                try:
                    if hasattr(system, 'predict_for_period'):
                        # 使用统一框架接口
                        result = system.predict_for_period(train_end + i - 1, self.data)
                        predicted_red = result.red_balls[:5]
                        predicted_blue = result.blue_balls[:2]
                    else:
                        # 使用简化接口
                        predicted_red, predicted_blue = system.predict(train_data, actual_row['期号'])
                    
                    # 计算命中率
                    red_hits = len(set(predicted_red) & set(actual_red))
                    blue_hits = len(set(predicted_blue) & set(actual_blue))
                    
                    red_hit_rate = red_hits / 5.0
                    blue_hit_rate = blue_hits / 2.0
                    total_hit_rate = (red_hits + blue_hits) / 7.0
                    
                    # 存储结果
                    system_results['predictions'].append({
                        'red_balls': predicted_red,
                        'blue_balls': predicted_blue
                    })
                    system_results['actual_results'].append({
                        'red_balls': actual_red,
                        'blue_balls': actual_blue
                    })
                    system_results['hit_rates'].append(total_hit_rate)
                    system_results['red_hit_rates'].append(red_hit_rate)
                    system_results['blue_hit_rates'].append(blue_hit_rate)
                    system_results['periods'].append(actual_row['期号'])
                    
                    logger.debug(f"期号{actual_row['期号']}: 总命中率{total_hit_rate:.3f}")
                    
                except Exception as e:
                    logger.error(f"系统{system_name}预测期号{actual_row['期号']}时出错: {e}")
                    continue
            
            self.results[system_name] = system_results
            
            # 计算系统总体性能
            if system_results['hit_rates']:
                avg_hit_rate = np.mean(system_results['hit_rates'])
                avg_red_hit_rate = np.mean(system_results['red_hit_rates'])
                avg_blue_hit_rate = np.mean(system_results['blue_hit_rates'])
                
                logger.info(f"系统{system_name}性能:")
                logger.info(f"  平均总命中率: {avg_hit_rate:.3f}")
                logger.info(f"  平均红球命中率: {avg_red_hit_rate:.3f}")
                logger.info(f"  平均蓝球命中率: {avg_blue_hit_rate:.3f}")
        
        logger.info("对比测试完成")
    
    def generate_report(self):
        """生成对比报告"""
        logger.info("生成对比报告...")
        
        if not self.results:
            logger.error("没有测试结果，无法生成报告")
            return
        
        # 计算各系统的综合指标
        summary_data = []
        
        for system_name, results in self.results.items():
            if not results['hit_rates']:
                continue
            
            metrics = {
                '系统名称': system_name,
                '平均总命中率': np.mean(results['hit_rates']),
                '平均红球命中率': np.mean(results['red_hit_rates']),
                '平均蓝球命中率': np.mean(results['blue_hit_rates']),
                '命中率标准差': np.std(results['hit_rates']),
                '最高命中率': np.max(results['hit_rates']),
                '最低命中率': np.min(results['hit_rates']),
                '稳定性指数': 1.0 - (np.std(results['hit_rates']) / (np.mean(results['hit_rates']) + 1e-8)),
                '测试期数': len(results['hit_rates'])
            }
            summary_data.append(metrics)
        
        # 创建汇总表
        summary_df = pd.DataFrame(summary_data)
        summary_df = summary_df.sort_values('平均总命中率', ascending=False)
        
        # 打印结果
        print("\n" + "="*80)
        print("真实选号系统性能对比结果")
        print("="*80)
        print(summary_df.to_string(index=False, float_format='%.3f'))
        
        # 找出最佳系统
        if len(summary_df) > 0:
            best_system = summary_df.iloc[0]
            print(f"\n最佳系统: {best_system['系统名称']}")
            print(f"平均总命中率: {best_system['平均总命中率']:.3f}")
            print(f"平均红球命中率: {best_system['平均红球命中率']:.3f}")
            print(f"平均蓝球命中率: {best_system['平均蓝球命中率']:.3f}")
            print(f"稳定性指数: {best_system['稳定性指数']:.3f}")
        
        return summary_df


# 简化的测试系统实现
class SimpleRandomSelector:
    """简单随机选号器"""
    
    def predict(self, train_data, period):
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        return red_balls, blue_balls


class SimpleFrequencySelector:
    """简单频率选号器"""
    
    def predict(self, train_data, period):
        # 统计红球频率
        red_freq = {}
        for i in range(1, 6):
            for ball in train_data[f'红球{i}']:
                red_freq[ball] = red_freq.get(ball, 0) + 1
        
        # 统计蓝球频率
        blue_freq = {}
        for i in range(1, 3):
            for ball in train_data[f'蓝球{i}']:
                blue_freq[ball] = blue_freq.get(ball, 0) + 1
        
        # 选择频率最高的号码
        red_sorted = sorted(red_freq.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_freq.items(), key=lambda x: x[1], reverse=True)
        
        red_balls = [ball for ball, _ in red_sorted[:5]]
        blue_balls = [ball for ball, _ in blue_sorted[:2]]
        
        # 补充不足的号码
        while len(red_balls) < 5:
            candidate = np.random.randint(1, 36)
            if candidate not in red_balls:
                red_balls.append(candidate)
        
        while len(blue_balls) < 2:
            candidate = np.random.randint(1, 13)
            if candidate not in blue_balls:
                blue_balls.append(candidate)
        
        return sorted(red_balls), sorted(blue_balls)


class SimpleMarkovSelector:
    """简单马尔可夫选号器"""
    
    def predict(self, train_data, period):
        if len(train_data) < 2:
            return SimpleRandomSelector().predict(train_data, period)
        
        # 获取最近的号码
        last_red = [train_data.iloc[-1][f'红球{i}'] for i in range(1, 6)]
        last_blue = [train_data.iloc[-1][f'蓝球{i}'] for i in range(1, 3)]
        
        # 基于最近号码进行调整
        red_balls = []
        for ball in last_red:
            adjustment = np.random.randint(-3, 4)
            new_ball = max(1, min(35, ball + adjustment))
            if new_ball not in red_balls:
                red_balls.append(new_ball)
        
        while len(red_balls) < 5:
            candidate = np.random.randint(1, 36)
            if candidate not in red_balls:
                red_balls.append(candidate)
        
        blue_balls = []
        for ball in last_blue:
            adjustment = np.random.randint(-2, 3)
            new_ball = max(1, min(12, ball + adjustment))
            if new_ball not in blue_balls:
                blue_balls.append(new_ball)
        
        while len(blue_balls) < 2:
            candidate = np.random.randint(1, 13)
            if candidate not in blue_balls:
                blue_balls.append(candidate)
        
        return sorted(red_balls), sorted(blue_balls)


class SimpleTrendSelector:
    """简单趋势选号器"""
    
    def predict(self, train_data, period):
        if len(train_data) < 5:
            return SimpleRandomSelector().predict(train_data, period)
        
        # 分析最近5期的趋势
        recent_data = train_data.tail(5)
        
        # 计算平均值
        red_avg = []
        for i in range(1, 6):
            avg = recent_data[f'红球{i}'].mean()
            red_avg.append(int(round(avg)))
        
        blue_avg = []
        for i in range(1, 3):
            avg = recent_data[f'蓝球{i}'].mean()
            blue_avg.append(int(round(avg)))
        
        # 添加随机扰动
        red_balls = []
        for avg in red_avg:
            adjustment = np.random.randint(-2, 3)
            new_ball = max(1, min(35, avg + adjustment))
            if new_ball not in red_balls:
                red_balls.append(new_ball)
        
        while len(red_balls) < 5:
            candidate = np.random.randint(1, 36)
            if candidate not in red_balls:
                red_balls.append(candidate)
        
        blue_balls = []
        for avg in blue_avg:
            adjustment = np.random.randint(-1, 2)
            new_ball = max(1, min(12, avg + adjustment))
            if new_ball not in blue_balls:
                blue_balls.append(new_ball)
        
        while len(blue_balls) < 2:
            candidate = np.random.randint(1, 13)
            if candidate not in blue_balls:
                blue_balls.append(candidate)
        
        return sorted(red_balls), sorted(blue_balls)


def main():
    """主函数"""
    print("="*80)
    print("真实选号系统性能对比分析")
    print("="*80)
    
    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)
    
    # 创建对比器
    comparator = RealSystemComparator()
    
    # 加载数据
    comparator.load_data()
    
    # 初始化系统
    comparator.initialize_systems()
    
    # 运行对比测试
    comparator.run_comparison(test_periods=30)
    
    # 生成报告
    summary_df = comparator.generate_report()
    
    print("\n分析完成！")
    
    return summary_df


if __name__ == "__main__":
    try:
        result = main()
    except Exception as e:
        logger.error(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()