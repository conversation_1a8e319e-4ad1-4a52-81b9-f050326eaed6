#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选号适配器测试
"""

import sys
import os
import time
import pandas as pd
import numpy as np

# 设置路径
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    data = []
    for i in range(50):
        period = f"250{i:02d}"
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        
        row = {
            '期号': period,
            '红球1': red_balls[0], '红球2': red_balls[1], '红球3': red_balls[2],
            '红球4': red_balls[3], '红球5': red_balls[4],
            '蓝球1': blue_balls[0], '蓝球2': blue_balls[1]
        }
        data.append(row)
    
    return pd.DataFrame(data)

def test_adapter():
    """测试适配器"""
    print("测试选号适配器...")
    
    try:
        # 尝试导入
        from systems.number_selection_adapter import NumberSelectionAdapter, create_selection_adapter
        
        test_data = create_test_data()
        adapter = create_selection_adapter(optimized=True)
        
        data_index = len(test_data) - 1
        result = adapter.predict_for_period(data_index, test_data)
        
        print("测试成功:")
        print(f"   期号: {result.period_number}")
        print(f"   红球: {result.generated_numbers[0]}")
        print(f"   蓝球: {result.generated_numbers[1]}")
        print(f"   置信度: {result.confidence:.3f}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_adapter()
    print("测试完成!" if success else "测试失败!")