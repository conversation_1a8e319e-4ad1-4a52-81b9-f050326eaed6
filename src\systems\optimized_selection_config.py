"""
优化的选号系统配置
针对提升命中率进行的参数优化
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
import numpy as np


@dataclass
class OptimizedSelectionConfig:
    """优化的选号系统配置 - 针对命中率提升"""

    # ==================== 数据处理优化 ====================
    # 增加序列长度以捕捉更长期的模式
    sequence_length: int = 35  # 从20增加到35

    # 调整数据分割比例，增加训练数据
    train_ratio: float = 0.85  # 从0.8增加到0.85
    validation_ratio: float = 0.1

    # 特征工程优化
    enable_advanced_features: bool = True
    feature_selection_threshold: float = 0.01
    use_feature_scaling: bool = True

    # 数据增强
    enable_data_augmentation: bool = True
    augmentation_factor: float = 1.5

    # ==================== 深度学习优化 ====================
    # 训练参数优化
    dl_epochs: int = 200  # 从100增加到200
    dl_batch_size: int = 16  # 从32减少到16，更精细训练
    dl_initial_learning_rate: float = 0.002  # 从0.001增加到0.002

    # 学习率调度
    use_learning_rate_scheduler: bool = True
    lr_scheduler_factor: float = 0.5
    lr_scheduler_patience: int = 15
    lr_scheduler_min_lr: float = 1e-6

    # 早停机制
    use_early_stopping: bool = True
    early_stopping_patience: int = 25
    early_stopping_min_delta: float = 0.001

    # 模型架构优化
    lstm_config: Dict[str, Any] = field(
        default_factory=lambda: {
            "units": 256,  # 从128增加到256
            "layers": 3,  # 从2增加到3
            "dropout": 0.4,  # 从0.3增加到0.4
            "recurrent_dropout": 0.4,
            "use_batch_norm": True,  # 新增批量归一化
            "use_residual": True,  # 新增残差连接
            "activation": "tanh",
        }
    )

    transformer_config: Dict[str, Any] = field(
        default_factory=lambda: {
            "num_heads": 12,  # 从8增加到12
            "ff_dim": 512,  # 从256增加到512
            "num_layers": 6,  # 从4增加到6
            "dropout": 0.15,  # 从0.1增加到0.15
            "use_positional_encoding": True,
            "attention_dropout": 0.1,
        }
    )

    multi_task_config: Dict[str, Any] = field(
        default_factory=lambda: {
            "dense_units": 512,  # 从256增加到512
            "num_tasks": 7,  # 从5增加到7
            "dropout": 0.3,  # 从0.2增加到0.3
            "use_batch_norm": True,
            "activation": "relu",
        }
    )

    def __post_init__(self):
        if self.lstm_config is None:
            self.lstm_config = {
                "units": 256,  # 从128增加到256
                "layers": 3,  # 从2增加到3
                "dropout": 0.4,  # 从0.3增加到0.4
                "recurrent_dropout": 0.4,
                "use_batch_norm": True,  # 新增批量归一化
                "use_residual": True,  # 新增残差连接
                "activation": "tanh",
            }

        if self.transformer_config is None:
            self.transformer_config = {
                "num_heads": 12,  # 从8增加到12
                "ff_dim": 512,  # 从256增加到512
                "num_layers": 6,  # 从4增加到6
                "dropout": 0.15,  # 从0.1增加到0.15
                "use_positional_encoding": True,
                "attention_dropout": 0.1,
            }

        if self.multi_task_config is None:
            self.multi_task_config = {
                "dense_units": 512,  # 从256增加到512
                "num_tasks": 7,  # 从5增加到7
                "dropout": 0.3,  # 从0.2增加到0.3
                "use_batch_norm": True,
                "activation": "relu",
            }

    # ==================== 传统ML优化 ====================
    # 随机森林优化
    rf_n_estimators: int = 200  # 从100增加到200
    rf_max_depth: int = 15  # 从10增加到15
    rf_min_samples_split: int = 3
    rf_min_samples_leaf: int = 2
    rf_max_features: str = "sqrt"

    # 梯度提升优化
    gb_n_estimators: int = 200
    gb_learning_rate: float = 0.05  # 从默认降低学习率
    gb_max_depth: int = 8
    gb_subsample: float = 0.8
    gb_max_features: str = "sqrt"

    # 极端随机树优化
    et_n_estimators: int = 150
    et_max_depth: int = 12
    et_min_samples_split: int = 4

    # XGBoost配置（新增）
    use_xgboost: bool = True
    xgb_n_estimators: int = 200
    xgb_learning_rate: float = 0.1
    xgb_max_depth: int = 8
    xgb_subsample: float = 0.8
    xgb_colsample_bytree: float = 0.8

    # 兼容性配置（映射到传统ML参数）
    ml_n_estimators: int = 200  # 映射到rf_n_estimators
    ml_max_depth: int = 15  # 映射到rf_max_depth
    ml_random_state: int = 42

    # 兼容性配置（映射到深度学习参数）
    dl_learning_rate: float = 0.002  # 映射到dl_initial_learning_rate

    # ==================== 集成策略优化 ====================
    # 多级集成
    use_multi_level_ensemble: bool = True
    ensemble_levels: int = 3

    # 动态权重调整
    use_dynamic_weights: bool = True
    weight_update_frequency: int = 10

    # 置信度加权
    use_confidence_weighting: bool = True
    confidence_threshold: float = 0.6

    # 集成方法
    ensemble_methods: List[str] = field(
        default_factory=lambda: ["weighted_average", "voting", "stacking", "blending"]
    )

    # ==================== 评估优化 ====================
    # 回测配置
    backtest_periods: int = 100  # 从50增加到100
    cross_validation_folds: int = 10  # 从5增加到10

    # 评估指标
    evaluation_metrics: List[str] = field(
        default_factory=lambda: [
            "hit_rate",
            "partial_hit_rate",
            "roi",
            "stability",
            "consistency",
            "sharpe_ratio",
        ]
    )

    # ==================== 特征工程优化 ====================
    # 新增特征类型
    enable_fourier_features: bool = True
    enable_wavelet_features: bool = True
    enable_lag_features: bool = True
    enable_rolling_features: bool = True
    enable_interaction_features: bool = True

    # 特征选择
    feature_selection_method: str = "mutual_info"  # 'mutual_info', 'chi2', 'f_classif'
    max_features: int = 50  # 从35增加到50

    # 滚动窗口特征
    rolling_windows: List[int] = field(default_factory=lambda: [3, 5, 7, 10, 15, 20])

    # ==================== 正则化优化 ====================
    # L1/L2正则化
    use_l1_regularization: bool = True
    l1_alpha: float = 0.001

    use_l2_regularization: bool = True
    l2_alpha: float = 0.01

    # Dropout优化
    adaptive_dropout: bool = True
    dropout_schedule: Dict[str, float] = field(
        default_factory=lambda: {"initial": 0.5, "middle": 0.3, "final": 0.2}
    )

    # ==================== 输出配置 ====================
    save_models: bool = True
    save_predictions: bool = True
    generate_detailed_report: bool = True
    create_visualizations: bool = True
    verbose: bool = True

    # 随机种子
    random_state: int = 42

    def get_optimization_summary(self) -> Dict[str, str]:
        """获取优化摘要"""
        return {
            "数据处理": f"序列长度: {self.sequence_length} (+75%), 训练比例: {self.train_ratio} (+6.25%)",
            "深度学习": f"训练轮数: {self.dl_epochs} (+100%), LSTM单元: {self.lstm_config['units']} (+100%)",
            "传统ML": f"随机森林: {self.rf_n_estimators} (+100%), 梯度提升: {self.gb_n_estimators} (+100%)",
            "集成策略": f"多级集成: {self.use_multi_level_ensemble}, 动态权重: {self.use_dynamic_weights}",
            "特征工程": f"最大特征数: {self.max_features} (+43%), 高级特征: {self.enable_advanced_features}",
            "评估": f"回测期数: {self.backtest_periods} (+100%), 交叉验证: {self.cross_validation_folds} (+100%)",
        }


# 预定义的优化配置
AGGRESSIVE_CONFIG = OptimizedSelectionConfig(
    sequence_length=50,
    dl_epochs=300,
    lstm_config={
        "units": 512,
        "layers": 4,
        "dropout": 0.5,
        "recurrent_dropout": 0.5,
        "use_batch_norm": True,
        "use_residual": True,
        "activation": "tanh",
    },
    rf_n_estimators=300,
    gb_n_estimators=300,
    backtest_periods=150,
)

BALANCED_CONFIG = OptimizedSelectionConfig()  # 使用默认优化配置

CONSERVATIVE_CONFIG = OptimizedSelectionConfig(
    sequence_length=25,
    dl_epochs=150,
    lstm_config={
        "units": 192,
        "layers": 2,
        "dropout": 0.3,
        "recurrent_dropout": 0.3,
        "use_batch_norm": True,
        "use_residual": False,
        "activation": "tanh",
    },
    rf_n_estimators=150,
    gb_n_estimators=150,
    backtest_periods=75,
)
