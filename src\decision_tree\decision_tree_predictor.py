"""基础决策树预测器

实现基于决策树的号码预测功能，支持奇偶比和大小比预测。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any
import pandas as pd
import numpy as np
import pickle
import os
from sklearn.tree import DecisionTreeClassifier
from sklearn.preprocessing import LabelEncoder
from collections import Counter

from ..framework.interfaces import PredictorInterface
from ..framework.data_models import PredictionResult
from ..utils.logger import get_logger


class DecisionTreePredictor(PredictorInterface, ABC):
    """决策树预测器基类"""
    
    def __init__(self, 
                 max_depth: int = 10,
                 min_samples_split: int = 5,
                 min_samples_leaf: int = 2,
                 random_state: int = 42):
        """
        初始化决策树预测器
        
        Args:
            max_depth: 树的最大深度
            min_samples_split: 分裂所需的最小样本数
            min_samples_leaf: 叶节点的最小样本数
            random_state: 随机种子
        """
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf
        self.random_state = random_state
        
        # 决策树模型
        self.odd_even_tree = None
        self.red_size_tree = None
        self.blue_size_tree = None
        
        # 标签编码器
        self.odd_even_encoder = LabelEncoder()
        self.red_size_encoder = LabelEncoder()
        self.blue_size_encoder = LabelEncoder()
        
        self.logger = get_logger(self.__class__.__name__)
        
    def _create_tree_model(self) -> DecisionTreeClassifier:
        """创建决策树模型"""
        return DecisionTreeClassifier(
            max_depth=self.max_depth,
            min_samples_split=self.min_samples_split,
            min_samples_leaf=self.min_samples_leaf,
            random_state=self.random_state
        )
    
    def _extract_features(self, data: pd.DataFrame, current_index: int = None) -> pd.DataFrame:
        """
        提取特征
        
        Args:
            data: 历史数据
            current_index: 当前索引（如果提供，只处理到该索引）
            
        Returns:
            特征DataFrame
        """
        features = []
        
        end_index = current_index if current_index is not None else len(data) - 1
        for i in range(end_index + 1):
            row = data.iloc[i]
            
            # 计算当期的奇偶比和大小比
            red_balls = self._get_red_balls(row)
            blue_balls = self._get_blue_balls(row)
            
            red_odd_even = self._calculate_odd_even_ratio(red_balls)
            red_size = self._calculate_size_ratio(red_balls, 17)  # 红球分界线17
            blue_size = self._calculate_blue_size(blue_balls)
            
            # 基础特征
            feature_dict = {
                '期号': row['期号'],
                '红球奇偶比': red_odd_even,
                '红球大小比': red_size,
                '蓝球大小': blue_size,
            }
            
            # 历史统计特征
            if i >= 5:  # 至少需要5期历史数据
                recent_data = data.iloc[max(0, i-5):i]
                
                # 计算近5期的比例分布
                recent_odd_even = []
                recent_size = []
                recent_blue_size = []
                
                for j in range(len(recent_data)):
                    hist_row = recent_data.iloc[j]
                    hist_red = self._get_red_balls(hist_row)
                    hist_blue = self._get_blue_balls(hist_row)
                    
                    recent_odd_even.append(self._calculate_odd_even_ratio(hist_red))
                    recent_size.append(self._calculate_size_ratio(hist_red, 17))
                    recent_blue_size.append(self._calculate_blue_size(hist_blue))
                
                # 奇偶比统计
                from collections import Counter
                odd_even_counts = Counter(recent_odd_even)
                feature_dict['近5期_奇偶比_3_3'] = odd_even_counts.get('3:3', 0)
                feature_dict['近5期_奇偶比_4_2'] = odd_even_counts.get('4:2', 0)
                feature_dict['近5期_奇偶比_2_4'] = odd_even_counts.get('2:4', 0)
                feature_dict['近5期_奇偶比_5_1'] = odd_even_counts.get('5:1', 0)
                feature_dict['近5期_奇偶比_1_5'] = odd_even_counts.get('1:5', 0)
                
                # 大小比统计
                size_counts = Counter(recent_size)
                feature_dict['近5期_大小比_3_3'] = size_counts.get('3:3', 0)
                feature_dict['近5期_大小比_4_2'] = size_counts.get('4:2', 0)
                feature_dict['近5期_大小比_2_4'] = size_counts.get('2:4', 0)
                feature_dict['近5期_大小比_5_1'] = size_counts.get('5:1', 0)
                feature_dict['近5期_大小比_1_5'] = size_counts.get('1:5', 0)
                
                # 蓝球大小统计
                blue_size_counts = Counter(recent_blue_size)
                feature_dict['近5期_蓝球大'] = blue_size_counts.get('大', 0)
                feature_dict['近5期_蓝球小'] = blue_size_counts.get('小', 0)
            
            features.append(feature_dict)
        
        return pd.DataFrame(features)
    
    def _get_red_balls(self, row: pd.Series) -> List[int]:
        """
        从数据行中提取红球号码
        """
        if '红球' in row:
            red_str = str(row['红球'])
            return [int(x) for x in red_str.split(',') if x.strip().isdigit()]
        else:
            # 尝试从红球1-5列中提取
            red_balls = []
            for i in range(1, 6):
                col_name = f'红球{i}'
                if col_name in row and pd.notna(row[col_name]):
                    red_balls.append(int(row[col_name]))
            return red_balls
    
    def _get_blue_balls(self, row: pd.Series) -> List[int]:
        """
        从数据行中提取蓝球号码
        """
        if '蓝球' in row:
            blue_str = str(row['蓝球'])
            return [int(x) for x in blue_str.split(',') if x.strip().isdigit()]
        else:
            # 尝试从蓝球1-2列中提取
            blue_balls = []
            for i in range(1, 3):
                col_name = f'蓝球{i}'
                if col_name in row and pd.notna(row[col_name]):
                    blue_balls.append(int(row[col_name]))
            return blue_balls
    
    def _calculate_odd_even_ratio(self, red_balls: List[int]) -> str:
        """
        计算红球奇偶比
        """
        odd_count = sum(1 for x in red_balls if x % 2 == 1)
        even_count = len(red_balls) - odd_count
        return f"{odd_count}:{even_count}"
    
    def _calculate_size_ratio(self, red_balls: List[int], boundary: int = 18) -> str:
        """
        计算红球大小比 - 使用统一标准 (大数:小数格式)
        红球：1-17为小，18-35为大
        """
        small_count = sum(1 for x in red_balls if x <= 17)
        big_count = len(red_balls) - small_count
        return f"{big_count}:{small_count}"
    
    def _calculate_blue_size(self, blue_balls: List[int]) -> str:
        """
        计算蓝球大小
        蓝球：1-6为小，7-12为大
        """
        if not blue_balls:
            return "小"
        small_count = sum(1 for x in blue_balls if x <= 6)
        big_count = len(blue_balls) - small_count
        return "小" if small_count > big_count else "大"
    
    def _prepare_training_data(self, features: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series, pd.Series, pd.Series]:
        """
        准备训练数据
        
        Args:
            features: 特征数据
            
        Returns:
            (X, y_odd_even, y_red_size, y_blue_size)
        """
        # 移除最后一行（预测目标）
        X = features.iloc[:-1].copy()
        
        # 目标变量（下一期的结果）
        y_odd_even = features['红球奇偶比'].iloc[1:].reset_index(drop=True)
        y_red_size = features['红球大小比'].iloc[1:].reset_index(drop=True)
        y_blue_size = features['蓝球大小'].iloc[1:].reset_index(drop=True)
        
        # 处理特征
        X = X.drop(['期号', '红球奇偶比', '红球大小比', '蓝球大小'], axis=1)
        X = X.fillna(0)  # 填充缺失值
        
        return X, y_odd_even, y_red_size, y_blue_size
    
    def _train_models(self, X: pd.DataFrame, y_odd_even: pd.Series, 
                     y_red_size: pd.Series, y_blue_size: pd.Series) -> None:
        """
        训练决策树模型
        
        Args:
            X: 特征数据
            y_odd_even: 奇偶比标签
            y_red_size: 红球大小比标签
            y_blue_size: 蓝球大小标签
        """
        if len(X) < 10:  # 至少需要10期数据
            self.logger.warning(f"训练数据不足，仅有{len(X)}期")
            return
        
        try:
            # 训练奇偶比模型
            valid_odd_even = y_odd_even.dropna()
            if len(valid_odd_even) > 0:
                X_odd_even = X.loc[valid_odd_even.index]
                self.odd_even_tree = self._create_tree_model()
                self.odd_even_encoder.fit(valid_odd_even)
                y_odd_even_encoded = self.odd_even_encoder.transform(valid_odd_even)
                self.odd_even_tree.fit(X_odd_even, y_odd_even_encoded)
            
            # 训练红球大小比模型
            valid_red_size = y_red_size.dropna()
            if len(valid_red_size) > 0:
                X_red_size = X.loc[valid_red_size.index]
                self.red_size_tree = self._create_tree_model()
                self.red_size_encoder.fit(valid_red_size)
                y_red_size_encoded = self.red_size_encoder.transform(valid_red_size)
                self.red_size_tree.fit(X_red_size, y_red_size_encoded)
            
            # 训练蓝球大小模型
            valid_blue_size = y_blue_size.dropna()
            if len(valid_blue_size) > 0:
                X_blue_size = X.loc[valid_blue_size.index]
                self.blue_size_tree = self._create_tree_model()
                self.blue_size_encoder.fit(valid_blue_size)
                y_blue_size_encoded = self.blue_size_encoder.transform(valid_blue_size)
                self.blue_size_tree.fit(X_blue_size, y_blue_size_encoded)
                
        except Exception as e:
            self.logger.error(f"训练模型时出错: {e}")
    
    def _predict_ratios(self, current_features: pd.DataFrame) -> Tuple[List[Tuple[str, float]], 
                                                                      List[Tuple[str, float]], 
                                                                      List[Tuple[str, float]]]:
        """
        预测比例
        
        Args:
            current_features: 当前特征
            
        Returns:
            (奇偶比预测, 红球大小比预测, 蓝球大小预测)
        """
        # 准备预测特征
        X_pred = current_features.drop(['期号', '红球奇偶比', '红球大小比', '蓝球大小'], axis=1)
        X_pred = X_pred.fillna(0)
        
        # 奇偶比预测
        odd_even_predictions = []
        if self.odd_even_tree is not None:
            try:
                proba = self.odd_even_tree.predict_proba(X_pred.iloc[[-1]])[0]
                classes = self.odd_even_encoder.classes_
                for cls, prob in zip(classes, proba):
                    odd_even_predictions.append((cls, prob))
                odd_even_predictions.sort(key=lambda x: x[1], reverse=True)
            except Exception as e:
                self.logger.error(f"奇偶比预测出错: {e}")
                odd_even_predictions = [('3:3', 0.4), ('4:2', 0.3), ('2:4', 0.3)]
        else:
            odd_even_predictions = [('3:3', 0.4), ('4:2', 0.3), ('2:4', 0.3)]
        
        # 红球大小比预测
        red_size_predictions = []
        if self.red_size_tree is not None:
            try:
                proba = self.red_size_tree.predict_proba(X_pred.iloc[[-1]])[0]
                classes = self.red_size_encoder.classes_
                for cls, prob in zip(classes, proba):
                    red_size_predictions.append((cls, prob))
                red_size_predictions.sort(key=lambda x: x[1], reverse=True)
            except Exception as e:
                self.logger.error(f"红球大小比预测出错: {e}")
                red_size_predictions = [('3:3', 0.4), ('4:2', 0.3), ('2:4', 0.3)]
        else:
            red_size_predictions = [('3:3', 0.4), ('4:2', 0.3), ('2:4', 0.3)]
        
        # 蓝球大小预测
        blue_size_predictions = []
        if self.blue_size_tree is not None:
            try:
                proba = self.blue_size_tree.predict_proba(X_pred.iloc[[-1]])[0]
                classes = self.blue_size_encoder.classes_
                for cls, prob in zip(classes, proba):
                    blue_size_predictions.append((cls, prob))
                blue_size_predictions.sort(key=lambda x: x[1], reverse=True)
            except Exception as e:
                self.logger.error(f"蓝球大小预测出错: {e}")
                blue_size_predictions = [('大', 0.5), ('小', 0.5)]
        else:
            blue_size_predictions = [('大', 0.5), ('小', 0.5)]
        
        return odd_even_predictions, red_size_predictions, blue_size_predictions
    
    @abstractmethod
    def _generate_numbers(self, odd_even_pred: List[Tuple[str, float]], 
                         red_size_pred: List[Tuple[str, float]], 
                         blue_size_pred: List[Tuple[str, float]]) -> Tuple[List[int], List[int]]:
        """根据比例预测生成号码（子类实现）"""
        pass
    
    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """
        为指定数据索引预测下一期
        
        Args:
            data_index: 数据索引
            data: 完整的历史数据
            
        Returns:
            PredictionResult: 预测结果
        """
        try:
            # 提取特征
            features = self._extract_features(data, data_index)
            
            # 准备训练数据
            X, y_odd_even, y_red_size, y_blue_size = self._prepare_training_data(features)
            
            # 训练模型
            self._train_models(X, y_odd_even, y_red_size, y_blue_size)
            
            # 预测比例
            odd_even_pred, red_size_pred, blue_size_pred = self._predict_ratios(features)
            
            # 生成号码
            red_balls, blue_balls = self._generate_numbers(odd_even_pred, red_size_pred, blue_size_pred)
            
            # 构建预测结果
            period_number = str(data.iloc[data_index]['期号'])
            
            return PredictionResult(
                period_number=period_number,
                data_index=data_index,
                red_odd_even_predictions=odd_even_pred,
                red_size_predictions=red_size_pred,
                blue_size_predictions=blue_size_pred,
                generated_numbers=(red_balls, blue_balls),
                kill_numbers={},
                predictor_name=self.get_predictor_name(),
                training_data_size=len(X)
            )
            
        except Exception as e:
            self.logger.error(f"预测过程中出错: {e}")
            # 返回默认预测结果
            period_number = str(data.iloc[data_index]['期号'])
            return PredictionResult(
                period_number=period_number,
                data_index=data_index,
                red_odd_even_predictions=[('3:3', 0.4), ('4:2', 0.3), ('2:4', 0.3)],
                red_size_predictions=[('3:3', 0.4), ('4:2', 0.3), ('2:4', 0.3)],
                blue_size_predictions=[('大', 0.5), ('小', 0.5)],
                generated_numbers=([1, 2, 3, 4, 5, 6], [1]),
                kill_numbers={},
                predictor_name=self.get_predictor_name(),
                training_data_size=0
            )
    
    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return "DecisionTreePredictor"
    
    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return "1.0.0"
    
    def save_models(self, filepath: str):
        """保存决策树模型"""
        model_data = {
            'odd_even_tree': self.odd_even_tree,
            'red_size_tree': self.red_size_tree,
            'blue_size_tree': self.blue_size_tree,
            'odd_even_encoder': self.odd_even_encoder,
            'red_size_encoder': self.red_size_encoder,
            'blue_size_encoder': self.blue_size_encoder,
            'max_depth': self.max_depth,
            'min_samples_split': self.min_samples_split,
            'min_samples_leaf': self.min_samples_leaf,
            'random_state': self.random_state,
            'predictor_name': self.get_predictor_name(),
            'predictor_version': self.get_predictor_version()
        }
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        self.logger.info(f"决策树模型已保存到 {filepath}")
    
    def load_models(self, filepath: str) -> bool:
        """加载决策树模型"""
        if not os.path.exists(filepath):
            self.logger.error(f"模型文件 {filepath} 不存在")
            return False
        
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            # 恢复模型
            self.odd_even_tree = model_data.get('odd_even_tree')
            self.red_size_tree = model_data.get('red_size_tree')
            self.blue_size_tree = model_data.get('blue_size_tree')
            
            # 恢复编码器
            self.odd_even_encoder = model_data.get('odd_even_encoder', LabelEncoder())
            self.red_size_encoder = model_data.get('red_size_encoder', LabelEncoder())
            self.blue_size_encoder = model_data.get('blue_size_encoder', LabelEncoder())
            
            # 恢复参数
            self.max_depth = model_data.get('max_depth', self.max_depth)
            self.min_samples_split = model_data.get('min_samples_split', self.min_samples_split)
            self.min_samples_leaf = model_data.get('min_samples_leaf', self.min_samples_leaf)
            self.random_state = model_data.get('random_state', self.random_state)
            
            self.logger.info(f"决策树模型已从 {filepath} 加载")
            self.logger.info(f"模型版本: {model_data.get('predictor_version', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载决策树模型失败: {e}")
            return False
    
    def is_trained(self) -> bool:
        """检查模型是否已训练"""
        return (self.odd_even_tree is not None and 
                self.red_size_tree is not None and 
                self.blue_size_tree is not None)