#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础验证脚本 - 验证核心模块导入和基本功能
不依赖外部库，仅使用Python标准库
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_core_imports():
    """测试核心模块导入"""
    print("测试核心模块导入...")
    
    try:
        # 测试核心接口
        from core.interfaces import IPredictor, IStandardPredictor
        print("[OK] 核心接口导入成功")
        
        # 测试基础类
        from core.base import BasePredictor, StandardBasePredictor
        print("[OK] 基础类导入成功")
        
        # 测试异常类
        from core.exceptions import LotteryPredictorException
        print("[OK] 异常类导入成功")
        
        # 测试配置管理器
        from core.config_manager import ConfigManager
        print("[OK] 配置管理器导入成功")
        
        return True
    except ImportError as e:
        print(f"[FAIL] 核心模块导入失败: {e}")
        return False

def test_predictor_imports():
    """测试预测器模块导入"""
    print("\n 测试预测器模块导入...")
    
    try:
        # 测试决策树预测器
        from decision_tree.improved_odd_even_tree import ImprovedOddEvenTreePredictor
        print("[OK] 改进奇偶决策树预测器导入成功")
        
        # 测试增强预测器
        from models.enhanced_size_ratio_predictor import EnhancedSizeRatioPredictor
        print("[OK] 增强大小比预测器导入成功")
        
        # 测试统一预测器
        from models.unified_odd_even_predictor import UnifiedOddEvenPredictor
        print("[OK] 统一奇偶预测器导入成功")
        
        return True
    except ImportError as e:
        print(f"[FAIL] 预测器模块导入失败: {e}")
        return False

def test_framework_imports():
    """测试框架模块导入"""
    print("\n 测试框架模块导入...")
    
    try:
        # 测试预测器接口
        from framework.interfaces import PredictorInterface
        print("[OK] 预测器接口导入成功")
        
        # 测试适配器
        from core.predictor_adapter import LegacyPredictorAdapter
        print("[OK] 预测器适配器导入成功")
        
        return True
    except ImportError as e:
        print(f"[FAIL] 框架模块导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n 测试基本功能...")
    
    try:
        # 测试配置管理器基本功能
        from core.config_manager import ConfigManager, ConfigFormat
        
        config_manager = ConfigManager()
        print("[OK] 配置管理器实例化成功")
        
        # 测试枚举
        assert ConfigFormat.JSON.value == "json"
        print("[OK] 配置格式枚举正常")
        
        return True
    except Exception as e:
        print(f"[FAIL] 基本功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print(" 开始基础验证测试")
    print("=" * 50)
    
    tests = [
        test_core_imports,
        test_predictor_imports,
        test_framework_imports,
        test_basic_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f" 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print(" 所有基础验证测试通过！")
        return True
    else:
        print("  部分测试失败，需要检查模块结构")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)