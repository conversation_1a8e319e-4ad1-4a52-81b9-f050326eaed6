#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Number Selection System Adapter - 选号系统适配器
将选号系统适配到统一回测框架
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

from ..framework.interfaces import PredictorInterface
from ..framework.data_models import PredictionResult as FrameworkPredictionResult
from .number_selection_system import (
    NumberSelectionSystem,
    SelectionMethod,
    SelectionConfig,
)
from .optimized_selection_config import OptimizedSelectionConfig


class NumberSelectionAdapter(PredictorInterface):
    """选号系统适配器 - 适配到统一回测框架"""

    def __init__(
        self,
        config: Optional[SelectionConfig] = None,
        method: SelectionMethod = SelectionMethod.ENSEMBLE,
    ):
        """
        初始化适配器

        Args:
            config: 选号配置
            method: 选号方法
        """
        self.config = config or SelectionConfig()
        self.method = method
        self.logger = logging.getLogger(self.__class__.__name__)

        # 创建选号系统实例
        self.selection_system = NumberSelectionSystem(config)

        self.logger.info(f"选号系统适配器初始化完成 - 方法: {method.value}")

    def predict_for_period(
        self, data_index: int, data: pd.DataFrame
    ) -> FrameworkPredictionResult:
        """
        为指定数据索引预测下一期

        Args:
            data_index: 数据索引（不是期号！）
            data: 完整的历史数据

        Returns:
            FrameworkPredictionResult: 标准化的预测结果
        """
        try:
            # 获取期号（只作为标志）
            period_number = str(data.iloc[data_index]["期号"])

            # 使用到当前索引的所有数据作为训练数据
            train_data = data.iloc[: data_index + 1].copy()

            self.logger.debug(
                f"预测期号 {period_number}，使用 {len(train_data)} 期训练数据"
            )

            # 更新选号系统的数据
            self.selection_system.data = train_data

            # 执行选号
            selection_result = self.selection_system.select_numbers(self.method)

            # 转换为框架标准格式
            framework_result = FrameworkPredictionResult(
                period_number=period_number,
                data_index=data_index,
                red_odd_even_predictions=[("未知", 0.5)],  # 占位符
                red_size_predictions=[("未知", 0.5)],  # 占位符
                blue_size_predictions=[("未知", 0.5)],  # 占位符
                generated_numbers=(
                    selection_result.red_balls,
                    selection_result.blue_balls,
                ),
                kill_numbers={},  # 占位符
                predictor_name=self.get_predictor_name(),
                training_data_size=len(train_data),
            )

            return framework_result

        except Exception as e:
            self.logger.error(f"预测失败 - 期号: {period_number}, 错误: {e}")

            # 返回默认预测结果
            return FrameworkPredictionResult(
                period_number=str(data.iloc[data_index]["期号"]),
                data_index=data_index,
                red_odd_even_predictions=[("未知", 0.5)],
                red_size_predictions=[("未知", 0.5)],
                blue_size_predictions=[("未知", 0.5)],
                generated_numbers=([1, 2, 3, 4, 5], [1, 2]),  # 默认号码
                kill_numbers={"error": str(e)},
                predictor_name=self.get_predictor_name(),
                training_data_size=0,
            )

    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return f"NumberSelection-{self.method.value}"

    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return "1.0.0"


class OptimizedNumberSelectionAdapter(NumberSelectionAdapter):
    """优化版选号系统适配器"""

    def __init__(
        self,
        config: Optional[OptimizedSelectionConfig] = None,
        method: SelectionMethod = SelectionMethod.ENSEMBLE,
    ):
        """
        初始化优化适配器

        Args:
            config: 优化选号配置
            method: 选号方法
        """
        self.optimized_config = config or OptimizedSelectionConfig()

        # 调用父类初始化
        super().__init__(config, method)

        self.logger.info(f"优化选号系统适配器初始化完成 - 方法: {method.value}")

    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return f"OptimizedNumberSelection-{self.method.value}"

    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return "2.0.0-optimized"


def create_selection_adapter(
    method: SelectionMethod = SelectionMethod.ENSEMBLE,
    optimized: bool = False,
    config: Optional[Dict[str, Any]] = None,
) -> PredictorInterface:
    """
    创建选号系统适配器的工厂方法

    Args:
        method: 选号方法
        optimized: 是否使用优化配置
        config: 自定义配置参数

    Returns:
        PredictorInterface: 适配器实例
    """
    if optimized:
        if config:
            # 创建自定义优化配置
            opt_config = OptimizedSelectionConfig()
            for key, value in config.items():
                if hasattr(opt_config, key):
                    setattr(opt_config, key, value)
            return OptimizedNumberSelectionAdapter(opt_config, method)
        else:
            return OptimizedNumberSelectionAdapter(method=method)
    else:
        if config:
            # 创建自定义配置
            sel_config = SelectionConfig()
            for key, value in config.items():
                if hasattr(sel_config, key):
                    setattr(sel_config, key, value)
            return NumberSelectionAdapter(sel_config, method)
        else:
            return NumberSelectionAdapter(method=method)


# 预定义的适配器实例
def get_deep_learning_adapter(optimized: bool = False) -> PredictorInterface:
    """获取深度学习选号适配器"""
    return create_selection_adapter(SelectionMethod.DEEP_LEARNING, optimized)


def get_traditional_ml_adapter(optimized: bool = False) -> PredictorInterface:
    """获取传统ML选号适配器"""
    return create_selection_adapter(SelectionMethod.TRADITIONAL_ML, optimized)


def get_ensemble_adapter(optimized: bool = False) -> PredictorInterface:
    """获取集成选号适配器"""
    return create_selection_adapter(SelectionMethod.ENSEMBLE, optimized)


if __name__ == "__main__":
    # 测试适配器
    print("测试选号系统适配器...")

    # 创建测试数据
    test_data = pd.DataFrame(
        {
            "期号": ["24001", "24002", "24003"],
            "红球1": [1, 2, 3],
            "红球2": [5, 6, 7],
            "红球3": [10, 11, 12],
            "红球4": [15, 16, 17],
            "红球5": [20, 21, 22],
            "蓝球1": [1, 2, 3],
            "蓝球2": [5, 6, 7],
        }
    )

    # 测试适配器
    adapter = create_selection_adapter(SelectionMethod.ENSEMBLE, optimized=True)

    try:
        result = adapter.predict_for_period(1, test_data)
        print(f"预测成功: {result.period_number}")
        print(f"红球: {result.generated_numbers[0]}")
        print(f"蓝球: {result.generated_numbers[1]}")
        print(f"预测器: {result.predictor_name}")
    except Exception as e:
        print(f"测试失败: {e}")
