"""大小比决策树预测器

专门用于预测红球大小比的决策树模型。
"""

from typing import List, Tuple
import random
from .decision_tree_predictor import DecisionTreePredictor


class SizeRatioTreePredictor(DecisionTreePredictor):
    """大小比决策树预测器"""
    
    def __init__(self, **kwargs):
        """初始化大小比决策树预测器"""
        super().__init__(**kwargs)
    
    def _generate_numbers(self, odd_even_pred: List[Tuple[str, float]], 
                         red_size_pred: List[Tuple[str, float]], 
                         blue_size_pred: List[Tuple[str, float]]) -> Tuple[List[int], List[int]]:
        """
        根据大小比预测生成号码
        
        Args:
            odd_even_pred: 奇偶比预测结果
            red_size_pred: 红球大小比预测结果
            blue_size_pred: 蓝球大小预测结果
            
        Returns:
            (红球列表, 蓝球列表)
        """
        # 获取最可能的比例
        target_odd_even = odd_even_pred[0][0] if odd_even_pred else '3:3'
        target_red_size = red_size_pred[0][0] if red_size_pred else '3:3'
        target_blue_size = blue_size_pred[0][0] if blue_size_pred else '大'
        
        # 解析大小比（重点关注）
        if ':' in target_red_size:
            small_count, big_count = map(int, target_red_size.split(':'))
        else:
            small_count, big_count = 3, 3
        
        # 解析奇偶比（辅助）
        if ':' in target_odd_even:
            odd_count, even_count = map(int, target_odd_even.split(':'))
        else:
            odd_count, even_count = 3, 3
        
        # 生成红球（优先满足大小比）
        red_balls = self._generate_red_balls_by_size(small_count, big_count, odd_count, even_count)
        
        # 生成蓝球
        blue_balls = self._generate_blue_balls(target_blue_size)
        
        return red_balls, blue_balls
    
    def _generate_red_balls_by_size(self, small_count: int, big_count: int,
                                   odd_count: int, even_count: int) -> List[int]:
        """
        优先按大小比生成红球

        Args:
            small_count: 小数个数（1-18）- 使用统一标准
            big_count: 大数个数（19-35）- 使用统一标准
            odd_count: 奇数个数（辅助参考）
            even_count: 偶数个数（辅助参考）

        Returns:
            红球列表
        """
        # 定义号码池 - 使用统一标准
        small_numbers = list(range(1, 19))  # 1-18
        big_numbers = list(range(19, 36))   # 19-35
        
        red_balls = []
        
        try:
            # 首先按大小比选择
            if small_count > 0 and small_count <= len(small_numbers):
                selected_small = random.sample(small_numbers, small_count)
                red_balls.extend(selected_small)
            
            if big_count > 0 and big_count <= len(big_numbers):
                selected_big = random.sample(big_numbers, big_count)
                red_balls.extend(selected_big)
            
            # 如果总数不足6个，补充号码
            if len(red_balls) < 6:
                all_numbers = list(range(1, 34))
                remaining_numbers = [n for n in all_numbers if n not in red_balls]
                needed = 6 - len(red_balls)
                
                if len(remaining_numbers) >= needed:
                    # 尝试在补充时考虑奇偶比
                    current_odd = sum(1 for n in red_balls if n % 2 == 1)
                    current_even = len(red_balls) - current_odd
                    
                    need_odd = max(0, odd_count - current_odd)
                    need_even = max(0, even_count - current_even)
                    
                    remaining_odd = [n for n in remaining_numbers if n % 2 == 1]
                    remaining_even = [n for n in remaining_numbers if n % 2 == 0]
                    
                    additional = []
                    
                    # 优先满足奇偶需求
                    if need_odd > 0 and len(remaining_odd) >= need_odd:
                        additional.extend(random.sample(remaining_odd, min(need_odd, needed)))
                        needed -= len(additional)
                    
                    if need_even > 0 and needed > 0 and len(remaining_even) >= need_even:
                        additional.extend(random.sample(remaining_even, min(need_even, needed)))
                        needed -= (len(additional) - len([n for n in additional if n % 2 == 1]))
                    
                    # 如果还需要更多号码，随机选择
                    if needed > 0:
                        remaining_for_random = [n for n in remaining_numbers if n not in additional]
                        if len(remaining_for_random) >= needed:
                            additional.extend(random.sample(remaining_for_random, needed))
                    
                    red_balls.extend(additional)
                else:
                    red_balls.extend(remaining_numbers)
            
            # 如果超过6个，随机移除多余的
            if len(red_balls) > 6:
                red_balls = random.sample(red_balls, 6)
            
            # 如果还是不足6个，随机补充
            if len(red_balls) < 6:
                all_numbers = list(range(1, 34))
                remaining_numbers = [n for n in all_numbers if n not in red_balls]
                needed = 6 - len(red_balls)
                if len(remaining_numbers) >= needed:
                    red_balls.extend(random.sample(remaining_numbers, needed))
                else:
                    # 最后的保险措施
                    red_balls = random.sample(range(1, 34), 6)
            
        except (ValueError, IndexError) as e:
            self.logger.warning(f"生成红球时出错: {e}，使用随机生成")
            red_balls = random.sample(range(1, 34), 6)
        
        return sorted(red_balls)
    
    def _generate_blue_balls(self, target_size: str) -> List[int]:
        """
        生成蓝球
        
        Args:
            target_size: 目标大小（'大' 或 '小'）
            
        Returns:
            蓝球列表
        """
        if target_size == '大':
            # 蓝球大号：7-12
            blue_pool = list(range(7, 13))
        else:
            # 蓝球小号：1-6
            blue_pool = list(range(1, 7))
        
        return [random.choice(blue_pool)]
    
    def _calculate_size_ratio(self, numbers: List[int], boundary: int = 18) -> str:
        """
        计算号码的大小比 - 使用统一标准 (大数:小数格式)
        红球：1-18为小，19-35为大

        Args:
            numbers: 号码列表
            boundary: 大小分界线，默认18（统一标准）

        Returns:
            大小比字符串 (大数:小数格式)
        """
        small_count = sum(1 for n in numbers if n <= 18)
        big_count = len(numbers) - small_count
        return f"{big_count}:{small_count}"
    
    def _calculate_odd_even_ratio(self, numbers: List[int]) -> str:
        """
        计算号码的奇偶比
        
        Args:
            numbers: 号码列表
            
        Returns:
            奇偶比字符串
        """
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        even_count = len(numbers) - odd_count
        return f"{odd_count}:{even_count}"
    
    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return "SizeRatioTreePredictor"
    
    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return "1.0.0"