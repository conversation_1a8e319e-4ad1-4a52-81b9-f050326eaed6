#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deep Learning Selector - 深度学习选号器
使用LSTM、Transformer等深度学习模型进行选号
支持GPU加速训练
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import logging
import time
from pathlib import Path
import warnings

warnings.filterwarnings("ignore")

# GPU配置导入
from ..utils.gpu_config import (
    get_gpu_config,
    is_gpu_available,
    get_device,
    get_optimized_training_config,
    to_device,
)

# 深度学习相关导入
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torch.nn.functional as F
    from torch.utils.data import DataLoader, TensorDataset
    from torch.cuda.amp import autocast, GradScaler
    from .pytorch_models import create_model, ModelTrainer

    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    # 使用传统机器学习作为后备
    from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
    from sklearn.multioutput import MultiOutputRegressor, MultiOutputClassifier


class DeepLearningSelector:
    """深度学习选号器"""

    def __init__(self, config):
        """
        初始化深度学习选号器

        Args:
            config: 系统配置
        """
        self.config = config
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")

        # GPU配置
        self.gpu_config = get_gpu_config()
        self.device = get_device()
        self.training_config = get_optimized_training_config()

        # 模型存储
        self.models = {}
        self.model_history = {}

        # 根据GPU可用性调整模型配置
        gpu_multiplier = 2 if is_gpu_available() else 1

        # PyTorch模型配置
        self.model_configs = {
            "lstm": {
                "hidden_size": 128 * gpu_multiplier,
                "num_layers": 2,
                "dropout": 0.3,
                "batch_size": self.training_config["batch_size"],
                "epochs": self.training_config["epochs"],
                "learning_rate": self.training_config["learning_rate"],
            },
            "transformer": {
                "num_heads": 8,
                "hidden_dim": 256 * gpu_multiplier,
                "num_layers": 4,
                "dropout": 0.1,
                "batch_size": self.training_config["batch_size"],
                "epochs": self.training_config["epochs"],
                "learning_rate": self.training_config["learning_rate"],
            },
            "multi_task": {
                "hidden_size": 256 * gpu_multiplier,
                "num_layers": 3,
                "dropout": 0.2,
                "batch_size": self.training_config["batch_size"],
                "epochs": self.training_config["epochs"],
                "learning_rate": self.training_config["learning_rate"],
            },
        }

        # 模型保存路径
        self.models_dir = Path("models/deep_learning_selection")
        self.models_dir.mkdir(parents=True, exist_ok=True)

        # 初始化混合精度训练（如果GPU可用）
        if is_gpu_available() and self.training_config.get(
            "use_mixed_precision", False
        ):
            self.scaler = GradScaler()
            self.logger.info("启用混合精度训练")

        self.logger.info(
            f"深度学习选号器初始化完成 (PyTorch可用: {PYTORCH_AVAILABLE}, GPU可用: {is_gpu_available()}, 设备: {self.device})"
        )

    def train_and_select(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        训练模型并进行选号

        Args:
            processed_data: 处理后的数据

        Returns:
            Dict: 选号结果
        """
        self.logger.info("开始深度学习模型训练和选号...")

        if not PYTORCH_AVAILABLE:
            return self._fallback_selection(processed_data)

        # 训练多个模型
        models_results = {}

        # 1. LSTM模型
        self.logger.info("训练LSTM模型...")
        lstm_result = self._train_lstm_model(processed_data)
        models_results["lstm"] = lstm_result

        # 2. Transformer模型
        self.logger.info("训练Transformer模型...")
        transformer_result = self._train_transformer_model(processed_data)
        models_results["transformer"] = transformer_result

        # 3. 多任务神经网络
        self.logger.info("训练多任务神经网络...")
        multi_task_result = self._train_multi_task_model(processed_data)
        models_results["multi_task"] = multi_task_result

        # 集成结果
        ensemble_result = self._ensemble_models(models_results, processed_data)

        self.logger.info("深度学习模型训练和选号完成")
        return ensemble_result

    def select_numbers(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """使用训练好的模型选择号码"""
        if not PYTORCH_AVAILABLE:
            return self._fallback_selection(processed_data)

        try:
            # 获取最新的序列数据用于预测
            latest_sequence = processed_data["latest_sequence"]["features_scaled"]
            latest_sequence = (
                torch.FloatTensor(latest_sequence).unsqueeze(0).to(self.device)
            )  # 添加batch维度

            predictions = {}

            for model_name, model in self.models.items():
                try:
                    model.eval()
                    with torch.no_grad():
                        pred = model(latest_sequence)
                        # 转换为numpy数组
                        red_pred = pred["red"].cpu().numpy()[0]
                        blue_pred = pred["blue"].cpu().numpy()[0]
                        predictions[model_name] = {"red": red_pred, "blue": blue_pred}
                except Exception as e:
                    self.logger.warning(f"模型 {model_name} 预测失败: {e}")
                    continue

            if not predictions:
                self.logger.warning("所有模型预测失败，使用后备选择")
                return self._fallback_selection(processed_data)

            # 集成预测结果
            ensemble_result = self._ensemble_predictions(predictions, processed_data)

            return ensemble_result

        except Exception as e:
            self.logger.error(f"深度学习选号失败: {e}")
            return self._fallback_selection(processed_data)

    def _train_lstm_model(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """训练LSTM模型"""
        try:
            # 获取训练数据
            train_X = processed_data["train_data"]["sequences"]["features_scaled"]
            train_y_red = processed_data["train_data"]["sequences"]["targets"][
                "red_balls"
            ]
            train_y_blue = processed_data["train_data"]["sequences"]["targets"][
                "blue_balls"
            ]

            val_X = processed_data["val_data"]["sequences"]["features_scaled"]
            val_y_red = processed_data["val_data"]["sequences"]["targets"]["red_balls"]
            val_y_blue = processed_data["val_data"]["sequences"]["targets"][
                "blue_balls"
            ]

            # 转换为PyTorch张量
            train_X_tensor = torch.FloatTensor(train_X).to(self.device)
            train_y_red_tensor = torch.FloatTensor(train_y_red).to(self.device)
            train_y_blue_tensor = torch.FloatTensor(train_y_blue).to(self.device)

            val_X_tensor = torch.FloatTensor(val_X).to(self.device)
            val_y_red_tensor = torch.FloatTensor(val_y_red).to(self.device)
            val_y_blue_tensor = torch.FloatTensor(val_y_blue).to(self.device)

            # 创建数据加载器
            train_dataset = TensorDataset(
                train_X_tensor, train_y_red_tensor, train_y_blue_tensor
            )
            val_dataset = TensorDataset(
                val_X_tensor, val_y_red_tensor, val_y_blue_tensor
            )

            train_loader = DataLoader(
                train_dataset,
                batch_size=self.model_configs["lstm"]["batch_size"],
                shuffle=True,
                num_workers=0,  # 禁用多进程
                pin_memory=False,  # 禁用pin_memory
            )
            val_loader = DataLoader(
                val_dataset,
                batch_size=64,
                shuffle=False,
                num_workers=0,
                pin_memory=False,
            )

            # 创建LSTM模型
            model = create_model("lstm", self.model_configs["lstm"], train_X.shape[-1])
            model = model.to(self.device)

            # 创建训练器
            trainer = ModelTrainer(
                model=model,
                device=self.device,
                use_mixed_precision=self.training_config.get(
                    "use_mixed_precision", False
                ),
            )

            # 设置优化器和损失函数
            optimizer = optim.Adam(
                model.parameters(), lr=self.model_configs["lstm"]["learning_rate"]
            )
            criterion = nn.BCEWithLogitsLoss()
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, factor=0.5, patience=5
            )

            # 训练循环
            train_losses = []
            val_losses = []
            best_val_loss = float("inf")
            patience_counter = 0

            for epoch in range(self.model_configs["lstm"]["epochs"]):
                # 训练
                train_loss = self._train_epoch_pytorch(
                    model, train_loader, optimizer, criterion
                )
                train_losses.append(train_loss)

                # 验证
                val_loss = self._validate_epoch_pytorch(model, val_loader, criterion)
                val_losses.append(val_loss)

                # 学习率调度
                scheduler.step(val_loss)

                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    torch.save(model.state_dict(), self.models_dir / "lstm_best.pth")
                else:
                    patience_counter += 1
                    if patience_counter >= 10:  # 早停
                        self.logger.info(f"LSTM训练早停于epoch {epoch+1}")
                        break

                if epoch % 10 == 0:
                    self.logger.info(
                        f"LSTM Epoch {epoch+1}/{self.model_configs['lstm']['epochs']}, "
                        f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}"
                    )

            # 加载最佳模型
            model.load_state_dict(torch.load(self.models_dir / "lstm_best.pth"))

            # 保存模型
            self.models["lstm"] = model
            self.model_history["lstm"] = {
                "train_loss": train_losses,
                "val_loss": val_losses,
            }

            return {
                "model": model,
                "history": {"train_loss": train_losses, "val_loss": val_losses},
                "val_loss": best_val_loss,
                "success": True,
            }

        except Exception as e:
            self.logger.error(f"LSTM模型训练失败: {e}")
            return {"success": False, "error": str(e)}

    def _train_epoch_pytorch(self, model, dataloader, optimizer, criterion):
        """PyTorch训练一个epoch"""
        model.train()
        total_loss = 0.0

        for batch_idx, (data, red_targets, blue_targets) in enumerate(dataloader):
            optimizer.zero_grad()

            if self.training_config.get("use_mixed_precision", False):
                with autocast():
                    outputs = model(data)
                    red_loss = criterion(outputs["red"], red_targets)
                    blue_loss = criterion(outputs["blue"], blue_targets)
                    loss = red_loss + blue_loss

                # 使用GradScaler进行混合精度训练
                if hasattr(self, "scaler"):
                    self.scaler.scale(loss).backward()
                    self.scaler.step(optimizer)
                    self.scaler.update()
                else:
                    loss.backward()
                    optimizer.step()
            else:
                outputs = model(data)
                red_loss = criterion(outputs["red"], red_targets)
                blue_loss = criterion(outputs["blue"], blue_targets)
                loss = red_loss + blue_loss

                loss.backward()
                optimizer.step()

            total_loss += loss.item()

        return total_loss / len(dataloader)

    def _validate_epoch_pytorch(self, model, dataloader, criterion):
        """PyTorch验证一个epoch"""
        model.eval()
        total_loss = 0.0

        with torch.no_grad():
            for data, red_targets, blue_targets in dataloader:
                outputs = model(data)
                red_loss = criterion(outputs["red"], red_targets)
                blue_loss = criterion(outputs["blue"], blue_targets)
                loss = red_loss + blue_loss

                total_loss += loss.item()

        return total_loss / len(dataloader)

    def _build_lstm_model(self, input_shape):
        """构建LSTM模型 - 已弃用，使用pytorch_models.py中的模型"""
        # 这个方法已经被PyTorch模型替代
        # 使用 create_model('lstm', config, input_size) 来创建模型
        raise NotImplementedError(
            "请使用 create_model('lstm', config, input_size) 创建LSTM模型"
        )

    def _train_transformer_model(
        self, processed_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """训练Transformer模型"""
        try:
            # 获取训练数据
            train_X = processed_data["train_data"]["sequences"]["features_scaled"]
            train_y_red = processed_data["train_data"]["sequences"]["targets"][
                "red_balls"
            ]
            train_y_blue = processed_data["train_data"]["sequences"]["targets"][
                "blue_balls"
            ]

            val_X = processed_data["val_data"]["sequences"]["features_scaled"]
            val_y_red = processed_data["val_data"]["sequences"]["targets"]["red_balls"]
            val_y_blue = processed_data["val_data"]["sequences"]["targets"][
                "blue_balls"
            ]

            # 转换为PyTorch张量
            train_X_tensor = torch.FloatTensor(train_X).to(self.device)
            train_y_red_tensor = torch.FloatTensor(train_y_red).to(self.device)
            train_y_blue_tensor = torch.FloatTensor(train_y_blue).to(self.device)

            val_X_tensor = torch.FloatTensor(val_X).to(self.device)
            val_y_red_tensor = torch.FloatTensor(val_y_red).to(self.device)
            val_y_blue_tensor = torch.FloatTensor(val_y_blue).to(self.device)

            # 创建数据加载器
            train_dataset = TensorDataset(
                train_X_tensor, train_y_red_tensor, train_y_blue_tensor
            )
            val_dataset = TensorDataset(
                val_X_tensor, val_y_red_tensor, val_y_blue_tensor
            )

            train_loader = DataLoader(
                train_dataset,
                batch_size=self.model_configs["transformer"]["batch_size"],
                shuffle=True,
                num_workers=0,  # 禁用多进程
                pin_memory=False,  # 禁用pin_memory
            )
            val_loader = DataLoader(
                val_dataset,
                batch_size=64,
                shuffle=False,
                num_workers=0,
                pin_memory=False,
            )

            # 创建Transformer模型
            model = create_model(
                "transformer", self.model_configs["transformer"], train_X.shape[-1]
            )
            model = model.to(self.device)

            # 设置优化器和损失函数
            optimizer = optim.Adam(
                model.parameters(),
                lr=self.model_configs["transformer"]["learning_rate"],
            )
            criterion = nn.BCEWithLogitsLoss()
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, factor=0.5, patience=5
            )

            # 训练循环
            train_losses = []
            val_losses = []
            best_val_loss = float("inf")
            patience_counter = 0

            for epoch in range(self.model_configs["transformer"]["epochs"]):
                # 训练
                train_loss = self._train_epoch_pytorch(
                    model, train_loader, optimizer, criterion
                )
                train_losses.append(train_loss)

                # 验证
                val_loss = self._validate_epoch_pytorch(model, val_loader, criterion)
                val_losses.append(val_loss)

                # 学习率调度
                scheduler.step(val_loss)

                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    torch.save(
                        model.state_dict(), self.models_dir / "transformer_best.pth"
                    )
                else:
                    patience_counter += 1
                    if patience_counter >= 10:  # 早停
                        self.logger.info(f"Transformer训练早停于epoch {epoch+1}")
                        break

                if epoch % 10 == 0:
                    self.logger.info(
                        f"Transformer Epoch {epoch+1}/{self.model_configs['transformer']['epochs']}, "
                        f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}"
                    )

            # 加载最佳模型
            model.load_state_dict(torch.load(self.models_dir / "transformer_best.pth"))

            # 保存模型
            self.models["transformer"] = model
            self.model_history["transformer"] = {
                "train_loss": train_losses,
                "val_loss": val_losses,
            }

            return {
                "model": model,
                "history": {"train_loss": train_losses, "val_loss": val_losses},
                "val_loss": best_val_loss,
                "success": True,
            }

        except Exception as e:
            self.logger.error(f"Transformer模型训练失败: {e}")
            return {"success": False, "error": str(e)}

    def _build_transformer_model(self, input_shape):
        """构建Transformer模型 - 已弃用，使用pytorch_models.py中的模型"""
        # 这个方法已经被PyTorch模型替代
        # 使用 create_model('transformer', config, input_size) 来创建模型
        raise NotImplementedError(
            "请使用 create_model('transformer', config, input_size) 创建Transformer模型"
        )

    def _train_multi_task_model(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """训练多任务模型"""
        try:
            # 获取训练数据
            train_X = processed_data["train_data"]["sequences"]["features_scaled"]
            train_y_red = processed_data["train_data"]["sequences"]["targets"][
                "red_balls"
            ]
            train_y_blue = processed_data["train_data"]["sequences"]["targets"][
                "blue_balls"
            ]

            val_X = processed_data["val_data"]["sequences"]["features_scaled"]
            val_y_red = processed_data["val_data"]["sequences"]["targets"]["red_balls"]
            val_y_blue = processed_data["val_data"]["sequences"]["targets"][
                "blue_balls"
            ]

            # 转换为PyTorch张量
            train_X_tensor = torch.FloatTensor(train_X).to(self.device)
            train_y_red_tensor = torch.FloatTensor(train_y_red).to(self.device)
            train_y_blue_tensor = torch.FloatTensor(train_y_blue).to(self.device)

            val_X_tensor = torch.FloatTensor(val_X).to(self.device)
            val_y_red_tensor = torch.FloatTensor(val_y_red).to(self.device)
            val_y_blue_tensor = torch.FloatTensor(val_y_blue).to(self.device)

            # 创建数据加载器
            train_dataset = TensorDataset(
                train_X_tensor, train_y_red_tensor, train_y_blue_tensor
            )
            val_dataset = TensorDataset(
                val_X_tensor, val_y_red_tensor, val_y_blue_tensor
            )

            train_loader = DataLoader(
                train_dataset,
                batch_size=self.model_configs["multi_task"]["batch_size"],
                shuffle=True,
                num_workers=0,  # 禁用多进程
                pin_memory=False,  # 禁用pin_memory
            )
            val_loader = DataLoader(
                val_dataset,
                batch_size=64,
                shuffle=False,
                num_workers=0,
                pin_memory=False,
            )

            # 创建多任务模型
            model = create_model(
                "multi_task", self.model_configs["multi_task"], train_X.shape[-1]
            )
            model = model.to(self.device)

            # 设置优化器和损失函数
            optimizer = optim.Adam(
                model.parameters(), lr=self.model_configs["multi_task"]["learning_rate"]
            )
            criterion = nn.BCEWithLogitsLoss()
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer, factor=0.5, patience=5
            )

            # 训练循环
            train_losses = []
            val_losses = []
            best_val_loss = float("inf")
            patience_counter = 0

            for epoch in range(self.model_configs["multi_task"]["epochs"]):
                # 训练
                train_loss = self._train_epoch_pytorch(
                    model, train_loader, optimizer, criterion
                )
                train_losses.append(train_loss)

                # 验证
                val_loss = self._validate_epoch_pytorch(model, val_loader, criterion)
                val_losses.append(val_loss)

                # 学习率调度
                scheduler.step(val_loss)

                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # 保存最佳模型
                    torch.save(
                        model.state_dict(), self.models_dir / "multi_task_best.pth"
                    )
                else:
                    patience_counter += 1
                    if patience_counter >= 10:  # 早停
                        self.logger.info(f"多任务模型训练早停于epoch {epoch+1}")
                        break

                if epoch % 10 == 0:
                    self.logger.info(
                        f"MultiTask Epoch {epoch+1}/{self.model_configs['multi_task']['epochs']}, "
                        f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}"
                    )

            # 加载最佳模型
            model.load_state_dict(torch.load(self.models_dir / "multi_task_best.pth"))

            # 保存模型
            self.models["multi_task"] = model
            self.model_history["multi_task"] = {
                "train_loss": train_losses,
                "val_loss": val_losses,
            }

            return {
                "model": model,
                "history": {"train_loss": train_losses, "val_loss": val_losses},
                "val_loss": best_val_loss,
                "success": True,
            }

        except Exception as e:
            self.logger.error(f"多任务模型训练失败: {e}")
            return {"success": False, "error": str(e)}

    def _build_multi_task_model(self, input_shape):
        """构建多任务模型 - 已弃用，使用pytorch_models.py中的模型"""
        # 这个方法已经被PyTorch模型替代
        # 使用 create_model('multi_task', config, input_size) 来创建模型
        raise NotImplementedError(
            "请使用 create_model('multi_task', config, input_size) 创建多任务模型"
        )

    def _ensemble_models(
        self, models_results: Dict[str, Dict], processed_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """集成多个模型的结果"""
        successful_models = {
            k: v for k, v in models_results.items() if v.get("success", False)
        }

        if not successful_models:
            return self._fallback_selection(processed_data)

        # 使用验证数据进行预测
        val_data = processed_data.get("val_data", {}).get("sequences", {})
        val_features = val_data.get("features_scaled")

        if val_features is None or len(val_features) == 0:
            return self._fallback_selection(processed_data)

        # 收集所有模型的预测
        all_predictions = {}

        for model_name, result in successful_models.items():
            if "model" in result:
                try:
                    model = result["model"]
                    model.eval()

                    # 转换为PyTorch张量
                    input_tensor = torch.FloatTensor(val_features[-1:]).to(self.device)

                    with torch.no_grad():
                        pred = model(input_tensor)
                        all_predictions[model_name] = pred

                except Exception as e:
                    self.logger.warning(f"模型 {model_name} 预测失败: {e}")

        # 集成预测结果
        return self._ensemble_predictions(all_predictions, processed_data)

    def _ensemble_predictions(
        self, predictions: Dict[str, Dict], processed_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """集成多个模型的预测结果"""
        try:
            # 模型权重（可以根据验证性能调整）
            model_weights = {"lstm": 0.4, "transformer": 0.35, "multi_task": 0.25}

            # 初始化集成结果
            ensemble_red = np.zeros(35)  # 红球1-35
            ensemble_blue = np.zeros(12)  # 蓝球1-12
            total_weight = 0

            # 加权平均预测结果（logits）
            for model_name, pred in predictions.items():
                weight = model_weights.get(model_name, 0.1)

                # 处理PyTorch张量
                if hasattr(pred["red"], "cpu"):
                    red_logits = pred["red"].cpu().numpy()
                    blue_logits = pred["blue"].cpu().numpy()
                else:
                    red_logits = pred["red"]
                    blue_logits = pred["blue"]

                # 如果是批次数据，取第一个样本
                if len(red_logits.shape) > 1:
                    red_logits = red_logits[0]
                    blue_logits = blue_logits[0]

                ensemble_red += red_logits * weight
                ensemble_blue += blue_logits * weight
                total_weight += weight

            # 归一化
            if total_weight > 0:
                ensemble_red /= total_weight
                ensemble_blue /= total_weight

            # 应用sigmoid获得概率（因为使用BCEWithLogitsLoss）
            ensemble_red_prob = 1 / (1 + np.exp(-ensemble_red))
            ensemble_blue_prob = 1 / (1 + np.exp(-ensemble_blue))

            # 选择概率最高的号码
            # 红球：选择概率最高的5个
            red_indices = np.argsort(ensemble_red_prob)[-5:][::-1]
            red_balls = [int(idx + 1) for idx in red_indices]  # 转换为1-35范围

            # 蓝球：选择概率最高的2个
            blue_indices = np.argsort(ensemble_blue_prob)[-2:][::-1]
            blue_balls = [int(idx + 1) for idx in blue_indices]  # 转换为1-12范围

            # 计算置信度
            red_confidence = np.mean([ensemble_red_prob[idx] for idx in red_indices])
            blue_confidence = np.mean([ensemble_blue_prob[idx] for idx in blue_indices])

            result = {
                "success": True,
                "red_balls": sorted(red_balls),
                "blue_balls": sorted(blue_balls),
                "confidence": {
                    "red": float(red_confidence),
                    "blue": float(blue_confidence),
                    "overall": float((red_confidence + blue_confidence) / 2),
                },
                "probabilities": {
                    "red": ensemble_red_prob.tolist(),
                    "blue": ensemble_blue_prob.tolist(),
                },
                "model_predictions": predictions,
                "method": "deep_learning_ensemble",
            }

            self.logger.info(
                f"深度学习集成预测完成: 红球{red_balls}, 蓝球{blue_balls}, "
                f"置信度: {result['confidence']['overall']:.3f}"
            )

            return result

        except Exception as e:
            self.logger.error(f"集成预测失败: {e}")
            return self._fallback_selection(processed_data)

    def _fallback_selection(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """后备选号方法"""
        self.logger.warning("使用后备选号方法")

        # 简单随机选号
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))

        return {
            "success": False,
            "red_balls": (
                red_balls.tolist() if hasattr(red_balls, "tolist") else list(red_balls)
            ),
            "blue_balls": (
                blue_balls.tolist()
                if hasattr(blue_balls, "tolist")
                else list(blue_balls)
            ),
            "confidence": {"overall": 0.1},
            "model_info": {
                "method": "random_fallback",
                "reason": "deep_learning_unavailable_or_failed",
            },
        }
