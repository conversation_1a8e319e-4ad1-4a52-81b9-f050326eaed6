"""改进版决策树预测器基类

集成配置管理、类型注解、性能监控和缓存机制的决策树预测器。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any, Optional, Union
import pandas as pd
import numpy as np
import pickle
import time
from pathlib import Path
from sklearn.tree import DecisionTreeClassifier
from sklearn.preprocessing import LabelEncoder
from collections import Counter
from dataclasses import dataclass
from functools import lru_cache
import hashlib

from ..framework.interfaces import PredictorInterface
from ..framework.data_models import PredictionResult
from ..utils.logger import get_logger
from .config import DecisionTreeConfig, get_config


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""

    training_time: float = 0.0
    prediction_time: float = 0.0
    feature_extraction_time: float = 0.0
    cache_hit_rate: float = 0.0
    total_predictions: int = 0
    successful_predictions: int = 0

    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_predictions == 0:
            return 0.0
        return self.successful_predictions / self.total_predictions


class FeatureCache:
    """特征缓存管理器"""

    def __init__(self, ttl_seconds: int = 3600, max_size: int = 1000):
        """初始化缓存

        Args:
            ttl_seconds: 缓存生存时间（秒）
            max_size: 最大缓存条目数
        """
        self.ttl_seconds = ttl_seconds
        self.max_size = max_size
        self._cache: Dict[str, Tuple[Any, float]] = {}
        self._access_times: Dict[str, float] = {}
        self.hit_count = 0
        self.miss_count = 0

    def _generate_key(
        self, data: pd.DataFrame, current_index: Optional[int] = None
    ) -> str:
        """生成缓存键

        Args:
            data: 数据DataFrame
            current_index: 当前索引

        Returns:
            缓存键
        """
        # 使用数据的哈希值和索引生成键
        data_str = str(data.values.tobytes()) + str(current_index)
        return hashlib.md5(data_str.encode()).hexdigest()

    def get(
        self, data: pd.DataFrame, current_index: Optional[int] = None
    ) -> Optional[pd.DataFrame]:
        """获取缓存的特征

        Args:
            data: 数据DataFrame
            current_index: 当前索引

        Returns:
            缓存的特征DataFrame，如果不存在则返回None
        """
        key = self._generate_key(data, current_index)
        current_time = time.time()

        if key in self._cache:
            features, timestamp = self._cache[key]

            # 检查是否过期
            if current_time - timestamp <= self.ttl_seconds:
                self._access_times[key] = current_time
                self.hit_count += 1
                return features
            else:
                # 过期，删除
                del self._cache[key]
                if key in self._access_times:
                    del self._access_times[key]

        self.miss_count += 1
        return None

    def put(
        self,
        data: pd.DataFrame,
        features: pd.DataFrame,
        current_index: Optional[int] = None,
    ) -> None:
        """存储特征到缓存

        Args:
            data: 数据DataFrame
            features: 特征DataFrame
            current_index: 当前索引
        """
        key = self._generate_key(data, current_index)
        current_time = time.time()

        # 如果缓存已满，删除最久未访问的条目
        if len(self._cache) >= self.max_size:
            oldest_key = min(
                self._access_times.keys(), key=lambda k: self._access_times[k]
            )
            del self._cache[oldest_key]
            del self._access_times[oldest_key]

        self._cache[key] = (features.copy(), current_time)
        self._access_times[key] = current_time

    def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self._access_times.clear()
        self.hit_count = 0
        self.miss_count = 0

    @property
    def hit_rate(self) -> float:
        """计算缓存命中率"""
        total = self.hit_count + self.miss_count
        if total == 0:
            return 0.0
        return self.hit_count / total


class ImprovedDecisionTreePredictor(PredictorInterface, ABC):
    """改进版决策树预测器基类"""

    def __init__(
        self, config: Optional[DecisionTreeConfig] = None, **kwargs: Any
    ) -> None:
        """初始化改进版决策树预测器

        Args:
            config: 配置对象，如果为None则使用全局配置
            **kwargs: 额外的配置参数
        """
        # 获取配置
        if config is None:
            self.config = get_config()
        else:
            self.config = config

        # 更新配置
        if kwargs:
            self.config.update(**kwargs)

        # 决策树模型
        self.odd_even_tree: Optional[DecisionTreeClassifier] = None
        self.red_size_tree: Optional[DecisionTreeClassifier] = None
        self.blue_size_tree: Optional[DecisionTreeClassifier] = None

        # 标签编码器
        self.odd_even_encoder = LabelEncoder()
        self.red_size_encoder = LabelEncoder()
        self.blue_size_encoder = LabelEncoder()

        # 特征缓存
        if self.config.enable_feature_cache:
            self.feature_cache = FeatureCache(
                ttl_seconds=self.config.cache_ttl_seconds, max_size=1000
            )
        else:
            self.feature_cache = None

        # 性能监控
        self.performance_metrics = PerformanceMetrics()

        # 日志记录器
        self.logger = get_logger(self.__class__.__name__)

        if self.config.enable_detailed_logging:
            self.logger.info(f"初始化 {self.__class__.__name__} 完成")
            self.logger.info(f"配置: {self.config.to_dict()}")

    def _create_tree_model(self) -> DecisionTreeClassifier:
        """创建决策树模型

        Returns:
            决策树分类器实例
        """
        return DecisionTreeClassifier(**self.config.get_sklearn_params())

    def _extract_features(
        self, data: pd.DataFrame, current_index: Optional[int] = None
    ) -> pd.DataFrame:
        """提取特征（带缓存支持）

        Args:
            data: 历史数据
            current_index: 当前索引（如果提供，只处理到该索引）

        Returns:
            特征DataFrame
        """
        start_time = time.time()

        # 尝试从缓存获取
        if self.feature_cache is not None:
            cached_features = self.feature_cache.get(data, current_index)
            if cached_features is not None:
                self.performance_metrics.feature_extraction_time += (
                    time.time() - start_time
                )
                return cached_features

        # 提取特征
        features = self._do_extract_features(data, current_index)

        # 存储到缓存
        if self.feature_cache is not None:
            self.feature_cache.put(data, features, current_index)

        extraction_time = time.time() - start_time
        self.performance_metrics.feature_extraction_time += extraction_time

        if self.config.enable_detailed_logging:
            self.logger.debug(f"特征提取耗时: {extraction_time:.4f}秒")

        return features

    def _do_extract_features(
        self, data: pd.DataFrame, current_index: Optional[int] = None
    ) -> pd.DataFrame:
        """实际的特征提取逻辑

        Args:
            data: 历史数据
            current_index: 当前索引

        Returns:
            特征DataFrame
        """
        features = []

        end_index = current_index if current_index is not None else len(data) - 1
        for i in range(end_index + 1):
            row = data.iloc[i]

            # 计算当期的奇偶比和大小比
            red_balls = self._get_red_balls(row)
            blue_balls = self._get_blue_balls(row)

            red_odd_even = self._calculate_odd_even_ratio(red_balls)
            red_size = self._calculate_size_ratio(red_balls, 17)
            blue_size = self._calculate_blue_size(blue_balls)

            # 基础特征
            feature_dict = {
                "期号": row["期号"],
                "红球奇偶比": red_odd_even,
                "红球大小比": red_size,
                "蓝球大小": blue_size,
            }

            # 历史统计特征
            if i >= self.config.feature_window_size:
                recent_data = data.iloc[max(0, i - self.config.feature_window_size) : i]
                self._add_historical_features(feature_dict, recent_data)

            features.append(feature_dict)

        return pd.DataFrame(features)

    def _add_historical_features(
        self, feature_dict: Dict[str, Any], recent_data: pd.DataFrame
    ) -> None:
        """添加历史统计特征

        Args:
            feature_dict: 特征字典
            recent_data: 最近的历史数据
        """
        recent_odd_even = []
        recent_size = []
        recent_blue_size = []

        for j in range(len(recent_data)):
            hist_row = recent_data.iloc[j]
            hist_red = self._get_red_balls(hist_row)
            hist_blue = self._get_blue_balls(hist_row)

            recent_odd_even.append(self._calculate_odd_even_ratio(hist_red))
            recent_size.append(self._calculate_size_ratio(hist_red, 17))
            recent_blue_size.append(self._calculate_blue_size(hist_blue))

        # 奇偶比统计
        odd_even_counts = Counter(recent_odd_even)
        for ratio in ["3:3", "4:2", "2:4", "5:1", "1:5"]:
            feature_dict[
                f'近{self.config.feature_window_size}期_奇偶比_{ratio.replace(":", "_")}'
            ] = odd_even_counts.get(ratio, 0)

        # 大小比统计
        size_counts = Counter(recent_size)
        for ratio in ["3:3", "4:2", "2:4", "5:1", "1:5"]:
            feature_dict[
                f'近{self.config.feature_window_size}期_大小比_{ratio.replace(":", "_")}'
            ] = size_counts.get(ratio, 0)

        # 蓝球大小统计
        blue_size_counts = Counter(recent_blue_size)
        feature_dict[f"近{self.config.feature_window_size}期_蓝球大"] = (
            blue_size_counts.get("大", 0)
        )
        feature_dict[f"近{self.config.feature_window_size}期_蓝球小"] = (
            blue_size_counts.get("小", 0)
        )

    def _get_red_balls(self, row: pd.Series) -> List[int]:
        """从数据行中提取红球号码

        Args:
            row: 数据行

        Returns:
            红球号码列表
        """
        if "红球" in row:
            red_str = str(row["红球"])
            return [int(x) for x in red_str.split(",") if x.strip().isdigit()]
        else:
            # 尝试从红球1-6列中提取
            red_balls = []
            for i in range(1, 7):
                col_name = f"红球{i}"
                if col_name in row and pd.notna(row[col_name]):
                    red_balls.append(int(row[col_name]))
            return red_balls

    def _get_blue_balls(self, row: pd.Series) -> List[int]:
        """从数据行中提取蓝球号码

        Args:
            row: 数据行

        Returns:
            蓝球号码列表
        """
        if "蓝球" in row:
            blue_str = str(row["蓝球"])
            return [int(x) for x in blue_str.split(",") if x.strip().isdigit()]
        else:
            # 尝试从蓝球列中提取
            if "蓝球" in row and pd.notna(row["蓝球"]):
                return [int(row["蓝球"])]
            return []

    def _calculate_odd_even_ratio(self, red_balls: List[int]) -> str:
        """计算红球奇偶比

        Args:
            red_balls: 红球号码列表

        Returns:
            奇偶比字符串
        """
        odd_count = sum(1 for x in red_balls if x % 2 == 1)
        even_count = len(red_balls) - odd_count
        return f"{odd_count}:{even_count}"

    def _calculate_size_ratio(self, red_balls: List[int], boundary: int = 17) -> str:
        """计算红球大小比

        Args:
            red_balls: 红球号码列表
            boundary: 大小分界线

        Returns:
            大小比字符串
        """
        small_count = sum(1 for x in red_balls if x <= boundary)
        big_count = len(red_balls) - small_count
        return f"{small_count}:{big_count}"

    def _calculate_blue_size(self, blue_balls: List[int]) -> str:
        """计算蓝球大小

        Args:
            blue_balls: 蓝球号码列表

        Returns:
            蓝球大小字符串
        """
        if not blue_balls:
            return "小"
        small_count = sum(1 for x in blue_balls if x <= 6)
        big_count = len(blue_balls) - small_count
        return "小" if small_count > big_count else "大"

    def train(self, data: pd.DataFrame) -> None:
        """训练模型

        Args:
            data: 训练数据
        """
        start_time = time.time()

        try:
            # 输入验证
            if self.config.enable_input_validation:
                self._validate_training_data(data)

            # 提取特征
            features_df = self._extract_features(data)

            if len(features_df) < self.config.min_training_samples:
                raise ValueError(
                    f"训练样本数量不足: {len(features_df)} < {self.config.min_training_samples}"
                )

            # 准备训练数据
            X = self._prepare_feature_matrix(features_df)

            # 训练奇偶比模型
            y_odd_even = features_df["红球奇偶比"].values
            self.odd_even_encoder.fit(y_odd_even)
            y_odd_even_encoded = self.odd_even_encoder.transform(y_odd_even)

            self.odd_even_tree = self._create_tree_model()
            self.odd_even_tree.fit(X, y_odd_even_encoded)

            # 训练红球大小比模型
            y_red_size = features_df["红球大小比"].values
            self.red_size_encoder.fit(y_red_size)
            y_red_size_encoded = self.red_size_encoder.transform(y_red_size)

            self.red_size_tree = self._create_tree_model()
            self.red_size_tree.fit(X, y_red_size_encoded)

            # 训练蓝球大小模型
            y_blue_size = features_df["蓝球大小"].values
            self.blue_size_encoder.fit(y_blue_size)
            y_blue_size_encoded = self.blue_size_encoder.transform(y_blue_size)

            self.blue_size_tree = self._create_tree_model()
            self.blue_size_tree.fit(X, y_blue_size_encoded)

            training_time = time.time() - start_time
            self.performance_metrics.training_time = training_time

            if self.config.enable_performance_monitoring:
                self.logger.info(f"模型训练完成，耗时: {training_time:.4f}秒")
                self.logger.info(f"训练样本数: {len(features_df)}")

        except Exception as e:
            self.logger.error(f"模型训练失败: {e}")
            raise

    def _validate_training_data(self, data: pd.DataFrame) -> None:
        """验证训练数据

        Args:
            data: 训练数据
        """
        if data.empty:
            raise ValueError("训练数据为空")

        required_columns = ["期号"]
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            raise ValueError(f"缺少必要的列: {missing_columns}")

        if len(data) < self.config.min_training_samples:
            raise ValueError(
                f"训练数据行数不足: {len(data)} < {self.config.min_training_samples}"
            )

    def _prepare_feature_matrix(self, features_df: pd.DataFrame) -> np.ndarray:
        """准备特征矩阵

        Args:
            features_df: 特征DataFrame

        Returns:
            特征矩阵
        """
        # 选择数值特征
        numeric_columns = [
            col
            for col in features_df.columns
            if col.startswith("近") and ("期_" in col)
        ]

        if not numeric_columns:
            # 如果没有历史特征，使用基础特征
            return np.ones((len(features_df), 1))  # 占位符特征

        return features_df[numeric_columns].fillna(0).values

    def predict(self, data: pd.DataFrame) -> PredictionResult:
        """预测

        Args:
            data: 历史数据

        Returns:
            预测结果
        """
        start_time = time.time()

        try:
            self.performance_metrics.total_predictions += 1

            # 检查模型是否已训练
            if not self._is_trained():
                raise ValueError("模型尚未训练")

            # 输入验证
            if self.config.enable_input_validation:
                self._validate_prediction_data(data)

            # 提取特征
            features_df = self._extract_features(data)

            if features_df.empty:
                raise ValueError("无法提取特征")

            # 使用最后一行特征进行预测
            last_features = features_df.iloc[-1:]
            X = self._prepare_feature_matrix(last_features)

            # 预测各个目标
            odd_even_pred = self._predict_with_confidence(
                self.odd_even_tree, self.odd_even_encoder, X
            )
            red_size_pred = self._predict_with_confidence(
                self.red_size_tree, self.red_size_encoder, X
            )
            blue_size_pred = self._predict_with_confidence(
                self.blue_size_tree, self.blue_size_encoder, X
            )

            # 生成号码
            red_balls, blue_balls = self._generate_numbers(
                odd_even_pred, red_size_pred, blue_size_pred
            )

            # 创建预测结果
            result = PredictionResult(
                red_balls=red_balls,
                blue_balls=blue_balls,
                confidence=min(
                    odd_even_pred[0][1], red_size_pred[0][1], blue_size_pred[0][1]
                ),
                metadata={
                    "odd_even_prediction": odd_even_pred,
                    "red_size_prediction": red_size_pred,
                    "blue_size_prediction": blue_size_pred,
                    "predictor_name": self.get_predictor_name(),
                    "predictor_version": self.get_predictor_version(),
                    "feature_cache_hit_rate": (
                        self.feature_cache.hit_rate if self.feature_cache else 0.0
                    ),
                },
            )

            prediction_time = time.time() - start_time
            self.performance_metrics.prediction_time += prediction_time
            self.performance_metrics.successful_predictions += 1

            if self.config.enable_detailed_logging:
                self.logger.debug(f"预测完成，耗时: {prediction_time:.4f}秒")

            return result

        except Exception as e:
            self.logger.error(f"预测失败: {e}")
            raise

    def _is_trained(self) -> bool:
        """检查模型是否已训练

        Returns:
            是否已训练
        """
        return (
            self.odd_even_tree is not None
            and self.red_size_tree is not None
            and self.blue_size_tree is not None
        )

    def _validate_prediction_data(self, data: pd.DataFrame) -> None:
        """验证预测数据

        Args:
            data: 预测数据
        """
        if data.empty:
            raise ValueError("预测数据为空")

        if "期号" not in data.columns:
            raise ValueError("预测数据缺少期号列")

    def _predict_with_confidence(
        self, model: DecisionTreeClassifier, encoder: LabelEncoder, X: np.ndarray
    ) -> List[Tuple[str, float]]:
        """带置信度的预测

        Args:
            model: 决策树模型
            encoder: 标签编码器
            X: 特征矩阵

        Returns:
            预测结果和置信度列表
        """
        # 获取预测概率
        probabilities = model.predict_proba(X)[0]

        # 获取类别标签
        classes = encoder.classes_

        # 组合结果并按概率排序
        results = [(classes[i], probabilities[i]) for i in range(len(classes))]
        results.sort(key=lambda x: x[1], reverse=True)

        return results

    @abstractmethod
    def _generate_numbers(
        self,
        odd_even_pred: List[Tuple[str, float]],
        red_size_pred: List[Tuple[str, float]],
        blue_size_pred: List[Tuple[str, float]],
    ) -> Tuple[List[int], List[int]]:
        """根据预测结果生成号码

        Args:
            odd_even_pred: 奇偶比预测结果
            red_size_pred: 红球大小比预测结果
            blue_size_pred: 蓝球大小预测结果

        Returns:
            (红球列表, 蓝球列表)
        """
        pass

    def save_model(self, model_path: str) -> None:
        """保存模型

        Args:
            model_path: 模型保存路径
        """
        try:
            model_dir = Path(model_path).parent
            model_dir.mkdir(parents=True, exist_ok=True)

            model_data = {
                "odd_even_tree": self.odd_even_tree,
                "red_size_tree": self.red_size_tree,
                "blue_size_tree": self.blue_size_tree,
                "odd_even_encoder": self.odd_even_encoder,
                "red_size_encoder": self.red_size_encoder,
                "blue_size_encoder": self.blue_size_encoder,
                "config": self.config.to_dict(),
                "performance_metrics": self.performance_metrics,
                "predictor_name": self.get_predictor_name(),
                "predictor_version": self.get_predictor_version(),
            }

            with open(model_path, "wb") as f:
                pickle.dump(model_data, f)

            self.logger.info(f"模型已保存到: {model_path}")

        except Exception as e:
            self.logger.error(f"保存模型失败: {e}")
            raise

    def load_model(self, model_path: str) -> None:
        """加载模型

        Args:
            model_path: 模型文件路径
        """
        try:
            with open(model_path, "rb") as f:
                model_data = pickle.load(f)

            self.odd_even_tree = model_data["odd_even_tree"]
            self.red_size_tree = model_data["red_size_tree"]
            self.blue_size_tree = model_data["blue_size_tree"]
            self.odd_even_encoder = model_data["odd_even_encoder"]
            self.red_size_encoder = model_data["red_size_encoder"]
            self.blue_size_encoder = model_data["blue_size_encoder"]

            # 更新配置（如果存在）
            if "config" in model_data:
                loaded_config = DecisionTreeConfig(**model_data["config"])
                self.config.update(**loaded_config.to_dict())

            # 恢复性能指标（如果存在）
            if "performance_metrics" in model_data:
                self.performance_metrics = model_data["performance_metrics"]

            self.logger.info(f"模型已从 {model_path} 加载")

        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            raise

    def get_performance_metrics(self) -> PerformanceMetrics:
        """获取性能指标

        Returns:
            性能指标
        """
        if self.feature_cache is not None:
            self.performance_metrics.cache_hit_rate = self.feature_cache.hit_rate

        return self.performance_metrics

    def clear_cache(self) -> None:
        """清空缓存"""
        if self.feature_cache is not None:
            self.feature_cache.clear()
            self.logger.info("特征缓存已清空")

    @abstractmethod
    def get_predictor_name(self) -> str:
        """获取预测器名称

        Returns:
            预测器名称
        """
        pass

    @abstractmethod
    def get_predictor_version(self) -> str:
        """获取预测器版本

        Returns:
            预测器版本
        """
        pass
