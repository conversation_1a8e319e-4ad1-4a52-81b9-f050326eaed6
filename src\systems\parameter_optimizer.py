"""
参数优化器 - 用于提升选号系统命中率
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import joblib
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
from sklearn.metrics import make_scorer
import itertools
import random

from .optimized_selection_config import (
    OptimizedSelectionConfig,
    AGGRESSIVE_CONFIG,
    BALANCED_CONFIG,
    CONSERVATIVE_CONFIG,
)
from .number_selection_system import NumberSelectionSystem, SelectionMethod
from .number_selection_adapter import create_selection_adapter

# 统一回测框架
from ..framework import BacktestFramework, BacktestConfig, BacktestResult
from ..framework.interfaces import PredictorInterface


class ParameterOptimizer:
    """参数优化器"""

    def __init__(self, config: Optional[OptimizedSelectionConfig] = None):
        self.config = config or BALANCED_CONFIG
        self.logger = logging.getLogger(__name__)

        # 优化历史
        self.optimization_history = []
        self.best_params = {}
        self.best_score = 0.0

        # 结果保存路径
        self.results_dir = Path("optimization_results")
        self.results_dir.mkdir(parents=True, exist_ok=True)

        self.logger.info("参数优化器初始化完成")

    def optimize_hyperparameters(
        self, data_path: str, optimization_method: str = "optuna", n_trials: int = 100
    ) -> Dict[str, Any]:
        """
        超参数优化

        Args:
            data_path: 数据文件路径
            optimization_method: 优化方法 ('optuna', 'grid_search', 'random_search')
            n_trials: 优化试验次数

        Returns:
            Dict: 优化结果
        """
        self.logger.info(
            f"开始超参数优化，方法: {optimization_method}, 试验次数: {n_trials}"
        )

        if optimization_method == "random_search":
            return self._optimize_with_random_search(data_path, n_trials)
        else:
            raise ValueError(f"不支持的优化方法: {optimization_method}")

    def _optimize_with_random_search(
        self, data_path: str, n_trials: int
    ) -> Dict[str, Any]:
        """使用随机搜索进行参数优化"""

        best_params = {}
        best_score = 0.0
        optimization_history = []

        self.logger.info(f"开始随机搜索优化，试验次数: {n_trials}")

        for trial_num in range(n_trials):
            # 随机生成参数
            config = OptimizedSelectionConfig()

            # 数据处理参数
            config.sequence_length = random.randint(20, 60)
            config.train_ratio = random.uniform(0.75, 0.9)

            # 深度学习参数
            config.dl_epochs = random.randint(50, 200)  # 减少范围以加速测试
            config.dl_batch_size = random.choice([8, 16, 32, 64])
            config.dl_initial_learning_rate = random.uniform(1e-4, 1e-2)

            # LSTM参数
            lstm_units = random.choice([128, 256, 512])
            lstm_layers = random.randint(2, 4)
            lstm_dropout = random.uniform(0.2, 0.6)

            config.lstm_config.update(
                {"units": lstm_units, "layers": lstm_layers, "dropout": lstm_dropout}
            )

            # 传统ML参数
            config.rf_n_estimators = random.randint(100, 300)
            config.rf_max_depth = random.randint(10, 20)
            config.gb_n_estimators = random.randint(100, 300)
            config.gb_learning_rate = random.uniform(0.01, 0.2)

            # 评估配置
            try:
                score = self._evaluate_config(config, data_path)
                optimization_history.append((trial_num, score))

                if score > best_score:
                    best_score = score
                    best_params = {
                        "sequence_length": config.sequence_length,
                        "train_ratio": config.train_ratio,
                        "dl_epochs": config.dl_epochs,
                        "dl_batch_size": config.dl_batch_size,
                        "dl_learning_rate": config.dl_initial_learning_rate,
                        "lstm_units": lstm_units,
                        "lstm_layers": lstm_layers,
                        "lstm_dropout": lstm_dropout,
                        "rf_n_estimators": config.rf_n_estimators,
                        "rf_max_depth": config.rf_max_depth,
                        "gb_n_estimators": config.gb_n_estimators,
                        "gb_learning_rate": config.gb_learning_rate,
                    }

                self.logger.info(f"试验 {trial_num + 1}/{n_trials}: 得分 {score:.4f}")

            except Exception as e:
                self.logger.warning(f"试验 {trial_num + 1} 失败: {e}")
                optimization_history.append((trial_num, 0.0))

        self.logger.info(f"随机搜索优化完成，最佳得分: {best_score:.4f}")

        return {
            "method": "random_search",
            "best_params": best_params,
            "best_score": best_score,
            "optimization_history": optimization_history,
        }

    def _evaluate_config(
        self, config: OptimizedSelectionConfig, data_path: str
    ) -> float:
        """评估配置性能 - 使用统一回测框架"""
        try:
            # 加载数据
            if not Path(data_path).exists():
                self.logger.warning(f"数据文件不存在: {data_path}")
                return 0.0

            data = pd.read_csv(data_path)
            if data.empty:
                self.logger.warning("数据为空")
                return 0.0

            # 创建回测框架
            framework = BacktestFramework(data)

            # 创建回测配置 - 使用较少期数进行快速评估
            backtest_config = BacktestConfig(
                num_periods=min(10, len(data) // 10),  # 最多10期回测
                min_train_periods=max(20, len(data) // 5),  # 最少训练期数
                display_periods=0,  # 不显示详细结果
                enable_detailed_output=False,  # 关闭详细输出
            )

            # 创建优化的选号适配器
            # 处理配置兼容性问题
            config_dict = config.__dict__.copy()

            # 如果是OptimizedSelectionConfig，需要映射到SelectionConfig的字段
            if not hasattr(config, "ml_n_estimators"):
                config_dict["ml_n_estimators"] = config_dict.get("rf_n_estimators", 100)
                config_dict["ml_max_depth"] = config_dict.get("rf_max_depth", 10)
                config_dict["ml_random_state"] = 42

            adapter = create_selection_adapter(
                method=SelectionMethod.ENSEMBLE, optimized=True, config=config_dict
            )

            # 运行回测
            result = framework.run_backtest(adapter, backtest_config)

            # 计算综合得分
            if result.statistics:
                # 使用多个指标的加权平均
                red_odd_even_rate = result.statistics.red_odd_even_rate
                red_size_rate = result.statistics.red_size_rate
                blue_size_rate = result.statistics.blue_size_rate
                hit_2_plus_1_rate = result.statistics.hit_2_plus_1_rate

                # 综合得分：各项命中率的加权平均
                score = (
                    red_odd_even_rate * 0.3
                    + red_size_rate * 0.3
                    + blue_size_rate * 0.2
                    + hit_2_plus_1_rate * 0.2
                )
            else:
                # 备用计算方法
                total_periods = len(result.period_results)
                if total_periods > 0:
                    success_count = sum(
                        1 for period in result.period_results if period.success
                    )
                    score = success_count / total_periods
                else:
                    score = 0.0

            self.logger.debug(f"配置评估完成，得分: {score:.4f}")
            return score

        except Exception as e:
            self.logger.warning(f"配置评估失败: {e}")
            return 0.0

    def apply_optimized_config(
        self, optimization_result: Dict[str, Any], config_type: str = "balanced"
    ) -> OptimizedSelectionConfig:
        """应用优化后的配置"""

        if config_type == "aggressive":
            base_config = AGGRESSIVE_CONFIG
        elif config_type == "conservative":
            base_config = CONSERVATIVE_CONFIG
        else:
            base_config = BALANCED_CONFIG

        # 应用优化参数
        best_params = optimization_result["best_params"]

        # 更新配置
        if "sequence_length" in best_params:
            base_config.sequence_length = best_params["sequence_length"]
        if "train_ratio" in best_params:
            base_config.train_ratio = best_params["train_ratio"]
        if "dl_epochs" in best_params:
            base_config.dl_epochs = best_params["dl_epochs"]
        if "dl_batch_size" in best_params:
            base_config.dl_batch_size = best_params["dl_batch_size"]
        if "dl_learning_rate" in best_params:
            base_config.dl_initial_learning_rate = best_params["dl_learning_rate"]

        # 更新LSTM配置
        if "lstm_units" in best_params:
            base_config.lstm_config["units"] = best_params["lstm_units"]
        if "lstm_layers" in best_params:
            base_config.lstm_config["layers"] = best_params["lstm_layers"]
        if "lstm_dropout" in best_params:
            base_config.lstm_config["dropout"] = best_params["lstm_dropout"]

        # 更新传统ML配置
        if "rf_n_estimators" in best_params:
            base_config.rf_n_estimators = best_params["rf_n_estimators"]
        if "rf_max_depth" in best_params:
            base_config.rf_max_depth = best_params["rf_max_depth"]
        if "gb_n_estimators" in best_params:
            base_config.gb_n_estimators = best_params["gb_n_estimators"]
        if "gb_learning_rate" in best_params:
            base_config.gb_learning_rate = best_params["gb_learning_rate"]

        self.logger.info("优化配置应用完成")
        return base_config

    def run_optimization_comparison(self, data_path: str) -> Dict[str, Any]:
        """运行优化对比测试"""
        self.logger.info("开始运行优化对比测试...")

        results = {}

        # 加载数据
        import pandas as pd

        data = pd.read_csv(data_path)

        # 1. 原始配置测试
        self.logger.info("测试原始配置...")
        original_system = NumberSelectionSystem()
        original_result = original_system.run_comparison(data)
        results["original"] = original_result

        # 2. 平衡优化配置测试
        self.logger.info("测试平衡优化配置...")
        balanced_system = NumberSelectionSystem(BALANCED_CONFIG)
        balanced_result = balanced_system.run_comparison(data)
        results["balanced_optimized"] = balanced_result

        # 3. 激进优化配置测试
        self.logger.info("测试激进优化配置...")
        aggressive_system = NumberSelectionSystem(AGGRESSIVE_CONFIG)
        aggressive_result = aggressive_system.run_comparison(data)
        results["aggressive_optimized"] = aggressive_result

        # 4. 保守优化配置测试
        self.logger.info("测试保守优化配置...")
        conservative_system = NumberSelectionSystem(CONSERVATIVE_CONFIG)
        conservative_result = conservative_system.run_comparison(data)
        results["conservative_optimized"] = conservative_result

        # 生成对比报告
        comparison_report = self._generate_comparison_report(results)

        # 保存结果
        self._save_optimization_results(results, comparison_report)

        self.logger.info("优化对比测试完成")
        return {
            "results": results,
            "comparison_report": comparison_report,
            "recommendation": self._get_best_configuration(results),
        }

    def _generate_comparison_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成对比报告"""
        report = {"summary": {}, "improvements": {}, "recommendations": []}

        # 提取关键指标
        for config_name, result in results.items():
            dl_hit_rate = result["deep_learning_results"].get("hit_rate", 0)
            ml_hit_rate = result["traditional_ml_results"].get("hit_rate", 0)
            avg_hit_rate = (dl_hit_rate + ml_hit_rate) / 2

            report["summary"][config_name] = {
                "deep_learning_hit_rate": dl_hit_rate,
                "traditional_ml_hit_rate": ml_hit_rate,
                "average_hit_rate": avg_hit_rate,
            }

        # 计算改进幅度
        if "original" in results:
            original_avg = report["summary"]["original"]["average_hit_rate"]

            for config_name in [
                "balanced_optimized",
                "aggressive_optimized",
                "conservative_optimized",
            ]:
                if config_name in report["summary"]:
                    optimized_avg = report["summary"][config_name]["average_hit_rate"]
                    improvement = ((optimized_avg - original_avg) / original_avg) * 100
                    report["improvements"][config_name] = improvement

        return report

    def _get_best_configuration(self, results: Dict[str, Any]) -> str:
        """获取最佳配置推荐"""
        best_config = "original"
        best_score = 0.0

        for config_name, result in results.items():
            dl_hit_rate = result["deep_learning_results"].get("hit_rate", 0)
            ml_hit_rate = result["traditional_ml_results"].get("hit_rate", 0)
            avg_hit_rate = (dl_hit_rate + ml_hit_rate) / 2

            if avg_hit_rate > best_score:
                best_score = avg_hit_rate
                best_config = config_name

        return best_config

    def _save_optimization_results(
        self, results: Dict[str, Any], report: Dict[str, Any]
    ):
        """保存优化结果"""
        # 保存详细结果
        results_file = self.results_dir / "optimization_results.joblib"
        joblib.dump(results, results_file)

        # 保存报告
        report_file = self.results_dir / "optimization_report.joblib"
        joblib.dump(report, report_file)

        self.logger.info(f"优化结果已保存到: {self.results_dir}")


def create_optimized_system(
    optimization_level: str = "balanced",
) -> NumberSelectionSystem:
    """创建优化的选号系统"""

    config_map = {
        "aggressive": AGGRESSIVE_CONFIG,
        "balanced": BALANCED_CONFIG,
        "conservative": CONSERVATIVE_CONFIG,
    }

    config = config_map.get(optimization_level, BALANCED_CONFIG)
    return NumberSelectionSystem(config)
