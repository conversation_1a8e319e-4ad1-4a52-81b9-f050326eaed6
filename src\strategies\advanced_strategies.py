#!/usr/bin/env python3
"""
高级预测策略模块
包含多种创新的预测方法和验证策略
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Any
from collections import defaultdict, Counter
import random
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler


class AdvancedStrategies:
    """高级预测策略集合"""

    def __init__(self, data: pd.DataFrame):
        """
        初始化高级策略

        Args:
            data: 历史数据
        """
        self.data = data
        self.scaler = StandardScaler()

    def _parse_balls(self, row) -> Tuple[List[int], List[int]]:
        """
        解析单行数据中的红球和蓝球

        Args:
            row: 数据行

        Returns:
            (红球列表, 蓝球列表)
        """
        red_balls = [int(row[f"红球{i}"]) for i in range(1, 6)]
        blue_balls = [int(row[f"蓝球{i}"]) for i in range(1, 3)]
        return red_balls, blue_balls

    def neural_network_prediction(self, periods: int = 100) -> Dict[str, Any]:
        """
        神经网络预测策略
        基于深度学习模型预测号码模式

        Args:
            periods: 训练期数

        Returns:
            预测结果
        """
        try:
            from sklearn.neural_network import MLPClassifier

            # 准备训练数据
            recent_data = self.data.tail(periods)

            # 特征工程
            features = []
            targets_red = []
            targets_blue = []

            for i in range(5, len(recent_data)):
                # 使用前5期数据作为特征
                feature_row = []
                for j in range(5):
                    row = recent_data.iloc[i - 5 + j]
                    red_balls = eval(row["红球"])
                    blue_balls = eval(row["蓝球"])

                    # 红球特征
                    red_features = [0] * 35
                    for ball in red_balls:
                        red_features[ball - 1] = 1

                    # 蓝球特征
                    blue_features = [0] * 12
                    for ball in blue_balls:
                        blue_features[ball - 1] = 1

                    feature_row.extend(red_features + blue_features)

                features.append(feature_row)

                # 目标值
                current_row = recent_data.iloc[i]
                current_red = eval(current_row["红球"])
                current_blue = eval(current_row["蓝球"])

                targets_red.append(current_red)
                targets_blue.append(current_blue)

            if len(features) < 10:
                return {"error": "训练数据不足"}

            features = np.array(features)

            # 训练红球模型
            red_predictions = []
            for pos in range(5):  # 5个红球位置
                y_red = [
                    targets[pos] if len(targets) > pos else 1 for targets in targets_red
                ]

                model = MLPClassifier(
                    hidden_layer_sizes=(100, 50), max_iter=500, random_state=42
                )
                model.fit(features, y_red)

                # 预测
                last_feature = features[-1].reshape(1, -1)
                pred = model.predict(last_feature)[0]
                red_predictions.append(int(pred))

            # 训练蓝球模型
            blue_predictions = []
            for pos in range(2):  # 2个蓝球位置
                y_blue = [
                    targets[pos] if len(targets) > pos else 1
                    for targets in targets_blue
                ]

                model = MLPClassifier(
                    hidden_layer_sizes=(50, 25), max_iter=500, random_state=42
                )
                model.fit(features, y_blue)

                # 预测
                last_feature = features[-1].reshape(1, -1)
                pred = model.predict(last_feature)[0]
                blue_predictions.append(int(pred))

            # 确保号码有效性
            red_predictions = [max(1, min(35, x)) for x in red_predictions]
            blue_predictions = [max(1, min(12, x)) for x in blue_predictions]

            # 去重并补充
            red_predictions = list(set(red_predictions))
            while len(red_predictions) < 5:
                candidate = random.randint(1, 35)
                if candidate not in red_predictions:
                    red_predictions.append(candidate)

            blue_predictions = list(set(blue_predictions))
            while len(blue_predictions) < 2:
                candidate = random.randint(1, 12)
                if candidate not in blue_predictions:
                    blue_predictions.append(candidate)

            return {
                "strategy": "neural_network",
                "red_balls": sorted(red_predictions[:5]),
                "blue_balls": sorted(blue_predictions[:2]),
                "confidence": 0.7,
            }

        except Exception as e:
            return {"error": f"神经网络预测失败: {e}"}

    def ensemble_voting_prediction(self) -> Dict[str, Any]:
        """
        集成投票预测策略
        结合多种算法的投票结果
        """
        try:
            strategies = [
                self.frequency_based_prediction(),
                self.pattern_recognition_prediction(),
                self.statistical_regression_prediction(),
                self.random_forest_prediction(),
            ]

            # 收集所有有效预测
            valid_strategies = [s for s in strategies if "error" not in s]

            if not valid_strategies:
                return {"error": "所有策略都失败了"}

            # 红球投票
            red_votes = defaultdict(int)
            for strategy in valid_strategies:
                for ball in strategy.get("red_balls", []):
                    red_votes[ball] += 1

            # 蓝球投票
            blue_votes = defaultdict(int)
            for strategy in valid_strategies:
                for ball in strategy.get("blue_balls", []):
                    blue_votes[ball] += 1

            # 选择得票最多的号码
            red_result = sorted(red_votes.items(), key=lambda x: x[1], reverse=True)[:5]
            blue_result = sorted(blue_votes.items(), key=lambda x: x[1], reverse=True)[
                :2
            ]

            red_balls = [ball for ball, votes in red_result]
            blue_balls = [ball for ball, votes in blue_result]

            # 如果票数不够，随机补充
            while len(red_balls) < 5:
                candidate = random.randint(1, 35)
                if candidate not in red_balls:
                    red_balls.append(candidate)

            while len(blue_balls) < 2:
                candidate = random.randint(1, 12)
                if candidate not in blue_balls:
                    blue_balls.append(candidate)

            return {
                "strategy": "ensemble_voting",
                "red_balls": sorted(red_balls),
                "blue_balls": sorted(blue_balls),
                "confidence": len(valid_strategies) / 4.0,
                "participating_strategies": len(valid_strategies),
            }

        except Exception as e:
            return {"error": f"集成投票预测失败: {e}"}

    def frequency_based_prediction(self) -> Dict[str, Any]:
        """基于频率的预测"""
        try:
            recent_data = self.data.tail(100)

            red_counter = Counter()
            blue_counter = Counter()

            for _, row in recent_data.iterrows():
                red_balls, blue_balls = self._parse_balls(row)
                red_counter.update(red_balls)
                blue_counter.update(blue_balls)

            # 选择频率最高的号码
            red_balls = [ball for ball, count in red_counter.most_common(5)]
            blue_balls = [ball for ball, count in blue_counter.most_common(2)]

            return {
                "strategy": "frequency_based",
                "red_balls": red_balls,
                "blue_balls": blue_balls,
                "confidence": 0.6,
            }

        except Exception as e:
            return {"error": f"频率预测失败: {e}"}

    def pattern_recognition_prediction(self) -> Dict[str, Any]:
        """模式识别预测"""
        try:
            recent_data = self.data.tail(50)

            # 分析连号模式
            consecutive_patterns = []
            for _, row in recent_data.iterrows():
                red_balls, _ = self._parse_balls(row)
                red_balls = sorted(red_balls)
                consecutive = []
                for i in range(len(red_balls) - 1):
                    if red_balls[i + 1] - red_balls[i] == 1:
                        consecutive.extend([red_balls[i], red_balls[i + 1]])
                consecutive_patterns.extend(list(set(consecutive)))

            # 选择最常见的连号
            consecutive_counter = Counter(consecutive_patterns)
            common_consecutive = [
                ball for ball, count in consecutive_counter.most_common(3)
            ]

            # 补充其他号码
            red_balls = common_consecutive.copy()
            while len(red_balls) < 5:
                candidate = random.randint(1, 35)
                if candidate not in red_balls:
                    red_balls.append(candidate)

            # 蓝球简单随机选择
            blue_balls = random.sample(range(1, 13), 2)

            return {
                "strategy": "pattern_recognition",
                "red_balls": sorted(red_balls[:5]),
                "blue_balls": sorted(blue_balls),
                "confidence": 0.5,
            }

        except Exception as e:
            return {"error": f"模式识别失败: {e}"}

    def statistical_regression_prediction(self) -> Dict[str, Any]:
        """统计回归预测"""
        try:
            recent_data = self.data.tail(80)

            # 特征工程：计算各种统计特征
            features = []
            targets = []

            for i in range(5, len(recent_data)):
                # 前5期的统计特征
                window_data = recent_data.iloc[i - 5 : i]

                feature_row = []

                # 红球统计特征
                all_red = []
                for _, row in window_data.iterrows():
                    all_red.extend(eval(row["红球"]))

                feature_row.extend(
                    [
                        np.mean(all_red),  # 平均值
                        np.std(all_red),  # 标准差
                        max(all_red) - min(all_red),  # 跨度
                        len(set(all_red)),  # 不重复数量
                    ]
                )

                # 蓝球统计特征
                all_blue = []
                for _, row in window_data.iterrows():
                    all_blue.extend(eval(row["蓝球"]))

                feature_row.extend([np.mean(all_blue), np.std(all_blue)])

                features.append(feature_row)

                # 目标：下一期的号码
                next_row = recent_data.iloc[i]
                next_red = eval(next_row["红球"])
                next_blue = eval(next_row["蓝球"])
                targets.append(next_red + next_blue)

            if len(features) < 5:
                return {"error": "回归训练数据不足"}

            # 使用逻辑回归预测每个号码的出现概率
            features = np.array(features)

            # 预测红球
            red_probs = {}
            for ball in range(1, 36):
                y = [1 if ball in target[:5] else 0 for target in targets]
                if sum(y) > 0:  # 确保有正样本
                    model = LogisticRegression(random_state=42)
                    model.fit(features, y)
                    prob = model.predict_proba(features[-1].reshape(1, -1))[0][1]
                    red_probs[ball] = prob

            # 选择概率最高的5个红球
            red_balls = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)[:5]
            red_balls = [ball for ball, prob in red_balls]

            # 预测蓝球
            blue_probs = {}
            for ball in range(1, 13):
                y = [1 if ball in target[5:] else 0 for target in targets]
                if sum(y) > 0:
                    model = LogisticRegression(random_state=42)
                    model.fit(features, y)
                    prob = model.predict_proba(features[-1].reshape(1, -1))[0][1]
                    blue_probs[ball] = prob

            blue_balls = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)[
                :2
            ]
            blue_balls = [ball for ball, prob in blue_balls]

            return {
                "strategy": "statistical_regression",
                "red_balls": red_balls,
                "blue_balls": blue_balls,
                "confidence": 0.65,
            }

        except Exception as e:
            return {"error": f"统计回归失败: {e}"}

    def random_forest_prediction(self) -> Dict[str, Any]:
        """随机森林预测"""
        try:
            recent_data = self.data.tail(100)

            # 准备特征和标签
            features = []
            red_targets = []
            blue_targets = []

            for i in range(3, len(recent_data)):
                # 使用前3期作为特征
                feature_row = []
                for j in range(3):
                    row = recent_data.iloc[i - 3 + j]
                    red_balls = eval(row["红球"])
                    blue_balls = eval(row["蓝球"])

                    # 编码为二进制特征
                    red_binary = [1 if k + 1 in red_balls else 0 for k in range(35)]
                    blue_binary = [1 if k + 1 in blue_balls else 0 for k in range(12)]

                    feature_row.extend(red_binary + blue_binary)

                features.append(feature_row)

                # 当前期作为目标
                current_row = recent_data.iloc[i]
                current_red = eval(current_row["红球"])
                current_blue = eval(current_row["蓝球"])

                red_targets.append(current_red)
                blue_targets.append(current_blue)

            if len(features) < 10:
                return {"error": "随机森林训练数据不足"}

            features = np.array(features)

            # 预测每个号码的出现概率
            red_predictions = []
            for ball in range(1, 36):
                y = [1 if ball in target else 0 for target in red_targets]
                if sum(y) > 0:
                    rf = RandomForestClassifier(n_estimators=50, random_state=42)
                    rf.fit(features, y)
                    prob = rf.predict_proba(features[-1].reshape(1, -1))[0][1]
                    red_predictions.append((ball, prob))

            blue_predictions = []
            for ball in range(1, 13):
                y = [1 if ball in target else 0 for target in blue_targets]
                if sum(y) > 0:
                    rf = RandomForestClassifier(n_estimators=30, random_state=42)
                    rf.fit(features, y)
                    prob = rf.predict_proba(features[-1].reshape(1, -1))[0][1]
                    blue_predictions.append((ball, prob))

            # 选择概率最高的号码
            red_predictions.sort(key=lambda x: x[1], reverse=True)
            blue_predictions.sort(key=lambda x: x[1], reverse=True)

            red_balls = [ball for ball, prob in red_predictions[:5]]
            blue_balls = [ball for ball, prob in blue_predictions[:2]]

            return {
                "strategy": "random_forest",
                "red_balls": red_balls,
                "blue_balls": blue_balls,
                "confidence": 0.7,
            }

        except Exception as e:
            return {"error": f"随机森林预测失败: {e}"}

    def get_all_strategies_predictions(self) -> List[Dict[str, Any]]:
        """获取所有策略的预测结果"""
        strategies = [
            self.frequency_based_prediction,
            self.pattern_recognition_prediction,
            self.statistical_regression_prediction,
            self.random_forest_prediction,
            self.neural_network_prediction,
            self.ensemble_voting_prediction,
        ]

        results = []
        for strategy_func in strategies:
            try:
                result = strategy_func()
                results.append(result)
            except Exception as e:
                results.append({"error": f"策略执行失败: {e}"})

        return results
