#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小验证脚本 - 仅测试不依赖外部库的核心模块
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_exceptions():
    """测试异常类模块"""
    print("测试异常类模块...")
    
    try:
        from core.exceptions import (
            LotteryPredictorException,
            DataException,
            ModelException,
            PredictionException,
            ConfigurationException,
            ValidationException,
            AlgorithmException,
            ResourceException,
            ErrorCode
        )
        print("[OK] 异常类导入成功")
        
        # 测试异常实例化
        base_exception = LotteryPredictorException("测试异常")
        data_exception = DataException("数据异常", ErrorCode.DATA_VALIDATION_ERROR)
        print("[OK] 异常实例化成功")
        
        return True
    except Exception as e:
        print(f"[FAIL] 异常类测试失败: {e}")
        return False

def test_config_enums():
    """测试配置枚举"""
    print("测试配置枚举...")
    
    try:
        from core.config_manager import (
            ConfigFormat,
            ConfigSource,
            ValidationLevel
        )
        print("[OK] 配置枚举导入成功")
        
        # 测试枚举值
        assert ConfigFormat.JSON.value == "json"
        assert ConfigSource.FILE.value == "file"
        assert ValidationLevel.NONE.value == "none"
        print("[OK] 枚举值验证成功")
        
        return True
    except Exception as e:
        print(f"[FAIL] 配置枚举测试失败: {e}")
        return False

def test_basic_imports():
    """测试基础导入（不依赖pandas的模块）"""
    print("测试基础导入...")
    
    try:
        # 测试工厂模式相关
        from core.predictor_factory import PredictorFactory, PredictorRegistry
        print("[OK] 预测器工厂导入成功")
        
        return True
    except Exception as e:
        print(f"[FAIL] 基础导入测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("测试目录结构...")
    
    try:
        # 检查关键目录是否存在
        src_dir = project_root / "src"
        core_dir = src_dir / "core"
        models_dir = src_dir / "models"
        algorithms_dir = src_dir / "algorithms"
        
        assert src_dir.exists(), "src目录不存在"
        assert core_dir.exists(), "core目录不存在"
        assert models_dir.exists(), "models目录不存在"
        assert algorithms_dir.exists(), "algorithms目录不存在"
        
        print("[OK] 目录结构验证成功")
        
        # 检查关键文件是否存在
        config_manager = core_dir / "config_manager.py"
        exceptions = core_dir / "exceptions.py"
        
        assert config_manager.exists(), "config_manager.py不存在"
        assert exceptions.exists(), "exceptions.py不存在"
        
        print("[OK] 关键文件验证成功")
        
        return True
    except Exception as e:
        print(f"[FAIL] 目录结构测试失败: {e}")
        return False

def test_file_cleanup():
    """测试文件清理结果"""
    print("测试文件清理结果...")
    
    try:
        # 检查已删除的重复文件是否确实被删除
        deleted_files = [
            "src/config/config_manager.py",
            "src/core/configuration_manager.py",
            "src/decision_tree/odd_even_tree.py",
            "src/decision_tree/size_ratio_tree.py",
            "src/models/neural/neural_predictor.py",
            "src/models/unified_predictor.py"
        ]
        
        for file_path in deleted_files:
            full_path = project_root / file_path
            if full_path.exists():
                print(f"[WARN] 文件应该被删除但仍存在: {file_path}")
            else:
                print(f"[OK] 文件已正确删除: {file_path}")
        
        # 检查保留的核心文件是否存在
        preserved_files = [
            "src/core/config_manager.py",
            "src/decision_tree/improved_odd_even_tree.py",
            "src/models/enhanced_size_ratio_predictor.py",
            "src/models/unified_odd_even_predictor.py"
        ]
        
        for file_path in preserved_files:
            full_path = project_root / file_path
            if full_path.exists():
                print(f"[OK] 核心文件已保留: {file_path}")
            else:
                print(f"[WARN] 核心文件缺失: {file_path}")
        
        return True
    except Exception as e:
        print(f"[FAIL] 文件清理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("开始最小验证测试")
    print("=" * 50)
    
    tests = [
        test_directory_structure,
        test_file_cleanup,
        test_exceptions,
        test_config_enums,
        test_basic_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("所有最小验证测试通过！")
        return True
    else:
        print("部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)