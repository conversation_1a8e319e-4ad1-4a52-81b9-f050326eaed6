"""
V4.0 Transformer配置模块
定义V4.0 Transformer预测器的所有配置参数
"""

from dataclasses import dataclass, field
from typing import Dict, Tuple, List, Any
from enum import Enum


class SamplingStrategy(Enum):
    """采样策略枚举"""

    STANDARD = "standard"
    TOP_K = "top_k"
    NUCLEUS = "nucleus"
    TEMPERATURE = "temperature"


class AttentionType(Enum):
    """注意力类型枚举"""

    MULTI_HEAD = "multi_head"
    CROSS_TASK = "cross_task"
    SELF_ATTENTION = "self_attention"


@dataclass
class V4TransformerConfig:
    """V4.0 Transformer配置类"""

    # ========== 架构参数 ==========
    d_model: int = 144  # 模型维度 (144 = 12 * 12)
    num_heads: int = 12  # 注意力头数量
    num_layers: int = 6  # Transformer层数
    dff: int = 512  # 前馈网络维度
    dropout_rate: float = 0.1  # Dropout率

    # ========== 序列参数 ==========
    sequence_length: int = 25  # 输入序列长度（期数）
    max_position_encoding: int = 100  # 最大位置编码长度

    # ========== 训练参数 ==========
    batch_size: int = 16  # 批次大小
    learning_rate: float = 0.0001  # 学习率
    epochs: int = 50  # 训练轮数
    validation_split: float = 0.2  # 验证集比例
    early_stopping_patience: int = 15  # 早停耐心值

    # ========== 优化器参数 ==========
    beta_1: float = 0.9  # Adam优化器beta1
    beta_2: float = 0.999  # Adam优化器beta2
    epsilon: float = 1e-7  # Adam优化器epsilon
    weight_decay: float = 0.01  # 权重衰减

    # ========== 损失权重 ==========
    loss_weights: Dict[str, float] = field(
        default_factory=lambda: {
            "red_odd_even": 1.8,  # 红球奇偶比预测权重
            "red_size": 1.8,  # 红球大小比预测权重
            "blue_size": 1.0,  # 蓝球大小比预测权重
            "red_numbers": 0.3,  # 红球号码生成权重
            "blue_numbers": 0.15,  # 蓝球号码生成权重
            "uncertainty": 0.25,  # 不确定性估计权重
        }
    )

    # ========== Monte Carlo参数 ==========
    monte_carlo_samples: int = 20  # MC采样次数
    mc_dropout_rate: float = 0.15  # MC Dropout率

    # ========== 温度采样参数 ==========
    base_temperature: float = 1.0  # 基础温度
    temperature_range: Tuple[float, float] = (0.3, 2.5)  # 温度范围
    adaptive_temperature: bool = True  # 是否使用自适应温度

    # ========== 采样策略参数 ==========
    sampling_strategy: SamplingStrategy = SamplingStrategy.TEMPERATURE
    top_k: int = 10  # Top-K采样参数
    nucleus_p: float = 0.9  # Nucleus采样参数

    # ========== 多样性控制参数 ==========
    diversity_weight: float = 0.3  # 多样性权重
    history_avoidance_strength: float = 0.2  # 历史避免强度
    pattern_penalty: float = 0.1  # 模式重复惩罚

    # ========== 不确定性参数 ==========
    uncertainty_threshold: float = 0.5  # 不确定性阈值
    confidence_calibration: bool = True  # 置信度校准

    # ========== 特征工程参数 ==========
    feature_engineering: Dict[str, Any] = field(
        default_factory=lambda: {
            "use_statistical_features": True,
            "use_trend_features": True,
            "use_cyclical_features": True,
            "use_pattern_features": True,
            "feature_window": 10,
            "trend_window": 5,
        }
    )

    # ========== 正则化参数 ==========
    l1_regularization: float = 0.0001  # L1正则化
    l2_regularization: float = 0.001  # L2正则化
    gradient_clip_norm: float = 1.0  # 梯度裁剪

    # ========== 模型保存参数 ==========
    model_save_path: str = "models/v4_transformer"
    save_best_only: bool = True
    save_weights_only: bool = False

    # ========== 性能优化参数 ==========
    use_mixed_precision: bool = False  # 混合精度训练
    use_gradient_accumulation: bool = False  # 梯度累积
    accumulation_steps: int = 4  # 累积步数

    # ========== 调试参数 ==========
    verbose_training: bool = False  # 详细训练日志
    plot_attention: bool = False  # 绘制注意力图
    save_intermediate_results: bool = False  # 保存中间结果

    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证基础参数
            if self.d_model <= 0:
                raise ValueError("d_model必须大于0")

            if self.num_heads <= 0 or self.d_model % self.num_heads != 0:
                raise ValueError("num_heads必须大于0且d_model必须能被num_heads整除")

            if self.num_layers <= 0:
                raise ValueError("num_layers必须大于0")

            if not 0 < self.dropout_rate < 1:
                raise ValueError("dropout_rate必须在0-1之间")

            # 验证序列参数
            if self.sequence_length <= 0:
                raise ValueError("sequence_length必须大于0")

            # 验证训练参数
            if self.batch_size <= 0:
                raise ValueError("batch_size必须大于0")

            if self.learning_rate <= 0:
                raise ValueError("learning_rate必须大于0")

            if self.epochs <= 0:
                raise ValueError("epochs必须大于0")

            if not 0 < self.validation_split < 1:
                raise ValueError("validation_split必须在0-1之间")

            # 验证损失权重
            if not self.loss_weights:
                raise ValueError("loss_weights不能为空")

            total_weight = sum(self.loss_weights.values())
            if total_weight <= 0:
                raise ValueError("损失权重总和必须大于0")

            # 验证Monte Carlo参数
            if self.monte_carlo_samples <= 0:
                raise ValueError("monte_carlo_samples必须大于0")

            if not 0 < self.mc_dropout_rate < 1:
                raise ValueError("mc_dropout_rate必须在0-1之间")

            # 验证温度参数
            if self.base_temperature <= 0:
                raise ValueError("base_temperature必须大于0")

            if (
                self.temperature_range[0] <= 0
                or self.temperature_range[1] <= self.temperature_range[0]
            ):
                raise ValueError("temperature_range必须为正数且上界大于下界")

            # 验证采样参数
            if self.top_k <= 0:
                raise ValueError("top_k必须大于0")

            if not 0 < self.nucleus_p <= 1:
                raise ValueError("nucleus_p必须在0-1之间")

            # 验证多样性参数
            if not 0 <= self.diversity_weight <= 1:
                raise ValueError("diversity_weight必须在0-1之间")

            if not 0 <= self.history_avoidance_strength <= 1:
                raise ValueError("history_avoidance_strength必须在0-1之间")

            return True

        except Exception as e:
            print(f"V4 Transformer配置验证失败: {e}")
            return False

    def get_model_summary(self) -> Dict[str, Any]:
        """获取模型配置摘要"""
        return {
            "architecture": {
                "d_model": self.d_model,
                "num_heads": self.num_heads,
                "num_layers": self.num_layers,
                "sequence_length": self.sequence_length,
            },
            "training": {
                "batch_size": self.batch_size,
                "learning_rate": self.learning_rate,
                "epochs": self.epochs,
            },
            "sampling": {
                "monte_carlo_samples": self.monte_carlo_samples,
                "temperature_range": self.temperature_range,
                "sampling_strategy": self.sampling_strategy.value,
            },
            "loss_weights": self.loss_weights,
        }

    def update_from_dict(self, config_dict: Dict[str, Any]) -> None:
        """从字典更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                print(f"警告: 未知配置参数 {key}")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, Enum):
                result[key] = value.value
            elif isinstance(value, (list, tuple, dict)):
                result[key] = value
            else:
                result[key] = value
        return result


# 预定义配置模板
class V4ConfigTemplates:
    """V4配置模板"""

    @staticmethod
    def get_default_config() -> V4TransformerConfig:
        """获取默认配置"""
        return V4TransformerConfig()

    @staticmethod
    def get_fast_training_config() -> V4TransformerConfig:
        """获取快速训练配置"""
        config = V4TransformerConfig()
        config.d_model = 96  # 96 = 12 * 8，确保能被num_heads整除
        config.num_heads = 8  # 减少注意力头数以加快训练
        config.epochs = 20
        config.batch_size = 32
        config.monte_carlo_samples = 10
        config.early_stopping_patience = 8
        return config

    @staticmethod
    def get_high_accuracy_config() -> V4TransformerConfig:
        """获取高精度配置"""
        config = V4TransformerConfig()
        config.num_layers = 8
        config.num_heads = 16
        config.d_model = 256
        config.epochs = 100
        config.monte_carlo_samples = 30
        config.early_stopping_patience = 25
        return config

    @staticmethod
    def get_lightweight_config() -> V4TransformerConfig:
        """获取轻量级配置"""
        config = V4TransformerConfig()
        config.num_layers = 4
        config.num_heads = 8
        config.d_model = 64
        config.dff = 256
        config.monte_carlo_samples = 10
        return config


if __name__ == "__main__":
    # 测试配置类
    config = V4TransformerConfig()
    print("默认配置验证:", config.validate())
    print("配置摘要:", config.get_model_summary())

    # 测试配置模板
    fast_config = V4ConfigTemplates.get_fast_training_config()
    print("快速训练配置验证:", fast_config.validate())
