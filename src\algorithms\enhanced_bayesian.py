#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版贝叶斯预测算法

主要改进:
1. 多层贝叶斯网络
2. 动态先验概率更新
3. 条件独立性分析
4. 证据权重调整
5. 不确定性量化
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from collections import defaultdict, Counter
from dataclasses import dataclass
import logging
from scipy import stats
from ..utils.utils import (
    parse_numbers,
    calculate_odd_even_ratio,
    calculate_size_ratio_red,
    calculate_size_ratio_blue,
    ratio_to_state,
    state_to_ratio,
)


@dataclass
class BayesianEvidence:
    """贝叶斯证据"""

    evidence_type: str
    evidence_value: Any
    weight: float
    confidence: float
    timestamp: int


@dataclass
class PriorDistribution:
    """先验分布"""

    distribution_type: str  # 'uniform', 'normal', 'beta', 'empirical'
    parameters: Dict[str, float]
    confidence: float
    update_count: int = 0


class EnhancedBayesianPredictor:
    """增强版贝叶斯预测器"""

    def __init__(
        self, evidence_types: List[str] = None, prior_update_rate: float = 0.1
    ):
        """
        初始化增强版贝叶斯预测器

        Args:
            evidence_types: 证据类型列表
            prior_update_rate: 先验更新率
        """
        self.evidence_types = evidence_types or [
            "frequency",
            "pattern",
            "trend",
            "correlation",
            "seasonal",
        ]
        self.prior_update_rate = prior_update_rate

        # 先验分布
        self.priors = {}
        self.posterior_cache = {}

        # 证据管理
        self.evidence_history = []
        self.evidence_weights = defaultdict(float)

        # 条件概率表
        self.conditional_probs = defaultdict(lambda: defaultdict(float))
        self.joint_probs = defaultdict(float)

        # 性能跟踪
        self.prediction_accuracy = []
        self.uncertainty_history = []

        # 自适应参数
        self.confidence_threshold = 0.6
        self.evidence_decay_rate = 0.95

        self.logger = logging.getLogger(__name__)

    def initialize_priors(self, data: pd.DataFrame) -> None:
        """初始化先验分布"""
        if len(data) < 10:
            self.logger.warning("数据量不足，使用默认先验分布")
            self._set_default_priors()
            return

        # 基于历史数据估计先验分布
        self._estimate_ratio_priors(data)
        self._estimate_pattern_priors(data)
        self._estimate_trend_priors(data)

        self.logger.info("先验分布初始化完成")

    def _set_default_priors(self) -> None:
        """设置默认先验分布"""
        # 奇偶比先验 (均匀分布)
        self.priors["odd_even_ratio"] = PriorDistribution(
            distribution_type="uniform",
            parameters={"low": 0.0, "high": 1.0},
            confidence=0.5,
        )

        # 大小比先验
        self.priors["size_red_ratio"] = PriorDistribution(
            distribution_type="uniform",
            parameters={"low": 0.0, "high": 1.0},
            confidence=0.5,
        )

        self.priors["size_blue_ratio"] = PriorDistribution(
            distribution_type="uniform",
            parameters={"low": 0.0, "high": 1.0},
            confidence=0.5,
        )

    def _estimate_ratio_priors(self, data: pd.DataFrame) -> None:
        """估计比值先验分布"""
        ratios = {"odd_even": [], "size_red": [], "size_blue": []}

        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)

            ratios["odd_even"].append(calculate_odd_even_ratio(red_balls))
            ratios["size_red"].append(calculate_size_ratio_red(red_balls))
            ratios["size_blue"].append(calculate_size_ratio_blue(blue_balls))

        # 为每个比值估计最佳分布
        for ratio_type, values in ratios.items():
            self._fit_distribution(f"{ratio_type}_ratio", values)

    def _estimate_pattern_priors(self, data: pd.DataFrame) -> None:
        """估计模式先验分布"""
        patterns = {"consecutive_count": [], "span": [], "sum_total": []}

        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)

            # 连号数量
            consecutive = self._count_consecutive(red_balls)
            patterns["consecutive_count"].append(consecutive)

            # 跨度
            span = max(red_balls) - min(red_balls)
            patterns["span"].append(span)

            # 和值
            total_sum = sum(red_balls)
            patterns["sum_total"].append(total_sum)

        for pattern_type, values in patterns.items():
            self._fit_distribution(pattern_type, values)

    def _estimate_trend_priors(self, data: pd.DataFrame) -> None:
        """估计趋势先验分布"""
        if len(data) < 5:
            return

        # 分析号码出现趋势
        number_trends = defaultdict(list)

        for i in range(len(data) - 4):
            window_data = data.iloc[i : i + 5]

            for num in range(1, 36):  # 红球范围
                appearances = 0
                for _, row in window_data.iterrows():
                    red_balls, _ = parse_numbers(row)
                    if num in red_balls:
                        appearances += 1

                number_trends[num].append(appearances)

        # 计算趋势分布
        trend_values = []
        for num, trends in number_trends.items():
            if len(trends) > 1:
                # 计算趋势斜率
                x = np.arange(len(trends))
                slope, _, _, _, _ = stats.linregress(x, trends)
                trend_values.append(slope)

        if trend_values:
            self._fit_distribution("trend_slope", trend_values)

    def _fit_distribution(self, param_name: str, values: List[float]) -> None:
        """拟合最佳分布"""
        if not values:
            return

        values = np.array(values)

        # 尝试不同分布类型
        distributions = {
            "normal": lambda: stats.norm.fit(values),
            "beta": lambda: self._fit_beta(values),
            "uniform": lambda: (np.min(values), np.max(values)),
        }

        best_dist = "normal"
        best_score = float("inf")
        best_params = None

        for dist_name, fit_func in distributions.items():
            try:
                params = fit_func()

                # 计算拟合优度 (简化版KS检验)
                if dist_name == "normal":
                    _, p_value = stats.kstest(
                        values, lambda x: stats.norm.cdf(x, *params)
                    )
                elif dist_name == "beta":
                    _, p_value = stats.kstest(
                        values, lambda x: stats.beta.cdf(x, *params)
                    )
                else:  # uniform
                    _, p_value = stats.kstest(
                        values,
                        lambda x: stats.uniform.cdf(
                            x, params[0], params[1] - params[0]
                        ),
                    )

                score = 1 - p_value  # 越小越好

                if score < best_score:
                    best_score = score
                    best_dist = dist_name
                    best_params = params

            except Exception as e:
                self.logger.debug(f"拟合 {dist_name} 分布失败: {e}")
                continue

        # 保存最佳分布
        if best_params is not None:
            if best_dist == "normal":
                param_dict = {"loc": best_params[0], "scale": best_params[1]}
            elif best_dist == "beta":
                param_dict = {
                    "a": best_params[0],
                    "b": best_params[1],
                    "loc": best_params[2],
                    "scale": best_params[3],
                }
            else:  # uniform
                param_dict = {"low": best_params[0], "high": best_params[1]}

            confidence = max(0.1, min(0.9, 1 - best_score))

            self.priors[param_name] = PriorDistribution(
                distribution_type=best_dist,
                parameters=param_dict,
                confidence=confidence,
            )

    def _fit_beta(self, values: List[float]) -> Tuple[float, float, float, float]:
        """拟合Beta分布"""
        # 将值标准化到[0,1]区间
        min_val, max_val = np.min(values), np.max(values)
        if max_val == min_val:
            return 1.0, 1.0, min_val, 1e-6

        normalized = (values - min_val) / (max_val - min_val)

        # 避免边界值
        normalized = np.clip(normalized, 1e-6, 1 - 1e-6)

        a, b, loc, scale = stats.beta.fit(normalized)

        # 转换回原始尺度
        return a, b, min_val, max_val - min_val

    def _count_consecutive(self, numbers: List[int]) -> int:
        """计算连号数量"""
        sorted_nums = sorted(numbers)
        max_consecutive = 1
        current_consecutive = 1

        for i in range(1, len(sorted_nums)):
            if sorted_nums[i] == sorted_nums[i - 1] + 1:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 1

        return max_consecutive

    def add_evidence(
        self,
        evidence_type: str,
        evidence_value: Any,
        weight: float = 1.0,
        confidence: float = 1.0,
    ) -> None:
        """添加证据"""
        evidence = BayesianEvidence(
            evidence_type=evidence_type,
            evidence_value=evidence_value,
            weight=weight,
            confidence=confidence,
            timestamp=len(self.evidence_history),
        )

        self.evidence_history.append(evidence)

        # 更新证据权重 (考虑时间衰减)
        self._update_evidence_weights()

    def _update_evidence_weights(self) -> None:
        """更新证据权重"""
        current_time = len(self.evidence_history)

        for evidence in self.evidence_history:
            # 时间衰减
            time_decay = self.evidence_decay_rate ** (current_time - evidence.timestamp)

            # 综合权重
            final_weight = evidence.weight * evidence.confidence * time_decay

            self.evidence_weights[evidence.evidence_type] += final_weight

    def calculate_posterior(
        self, hypothesis: str, evidence_dict: Dict[str, Any]
    ) -> float:
        """计算后验概率"""
        # 获取先验概率
        prior_prob = self._get_prior_probability(hypothesis)

        # 计算似然概率
        likelihood = self._calculate_likelihood(hypothesis, evidence_dict)

        # 计算边际概率 (简化处理)
        marginal_prob = self._calculate_marginal_probability(evidence_dict)

        if marginal_prob == 0:
            return prior_prob

        # 贝叶斯公式: P(H|E) = P(E|H) * P(H) / P(E)
        posterior_prob = (likelihood * prior_prob) / marginal_prob

        return min(1.0, max(0.0, posterior_prob))

    def _get_prior_probability(self, hypothesis: str) -> float:
        """获取先验概率"""
        if hypothesis not in self.priors:
            return 0.5  # 默认先验

        prior = self.priors[hypothesis]

        # 根据分布类型计算概率密度
        if prior.distribution_type == "uniform":
            return 1.0 / (prior.parameters["high"] - prior.parameters["low"])
        elif prior.distribution_type == "normal":
            # 使用概率密度函数的峰值作为代表
            return stats.norm.pdf(
                prior.parameters["loc"],
                prior.parameters["loc"],
                prior.parameters["scale"],
            )
        elif prior.distribution_type == "beta":
            # Beta分布的众数
            a, b = prior.parameters["a"], prior.parameters["b"]
            if a > 1 and b > 1:
                mode = (a - 1) / (a + b - 2)
                return stats.beta.pdf(
                    mode, a, b, prior.parameters["loc"], prior.parameters["scale"]
                )

        return prior.confidence

    def _calculate_likelihood(
        self, hypothesis: str, evidence_dict: Dict[str, Any]
    ) -> float:
        """计算似然概率"""
        likelihood = 1.0

        for evidence_type, evidence_value in evidence_dict.items():
            # 获取条件概率 P(E|H)
            conditional_prob = self.conditional_probs[hypothesis].get(
                evidence_type, 0.5
            )

            # 考虑证据权重
            evidence_weight = self.evidence_weights.get(evidence_type, 1.0)

            # 加权似然
            weighted_likelihood = conditional_prob**evidence_weight
            likelihood *= weighted_likelihood

        return likelihood

    def _calculate_marginal_probability(self, evidence_dict: Dict[str, Any]) -> float:
        """计算边际概率"""
        # 简化计算：假设证据独立
        marginal = 1.0

        for evidence_type, evidence_value in evidence_dict.items():
            # 计算该证据的总概率
            evidence_prob = 0.0

            # 遍历所有可能的假设
            for hypothesis in self.priors.keys():
                prior = self._get_prior_probability(hypothesis)
                conditional = self.conditional_probs[hypothesis].get(evidence_type, 0.5)
                evidence_prob += prior * conditional

            if evidence_prob > 0:
                marginal *= evidence_prob

        return max(1e-10, marginal)  # 避免除零

    def predict_ratios(self, recent_data: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """预测比值"""
        if len(recent_data) < 3:
            return self._get_default_ratio_predictions()

        # 收集证据
        evidence_dict = self._collect_evidence(recent_data)

        # 预测各种比值
        predictions = {}

        # 奇偶比预测
        odd_even_predictions = self._predict_ratio_range(
            "odd_even_ratio", evidence_dict
        )
        predictions["odd_even"] = odd_even_predictions

        # 大小比预测
        size_red_predictions = self._predict_ratio_range(
            "size_red_ratio", evidence_dict
        )
        predictions["size_red"] = size_red_predictions

        size_blue_predictions = self._predict_ratio_range(
            "size_blue_ratio", evidence_dict
        )
        predictions["size_blue"] = size_blue_predictions

        return predictions

    def _collect_evidence(self, recent_data: pd.DataFrame) -> Dict[str, Any]:
        """收集证据"""
        evidence = {}

        # 频率证据
        evidence["frequency"] = self._analyze_frequency_pattern(recent_data)

        # 趋势证据
        evidence["trend"] = self._analyze_trend_pattern(recent_data)

        # 模式证据
        evidence["pattern"] = self._analyze_pattern_evidence(recent_data)

        # 相关性证据
        evidence["correlation"] = self._analyze_correlation_evidence(recent_data)

        return evidence

    def _analyze_frequency_pattern(self, data: pd.DataFrame) -> Dict[str, float]:
        """分析频率模式"""
        number_counts = defaultdict(int)
        total_periods = len(data)

        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            for num in red_balls:
                number_counts[num] += 1

        # 计算频率分布特征
        frequencies = list(number_counts.values())

        return {
            "mean_frequency": np.mean(frequencies) if frequencies else 0,
            "frequency_variance": np.var(frequencies) if frequencies else 0,
            "max_frequency": max(frequencies) if frequencies else 0,
        }

    def _analyze_trend_pattern(self, data: pd.DataFrame) -> Dict[str, float]:
        """分析趋势模式"""
        if len(data) < 3:
            return {"trend_strength": 0.0}

        # 分析最近几期的比值趋势
        ratios = []
        for _, row in data.tail(5).iterrows():
            red_balls, blue_balls = parse_numbers(row)
            odd_even_ratio = calculate_odd_even_ratio(red_balls)
            ratios.append(odd_even_ratio)

        if len(ratios) < 2:
            return {"trend_strength": 0.0}

        # 计算趋势强度
        x = np.arange(len(ratios))
        slope, _, r_value, _, _ = stats.linregress(x, ratios)

        return {
            "trend_strength": abs(slope),
            "trend_direction": 1.0 if slope > 0 else -1.0,
            "trend_correlation": r_value**2,
        }

    def _analyze_pattern_evidence(self, data: pd.DataFrame) -> Dict[str, float]:
        """分析模式证据"""
        patterns = {"consecutive_avg": 0.0, "span_avg": 0.0, "sum_avg": 0.0}

        if len(data) == 0:
            return patterns

        consecutive_counts = []
        spans = []
        sums = []

        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)

            consecutive_counts.append(self._count_consecutive(red_balls))
            spans.append(max(red_balls) - min(red_balls))
            sums.append(sum(red_balls))

        patterns["consecutive_avg"] = np.mean(consecutive_counts)
        patterns["span_avg"] = np.mean(spans)
        patterns["sum_avg"] = np.mean(sums)

        return patterns

    def _analyze_correlation_evidence(self, data: pd.DataFrame) -> Dict[str, float]:
        """分析相关性证据"""
        if len(data) < 5:
            return {"correlation_strength": 0.0}

        # 分析红球和蓝球之间的相关性
        red_sums = []
        blue_sums = []

        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_sums.append(sum(red_balls))
            blue_sums.append(sum(blue_balls))

        if len(red_sums) < 2:
            return {"correlation_strength": 0.0}

        correlation, _ = stats.pearsonr(red_sums, blue_sums)

        return {
            "correlation_strength": abs(correlation),
            "correlation_direction": 1.0 if correlation > 0 else -1.0,
        }

    def _predict_ratio_range(
        self, ratio_type: str, evidence_dict: Dict[str, Any]
    ) -> Dict[str, float]:
        """预测比值范围"""
        # 定义可能的比值范围
        ratio_ranges = {"low": (0.0, 0.3), "medium": (0.3, 0.7), "high": (0.7, 1.0)}

        predictions = {}

        for range_name, (low, high) in ratio_ranges.items():
            # 构造假设
            hypothesis = f"{ratio_type}_{range_name}"

            # 计算后验概率
            posterior_prob = self.calculate_posterior(hypothesis, evidence_dict)

            predictions[range_name] = posterior_prob

        # 归一化概率
        total_prob = sum(predictions.values())
        if total_prob > 0:
            for range_name in predictions:
                predictions[range_name] /= total_prob

        return predictions

    def _get_default_ratio_predictions(self) -> Dict[str, Dict[str, float]]:
        """获取默认比值预测"""
        default_pred = {"low": 0.33, "medium": 0.34, "high": 0.33}

        return {
            "odd_even": default_pred.copy(),
            "size_red": default_pred.copy(),
            "size_blue": default_pred.copy(),
        }

    def update_model(self, actual_result: Dict[str, float]) -> None:
        """更新模型"""
        # 更新先验分布
        for ratio_type, actual_value in actual_result.items():
            if ratio_type in self.priors:
                self._update_prior(ratio_type, actual_value)

        # 更新条件概率
        self._update_conditional_probabilities(actual_result)

    def _update_prior(self, ratio_type: str, actual_value: float) -> None:
        """更新先验分布"""
        if ratio_type not in self.priors:
            return

        prior = self.priors[ratio_type]

        # 增量更新参数 (简化版)
        if prior.distribution_type == "normal":
            # 更新均值和方差
            old_mean = prior.parameters["loc"]
            old_var = prior.parameters["scale"] ** 2

            # 贝叶斯更新
            new_mean = (old_mean + self.prior_update_rate * actual_value) / (
                1 + self.prior_update_rate
            )
            new_var = (
                old_var * (1 - self.prior_update_rate)
                + self.prior_update_rate * (actual_value - new_mean) ** 2
            )

            prior.parameters["loc"] = new_mean
            prior.parameters["scale"] = np.sqrt(max(1e-6, new_var))

        prior.update_count += 1

        # 更新置信度
        prior.confidence = min(0.95, prior.confidence + 0.01)

    def _update_conditional_probabilities(
        self, actual_result: Dict[str, float]
    ) -> None:
        """更新条件概率"""
        # 基于实际结果更新条件概率表
        for ratio_type, actual_value in actual_result.items():
            # 确定实际值属于哪个范围
            if actual_value < 0.3:
                actual_range = "low"
            elif actual_value < 0.7:
                actual_range = "medium"
            else:
                actual_range = "high"

            hypothesis = f"{ratio_type}_{actual_range}"

            # 更新条件概率 (简化版)
            for evidence_type in self.evidence_types:
                current_prob = self.conditional_probs[hypothesis][evidence_type]
                # 增量更新
                self.conditional_probs[hypothesis][evidence_type] = (
                    current_prob * 0.9 + 0.1 * 0.8  # 假设正确预测的概率为0.8
                )

    def get_uncertainty_estimate(
        self, predictions: Dict[str, Dict[str, float]]
    ) -> Dict[str, float]:
        """估计预测不确定性"""
        uncertainty = {}

        for ratio_type, pred_dict in predictions.items():
            # 计算熵作为不确定性度量
            probs = list(pred_dict.values())
            entropy = -sum(p * np.log(p + 1e-10) for p in probs if p > 0)

            # 归一化熵 (最大熵为log(3))
            normalized_entropy = entropy / np.log(3)

            uncertainty[ratio_type] = normalized_entropy

        return uncertainty

    def get_model_confidence(self) -> float:
        """获取模型整体置信度"""
        if not self.priors:
            return 0.5

        # 基于先验分布的置信度
        prior_confidences = [prior.confidence for prior in self.priors.values()]

        # 基于预测历史的准确性
        if self.prediction_accuracy:
            accuracy_confidence = np.mean(self.prediction_accuracy[-20:])  # 最近20次
        else:
            accuracy_confidence = 0.5

        # 综合置信度
        overall_confidence = (
            np.mean(prior_confidences) * 0.6 + accuracy_confidence * 0.4
        )

        return overall_confidence

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            "evidence_types": self.evidence_types,
            "prior_count": len(self.priors),
            "evidence_count": len(self.evidence_history),
            "model_confidence": self.get_model_confidence(),
            "prior_distributions": {},
        }

        # 先验分布信息
        for name, prior in self.priors.items():
            info["prior_distributions"][name] = {
                "type": prior.distribution_type,
                "confidence": prior.confidence,
                "update_count": prior.update_count,
            }

        return info
