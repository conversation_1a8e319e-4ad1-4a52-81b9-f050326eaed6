#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lottery Prediction System - Interactive Launcher
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def display_banner():
    """Display system banner"""
    print("=" * 80)
    print("Lottery Prediction System - Interactive Launcher")
    print("=" * 80)


def display_main_menu():
    """Display main system selection menu"""
    print("\nSelect Prediction System:")
    print("  1. Main System - Ensemble Learning with Red Ball Size Optimizer")
    print("  2. Ultimate System - Multi-mode Prediction System")
    print("  3. Refactored System - Interactive Architecture Demo")
    print("  4. Simple Refactored System")
    print("  5. Number Selection System - Deep Learning vs Traditional ML Comparison")
    print("  0. Exit")
    print("-" * 60)


def display_action_menu():
    """Display action selection menu"""
    print("\nSelect Action:")
    print("  1. Predict Next Period")
    print("  2. Backtest Analysis")
    print("  3. Performance Comparison")
    print("  4. Interactive Mode")
    print("  0. Back to Main Menu")
    print("-" * 60)


def display_mode_menu():
    """Display mode selection menu (for Ultimate System)"""
    print("\nSelect Prediction Mode:")
    print("  1. Auto Mode - Automatically select best available mode")
    print("  2. Basic Mode - Basic prediction functionality")
    print("  3. Enhanced Mode - Enhanced prediction functionality")
    print("  4. Super Mode - Super prediction functionality")
    print("  5. Ultimate Mode - Ultimate prediction functionality")
    print("  0. Back")
    print("-" * 60)


def display_selection_menu():
    """Display selection system menu"""
    print("\nNumber Selection System:")
    print("  1. Run Full Comparison (Deep Learning vs Traditional ML)")
    print("  2. Run Optimization Comparison (Original vs Optimized)")
    print("  3. Parameter Optimization & Tuning")
    print("  4. Deep Learning Selection Only")
    print("  5. Traditional ML Selection Only")
    print("  6. Ensemble Selection (Combined)")
    print("  7. Performance Analysis")
    print("  0. Back")
    print("-" * 60)


def get_user_input(prompt, valid_choices):
    """Get user input with validation"""
    while True:
        try:
            choice = input(f"{prompt} ").strip()
            if choice in valid_choices:
                return choice
            else:
                print(f"Invalid choice. Please enter: {', '.join(valid_choices)}")
        except KeyboardInterrupt:
            print("\n\nUser cancelled. Exiting...")
            sys.exit(0)
        except EOFError:
            print("\n\nInput ended. Exiting...")
            sys.exit(0)


def get_backtest_periods():
    """Get number of backtest periods"""
    while True:
        try:
            periods_str = input("Enter backtest periods (default 10): ").strip()
            if not periods_str:
                return 10
            periods = int(periods_str)
            if 1 <= periods <= 100:
                return periods
            else:
                print("Periods should be between 1-100")
        except ValueError:
            print("Please enter a valid number")
        except KeyboardInterrupt:
            return 10


def run_main_system(action):
    """Run main system"""
    try:
        from src.systems.main import LotteryPredictor
        
        print("\nStarting Main System (Ensemble Learning Version)...")
        predictor = LotteryPredictor()
        
        if action == "1":  # Predict
            print("Executing ensemble learning prediction...")
            predictor.run_ensemble_backtest(num_periods=1, display_periods=1)
        elif action == "2":  # Backtest
            periods = get_backtest_periods()
            print(f"Executing backtest analysis ({periods} periods)...")
            predictor.run_ensemble_backtest(num_periods=periods, display_periods=min(periods, 5))
        elif action == "3":  # Compare
            periods = get_backtest_periods()
            print(f"Executing performance comparison ({periods} periods)...")
            predictor.run_ensemble_backtest(num_periods=periods, display_periods=min(periods, 3))
            print("\n" + "="*60)
            print("Running traditional backtest for comparison...")
            predictor.run_backtest(num_periods=periods, display_periods=min(periods, 3))
            
    except Exception as e:
        print(f"Main system failed: {e}")
        import traceback
        traceback.print_exc()


def run_ultimate_system(action, mode="auto"):
    """Run ultimate system"""
    try:
        from src.systems.main_ultimate_fixed import UltimateLotteryPredictor
        
        print(f"\nStarting Ultimate System ({mode.upper()} mode)...")
        predictor = UltimateLotteryPredictor(mode=mode)
        
        if action == "1":  # Predict
            print("Executing prediction...")
            predictor.predict_and_display()
        elif action == "2":  # Backtest
            periods = get_backtest_periods()
            print(f"Executing backtest analysis ({periods} periods)...")
            predictor.basic_predictor.run_backtest(num_periods=periods, display_periods=min(periods, 5))
        elif action == "3":  # Compare
            periods = get_backtest_periods()
            print(f"Executing comprehensive backtest comparison ({periods} periods)...")
            predictor.run_comprehensive_backtest(num_periods=periods, display_periods=min(periods, 5))
            
    except Exception as e:
        print(f"Ultimate system failed: {e}")
        import traceback
        traceback.print_exc()


def run_refactored_system():
    """Run refactored system"""
    try:
        from src.systems.refactored_main import RefactoredLotterySystem
        
        print("\nStarting Refactored System (Interactive Architecture Demo)...")
        system = RefactoredLotterySystem()
        system.run_interactive_mode()
        
    except Exception as e:
        print(f"Refactored system failed: {e}")
        import traceback
        traceback.print_exc()


def run_simple_refactored_system():
    """Run simple refactored system"""
    try:
        from src.systems.simple_refactored_main import SimpleRefactoredLotterySystem

        print("\nStarting Simple Refactored System...")
        system = SimpleRefactoredLotterySystem()
        print("Simple refactored system started successfully")

    except Exception as e:
        print(f"Simple refactored system failed: {e}")
        import traceback
        traceback.print_exc()


def run_number_selection_system(action):
    """Run number selection system"""
    try:
        from src.systems.number_selection_system import NumberSelectionSystem, SelectionMethod

        print("\nStarting Number Selection System...")
        system = NumberSelectionSystem()

        if action == "1":  # Full comparison
            print("Running full comparison between Deep Learning and Traditional ML...")
            result = system.run_comparison()
            print("\n" + "="*80)
            print("COMPARISON RESULTS")
            print("="*80)
            print(result.summary)
            print(f"\nWinner: {result.winner}")

        elif action == "2":  # Optimization Comparison
            print("Running Optimization Comparison...")
            try:
                from src.systems.parameter_optimizer import ParameterOptimizer

                print("正在运行优化对比测试...")
                print("注意：这是演示版本，使用模拟数据进行快速演示")

                # 模拟结果
                results = {
                    'original': {
                        'deep_learning_results': {'hit_rate': 0.15},
                        'traditional_ml_results': {'hit_rate': 0.12}
                    },
                    'balanced_optimized': {
                        'deep_learning_results': {'hit_rate': 0.18},
                        'traditional_ml_results': {'hit_rate': 0.15}
                    },
                    'aggressive_optimized': {
                        'deep_learning_results': {'hit_rate': 0.20},
                        'traditional_ml_results': {'hit_rate': 0.17}
                    }
                }

                print("\n" + "="*60)
                print("优化对比测试结果")
                print("="*60)

                for config_name, result in results.items():
                    dl_rate = result['deep_learning_results']['hit_rate']
                    ml_rate = result['traditional_ml_results']['hit_rate']
                    avg_rate = (dl_rate + ml_rate) / 2

                    print(f"\n{config_name.replace('_', ' ').title()}:")
                    print(f"  深度学习命中率: {dl_rate:.2%}")
                    print(f"  传统ML命中率: {ml_rate:.2%}")
                    print(f"  平均命中率: {avg_rate:.2%}")

                # 找出最佳配置
                best_config = max(results.keys(),
                                key=lambda k: (results[k]['deep_learning_results']['hit_rate'] +
                                             results[k]['traditional_ml_results']['hit_rate']) / 2)

                print(f"\n🏆 推荐配置: {best_config.replace('_', ' ').title()}")

            except Exception as e:
                print(f"优化对比失败: {e}")
                import traceback
                traceback.print_exc()

        elif action == "3":  # Parameter Optimization
            print("Running Parameter Optimization...")
            try:
                from src.systems.parameter_optimizer import ParameterOptimizer

                # 使用正确的数据文件路径
                data_file = "data/raw/dlt_data.csv"
                if not os.path.exists(data_file):
                    print(f"❌ 数据文件不存在: {data_file}")
                    print("请确保数据文件存在后重试")
                    return

                # 获取用户输入
                n_trials = input("请输入优化试验次数 (默认10): ").strip()
                n_trials = int(n_trials) if n_trials.isdigit() else 10

                print(f"正在进行参数优化，试验次数: {n_trials}")
                print(f"使用数据文件: {data_file}")

                # 创建参数优化器
                optimizer = ParameterOptimizer()

                # 运行优化
                result = optimizer.optimize_hyperparameters(
                    data_path=data_file,
                    n_trials=n_trials
                )

                best_params = result.get('best_params', {})
                best_score = result.get('best_score', 0.0)
                history = result.get('optimization_history', [])

                print("\n" + "="*60)
                print("参数优化结果")
                print("="*60)
                print(f"最佳得分: {best_score:.4f}")
                print("最佳参数:")
                for param, value in best_params.items():
                    print(f"  {param}: {value}")

                # 显示优化历史
                if len(history) > 1:
                    print(f"\n优化历史 (共{len(history)}次试验):")
                    for i, (trial, score) in enumerate(history[-5:], 1):  # 显示最后5次
                        print(f"  试验 {trial+1}: {score:.4f}")

            except Exception as e:
                print(f"参数优化失败: {e}")
                import traceback
                traceback.print_exc()

        elif action == "4":  # Deep Learning only
            print("Running Deep Learning selection...")
            result = system.select_numbers(SelectionMethod.DEEP_LEARNING)
            print(f"\nDeep Learning Selection Results:")
            print(f"Red Balls: {result.red_balls}")
            print(f"Blue Balls: {result.blue_balls}")
            print(f"Confidence: {result.confidence:.2%}")
            print(f"Prediction Time: {result.prediction_time:.2f}s")

        elif action == "5":  # Traditional ML only
            print("Running Traditional ML selection...")
            result = system.select_numbers(SelectionMethod.TRADITIONAL_ML)
            print(f"\nTraditional ML Selection Results:")
            print(f"Red Balls: {result.red_balls}")
            print(f"Blue Balls: {result.blue_balls}")
            print(f"Confidence: {result.confidence:.2%}")
            print(f"Prediction Time: {result.prediction_time:.2f}s")

        elif action == "6":  # Ensemble
            print("Running Ensemble selection...")
            result = system.select_numbers(SelectionMethod.ENSEMBLE)
            print(f"\nEnsemble Selection Results:")
            print(f"Red Balls: {result.red_balls}")
            print(f"Blue Balls: {result.blue_balls}")
            print(f"Confidence: {result.confidence:.2%}")
            print(f"Prediction Time: {result.prediction_time:.2f}s")

        elif action == "7":  # Performance analysis
            print("Running performance analysis...")
            result = system.run_comparison()
            print("\n" + "="*80)
            print("DETAILED PERFORMANCE ANALYSIS")
            print("="*80)

            # Display detailed metrics
            metrics = result.comparison_metrics
            print(f"\nDeep Learning Performance:")
            print(f"  Hit Rate: {metrics.get('dl_hit_rate', 0):.2%}")
            print(f"  ROI: {metrics.get('dl_roi', 0):.2f}")
            print(f"  Stability: {metrics.get('dl_stability', 0):.2f}")
            print(f"  Training Time: {result.deep_learning_results.get('training_time', 0):.2f}s")

            print(f"\nTraditional ML Performance:")
            print(f"  Hit Rate: {metrics.get('ml_hit_rate', 0):.2%}")
            print(f"  ROI: {metrics.get('ml_roi', 0):.2f}")
            print(f"  Stability: {metrics.get('ml_stability', 0):.2f}")
            print(f"  Training Time: {result.traditional_ml_results.get('training_time', 0):.2f}s")

            print(f"\nStatistical Analysis:")
            print(f"  P-value: {metrics.get('p_value', 1.0):.4f}")
            print(f"  Significant Difference: {'Yes' if metrics.get('significant', False) else 'No'}")
            print(f"  Effect Size: {metrics.get('effect_size', 0):.3f}")

            print(f"\nRecommendation: {result.winner}")

    except Exception as e:
        print(f"Number selection system failed: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main function"""
    display_banner()
    
    while True:
        display_main_menu()
        system_choice = get_user_input("Select system (0-5):", ["0", "1", "2", "3", "4", "5"])
        
        if system_choice == "0":
            print("\nThank you for using Lottery Prediction System. Goodbye!")
            break
            
        elif system_choice == "1":  # Main System
            while True:
                display_action_menu()
                action_choice = get_user_input("Select action (0-4):", ["0", "1", "2", "3", "4"])
                
                if action_choice == "0":
                    break
                elif action_choice in ["1", "2", "3"]:
                    run_main_system(action_choice)
                    input("\nPress Enter to continue...")
                elif action_choice == "4":
                    print("Main system does not support independent interactive mode")
                    
        elif system_choice == "2":  # Ultimate System
            while True:
                display_mode_menu()
                mode_choice = get_user_input("Select mode (0-5):", ["0", "1", "2", "3", "4", "5"])
                
                if mode_choice == "0":
                    break
                    
                mode_map = {"1": "auto", "2": "basic", "3": "enhanced", "4": "super", "5": "ultimate"}
                selected_mode = mode_map[mode_choice]
                
                while True:
                    display_action_menu()
                    action_choice = get_user_input("Select action (0-4):", ["0", "1", "2", "3", "4"])
                    
                    if action_choice == "0":
                        break
                    elif action_choice in ["1", "2", "3"]:
                        run_ultimate_system(action_choice, selected_mode)
                        input("\nPress Enter to continue...")
                    elif action_choice == "4":
                        print("Ultimate system does not support independent interactive mode")
                        
        elif system_choice == "3":  # Refactored System
            run_refactored_system()
            input("\nPress Enter to continue...")
            
        elif system_choice == "4":  # Simple Refactored System
            run_simple_refactored_system()
            input("\nPress Enter to continue...")

        elif system_choice == "5":  # Number Selection System
            while True:
                display_selection_menu()
                selection_choice = get_user_input("Select option (0-7):", ["0", "1", "2", "3", "4", "5", "6", "7"])

                if selection_choice == "0":
                    break
                elif selection_choice in ["1", "2", "3", "4", "5", "6", "7"]:
                    run_number_selection_system(selection_choice)
                    input("\nPress Enter to continue...")


if __name__ == "__main__":
    main()