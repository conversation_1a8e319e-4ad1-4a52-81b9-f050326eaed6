"""
LSTM时间序列预测器

使用LSTM神经网络捕捉彩票数据的时间序列模式，
专门针对彩票预测任务进行优化。
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
import logging
from dataclasses import dataclass

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, models, optimizers, callbacks
    from sklearn.preprocessing import StandardScaler, MinMaxScaler

    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler

from ...core.interfaces import (
    IStandardPredictor,
    PredictionResult,
    PredictionType,
    BallType,
)
from ...core.base import StandardBasePredictor


@dataclass
class LSTMConfig:
    """LSTM配置参数"""

    # 网络架构
    lstm_units: int = 128
    lstm_layers: int = 2
    dense_units: int = 64
    dropout_rate: float = 0.3
    recurrent_dropout: float = 0.2

    # 训练参数
    learning_rate: float = 0.001
    batch_size: int = 32
    epochs: int = 100
    validation_split: float = 0.2
    early_stopping_patience: int = 10

    # 数据参数
    sequence_length: int = 20  # 输入序列长度
    prediction_horizon: int = 1  # 预测步长
    feature_scaling: str = "standard"  # "standard" or "minmax"

    # 正则化
    l1_reg: float = 0.0001
    l2_reg: float = 0.0001

    # 优化器
    optimizer: str = "adam"  # "adam", "rmsprop", "sgd"

    # 模型保存
    save_best_only: bool = True
    monitor_metric: str = "val_loss"


class LSTMPredictor(StandardBasePredictor):
    """LSTM时间序列预测器"""

    def __init__(self, config: Optional[LSTMConfig] = None):
        super().__init__("LSTM预测器", PredictionType.DEEP_LEARNING)
        self.config = config or LSTMConfig()
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.is_trained_flag = False

        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("TensorFlow未安装，将使用传统机器学习方法")
            self.fallback_model = RandomForestRegressor(
                n_estimators=100, random_state=42
            )

    def _prepare_sequences(
        self, data: pd.DataFrame, target_col: str = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        准备LSTM输入序列

        Args:
            data: 历史数据
            target_col: 目标列名

        Returns:
            X, y: 输入序列和目标值
        """
        # 选择特征列
        if self.feature_columns is None:
            # 自动选择数值列作为特征
            numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
            if target_col and target_col in numeric_cols:
                numeric_cols.remove(target_col)
            self.feature_columns = numeric_cols

        # 提取特征数据
        feature_data = data[self.feature_columns].values

        # 数据标准化
        if self.scaler is None:
            if self.config.feature_scaling == "minmax":
                self.scaler = MinMaxScaler()
            else:
                self.scaler = StandardScaler()
            feature_data = self.scaler.fit_transform(feature_data)
        else:
            feature_data = self.scaler.transform(feature_data)

        # 创建序列
        X, y = [], []
        seq_len = self.config.sequence_length

        for i in range(seq_len, len(feature_data)):
            X.append(feature_data[i - seq_len : i])
            if target_col:
                y.append(data[target_col].iloc[i])
            else:
                # 如果没有指定目标列，预测下一个时间步的所有特征
                y.append(feature_data[i])

        return np.array(X), np.array(y)

    def _build_model(
        self, input_shape: Tuple[int, int], output_dim: int = 1
    ) -> keras.Model:
        """
        构建LSTM模型

        Args:
            input_shape: 输入形状 (sequence_length, n_features)
            output_dim: 输出维度

        Returns:
            Keras模型
        """
        if not TENSORFLOW_AVAILABLE:
            return None

        model = keras.Sequential()

        # 第一个LSTM层
        model.add(
            layers.LSTM(
                self.config.lstm_units,
                return_sequences=True if self.config.lstm_layers > 1 else False,
                input_shape=input_shape,
                dropout=self.config.dropout_rate,
                recurrent_dropout=self.config.recurrent_dropout,
                kernel_regularizer=keras.regularizers.l1_l2(
                    l1=self.config.l1_reg, l2=self.config.l2_reg
                ),
            )
        )

        # 额外的LSTM层
        for i in range(1, self.config.lstm_layers):
            return_seq = i < self.config.lstm_layers - 1
            model.add(
                layers.LSTM(
                    self.config.lstm_units,
                    return_sequences=return_seq,
                    dropout=self.config.dropout_rate,
                    recurrent_dropout=self.config.recurrent_dropout,
                    kernel_regularizer=keras.regularizers.l1_l2(
                        l1=self.config.l1_reg, l2=self.config.l2_reg
                    ),
                )
            )

        # 全连接层
        model.add(
            layers.Dense(
                self.config.dense_units,
                activation="relu",
                kernel_regularizer=keras.regularizers.l1_l2(
                    l1=self.config.l1_reg, l2=self.config.l2_reg
                ),
            )
        )
        model.add(layers.Dropout(self.config.dropout_rate))

        # 输出层
        if output_dim == 1:
            model.add(layers.Dense(1, activation="linear"))
        else:
            model.add(layers.Dense(output_dim, activation="softmax"))

        # 编译模型
        if self.config.optimizer == "adam":
            optimizer = optimizers.Adam(learning_rate=self.config.learning_rate)
        elif self.config.optimizer == "rmsprop":
            optimizer = optimizers.RMSprop(learning_rate=self.config.learning_rate)
        else:
            optimizer = optimizers.SGD(learning_rate=self.config.learning_rate)

        loss = "mse" if output_dim == 1 else "categorical_crossentropy"
        metrics = ["mae"] if output_dim == 1 else ["accuracy"]

        model.compile(optimizer=optimizer, loss=loss, metrics=metrics)

        return model

    def train(self, data: pd.DataFrame, target_col: str = None) -> Dict[str, Any]:
        """
        训练LSTM模型

        Args:
            data: 训练数据
            target_col: 目标列名

        Returns:
            训练结果
        """
        try:
            self.logger.info("开始训练LSTM模型...")

            # 准备数据
            X, y = self._prepare_sequences(data, target_col)

            if len(X) == 0:
                raise ValueError("训练数据不足，无法创建序列")

            self.logger.info(f"训练数据形状: X={X.shape}, y={y.shape}")

            if not TENSORFLOW_AVAILABLE:
                # 使用传统机器学习方法
                X_flat = X.reshape(X.shape[0], -1)
                self.fallback_model.fit(X_flat, y)
                self.is_trained_flag = True
                return {"method": "fallback", "samples": len(X)}

            # 构建模型
            output_dim = 1 if len(y.shape) == 1 else y.shape[1]
            self.model = self._build_model(X.shape[1:], output_dim)

            # 设置回调
            callbacks_list = [
                keras.callbacks.EarlyStopping(
                    monitor=self.config.monitor_metric,
                    patience=self.config.early_stopping_patience,
                    restore_best_weights=True,
                ),
                keras.callbacks.ReduceLROnPlateau(
                    monitor=self.config.monitor_metric,
                    factor=0.5,
                    patience=5,
                    min_lr=1e-7,
                ),
            ]

            # 训练模型
            history = self.model.fit(
                X,
                y,
                batch_size=self.config.batch_size,
                epochs=self.config.epochs,
                validation_split=self.config.validation_split,
                callbacks=callbacks_list,
                verbose=0,
            )

            self.is_trained_flag = True

            # 返回训练结果
            final_loss = history.history["loss"][-1]
            final_val_loss = (
                history.history["val_loss"][-1]
                if "val_loss" in history.history
                else None
            )

            self.logger.info(f"LSTM训练完成 - 损失: {final_loss:.4f}")

            return {
                "method": "lstm",
                "samples": len(X),
                "epochs_trained": len(history.history["loss"]),
                "final_loss": final_loss,
                "final_val_loss": final_val_loss,
                "model_params": self.model.count_params() if self.model else 0,
            }

        except Exception as e:
            self.logger.error(f"LSTM训练失败: {e}")
            return {"error": str(e)}

    def predict(
        self, data: pd.DataFrame, target_index: int, **kwargs
    ) -> PredictionResult:
        """
        执行预测

        Args:
            data: 历史数据
            target_index: 目标期数索引
            **kwargs: 额外参数

        Returns:
            预测结果
        """
        if not self.is_trained_flag:
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,  # 默认红球
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": "模型未训练", "predictions": {}},
            )

        try:
            # 准备预测数据
            seq_len = self.config.sequence_length
            if target_index < seq_len:
                return PredictionResult(
                    prediction_type=PredictionType.DEEP_LEARNING,
                    ball_type=BallType.RED,
                    value=None,
                    confidence=0.0,
                    metadata={
                        "success": False,
                        "error": f"目标索引{target_index}小于序列长度{seq_len}",
                        "predictions": {},
                    },
                )

            # 提取最近的序列
            recent_data = data.iloc[target_index - seq_len : target_index]
            feature_data = recent_data[self.feature_columns].values

            # 标准化
            feature_data = self.scaler.transform(feature_data)
            X = feature_data.reshape(1, seq_len, -1)

            if not TENSORFLOW_AVAILABLE:
                # 使用传统方法预测
                X_flat = X.reshape(1, -1)
                pred = self.fallback_model.predict(X_flat)[0]
            else:
                # 使用LSTM预测
                pred = self.model.predict(X, verbose=0)[0]

            # 处理预测结果
            if isinstance(pred, np.ndarray):
                if len(pred) == 1:
                    prediction_value = float(pred[0])
                else:
                    prediction_value = pred.tolist()
            else:
                prediction_value = float(pred)

            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=prediction_value,
                confidence=0.8,
                metadata={
                    "success": True,
                    "predictions": {
                        "lstm_prediction": prediction_value,
                        "confidence": 0.8,
                        "method": "lstm" if TENSORFLOW_AVAILABLE else "fallback",
                    },
                    "sequence_length": seq_len,
                    "feature_count": len(self.feature_columns),
                    "model_type": "LSTM" if TENSORFLOW_AVAILABLE else "RandomForest",
                },
            )

        except Exception as e:
            self.logger.error(f"LSTM预测失败: {e}")
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": str(e), "predictions": {}},
            )

    def predict_batch(
        self, data: pd.DataFrame, target_indices: List[int], **kwargs
    ) -> List[PredictionResult]:
        """批量预测"""
        results = []
        for idx in target_indices:
            result = self.predict(data, idx, **kwargs)
            results.append(result)
        return results

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        info = {
            "name": "LSTM时间序列预测器",
            "version": "1.0.0",
            "is_trained": self.is_trained_flag,
            "tensorflow_available": TENSORFLOW_AVAILABLE,
            "config": {
                "lstm_units": self.config.lstm_units,
                "lstm_layers": self.config.lstm_layers,
                "sequence_length": self.config.sequence_length,
                "learning_rate": self.config.learning_rate,
            },
        }

        if self.model and TENSORFLOW_AVAILABLE:
            info["model_params"] = self.model.count_params()
            info["model_layers"] = len(self.model.layers)

        return info
