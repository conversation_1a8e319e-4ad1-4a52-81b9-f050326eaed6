#!/usr/bin/env python3
"""
简化的蓝球优化器集成脚本
直接修改主系统文件，集成增强版蓝球优化器
"""

import os
import sys
from pathlib import Path

def integrate_blue_optimizer():
    """集成蓝球优化器到主系统"""
    print("[开始] 蓝球优化器集成...")
    
    # 1. 修改 advanced_probabilistic_system.py
    main_system_path = "src/apps/advanced_probabilistic_system.py"
    
    if not os.path.exists(main_system_path):
        print(f"[错误] 找不到主系统文件: {main_system_path}")
        return False
    
    print(f"[信息] 读取主系统文件: {main_system_path}")
    
    try:
        with open(main_system_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经集成
        if "BlueBallOptimizerAdapter" in content:
            print("[警告] 蓝球优化器已经集成，跳过修改")
            return True
        
        # 添加导入语句
        import_line = "from src.integrations.blue_optimizer_adapter import BlueBallOptimizerAdapter, LegacyBluePredictorWrapper"
        
        # 找到合适的位置插入导入
        lines = content.split('\n')
        insert_pos = 0
        
        for i, line in enumerate(lines):
            if line.startswith('from src.') or line.startswith('import '):
                insert_pos = i + 1
        
        # 插入导入语句
        lines.insert(insert_pos, import_line)
        
        # 在类初始化中添加适配器
        init_addition = """
        # 蓝球优化器适配器 - 增强版蓝球预测
        try:
            self.blue_optimizer_adapter = BlueBallOptimizerAdapter()
            self.legacy_blue_wrapper = LegacyBluePredictorWrapper(self.blue_optimizer_adapter)
            print("[成功] 蓝球优化器适配器初始化成功")
        except Exception as e:
            print(f"[警告] 蓝球优化器适配器初始化失败: {e}")
            self.blue_optimizer_adapter = None
            self.legacy_blue_wrapper = None"""
        
        # 找到 AdvancedProbabilisticSystem 类的 __init__ 方法
        in_init = False
        init_indent = 0
        
        for i, line in enumerate(lines):
            if "class AdvancedProbabilisticSystem" in line:
                # 找到这个类的 __init__ 方法
                for j in range(i, min(i+50, len(lines))):
                    if "def __init__" in lines[j]:
                        in_init = True
                        init_indent = len(lines[j]) - len(lines[j].lstrip())
                        continue
                    
                    if in_init:
                        # 找到 __init__ 方法的结尾
                        if lines[j].strip() == "" or lines[j].startswith(' ' * (init_indent + 4)):
                            continue
                        elif lines[j].startswith(' ' * init_indent) and lines[j].strip() and not lines[j].startswith(' ' * (init_indent + 4)):
                            # 找到下一个方法的开始，在这里插入
                            lines.insert(j, init_addition)
                            break
                break
        
        # 写回文件
        with open(main_system_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("[成功] advanced_probabilistic_system.py 修改完成")
        
        # 2. 修改 predict_blue_kills 方法
        print("[信息] 修改蓝球杀号方法...")
        
        # 重新读取文件
        with open(main_system_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换 predict_blue_kills 方法的实现
        old_method_pattern = r'def predict_blue_kills\(self, period_data: Dict, target_count: int = 2\) -> List\[int\]:.*?(?=def |\Z)'
        
        new_method = '''def predict_blue_kills(self, period_data: Dict, target_count: int = 2) -> List[int]:
        """预测蓝球杀号 - 使用增强版优化器"""
        # 优先使用增强版蓝球优化器
        if hasattr(self, 'blue_optimizer_adapter') and self.blue_optimizer_adapter and self.blue_optimizer_adapter.is_trained:
            try:
                kill_numbers = self.blue_optimizer_adapter.predict_blue_kills(period_data, target_count)
                print(f"[蓝球] 增强版蓝球杀号: {sorted(kill_numbers)}")
                return kill_numbers
            except Exception as e:
                print(f"[警告] 增强版蓝球杀号失败，使用传统方法: {e}")
        
        # 备用：使用原有算法
        try:
            print(f"[蓝球] 使用传统蓝球杀号算法...")
            
            # 获取当前期号用于动态调整
            current_period = period_data.get("current", {}).get("期号", 25068)
            period_num = int(str(current_period)[-3:])  # 取期号后三位
            
            # 计算每个蓝球号码的综合出现概率（增强版）
            ball_probabilities = self._calculate_enhanced_blue_probabilities(
                period_data, period_num
            )
            
            # 应用蓝球多样性约束，避免重复杀号
            ball_probabilities = self._apply_blue_diversity_constraints(
                ball_probabilities, period_data, period_num
            )
            
            # 按概率从小到大排序，选择概率最小的号码作为杀号
            sorted_balls = sorted(ball_probabilities.items(), key=lambda x: x[1])
            
            # 动态选择候选数量
            candidate_count = min(target_count * 3, 8)  # 蓝球候选数量
            kill_candidates = [ball for ball, prob in sorted_balls[:candidate_count]]
            
            # 应用增强安全过滤规则
            final_kills = self._apply_enhanced_blue_safety_filters(
                kill_candidates, period_data, target_count, period_num
            )
            
            print(f"[蓝球] 传统蓝球杀号: {sorted(final_kills)}")
            return final_kills
            
        except Exception as e:
            print(f"[警告] 蓝球杀号算法失败，使用备选策略: {e}")
            # 最后备选策略
            return [3, 6][:target_count]

'''
        
        # 使用正则表达式替换方法
        import re
        content = re.sub(old_method_pattern, new_method, content, flags=re.DOTALL)
        
        # 写回文件
        with open(main_system_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("[成功] predict_blue_kills 方法修改完成")
        
        print("\n[完成] 蓝球优化器集成完成！")
        print("\n[总结] 集成内容:")
        print("1. [完成] 添加了 BlueBallOptimizerAdapter 导入")
        print("2. [完成] 在类初始化中添加了适配器实例")
        print("3. [完成] 修改了 predict_blue_kills 方法，优先使用增强版优化器")
        print("4. [完成] 保留了传统算法作为备用")
        
        return True
        
    except Exception as e:
        print(f"[错误] 集成失败: {e}")
        return False

def main():
    """主函数"""
    print("[开始] 简化蓝球优化器集成流程...")
    
    # 检查必要文件是否存在
    required_files = [
        "src/apps/advanced_probabilistic_system.py",
        "src/integrations/blue_optimizer_adapter.py",
        "src/optimizers/enhanced_blue_optimizer.py"
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"[错误] 缺少必要文件: {file_path}")
            return False
    
    print("[成功] 所有必要文件存在")
    
    # 执行集成
    success = integrate_blue_optimizer()
    
    if success:
        print("\n[完成] 集成成功完成！")
        print("\n[下一步] 操作:")
        print("1. 运行主系统测试，验证集成效果")
        print("2. 检查 2+1 命中率是否有提升")
        print("3. 监控蓝球预测多样性和准确率")
        return True
    else:
        print("[错误] 集成失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)