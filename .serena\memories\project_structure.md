# 项目结构详解

## 顶层目录结构
```
rebuileCP/
├── src/                    # 源代码目录（标准src布局）
├── tests/                  # 测试代码目录
├── config/                 # 配置文件目录
├── data/                   # 数据文件目录
├── docs/                   # 文档目录
├── logs/                   # 日志文件目录
├── models/                 # 模型文件目录
├── notebooks/              # Jupyter笔记本
├── scripts/                # 独立脚本
├── templates/              # 模板文件
├── main.py                 # 主入口文件
├── pyproject.toml          # 项目配置文件
├── requirements.txt        # 依赖列表
└── README.md              # 项目说明
```

## src目录结构
- **apps/**: 主应用程序入口
- **core/**: 核心模块（分析器、基础类、接口等）
- **models/**: 预测模型（贝叶斯、马尔科夫、神经网络等）
- **algorithms/**: 算法实现
- **generators/**: 号码生成器
- **systems/**: 系统模块
- **utils/**: 工具函数
- **framework/**: 统一框架
- **features/**: 特征工程
- **validation/**: 验证模块

## 测试目录结构
- **unit/**: 单元测试
- **integration/**: 集成测试
- **performance/**: 性能测试
- **conftest.py**: pytest配置

## 关键特点
- 遵循Python标准项目布局
- 清晰的功能模块分离
- 测试代码与源代码分离
- 配置和数据外部化
- 支持多种预测算法和模型