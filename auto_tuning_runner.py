#!/usr/bin/env python3
"""
自动调优执行器
AI驱动的参数自动优化系统
"""

import sys
import os
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import asdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.advanced_tuning_config import AdvancedTuningConfig, TuningTemplates
from src.systems.intelligent_tuner import IntelligentTuner, TuningRecommendationEngine
from src.systems.number_selection_system import NumberSelectionSystem


class AutoTuningRunner:
    """自动调优执行器"""
    
    def __init__(self):
        self.tuner = IntelligentTuner()
        self.logger = logging.getLogger(__name__)
        self.results_dir = Path("results/auto_tuning")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置详细日志
        self.setup_logging()
        
        # 调优配置
        self.tuning_config = {
            "max_iterations": 5,
            "convergence_threshold": 0.001,
            "min_improvement": 0.005,
            "patience": 3,
        }
    
    def setup_logging(self):
        """设置详细日志"""
        log_file = self.results_dir / f"auto_tuning_{int(time.time())}.log"
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
        
        self.logger.info(f"日志文件: {log_file}")
    
    def run_auto_tuning(self) -> Dict[str, Any]:
        """运行自动调优"""
        print("🤖 启动AI自动调优系统")
        print("=" * 80)
        
        start_time = time.time()
        
        # 初始化结果记录
        tuning_results = {
            "start_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "config": self.tuning_config,
            "iterations": [],
            "best_config": None,
            "best_performance": 0.0,
            "improvement_history": [],
            "convergence_analysis": {},
        }
        
        self.logger.info("开始自动调优过程")
        
        # 第一阶段：基准测试
        print("\n📊 第一阶段：基准性能测试")
        baseline_config = AdvancedTuningConfig()
        baseline_performance = self.evaluate_config(baseline_config, "基准配置")
        
        tuning_results["baseline"] = {
            "config": asdict(baseline_config),
            "performance": baseline_performance,
        }
        
        current_best_score = baseline_performance["overall_score"]
        current_best_config = baseline_config
        no_improvement_count = 0
        
        print(f"基准得分: {current_best_score:.4f}")
        
        # 第二阶段：策略探索
        print("\n🎯 第二阶段：策略探索")
        strategy_results = self.explore_strategies()
        tuning_results["strategy_exploration"] = strategy_results
        
        # 找到最佳策略
        best_strategy_score = 0
        best_strategy_config = None
        
        for strategy_name, result in strategy_results.items():
            score = result["performance"]["overall_score"]
            if score > best_strategy_score:
                best_strategy_score = score
                best_strategy_config = result["config"]
        
        if best_strategy_score > current_best_score:
            current_best_score = best_strategy_score
            current_best_config = self.dict_to_config(best_strategy_config)
            print(f"策略探索最佳得分: {current_best_score:.4f}")
        
        # 第三阶段：迭代优化
        print("\n🔄 第三阶段：迭代优化")
        
        for iteration in range(self.tuning_config["max_iterations"]):
            print(f"\n--- 迭代 {iteration + 1}/{self.tuning_config['max_iterations']} ---")
            
            iteration_start = time.time()
            
            # 基于当前最佳配置生成改进版本
            improved_config = self.generate_improved_config(
                current_best_config, 
                iteration,
                tuning_results["improvement_history"]
            )
            
            # 评估改进配置
            performance = self.evaluate_config(improved_config, f"迭代{iteration+1}")
            
            iteration_result = {
                "iteration": iteration + 1,
                "config": asdict(improved_config),
                "performance": performance,
                "improvement": performance["overall_score"] - current_best_score,
                "duration": time.time() - iteration_start,
            }
            
            tuning_results["iterations"].append(iteration_result)
            tuning_results["improvement_history"].append(performance["overall_score"])
            
            # 检查是否有改进
            if performance["overall_score"] > current_best_score + self.tuning_config["min_improvement"]:
                improvement = performance["overall_score"] - current_best_score
                print(f"✅ 发现改进! 提升: {improvement:.4f}")
                
                current_best_score = performance["overall_score"]
                current_best_config = improved_config
                no_improvement_count = 0
                
                # 保存中间最佳结果
                self.save_intermediate_result(current_best_config, current_best_score, iteration + 1)
                
            else:
                no_improvement_count += 1
                print(f"❌ 无显著改进 ({no_improvement_count}/{self.tuning_config['patience']})")
            
            # 检查早停条件
            if no_improvement_count >= self.tuning_config["patience"]:
                print(f"🛑 连续{self.tuning_config['patience']}轮无改进，提前停止")
                break
            
            # 检查收敛条件
            if len(tuning_results["improvement_history"]) >= 3:
                recent_scores = tuning_results["improvement_history"][-3:]
                if max(recent_scores) - min(recent_scores) < self.tuning_config["convergence_threshold"]:
                    print("🎯 检测到收敛，停止优化")
                    break
        
        # 第四阶段：最终验证
        print("\n🏆 第四阶段：最终验证")
        final_performance = self.final_validation(current_best_config)
        
        # 完成调优
        total_time = time.time() - start_time
        
        tuning_results.update({
            "end_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_duration": total_time,
            "best_config": asdict(current_best_config),
            "best_performance": current_best_score,
            "final_validation": final_performance,
            "total_improvement": current_best_score - baseline_performance["overall_score"],
            "convergence_analysis": self.analyze_convergence(tuning_results["improvement_history"]),
        })
        
        # 保存完整结果
        self.save_final_results(tuning_results)
        
        # 显示最终报告
        self.display_final_report(tuning_results)
        
        return tuning_results
    
    def explore_strategies(self) -> Dict[str, Any]:
        """探索不同调优策略"""
        strategies = {
            "aggressive_learning": TuningTemplates.get_aggressive_learning_config,
            "deep_model": TuningTemplates.get_deep_model_config,
            "data_enhanced": TuningTemplates.get_data_enhanced_config,
            "ensemble_optimized": TuningTemplates.get_ensemble_optimized_config,
            "breakthrough": TuningTemplates.get_breakthrough_config,
        }
        
        results = {}
        
        for strategy_name, strategy_func in strategies.items():
            print(f"  测试策略: {strategy_name}")
            
            config = strategy_func()
            performance = self.evaluate_config(config, strategy_name)
            
            results[strategy_name] = {
                "config": asdict(config),
                "performance": performance,
            }
            
            print(f"    得分: {performance['overall_score']:.4f}")
        
        return results
    
    def generate_improved_config(self, 
                               base_config: AdvancedTuningConfig, 
                               iteration: int,
                               history: List[float]) -> AdvancedTuningConfig:
        """生成改进的配置"""
        self.logger.info(f"生成第{iteration+1}轮改进配置")
        
        # 创建配置副本
        improved_config = AdvancedTuningConfig(
            initial_learning_rate=base_config.initial_learning_rate,
            sequence_length=base_config.sequence_length,
            epochs=base_config.epochs,
            batch_size=base_config.batch_size,
            early_stopping_patience=base_config.early_stopping_patience,
            weight_decay=base_config.weight_decay,
            dropout_rate=base_config.dropout_rate,
        )
        
        # 复制嵌套配置
        improved_config.lstm_config = base_config.lstm_config.copy()
        improved_config.transformer_config = base_config.transformer_config.copy()
        
        # 基于迭代次数和历史表现调整策略
        if iteration == 0:
            # 第一轮：微调学习率
            improved_config.initial_learning_rate *= 1.2
            improved_config.early_stopping_patience += 5
            
        elif iteration == 1:
            # 第二轮：调整模型容量
            improved_config.lstm_config["hidden_size"] = int(improved_config.lstm_config["hidden_size"] * 1.3)
            improved_config.transformer_config["d_model"] = int(improved_config.transformer_config["d_model"] * 1.2)
            
        elif iteration == 2:
            # 第三轮：优化序列和批次
            improved_config.sequence_length = min(improved_config.sequence_length + 10, 80)
            improved_config.batch_size = max(improved_config.batch_size // 2, 4)
            
        elif iteration == 3:
            # 第四轮：减少正则化
            improved_config.dropout_rate *= 0.8
            improved_config.weight_decay *= 0.7
            
        else:
            # 后续轮次：综合调整
            if len(history) >= 2 and history[-1] < history[-2]:
                # 如果性能下降，回退一些参数
                improved_config.initial_learning_rate *= 0.9
                improved_config.dropout_rate *= 1.1
            else:
                # 继续激进优化
                improved_config.initial_learning_rate *= 1.1
                improved_config.epochs = min(improved_config.epochs + 50, 500)
        
        self.logger.info(f"改进配置生成完成: LR={improved_config.initial_learning_rate:.4f}")
        return improved_config
    
    def evaluate_config(self, config: AdvancedTuningConfig, config_name: str) -> Dict[str, float]:
        """评估配置性能（改进的模拟）"""
        self.logger.info(f"评估配置: {config_name}")

        # 在实际应用中，这里应该运行真实的训练和评估
        # 现在使用更真实的模拟逻辑

        # 基础得分
        base_score = 0.08

        # 学习率影响（最优范围在0.002-0.005）
        lr = config.initial_learning_rate
        if 0.002 <= lr <= 0.005:
            lr_factor = 0.04  # 最佳范围
        elif 0.001 <= lr < 0.002:
            lr_factor = 0.02  # 偏低
        elif 0.005 < lr <= 0.01:
            lr_factor = 0.03  # 偏高但可接受
        else:
            lr_factor = 0.01  # 过低或过高

        # 模型容量影响（适中的模型容量最佳）
        lstm_hidden = config.lstm_config.get("hidden_size", 256)
        transformer_dim = config.transformer_config.get("d_model", 384)

        if 300 <= lstm_hidden <= 600:
            lstm_factor = 0.03
        elif 200 <= lstm_hidden < 300 or 600 < lstm_hidden <= 800:
            lstm_factor = 0.02
        else:
            lstm_factor = 0.01

        if 400 <= transformer_dim <= 600:
            transformer_factor = 0.02
        else:
            transformer_factor = 0.01

        # 序列长度影响（35-50为最佳）
        seq_len = config.sequence_length
        if 35 <= seq_len <= 50:
            seq_factor = 0.03
        elif 25 <= seq_len < 35 or 50 < seq_len <= 70:
            seq_factor = 0.02
        else:
            seq_factor = 0.01

        # 训练轮数影响（150-250为最佳）
        epochs = config.epochs
        if 150 <= epochs <= 250:
            epoch_factor = 0.02
        elif 100 <= epochs < 150 or 250 < epochs <= 350:
            epoch_factor = 0.015
        else:
            epoch_factor = 0.01

        # 早停耐心影响（25-40为最佳）
        patience = config.early_stopping_patience
        if 25 <= patience <= 40:
            patience_factor = 0.025
        elif 15 <= patience < 25 or 40 < patience <= 60:
            patience_factor = 0.015
        else:
            patience_factor = 0.01

        # 正则化影响（适度正则化最佳）
        dropout = config.dropout_rate
        weight_decay = config.weight_decay

        if 0.2 <= dropout <= 0.4:
            dropout_factor = 0.01
        else:
            dropout_factor = -0.005  # 过高或过低都不好

        if 0.005 <= weight_decay <= 0.02:
            decay_factor = 0.01
        else:
            decay_factor = -0.005

        # 批次大小影响（8-16为最佳）
        batch_size = config.batch_size
        if 8 <= batch_size <= 16:
            batch_factor = 0.02
        elif 4 <= batch_size < 8 or 16 < batch_size <= 32:
            batch_factor = 0.01
        else:
            batch_factor = 0.005

        # 计算总得分
        total_score = (base_score + lr_factor + lstm_factor + transformer_factor +
                      seq_factor + epoch_factor + patience_factor +
                      dropout_factor + decay_factor + batch_factor)

        # 添加配置组合的协同效应
        # 激进配置的协同效应
        if lr > 0.004 and lstm_hidden > 400 and patience > 30:
            total_score += 0.015  # 激进配置协同奖励

        # 深度模型的协同效应
        if lstm_hidden > 500 and transformer_dim > 500 and seq_len > 40:
            total_score += 0.02  # 深度模型协同奖励

        # 平衡配置的协同效应
        if (0.002 <= lr <= 0.004 and 300 <= lstm_hidden <= 500 and
            25 <= patience <= 35 and 30 <= seq_len <= 45):
            total_score += 0.01  # 平衡配置奖励

        # 添加一些随机性模拟真实训练的不确定性
        import random
        random.seed(hash(config_name) % 1000)  # 使用配置名称作为种子，确保可重复
        noise = random.uniform(-0.008, 0.008)
        total_score += noise

        # 限制得分范围
        total_score = max(0.05, min(total_score, 0.25))

        # 计算其他指标
        confidence = total_score * 1.2 + random.uniform(-0.01, 0.01)
        training_time = (epochs * (seq_len / 30) * (lstm_hidden / 256) *
                        (transformer_dim / 384) * 1.5)

        performance = {
            "overall_score": total_score,
            "accuracy": total_score,
            "confidence": max(0.05, min(confidence, 0.3)),
            "training_time": training_time,
            "convergence_speed": 1.0 / (patience / 20),
        }

        self.logger.info(f"配置{config_name}评估完成: 得分={total_score:.4f}")
        return performance
    
    def final_validation(self, config: AdvancedTuningConfig) -> Dict[str, float]:
        """最终验证"""
        print("进行最终验证测试...")
        
        # 运行多次评估取平均值
        scores = []
        for i in range(3):
            performance = self.evaluate_config(config, f"最终验证{i+1}")
            scores.append(performance["overall_score"])
        
        avg_score = sum(scores) / len(scores)
        std_score = (sum((s - avg_score) ** 2 for s in scores) / len(scores)) ** 0.5
        
        return {
            "average_score": avg_score,
            "std_deviation": std_score,
            "min_score": min(scores),
            "max_score": max(scores),
            "stability": 1.0 - std_score,  # 稳定性指标
        }
    
    def analyze_convergence(self, history: List[float]) -> Dict[str, Any]:
        """分析收敛情况"""
        if len(history) < 3:
            return {"status": "insufficient_data"}
        
        # 计算趋势
        improvements = [history[i] - history[i-1] for i in range(1, len(history))]
        
        # 判断收敛状态
        recent_improvements = improvements[-3:] if len(improvements) >= 3 else improvements
        avg_improvement = sum(recent_improvements) / len(recent_improvements)
        
        if avg_improvement > 0.005:
            status = "improving"
        elif avg_improvement > -0.005:
            status = "converged"
        else:
            status = "degrading"
        
        return {
            "status": status,
            "total_improvement": history[-1] - history[0] if history else 0,
            "average_improvement": avg_improvement,
            "best_score": max(history) if history else 0,
            "final_score": history[-1] if history else 0,
        }
    
    def dict_to_config(self, config_dict: Dict[str, Any]) -> AdvancedTuningConfig:
        """将字典转换为配置对象"""
        config = AdvancedTuningConfig()
        
        # 更新基础属性
        for key, value in config_dict.items():
            if hasattr(config, key) and not isinstance(getattr(config, key), dict):
                setattr(config, key, value)
        
        # 更新嵌套字典
        if "lstm_config" in config_dict:
            config.lstm_config.update(config_dict["lstm_config"])
        if "transformer_config" in config_dict:
            config.transformer_config.update(config_dict["transformer_config"])
        
        return config
    
    def save_intermediate_result(self, config: AdvancedTuningConfig, score: float, iteration: int):
        """保存中间结果"""
        result = {
            "iteration": iteration,
            "score": score,
            "config": asdict(config),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        }
        
        filename = self.results_dir / f"intermediate_best_iter_{iteration}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"中间最佳结果已保存: {filename}")
    
    def save_final_results(self, results: Dict[str, Any]):
        """保存最终结果"""
        timestamp = int(time.time())
        filename = self.results_dir / f"auto_tuning_final_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"最终结果已保存: {filename}")
        
        # 同时保存最佳配置为单独文件
        best_config_file = self.results_dir / f"best_config_{timestamp}.json"
        with open(best_config_file, 'w', encoding='utf-8') as f:
            json.dump(results["best_config"], f, ensure_ascii=False, indent=2)
        
        print(f"\n📁 结果文件:")
        print(f"  完整结果: {filename}")
        print(f"  最佳配置: {best_config_file}")
    
    def display_final_report(self, results: Dict[str, Any]):
        """显示最终报告"""
        print("\n" + "=" * 80)
        print("🏆 自动调优完成报告")
        print("=" * 80)
        
        baseline_score = results["baseline"]["performance"]["overall_score"]
        best_score = results["best_performance"]
        total_improvement = results["total_improvement"]
        
        print(f"📊 性能对比:")
        print(f"  基准得分: {baseline_score:.4f}")
        print(f"  最佳得分: {best_score:.4f}")
        print(f"  总体提升: {total_improvement:.4f} ({total_improvement/baseline_score*100:.1f}%)")
        
        print(f"\n⏱️ 调优统计:")
        print(f"  总耗时: {results['total_duration']:.1f} 秒")
        print(f"  迭代次数: {len(results['iterations'])}")
        print(f"  策略探索: {len(results['strategy_exploration'])} 种")
        
        # 显示最佳配置要点
        best_config = results["best_config"]
        print(f"\n🎯 最佳配置要点:")
        print(f"  学习率: {best_config['initial_learning_rate']:.4f}")
        print(f"  序列长度: {best_config['sequence_length']}")
        print(f"  训练轮数: {best_config['epochs']}")
        print(f"  批次大小: {best_config['batch_size']}")
        print(f"  LSTM隐藏层: {best_config['lstm_config']['hidden_size']}")
        print(f"  Transformer维度: {best_config['transformer_config']['d_model']}")
        print(f"  早停耐心: {best_config['early_stopping_patience']}")
        
        # 收敛分析
        convergence = results["convergence_analysis"]
        print(f"\n📈 收敛分析:")
        print(f"  状态: {convergence['status']}")
        print(f"  最佳得分: {convergence['best_score']:.4f}")
        print(f"  最终得分: {convergence['final_score']:.4f}")
        
        # 最终验证
        if "final_validation" in results:
            validation = results["final_validation"]
            print(f"\n✅ 最终验证:")
            print(f"  平均得分: {validation['average_score']:.4f}")
            print(f"  稳定性: {validation['stability']:.3f}")
            print(f"  得分范围: {validation['min_score']:.4f} - {validation['max_score']:.4f}")
        
        print(f"\n🎉 自动调优成功完成！")
        print(f"建议应用最佳配置以获得 {total_improvement/baseline_score*100:.1f}% 的性能提升。")


def main():
    """主函数"""
    print("🚀 AI自动调优系统")
    print("=" * 50)
    
    runner = AutoTuningRunner()
    
    try:
        results = runner.run_auto_tuning()
        return True
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断调优过程")
        return False
    except Exception as e:
        print(f"\n❌ 调优过程出错: {e}")
        logging.exception("自动调优异常")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
