#!/usr/bin/env python3
"""
蓝球优化器集成验证
验证增强版蓝球优化器是否正确集成到主系统中
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def validate_integration():
    """验证集成效果"""
    print("[开始] 验证蓝球优化器集成效果...")
    
    try:
        # 1. 测试导入
        print("[验证] 导入主系统...")
        from src.apps.advanced_probabilistic_system import AdvancedProbabilisticSystem
        print("[成功] 主系统导入成功")
        
        # 2. 测试实例化
        print("[验证] 实例化主系统...")
        system = AdvancedProbabilisticSystem()
        print("[成功] 主系统实例化成功")
        
        # 3. 检查适配器是否存在
        print("[验证] 检查蓝球优化器适配器...")
        if hasattr(system, 'blue_optimizer_adapter'):
            print("[成功] 蓝球优化器适配器存在")
            if system.blue_optimizer_adapter is not None:
                print("[成功] 蓝球优化器适配器已初始化")
                print(f"[信息] 适配器状态: {system.blue_optimizer_adapter.get_optimizer_status()}")
            else:
                print("[警告] 蓝球优化器适配器为None")
        else:
            print("[错误] 蓝球优化器适配器不存在")
            return False
        
        # 4. 检查包装器是否存在
        print("[验证] 检查传统蓝球预测器包装器...")
        if hasattr(system, 'legacy_blue_wrapper'):
            print("[成功] 传统蓝球预测器包装器存在")
        else:
            print("[错误] 传统蓝球预测器包装器不存在")
            return False
        
        # 5. 测试蓝球杀号功能
        print("[验证] 测试蓝球杀号功能...")
        
        # 构建测试数据
        test_period_data = {
            'current': {
                '期号': 25068,
                '红球': [1, 5, 12, 18, 25],
                '蓝球': [8, 11]
            },
            'recent_data': []  # 简化测试
        }
        
        try:
            kill_numbers = system.predict_blue_kills(test_period_data, target_count=2)
            print(f"[成功] 蓝球杀号预测: {sorted(kill_numbers)}")
            
            # 验证杀号数量
            if len(kill_numbers) == 2:
                print("[成功] 杀号数量正确")
            else:
                print(f"[警告] 杀号数量不正确，期望2个，实际{len(kill_numbers)}个")
            
            # 验证杀号范围
            if all(1 <= num <= 12 for num in kill_numbers):
                print("[成功] 杀号范围正确 (1-12)")
            else:
                print(f"[错误] 杀号范围错误: {kill_numbers}")
                return False
                
        except Exception as e:
            print(f"[错误] 蓝球杀号验证失败: {e}")
            return False
        
        print("\n[完成] 蓝球优化器集成验证完成！")
        print("\n[总结] 验证结果:")
        print("1. [完成] 主系统导入和实例化")
        print("2. [完成] 蓝球优化器适配器集成")
        print("3. [完成] 传统蓝球预测器包装器集成")
        print("4. [完成] 蓝球杀号功能验证")
        
        return True
        
    except Exception as e:
        print(f"[错误] 集成验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("[开始] 蓝球优化器集成验证...")
    
    success = validate_integration()
    
    if success:
        print("\n[成功] 所有验证通过！")
        print("\n[下一步] 建议:")
        print("1. 运行完整的系统测试")
        print("2. 进行回测验证 2+1 命中率提升")
        print("3. 开始 Phase 2: 深度学习效率优化")
        return True
    else:
        print("\n[失败] 验证未通过，需要检查集成问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)