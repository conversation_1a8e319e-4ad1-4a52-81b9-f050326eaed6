#!/usr/bin/env python3
"""
Phase 3 验证脚本 - 权重系统重构验证
验证智能权重管理系统的功能和集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.integrations.weight_manager_adapter import create_weight_manager_adapter
from src.integrations.weight_optimizer import create_weight_optimizer
from src.integrations.adaptive_weight_learner import create_adaptive_weight_learner
from src.integrations.weight_integration_adapter import create_weight_integration_adapter

def test_weight_manager():
    """测试权重管理器"""
    print("=" * 60)
    print("测试 1: 权重管理器")
    print("=" * 60)
    
    try:
        # 创建权重管理器
        manager = create_weight_manager_adapter()
        
        # 测试获取权重
        ensemble_weights = manager.get_ensemble_weights()
        model_weights = manager.get_model_weights()
        feature_weights = manager.get_feature_weights()
        loss_weights = manager.get_loss_weights()
        
        print("✅ 权重管理器创建成功")
        print(f"   集成权重数量: {len(ensemble_weights)}")
        print(f"   模型权重数量: {len(model_weights)}")
        print(f"   特征权重数量: {len(feature_weights)}")
        print(f"   损失权重数量: {len(loss_weights)}")
        
        # 测试权重更新
        manager.update_weight_performance('red_odd_even', 0.35, 0.8)
        print("✅ 权重性能更新成功")
        
        # 测试优化
        manager.optimize_for_2_plus_1()
        print("✅ 2+1命中率优化成功")
        
        # 获取状态
        status = manager.get_status()
        print(f"✅ 状态获取成功: {status['total_weights']} 个权重")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重管理器测试失败: {e}")
        return False

def test_weight_optimizer():
    """测试权重优化器"""
    print("\n" + "=" * 60)
    print("测试 2: 权重优化器")
    print("=" * 60)
    
    try:
        # 创建优化器
        optimizer = create_weight_optimizer(target_hit_rate=0.4)
        
        # 模拟权重和性能数据
        test_weights = {
            'red_odd_even': 0.3,
            'red_size': 0.4,
            'blue_size': 0.2,
            'bayesian': 0.15,
            'frequency': 0.25
        }
        
        test_categories = {
            'red_odd_even': 'feature',
            'red_size': 'feature',
            'blue_size': 'feature',
            'bayesian': 'ensemble',
            'frequency': 'ensemble'
        }
        
        test_performance = [0.2, 0.25, 0.3, 0.28, 0.32, 0.35, 0.33, 0.37, 0.36, 0.38]
        
        print("✅ 权重优化器创建成功")
        print(f"   目标命中率: {optimizer.target_hit_rate}")
        print(f"   测试权重数量: {len(test_weights)}")
        
        # 执行优化
        result = optimizer.optimize_for_2_plus_1(test_weights, test_categories, test_performance)
        
        print(f"✅ 优化执行成功")
        print(f"   改进幅度: {result.improvement:.4f}")
        print(f"   迭代次数: {result.iterations}")
        print(f"   收敛时间: {result.convergence_time:.2f}秒")
        print(f"   优化成功: {result.success}")
        
        # 获取建议
        suggestions = optimizer.suggest_weight_adjustments(test_weights, test_categories, 0.3)
        suggestion_count = sum(1 for s in suggestions.values() if s['recommendations'])
        print(f"✅ 权重建议生成成功: {suggestion_count} 个建议")
        
        # 获取报告
        report = optimizer.get_optimization_report()
        print(f"✅ 优化报告生成成功: {report.get('total_optimizations', 0)} 次优化")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重优化器测试失败: {e}")
        return False

def test_adaptive_learner():
    """测试自适应学习器"""
    print("\n" + "=" * 60)
    print("测试 3: 自适应权重学习器")
    print("=" * 60)
    
    try:
        # 创建学习器
        learner = create_adaptive_weight_learner()
        
        print("✅ 自适应学习器创建成功")
        print(f"   初始探索率: {learner.exploration_rate:.3f}")
        print(f"   学习率: {learner.adaptive_lr:.3f}")
        print(f"   动作空间大小: {len(learner.actions)}")
        
        # 模拟学习过程
        test_weights = {
            'red_odd_even': 0.3,
            'red_size': 0.4,
            'blue_size': 0.2,
            'bayesian': 0.15,
            'frequency': 0.25
        }
        
        print("\n模拟学习过程...")
        for i in range(5):
            # 模拟性能变化
            performance = 0.2 + i * 0.03
            
            # 学习并调整权重
            adjusted_weights = learner.learn_from_experience(test_weights, performance)
            test_weights = adjusted_weights
            
            print(f"   第{i+1}轮: 性能={performance:.3f}, 探索率={learner.exploration_rate:.3f}")
        
        print("✅ 学习过程模拟成功")
        
        # 获取学习洞察
        insights = learner.get_learning_insights()
        print(f"✅ 学习洞察生成成功")
        print(f"   总经验数: {insights['learning_progress']['total_experiences']}")
        print(f"   Q表大小: {insights['learning_progress']['q_table_size']}")
        print(f"   当前性能: {insights['performance_analysis'].get('current_performance', 0):.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自适应学习器测试失败: {e}")
        return False

def test_integration_adapter():
    """测试集成适配器"""
    print("\n" + "=" * 60)
    print("测试 4: 权重集成适配器")
    print("=" * 60)
    
    try:
        # 创建集成适配器
        adapter = create_weight_integration_adapter()
        
        print("✅ 权重集成适配器创建成功")
        
        # 获取集成权重
        weights = adapter.get_integrated_weights()
        total_weights = sum(len(category_weights) for category_weights in weights.values())
        print(f"✅ 集成权重获取成功: {total_weights} 个权重")
        
        for category, category_weights in weights.items():
            print(f"   {category}: {len(category_weights)} 个权重")
        
        # 模拟性能反馈
        print("\n模拟性能反馈...")
        for i in range(3):
            hit_rate = 0.25 + i * 0.05
            adapter.update_performance_feedback(hit_rate, accuracy=hit_rate * 0.8)
            print(f"   第{i+1}次反馈: 2+1命中率={hit_rate:.3f}")
        
        print("✅ 性能反馈更新成功")
        
        # 获取状态
        status = adapter.get_integration_status()
        print(f"✅ 集成状态获取成功")
        print(f"   预测次数: {status['performance_tracking']['prediction_count']}")
        print(f"   性能缓冲区大小: {status['performance_tracking']['performance_buffer_size']}")
        print(f"   当前性能: {status['performance_tracking'].get('current_performance', 0):.3f}")
        
        # 获取建议
        recommendations = adapter.get_weight_recommendations()
        print(f"✅ 权重建议获取成功")
        
        if 'learner_insights' in recommendations:
            insights = recommendations['learner_insights']
            if 'recommendations' in insights and insights['recommendations']:
                print(f"   学习器建议数量: {len(insights['recommendations'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 权重集成适配器测试失败: {e}")
        return False

def test_system_integration():
    """测试系统集成"""
    print("\n" + "=" * 60)
    print("测试 5: 系统集成验证")
    print("=" * 60)
    
    try:
        # 创建完整的集成系统
        adapter = create_weight_integration_adapter(enable_learning=True, enable_optimization=True)
        
        print("✅ 完整集成系统创建成功")
        
        # 模拟完整的预测周期
        print("\n模拟完整预测周期...")
        
        for cycle in range(3):
            print(f"\n--- 预测周期 {cycle + 1} ---")
            
            # 1. 获取当前权重配置
            weights = adapter.get_integrated_weights()
            print(f"   获取权重配置: {sum(len(w) for w in weights.values())} 个权重")
            
            # 2. 模拟预测过程（这里简化为随机性能）
            import random
            hit_rate = 0.2 + cycle * 0.1 + random.uniform(-0.05, 0.05)
            hit_rate = max(0, min(1, hit_rate))
            
            # 3. 更新性能反馈
            adapter.update_performance_feedback(
                hit_rate_2_plus_1=hit_rate,
                accuracy=hit_rate * 0.9,
                additional_metrics={
                    'prediction_consistency': 0.8,
                    'model_agreement': 0.75
                }
            )
            print(f"   性能反馈: 2+1命中率={hit_rate:.3f}")
            
            # 4. 获取系统状态
            status = adapter.get_integration_status()
            trend = status['performance_tracking'].get('performance_trend', 'unknown')
            print(f"   性能趋势: {trend}")
        
        print("\n✅ 系统集成验证成功")
        
        # 最终状态报告
        final_status = adapter.get_integration_status()
        print(f"\n最终系统状态:")
        print(f"   总预测次数: {final_status['performance_tracking']['prediction_count']}")
        print(f"   平均性能: {final_status['performance_tracking'].get('average_performance', 0):.3f}")
        print(f"   性能趋势: {final_status['performance_tracking'].get('performance_trend', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统集成验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("Phase 3 权重系统重构验证")
    print("=" * 80)
    print("验证智能权重管理系统的四个核心组件:")
    print("1. 智能权重管理器 (SmartWeightManager)")
    print("2. 权重优化器 (WeightOptimizer)")
    print("3. 自适应权重学习器 (AdaptiveWeightLearner)")
    print("4. 权重集成适配器 (WeightIntegrationAdapter)")
    print("5. 系统集成验证")
    
    # 执行所有测试
    tests = [
        ("权重管理器", test_weight_manager),
        ("权重优化器", test_weight_optimizer),
        ("自适应学习器", test_adaptive_learner),
        ("集成适配器", test_integration_adapter),
        ("系统集成", test_system_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结报告
    print("\n" + "=" * 80)
    print("Phase 3 验证总结报告")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 Phase 3 权重系统重构验证完全成功！")
        print("\n核心功能验证:")
        print("✅ 智能权重管理 - 统一权重配置管理")
        print("✅ 权重优化 - 针对2+1命中率优化")
        print("✅ 自适应学习 - 智能权重调整")
        print("✅ 系统集成 - 无缝集成现有系统")
        print("\nPhase 3 已准备就绪，可以集成到主系统！")
    else:
        print(f"\n⚠️  Phase 3 验证部分失败 ({passed}/{total})")
        print("需要修复失败的组件后再进行集成。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)