#!/usr/bin/env python3
"""
Phase 2 深度学习效率优化器验证
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def validate_phase2():
    """验证Phase 2集成"""
    print("[开始] Phase 2: 深度学习效率优化器验证")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 深度学习效率优化器
    try:
        from src.optimizers.dl_efficiency_optimizer import UnifiedDeepLearningInterface
        dl_interface = UnifiedDeepLearningInterface()
        print("[成功] 深度学习效率优化器创建成功")
        success_count += 1
    except Exception as e:
        print(f"[失败] 深度学习效率优化器失败: {e}")

    # 测试2: 深度学习效率适配器
    try:
        from src.integrations.dl_efficiency_adapter import DeepLearningEfficiencyAdapter
        adapter = DeepLearningEfficiencyAdapter()
        print("[成功] 深度学习效率适配器创建成功")
        success_count += 1
    except Exception as e:
        print(f"[失败] 深度学习效率适配器失败: {e}")

    # 测试3: 主系统集成
    try:
        from src.apps.advanced_probabilistic_system import AdvancedProbabilisticSystem
        system = AdvancedProbabilisticSystem()

        has_dl_adapter = hasattr(system, 'dl_efficiency_adapter') and system.dl_efficiency_adapter is not None
        has_dl_wrapper = hasattr(system, 'legacy_dl_wrapper') and system.legacy_dl_wrapper is not None

        if has_dl_adapter and has_dl_wrapper:
            print("[成功] 主系统深度学习集成成功")
            success_count += 1
        else:
            print("[失败] 主系统深度学习集成失败")
    except Exception as e:
        print(f"[失败] 主系统集成失败: {e}")

    print("\n" + "=" * 50)
    print(f"[结果] 验证结果: {success_count}/{total_tests} 成功")

    if success_count == total_tests:
        print("[完成] Phase 2 深度学习效率优化器集成完全成功！")
        print("\n[新增功能]:")
        print("   - 智能模型缓存系统")
        print("   - 增量学习管理")
        print("   - 训练效率优化")
        print("   - 统一深度学习接口")
        print("   - 主系统无缝集成")
        return True
    else:
        print("[警告] 部分功能集成失败")
        return False

if __name__ == "__main__":
    validate_phase2()