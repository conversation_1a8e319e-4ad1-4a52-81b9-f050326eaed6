#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phase 3 权重系统集成验证脚本

验证Phase 3智能权重管理系统与主彩票预测系统的集成效果
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def validate_phase3_integration():
    """验证Phase 3权重系统集成"""
    print("=" * 80)
    print("Phase 3 权重系统集成验证")
    print("=" * 80)
    
    try:
        # 导入主系统
        from src.systems.main import LotteryPredictor
        
        print("[1/5] 初始化主系统...")
        predictor = LotteryPredictor()
        
        # 检查权重系统是否成功初始化
        if hasattr(predictor, 'use_weight_integration') and predictor.use_weight_integration:
            print("[SUCCESS] Phase 3 权重系统已成功集成到主系统")
            print(f"   权重集成适配器: {type(predictor.weight_integration_adapter).__name__}")
        else:
            print("[FAILED] Phase 3 权重系统集成失败")
            return False
        
        print("\n[2/5] 验证权重系统基本功能...")
        
        # 验证获取当前权重
        try:
            current_weights = predictor.weight_integration_adapter.get_current_weights()
            print(f"[SUCCESS] 权重配置获取成功，共{len(current_weights)}个权重类别")
            for category, weights in current_weights.items():
                print(f"   - {category}: {len(weights)}个权重")
        except Exception as e:
            print(f"[FAILED] 权重配置获取失败: {e}")
            return False
        
        print("\n[3/5] 验证权重系统报告生成...")
        
        # 验证权重系统报告
        try:
            weight_report = predictor._get_weight_system_report()
            print(f"[SUCCESS] 权重系统报告生成成功")
            print(f"   状态: {weight_report.get('status', 'Unknown')}")
            print(f"   权重类别数: {weight_report.get('weight_categories', 0)}")
            print(f"   集成版本: {weight_report.get('integration_version', 'Unknown')}")
        except Exception as e:
            print(f"[FAILED] 权重系统报告生成失败: {e}")
            return False
        
        print("\n[4/5] 验证预测流程中的权重系统集成...")
        
        # 验证预测流程
        try:
            # 获取最新期号进行验证
            latest_period = predictor.data.iloc[0]['期号']
            validation_period = latest_period - 1  # 验证前一期
            
            print(f"   验证期号: {validation_period}")
            
            # 执行预测
            prediction_result = predictor.predict_next_period(validation_period)
            
            # 检查是否包含权重系统报告
            if 'weight_system_report' in prediction_result:
                weight_report = prediction_result['weight_system_report']
                if weight_report and weight_report.get('status') == '权重系统已启用':
                    print("[SUCCESS] 预测流程中权重系统集成成功")
                    print(f"   权重类别: {weight_report.get('weight_categories', 0)}")
                else:
                    print(f"[WARNING]  权重系统报告状态: {weight_report.get('status', 'Unknown') if weight_report else 'None'}")
            else:
                print("[FAILED] 预测结果中未包含权重系统报告")
                return False
                
        except Exception as e:
            print(f"[FAILED] 预测流程验证失败: {e}")
            return False
        
        print("\n[5/5] 验证权重系统性能反馈...")
        
        # 验证性能反馈功能
        try:
            # 模拟性能数据
            validation_performance_data = {
                'period': str(validation_period),
                'hit_2_plus_1': True,
                'individual_hits': {
                    'red_odd_even': True,
                    'red_size': True,
                    'blue_size': True
                },
                'predictions': {
                    'red_odd_even': '2:3',
                    'red_size': '3:2',
                    'blue_size': '1:1'
                },
                'actual_results': {
                    'red_odd_even': '2:3',
                    'red_size': '3:2',
                    'blue_size': '1:1'
                }
            }
            
            feedback_result = predictor.weight_integration_adapter.provide_performance_feedback(validation_performance_data)
            print(f"[SUCCESS] 性能反馈功能验证成功: {feedback_result}")
            
        except Exception as e:
            print(f"[FAILED] 性能反馈功能验证失败: {e}")
            return False
        
        print("\n" + "=" * 80)
        print("[CELEBRATION] Phase 3 权重系统集成验证全部通过!")
        print("=" * 80)
        
        # 显示集成摘要
        print("\n[SUMMARY] 集成摘要:")
        print(f"   [SUCCESS] 权重管理系统: 已集成")
        print(f"   [SUCCESS] 权重优化器: 已集成") 
        print(f"   [SUCCESS] 自适应学习器: 已集成")
        print(f"   [SUCCESS] 权重集成适配器: 已集成")
        print(f"   [SUCCESS] 性能反馈机制: 已集成")
        print(f"   [SUCCESS] 预测流程集成: 已集成")
        
        return True
        
    except ImportError as e:
        print(f"[FAILED] 导入失败: {e}")
        return False
    except Exception as e:
        print(f"[FAILED] 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = validate_phase3_integration()
    
    if success:
        print("\n[ROCKET] Phase 3 权重系统已成功集成到主彩票预测系统!")
        print("   可以开始使用 python main.py 运行完整的预测系统")
    else:
        print("\n[FAILED] Phase 3 权重系统集成验证失败")
        print("   请检查错误信息并修复相关问题")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())