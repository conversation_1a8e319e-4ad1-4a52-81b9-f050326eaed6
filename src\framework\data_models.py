"""
回测框架数据模型
定义标准化的数据结构
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime


@dataclass
class BacktestConfig:
    """回测配置"""

    num_periods: int = 10  # 回测期数
    min_train_periods: int = 0  # 最少训练期数（统一为0，使用所有可用数据）
    display_periods: int = 10  # 显示期数（统一为10）
    metrics: List[str] = field(
        default_factory=lambda: [
            "red_odd_even_hit",
            "red_size_hit",
            "blue_size_hit",
            "hit_2_plus_1",
            "red_kill_success",
            "blue_kill_success",
        ]
    )
    enable_detailed_output: bool = True  # 是否启用详细输出
    enable_statistics: bool = True  # 是否计算统计信息
    reverse_display: bool = True  # 是否倒序显示（最新期在前）

    def validate(self) -> bool:
        """验证配置参数"""
        if self.num_periods <= 0:
            return False
        if self.min_train_periods < 0:  # 修复：允许min_train_periods为0
            return False
        if self.display_periods < 0:
            return False
        return True


@dataclass
class PredictionResult:
    """标准化的预测结果"""

    period_number: str  # 期号（仅作为标志）
    data_index: int  # 数据索引

    # 比例预测
    red_odd_even_predictions: List[Tuple[str, float]]  # [(预测值, 概率), ...]
    red_size_predictions: List[Tuple[str, float]]
    blue_size_predictions: List[Tuple[str, float]]

    # 号码预测
    generated_numbers: Tuple[List[int], List[int]]  # (红球, 蓝球)

    # 杀号信息
    kill_numbers: Dict[str, Any]  # 杀号数据

    # 额外信息
    bayes_selected: Optional[List[Dict]] = None  # 贝叶斯选择
    all_combinations: Optional[List[Tuple[List[int], List[int]]]] = None  # 所有组合

    # 元数据
    predictor_name: str = ""  # 预测器名称
    prediction_time: datetime = field(default_factory=datetime.now)
    training_data_size: int = 0  # 训练数据大小


@dataclass
class EvaluationResult:
    """评估结果"""

    period_number: str  # 期号
    data_index: int  # 数据索引

    # 实际开奖数据
    actual_red: List[int]
    actual_blue: List[int]
    actual_red_odd_even: str
    actual_red_size: str
    actual_blue_size: str

    # 命中情况
    hits: Dict[str, bool] = field(default_factory=dict)  # 各项指标命中情况

    # 杀号成功情况
    kill_success: Dict[str, bool] = field(default_factory=dict)

    # 号码命中情况
    red_hits: int = 0  # 红球命中个数
    blue_hits: int = 0  # 蓝球命中个数
    total_hits: int = 0  # 总命中个数

    # 特殊指标
    hit_2_plus_1: bool = False  # 2+1命中

    # 评估时间
    evaluation_time: datetime = field(default_factory=datetime.now)


@dataclass
class PeriodResult:
    """单期回测结果"""

    prediction: PredictionResult
    evaluation: EvaluationResult
    success: bool = True
    error_message: str = ""


@dataclass
class Statistics:
    """统计信息"""

    total_periods: int = 0
    successful_periods: int = 0

    # 各项命中率
    hit_rates: Dict[str, float] = field(default_factory=dict)
    hit_counts: Dict[str, int] = field(default_factory=dict)

    # 杀号统计
    kill_success_rates: Dict[str, float] = field(default_factory=dict)
    kill_success_counts: Dict[str, int] = field(default_factory=dict)

    # 特殊统计
    hit_2_plus_1_rate: float = 0.0
    hit_2_plus_1_count: int = 0

    # 号码命中统计
    avg_red_hits: float = 0.0
    avg_blue_hits: float = 0.0
    avg_total_hits: float = 0.0

    @property
    def red_odd_even_rate(self) -> float:
        """红球奇偶比命中率"""
        return self.hit_rates.get("red_odd_even_hit", 0.0)

    @property
    def red_size_rate(self) -> float:
        """红球大小比命中率"""
        return self.hit_rates.get("red_size_hit", 0.0)

    @property
    def blue_size_rate(self) -> float:
        """蓝球大小比命中率"""
        return self.hit_rates.get("blue_size_hit", 0.0)

    @property
    def red_kill_success_rate(self) -> float:
        """红球杀号成功率"""
        return self.kill_success_rates.get("red_kill_success", 0.0)

    @property
    def blue_kill_success_rate(self) -> float:
        """蓝球杀号成功率"""
        return self.kill_success_rates.get("blue_kill_success", 0.0)

    # 计算时间
    calculation_time: datetime = field(default_factory=datetime.now)


@dataclass
class BacktestResult:
    """完整的回测结果"""

    config: BacktestConfig
    period_results: List[PeriodResult]
    statistics: Statistics

    # 元数据
    predictor_name: str = ""
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    total_duration: Optional[float] = None  # 秒

    # 数据信息
    data_source: str = ""
    total_data_periods: int = 0
    backtest_data_range: str = ""

    def get_success_rate(self) -> float:
        """获取总体成功率"""
        if not self.period_results:
            return 0.0
        successful = sum(1 for r in self.period_results if r.success)
        return successful / len(self.period_results)

    def get_display_results(self) -> List[PeriodResult]:
        """获取用于显示的结果（根据配置）"""
        results = self.period_results
        if self.config.display_periods > 0:
            results = results[-self.config.display_periods :]
        if self.config.reverse_display:
            results = results[::-1]
        return results
