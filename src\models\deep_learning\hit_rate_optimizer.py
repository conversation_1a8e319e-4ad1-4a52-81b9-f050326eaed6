#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习命中率优化器

专门针对彩票预测命中率优化的深度学习系统，解决当前系统的核心问题：
1. 2+1命中率为0%的问题
2. 比例预测准确率低的问题
3. 号码生成策略失效的问题

核心特性：
- 多任务学习：同时优化比例预测和号码生成
- 注意力机制：关注对命中率最重要的历史模式
- 自定义损失函数：直接优化2+1命中率
- 强化学习：基于实际命中结果进行在线学习
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, models, optimizers, losses
    from sklearn.preprocessing import StandardScaler, LabelEncoder

    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.multioutput import MultiOutputClassifier, MultiOutputRegressor

from ...core.interfaces import (
    IStandardPredictor,
    PredictionResult,
    PredictionType,
    BallType,
)
from ...core.base import StandardBasePredictor


@dataclass
class HitRateOptimizerConfig:
    """命中率优化器配置（V3.2 - 增强温度采样版本）"""

    # 网络架构（V3.1优化版本）
    sequence_length: int = 25  # 输入序列长度（V3.1增加到25期）
    embedding_dim: int = 128  # 嵌入维度（V3.1增强）
    lstm_units: int = 256  # LSTM单元数（V3.1增强）
    attention_heads: int = 12  # 注意力头数（V3.1增强）
    dense_units: int = 512  # 全连接层单元数（V3.1增强）
    dropout_rate: float = 0.25  # Dropout率（V3.1优化）

    # 比例预测专用子网络架构（V3.1优化）
    ratio_subnet_depth: int = 4  # 比例预测子网络深度（V3.1增加到4层）
    ratio_subnet_units: int = 512  # 比例预测子网络单元数（V3.1增强）
    ratio_attention_heads: int = 8  # 比例预测专用注意力头数（V3.1增强）
    ratio_dropout_rate: float = 0.15  # 比例预测专用Dropout率（V3.1优化）

    # 不确定性建模参数（V3.1优化）
    use_variational_layers: bool = True  # 使用变分层
    mc_dropout_samples: int = 15  # Monte Carlo Dropout采样次数（V3.1增加）
    uncertainty_weight: float = 0.15  # 不确定性损失权重（V3.1增强）

    # 温度采样参数（V3.2增强版本）
    temperature_min: float = 0.5  # 最小温度参数（V3.2降低）
    temperature_max: float = 3.0  # 最大温度参数（V3.2提高）
    adaptive_temperature: bool = True  # 自适应温度调整

    # V3.2新增：非线性温度映射
    temperature_mapping: str = (
        "sigmoid"  # 温度映射方式: "linear", "sigmoid", "exponential"
    )
    temperature_sensitivity: float = 2.0  # 温度敏感度参数

    # V3.2新增：多源噪声参数
    gaussian_noise_scale: float = 0.15  # 高斯噪声强度（V3.2增强）
    uniform_noise_scale: float = 0.08  # 均匀噪声强度
    periodic_noise_scale: float = 0.05  # 周期性噪声强度

    # V3.2新增：采样策略参数
    sampling_strategy: str = "nucleus"  # 采样策略: "standard", "top_k", "nucleus"
    top_k: int = 3  # Top-k采样参数
    nucleus_p: float = 0.8  # 核采样参数

    # V3.2新增：历史避免机制
    enable_history_avoidance: bool = True
    history_window: int = 5  # 历史窗口大小
    avoidance_strength: float = 0.2  # 避免强度

    # 多任务权重（V3.1版本 - 重新平衡）
    ratio_prediction_weight: float = 5.0  # 比例预测权重（V3.1提升到5.0）
    number_generation_weight: float = 0.4  # 号码生成权重（V3.1微调）
    hit_rate_weight: float = 0.3  # 命中率权重（V3.1增强）

    # 训练参数
    learning_rate: float = 0.001
    batch_size: int = 32
    epochs: int = 200
    validation_split: float = 0.2
    early_stopping_patience: int = 20

    # 强化学习参数
    rl_learning_rate: float = 0.0001
    reward_decay: float = 0.95
    exploration_rate: float = 0.1

    # 特征工程
    use_traditional_features: bool = True  # 使用传统算法特征
    use_kill_features: bool = True  # 使用杀号特征
    use_pattern_features: bool = True  # 使用模式特征

    # 优化目标
    target_hit_rate: float = 0.25  # 目标命中率
    target_ratio_accuracy: float = 0.6  # 目标比例准确率


class TaskType(Enum):
    """任务类型"""

    RED_ODD_EVEN_RATIO = "red_odd_even_ratio"
    RED_SIZE_RATIO = "red_size_ratio"
    BLUE_SIZE_RATIO = "blue_size_ratio"
    NUMBER_GENERATION = "number_generation"
    HIT_RATE_PREDICTION = "hit_rate_prediction"


class HitRateOptimizer(StandardBasePredictor):
    """深度学习命中率优化器"""

    def __init__(self, config: Optional[HitRateOptimizerConfig] = None):
        super().__init__("深度学习命中率优化器", PredictionType.DEEP_LEARNING)
        self.config = config or HitRateOptimizerConfig()

        # 模型组件
        self.model = None
        self.feature_extractor = None
        self.scalers = {}
        self.encoders = {}

        # 训练状态
        self.is_trained_flag = False
        self.training_history = {}

        # V3.2新增：历史预测记录（用于避免重复）
        self.prediction_history = {
            "red_odd_even_ratio": [],
            "red_size_ratio": [],
            "blue_size_ratio": [],
        }
        self.performance_metrics = {}

        # 强化学习组件
        self.rl_agent = None
        self.experience_buffer = []
        self.reward_history = []

        # 特征列
        self.feature_columns = []
        self.target_columns = []

        # 传统算法集成
        self.traditional_predictors = {}

        if not TENSORFLOW_AVAILABLE:
            self.logger.warning("TensorFlow未安装，将使用传统机器学习方法")
            self._initialize_fallback_models()

    def _initialize_fallback_models(self):
        """初始化备用模型"""
        self.fallback_models = {
            TaskType.RED_ODD_EVEN_RATIO: RandomForestClassifier(
                n_estimators=100, random_state=42
            ),
            TaskType.RED_SIZE_RATIO: RandomForestClassifier(
                n_estimators=100, random_state=42
            ),
            TaskType.BLUE_SIZE_RATIO: RandomForestClassifier(
                n_estimators=100, random_state=42
            ),
            TaskType.NUMBER_GENERATION: MultiOutputRegressor(
                GradientBoostingRegressor(n_estimators=100, random_state=42)
            ),
            TaskType.HIT_RATE_PREDICTION: GradientBoostingRegressor(
                n_estimators=100, random_state=42
            ),
        }

    def _extract_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """提取深度学习特征"""
        features = []

        # 检测列名格式（中文或英文）
        if "红球1" in data.columns:
            # 中文列名
            red_cols = [f"红球{i}" for i in range(1, 6)]
            blue_cols = [f"蓝球{i}" for i in range(1, 3)]
        else:
            # 英文列名
            red_cols = [f"red_{i}" for i in range(1, 6)]
            blue_cols = [f"blue_{i}" for i in range(1, 3)]

        # 基础数字特征
        for col in red_cols:
            features.append(data[col])
        for col in blue_cols:
            features.append(data[col])

        # 比例特征
        features.append(
            data["red_odd_count"]
            if "red_odd_count" in data.columns
            else data[red_cols].apply(
                lambda row: sum(1 for val in row if val % 2 == 1), axis=1
            )
        )
        features.append(
            data["red_large_count"]
            if "red_large_count" in data.columns
            else data[red_cols].apply(
                lambda row: sum(1 for val in row if val > 17), axis=1
            )
        )
        features.append(
            data["blue_large_count"]
            if "blue_large_count" in data.columns
            else data[blue_cols].apply(
                lambda row: sum(1 for val in row if val > 6), axis=1
            )
        )

        # 增强统计特征
        red_balls = data[red_cols]
        features.append(red_balls.sum(axis=1))  # 红球和值
        features.append(red_balls.max(axis=1) - red_balls.min(axis=1))  # 红球跨度
        features.append(red_balls.std(axis=1))  # 红球标准差
        features.append(red_balls.mean(axis=1))  # 红球平均值
        features.append(red_balls.median(axis=1))  # 红球中位数
        features.append(red_balls.skew(axis=1))  # 红球偏度
        features.append(red_balls.kurtosis(axis=1))  # 红球峰度

        blue_balls = data[blue_cols]
        features.append(blue_balls.sum(axis=1))  # 蓝球和值
        features.append(blue_balls.max(axis=1) - blue_balls.min(axis=1))  # 蓝球跨度
        features.append(blue_balls.std(axis=1))  # 蓝球标准差
        features.append(blue_balls.mean(axis=1))  # 蓝球平均值

        # 滑动窗口特征（如果数据足够）
        if len(data) >= 10:
            # 最近10期红球平均值的趋势
            red_rolling_mean = red_balls.rolling(window=10, min_periods=1).mean()
            features.append(red_rolling_mean.mean(axis=1))

            # 最近10期蓝球平均值的趋势
            blue_rolling_mean = blue_balls.rolling(window=10, min_periods=1).mean()
            features.append(blue_rolling_mean.mean(axis=1))

        # 模式特征
        if self.config.use_pattern_features:
            # 连号特征
            features.append(self._calculate_consecutive_count(red_balls))
            # 重复特征（与前期的重复数量）
            features.append(self._calculate_repeat_count(data, red_balls))

        # 组合特征矩阵
        feature_matrix = pd.concat(features, axis=1)
        feature_matrix.columns = [f"feature_{i}" for i in range(len(features))]

        return feature_matrix

    def _calculate_consecutive_count(self, balls: pd.DataFrame) -> pd.Series:
        """计算连号数量"""

        def count_consecutive(row):
            sorted_balls = sorted(row.values)
            consecutive_count = 0
            for i in range(len(sorted_balls) - 1):
                if sorted_balls[i + 1] - sorted_balls[i] == 1:
                    consecutive_count += 1
            return consecutive_count

        return balls.apply(count_consecutive, axis=1)

    def _calculate_repeat_count(
        self, data: pd.DataFrame, current_balls: pd.DataFrame
    ) -> pd.Series:
        """计算与前期的重复数量"""
        repeat_counts = []

        for idx in range(len(data)):
            if idx == 0:
                repeat_counts.append(0)
                continue

            current = set(current_balls.iloc[idx].values)
            previous = set(current_balls.iloc[idx - 1].values)
            repeat_count = len(current.intersection(previous))
            repeat_counts.append(repeat_count)

        return pd.Series(repeat_counts, index=data.index)

    def _build_multi_task_model(self, input_shape: Tuple[int, int]) -> keras.Model:
        """构建多任务深度学习模型（V3.0 - 专门比例预测子网络版本）"""
        if not TENSORFLOW_AVAILABLE:
            return None

        # 输入层
        inputs = layers.Input(shape=input_shape, name="sequence_input")

        # 嵌入层（可选，用于离散特征）
        x = layers.Dense(self.config.embedding_dim, activation="relu")(inputs)
        x = layers.Dropout(self.config.dropout_rate)(x)

        # LSTM层
        lstm_out = layers.LSTM(
            self.config.lstm_units,
            return_sequences=True,
            dropout=self.config.dropout_rate,
            recurrent_dropout=self.config.dropout_rate,
        )(x)

        # 注意力机制
        attention = layers.MultiHeadAttention(
            num_heads=self.config.attention_heads,
            key_dim=self.config.lstm_units // self.config.attention_heads,
        )(lstm_out, lstm_out)

        # 残差连接
        attention_out = layers.Add()([lstm_out, attention])
        attention_out = layers.LayerNormalization()(attention_out)

        # 全局特征提取
        global_features = layers.GlobalAveragePooling1D()(attention_out)
        global_features = layers.Dense(self.config.dense_units, activation="relu")(
            global_features
        )
        global_features = layers.Dropout(self.config.dropout_rate)(global_features)

        # 多任务输出分支
        outputs = {}

        # ===== V3.0 专门比例预测子网络 =====
        ratio_subnet = self._build_ratio_prediction_subnet(
            global_features, attention_out
        )
        outputs.update(ratio_subnet)

        # 号码生成任务
        number_branch = layers.Dense(256, activation="relu", name="number_branch")(
            global_features
        )
        number_branch = layers.Dropout(self.config.dropout_rate)(number_branch)

        # 红球生成概率（35个数字）
        outputs["red_ball_probs"] = layers.Dense(
            35, activation="sigmoid", name="red_ball_probs"
        )(number_branch)
        # 蓝球生成概率（12个数字）
        outputs["blue_ball_probs"] = layers.Dense(
            12, activation="sigmoid", name="blue_ball_probs"
        )(number_branch)

        # 命中率预测任务
        hit_rate_branch = layers.Dense(64, activation="relu", name="hit_rate_branch")(
            global_features
        )
        hit_rate_branch = layers.Dropout(self.config.dropout_rate)(hit_rate_branch)
        outputs["hit_rate"] = layers.Dense(1, activation="sigmoid", name="hit_rate")(
            hit_rate_branch
        )

        # 创建模型
        model = keras.Model(inputs=inputs, outputs=outputs, name="HitRateOptimizer_V3")

        return model

    def _build_ratio_prediction_subnet(self, global_features, sequence_features):
        """构建专门的比例预测子网络（V3.0核心创新）"""
        if not TENSORFLOW_AVAILABLE:
            return {}

        # 比例预测专用特征提取
        ratio_features = layers.Dense(
            self.config.ratio_subnet_units,
            activation="relu",
            name="ratio_feature_extractor",
        )(global_features)

        # 比例预测专用注意力机制
        ratio_attention = layers.MultiHeadAttention(
            num_heads=self.config.ratio_attention_heads,
            key_dim=self.config.ratio_subnet_units // self.config.ratio_attention_heads,
            name="ratio_attention",
        )(sequence_features, sequence_features)

        # 融合全局特征和序列注意力特征
        ratio_attention_pooled = layers.GlobalAveragePooling1D()(ratio_attention)
        ratio_combined = layers.Concatenate(name="ratio_feature_fusion")(
            [ratio_features, ratio_attention_pooled]
        )

        # 比例预测深度子网络（修复版本）
        ratio_subnet = ratio_combined
        previous_layer = ratio_combined  # 保存上一层用于残差连接

        for i in range(self.config.ratio_subnet_depth):
            current_units = max(
                64, self.config.ratio_subnet_units // (i + 1)
            )  # 确保最小64单元

            ratio_subnet = layers.Dense(
                current_units, activation="relu", name=f"ratio_subnet_layer_{i+1}"
            )(ratio_subnet)

            # 添加不确定性建模
            if self.config.use_variational_layers:
                ratio_subnet = self._add_variational_layer(
                    ratio_subnet, f"ratio_var_{i+1}"
                )

            ratio_subnet = layers.Dropout(
                self.config.ratio_dropout_rate, name=f"ratio_dropout_{i+1}"
            )(ratio_subnet)

            # 简化残差连接逻辑，避免维度不匹配问题
            if i == 0:  # 第一层后保存用于后续残差连接
                first_layer_output = ratio_subnet
            elif (
                i == self.config.ratio_subnet_depth - 1
                and current_units == first_layer_output.shape[-1]
            ):
                # 只在最后一层且维度匹配时添加残差连接
                ratio_subnet = layers.Add(name=f"ratio_residual_{i+1}")(
                    [ratio_subnet, first_layer_output]
                )

        # 独立的比例预测输出头
        outputs = {}

        # 红球奇偶比预测（专用分支）
        red_odd_even_branch = layers.Dense(
            64, activation="relu", name="red_odd_even_branch"
        )(ratio_subnet)
        red_odd_even_branch = layers.Dropout(self.config.ratio_dropout_rate)(
            red_odd_even_branch
        )
        outputs["red_odd_even_ratio"] = layers.Dense(
            6, activation="softmax", name="red_odd_even_ratio"
        )(red_odd_even_branch)

        # 红球大小比预测（专用分支）
        red_size_branch = layers.Dense(64, activation="relu", name="red_size_branch")(
            ratio_subnet
        )
        red_size_branch = layers.Dropout(self.config.ratio_dropout_rate)(
            red_size_branch
        )
        outputs["red_size_ratio"] = layers.Dense(
            6, activation="softmax", name="red_size_ratio"
        )(red_size_branch)

        # 蓝球大小比预测（专用分支）
        blue_size_branch = layers.Dense(32, activation="relu", name="blue_size_branch")(
            ratio_subnet
        )
        blue_size_branch = layers.Dropout(self.config.ratio_dropout_rate)(
            blue_size_branch
        )
        outputs["blue_size_ratio"] = layers.Dense(
            3, activation="softmax", name="blue_size_ratio"
        )(blue_size_branch)

        return outputs

    def _add_variational_layer(self, x, name_prefix):
        """添加变分层以增加不确定性建模"""
        if not TENSORFLOW_AVAILABLE:
            return x

        # 简化的变分实现：使用多个dropout层模拟不确定性
        variational_outputs = []
        for i in range(3):  # 创建3个不同的dropout版本
            var_x = layers.Dropout(
                self.config.ratio_dropout_rate * (1 + i * 0.1),
                name=f"{name_prefix}_var_dropout_{i}",
            )(
                x, training=True
            )  # 强制在推理时也使用dropout
            variational_outputs.append(var_x)

        # 平均多个变分输出
        if len(variational_outputs) > 1:
            x = layers.Average(name=f"{name_prefix}_var_average")(variational_outputs)

        return x

    def _compile_model(self, model: keras.Model):
        """编译模型（V3.0 - 大幅增强比例预测权重）"""
        if not TENSORFLOW_AVAILABLE:
            return

        # 定义损失函数
        losses = {
            "red_odd_even_ratio": "categorical_crossentropy",
            "red_size_ratio": "categorical_crossentropy",
            "blue_size_ratio": "categorical_crossentropy",
            "red_ball_probs": "binary_crossentropy",
            "blue_ball_probs": "binary_crossentropy",
            "hit_rate": "binary_crossentropy",
        }

        # V3.0 大幅增强的损失权重配置
        loss_weights = {
            # 比例预测权重大幅提升到4.0，并重新分配
            "red_odd_even_ratio": self.config.ratio_prediction_weight
            * 0.4,  # 4.0 * 0.4 = 1.6
            "red_size_ratio": self.config.ratio_prediction_weight
            * 0.4,  # 4.0 * 0.4 = 1.6
            "blue_size_ratio": self.config.ratio_prediction_weight
            * 0.2,  # 4.0 * 0.2 = 0.8
            "red_ball_probs": self.config.number_generation_weight
            * 0.7,  # 0.3 * 0.7 = 0.21
            "blue_ball_probs": self.config.number_generation_weight
            * 0.3,  # 0.3 * 0.3 = 0.09
            "hit_rate": self.config.hit_rate_weight,  # 0.2
        }

        # 注释掉不确定性损失（Functional模型不支持add_loss）
        # 将在自定义训练循环中处理不确定性损失
        # if self.config.use_variational_layers:
        #     # 为变分层添加KL散度损失
        #     for layer in model.layers:
        #         if 'var_' in layer.name:
        #             model.add_loss(self.config.uncertainty_weight * tf.reduce_mean(layer.losses))

        # 定义评估指标
        metrics = {
            "red_odd_even_ratio": ["accuracy"],
            "red_size_ratio": ["accuracy"],
            "blue_size_ratio": ["accuracy"],
            "red_ball_probs": ["binary_accuracy"],
            "blue_ball_probs": ["binary_accuracy"],
            "hit_rate": ["binary_accuracy", "mae"],
        }

        # 编译模型
        model.compile(
            optimizer=optimizers.Adam(learning_rate=self.config.learning_rate),
            loss=losses,
            loss_weights=loss_weights,
            metrics=metrics,
        )

        # 输出权重配置信息
        self.logger.info("V3.0 多任务模型编译完成")
        self.logger.info(
            f"比例预测权重配置: 红球奇偶={loss_weights['red_odd_even_ratio']:.2f}, "
            f"红球大小={loss_weights['red_size_ratio']:.2f}, "
            f"蓝球大小={loss_weights['blue_size_ratio']:.2f}"
        )
        self.logger.info(
            f"总比例预测权重: {sum([loss_weights['red_odd_even_ratio'], loss_weights['red_size_ratio'], loss_weights['blue_size_ratio']]):.2f}"
        )
        self.logger.info(
            f"号码生成权重: {sum([loss_weights['red_ball_probs'], loss_weights['blue_ball_probs']]):.2f}"
        )
        self.logger.info(f"命中率权重: {loss_weights['hit_rate']:.2f}")

    def is_trained(self) -> bool:
        """检查模型是否已训练"""
        return self.is_trained_flag

    def _prepare_training_data(
        self, data: pd.DataFrame
    ) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """准备训练数据"""
        # 提取特征
        features = self._extract_features(data)

        # 创建序列数据
        sequences = []
        targets = {task.value: [] for task in TaskType}

        seq_len = self.config.sequence_length

        for i in range(seq_len, len(data)):
            # 输入序列
            seq_features = features.iloc[i - seq_len : i].values
            sequences.append(seq_features)

            # 目标值
            current_row = data.iloc[i]

            # 检测列名格式
            if "红球1" in data.columns:
                red_cols = [f"红球{j}" for j in range(1, 6)]
                blue_cols = [f"蓝球{j}" for j in range(1, 3)]
            else:
                red_cols = [f"red_{j}" for j in range(1, 6)]
                blue_cols = [f"blue_{j}" for j in range(1, 3)]

            # 比例目标
            red_odd_count = sum(1 for col in red_cols if current_row[col] % 2 == 1)
            red_large_count = sum(1 for col in red_cols if current_row[col] > 17)
            blue_large_count = sum(1 for col in blue_cols if current_row[col] > 6)

            # 转换为one-hot编码
            red_odd_even_onehot = np.zeros(6)
            red_odd_even_onehot[red_odd_count] = 1
            targets[TaskType.RED_ODD_EVEN_RATIO.value].append(red_odd_even_onehot)

            red_size_onehot = np.zeros(6)
            red_size_onehot[red_large_count] = 1
            targets[TaskType.RED_SIZE_RATIO.value].append(red_size_onehot)

            blue_size_onehot = np.zeros(3)
            blue_size_onehot[blue_large_count] = 1
            targets[TaskType.BLUE_SIZE_RATIO.value].append(blue_size_onehot)

            # 号码生成目标
            red_ball_target = np.zeros(35)
            blue_ball_target = np.zeros(12)

            for col in red_cols:
                red_ball_target[current_row[col] - 1] = 1
            for col in blue_cols:
                blue_ball_target[current_row[col] - 1] = 1

            # 使用正确的键名
            if "red_ball_probs" not in targets:
                targets["red_ball_probs"] = []
            if "blue_ball_probs" not in targets:
                targets["blue_ball_probs"] = []

            targets["red_ball_probs"].append(red_ball_target)
            targets["blue_ball_probs"].append(blue_ball_target)

            # 命中率目标（简化版，实际需要更复杂的计算）
            hit_rate_target = 1.0 if i > seq_len else 0.0  # 占位符
            targets[TaskType.HIT_RATE_PREDICTION.value].append([hit_rate_target])

        X = np.array(sequences)
        y = {
            "red_odd_even_ratio": np.array(targets[TaskType.RED_ODD_EVEN_RATIO.value]),
            "red_size_ratio": np.array(targets[TaskType.RED_SIZE_RATIO.value]),
            "blue_size_ratio": np.array(targets[TaskType.BLUE_SIZE_RATIO.value]),
            "red_ball_probs": np.array(targets["red_ball_probs"]),
            "blue_ball_probs": np.array(targets["blue_ball_probs"]),
            "hit_rate": np.array(targets[TaskType.HIT_RATE_PREDICTION.value]),
        }

        return X, y

    def train(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """训练深度学习模型"""
        try:
            self.logger.info("开始训练深度学习命中率优化器...")

            # 准备训练数据
            X, y = self._prepare_training_data(data)

            if len(X) == 0:
                raise ValueError("训练数据不足，无法创建序列")

            self.logger.info(f"训练数据形状: X={X.shape}")
            for task, target in y.items():
                self.logger.info(f"  {task}: {target.shape}")

            if not TENSORFLOW_AVAILABLE:
                # 使用传统机器学习方法
                return self._train_fallback_models(X, y)

            # 构建和编译模型
            input_shape = (X.shape[1], X.shape[2])
            self.model = self._build_multi_task_model(input_shape)
            self._compile_model(self.model)

            # 设置回调函数
            callbacks = [
                keras.callbacks.EarlyStopping(
                    monitor="val_loss",
                    patience=self.config.early_stopping_patience,
                    restore_best_weights=True,
                ),
                keras.callbacks.ReduceLROnPlateau(
                    monitor="val_loss", factor=0.5, patience=5, min_lr=1e-6
                ),
            ]

            # 训练模型
            history = self.model.fit(
                X,
                y,
                batch_size=self.config.batch_size,
                epochs=self.config.epochs,
                validation_split=self.config.validation_split,
                callbacks=callbacks,
                verbose=1,
            )

            self.training_history = history.history
            self.is_trained_flag = True

            # 计算性能指标
            self._evaluate_model_performance(X, y)

            self.logger.info("深度学习模型训练完成")

            return {
                "success": True,
                "method": "deep_learning",
                "samples": len(X),
                "epochs_trained": len(history.history["loss"]),
                "final_loss": history.history["loss"][-1],
                "performance_metrics": self.performance_metrics,
            }

        except Exception as e:
            self.logger.error(f"深度学习训练失败: {e}")
            return {"success": False, "error": str(e)}

    def _train_fallback_models(
        self, X: np.ndarray, y: Dict[str, np.ndarray]
    ) -> Dict[str, Any]:
        """训练备用模型"""
        self.logger.info("使用传统机器学习方法训练...")

        # 展平序列数据
        X_flat = X.reshape(X.shape[0], -1)

        # 训练各个任务的模型
        for task_type, model in self.fallback_models.items():
            if task_type.value in y:
                target_data = y[task_type.value]
                if target_data.ndim > 1 and target_data.shape[1] > 1:
                    # 多输出任务
                    if hasattr(model, "fit"):
                        model.fit(X_flat, target_data)
                else:
                    # 单输出任务
                    model.fit(X_flat, target_data.ravel())

        self.is_trained_flag = True

        return {
            "success": True,
            "method": "fallback",
            "samples": len(X),
            "models_trained": len(self.fallback_models),
        }

    def _evaluate_model_performance(self, X: np.ndarray, y: Dict[str, np.ndarray]):
        """评估模型性能"""
        if not TENSORFLOW_AVAILABLE or self.model is None:
            return

        # 预测
        predictions = self.model.predict(X, verbose=0)

        # 计算各任务的准确率
        self.performance_metrics = {}

        for task_name, pred in predictions.items():
            if task_name in y:
                true_values = y[task_name]

                if "ratio" in task_name:
                    # 分类任务
                    pred_classes = np.argmax(pred, axis=1)
                    true_classes = np.argmax(true_values, axis=1)
                    accuracy = np.mean(pred_classes == true_classes)
                    self.performance_metrics[f"{task_name}_accuracy"] = accuracy

                elif "probs" in task_name:
                    # 二分类任务
                    pred_binary = (pred > 0.5).astype(int)
                    accuracy = np.mean(pred_binary == true_values)
                    self.performance_metrics[f"{task_name}_accuracy"] = accuracy

                elif task_name == "hit_rate":
                    # 回归任务
                    mae = np.mean(np.abs(pred.ravel() - true_values.ravel()))
                    self.performance_metrics[f"{task_name}_mae"] = mae

        self.logger.info(f"模型性能评估完成: {self.performance_metrics}")

    def predict(
        self, data: pd.DataFrame, target_index: int, **kwargs
    ) -> PredictionResult:
        """执行预测（V3.0 - 支持Monte Carlo Dropout多样性预测）"""
        if not self.is_trained_flag:
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": "模型未训练", "predictions": {}},
            )

        try:
            # 准备预测数据
            seq_len = self.config.sequence_length
            if target_index < seq_len:
                return PredictionResult(
                    prediction_type=PredictionType.DEEP_LEARNING,
                    ball_type=BallType.RED,
                    value=None,
                    confidence=0.0,
                    metadata={
                        "success": False,
                        "error": f"目标索引{target_index}小于序列长度{seq_len}",
                        "predictions": {},
                    },
                )

            # 提取特征序列
            features = self._extract_features(data)
            recent_features = features.iloc[
                target_index - seq_len : target_index
            ].values
            X = recent_features.reshape(1, seq_len, -1)

            if not TENSORFLOW_AVAILABLE:
                # 使用备用模型预测
                return self._predict_with_fallback(X)

            # V3.0 使用Monte Carlo Dropout进行多样性预测
            if self.config.use_variational_layers:
                predictions = self._monte_carlo_predict(X)
            else:
                predictions = self.model.predict(X, verbose=0)

            # 处理预测结果
            result_dict = {}

            # V3.0 改进的比例预测处理（增加多样性）
            if isinstance(predictions["red_odd_even_ratio"], list):
                # Monte Carlo预测结果
                red_odd_even_pred = self._process_mc_ratio_prediction(
                    predictions["red_odd_even_ratio"]
                )
                red_size_pred = self._process_mc_ratio_prediction(
                    predictions["red_size_ratio"]
                )
                blue_size_pred = self._process_mc_ratio_prediction(
                    predictions["blue_size_ratio"]
                )
            else:
                # 标准预测结果
                red_odd_even_pred = np.argmax(predictions["red_odd_even_ratio"][0])
                red_size_pred = np.argmax(predictions["red_size_ratio"][0])
                blue_size_pred = np.argmax(predictions["blue_size_ratio"][0])

            result_dict["red_odd_even_ratio"] = (
                f"{red_odd_even_pred}:{5-red_odd_even_pred}"
            )
            result_dict["red_size_ratio"] = f"{red_size_pred}:{5-red_size_pred}"
            result_dict["blue_size_ratio"] = f"{blue_size_pred}:{2-blue_size_pred}"

            # 号码生成概率（支持Monte Carlo）
            if isinstance(predictions["red_ball_probs"], list):
                red_probs = np.mean(predictions["red_ball_probs"], axis=0)[0]
                blue_probs = np.mean(predictions["blue_ball_probs"], axis=0)[0]
            else:
                red_probs = predictions["red_ball_probs"][0]
                blue_probs = predictions["blue_ball_probs"][0]

            # 选择概率最高的号码
            red_candidates = np.argsort(red_probs)[-10:] + 1  # 选择前10个红球候选
            blue_candidates = np.argsort(blue_probs)[-5:] + 1  # 选择前5个蓝球候选

            result_dict["red_ball_candidates"] = red_candidates.tolist()
            result_dict["blue_ball_candidates"] = blue_candidates.tolist()

            # 命中率预测（支持Monte Carlo）
            if isinstance(predictions["hit_rate"], list):
                hit_rate_pred = float(np.mean(predictions["hit_rate"]))
                hit_rate_std = float(np.std(predictions["hit_rate"]))
                result_dict["hit_rate_uncertainty"] = hit_rate_std
            else:
                hit_rate_pred = float(predictions["hit_rate"][0][0])

            result_dict["predicted_hit_rate"] = hit_rate_pred

            # 计算综合置信度
            confidence = min(0.95, max(0.1, hit_rate_pred))

            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=result_dict,
                confidence=confidence,
                metadata={
                    "success": True,
                    "predictions": result_dict,
                    "model_type": "深度学习命中率优化器",
                    "sequence_length": seq_len,
                    "performance_metrics": self.performance_metrics,
                },
            )

        except Exception as e:
            self.logger.error(f"深度学习预测失败: {e}")
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": str(e), "predictions": {}},
            )

    def _predict_with_fallback(self, X: np.ndarray) -> PredictionResult:
        """使用备用模型预测"""
        X_flat = X.reshape(1, -1)
        result_dict = {}

        try:
            # 使用备用模型预测
            for task_type, model in self.fallback_models.items():
                if hasattr(model, "predict"):
                    pred = model.predict(X_flat)
                    result_dict[task_type.value] = (
                        pred.tolist() if hasattr(pred, "tolist") else pred
                    )

            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=result_dict,
                confidence=0.6,
                metadata={
                    "success": True,
                    "predictions": result_dict,
                    "model_type": "传统机器学习备用模型",
                    "method": "fallback",
                },
            )

        except Exception as e:
            return PredictionResult(
                prediction_type=PredictionType.DEEP_LEARNING,
                ball_type=BallType.RED,
                value=None,
                confidence=0.0,
                metadata={"success": False, "error": str(e), "method": "fallback"},
            )

    def _monte_carlo_predict(self, X: np.ndarray) -> Dict[str, List]:
        """Monte Carlo Dropout预测（V3.0核心创新 - 增加预测多样性）"""
        if not TENSORFLOW_AVAILABLE or self.model is None:
            return {}

        # 执行多次预测以获得不确定性估计
        mc_predictions = {
            "red_odd_even_ratio": [],
            "red_size_ratio": [],
            "blue_size_ratio": [],
            "red_ball_probs": [],
            "blue_ball_probs": [],
            "hit_rate": [],
        }

        for _ in range(self.config.mc_dropout_samples):
            # 在推理时启用dropout以增加随机性
            pred = self.model(X, training=True)  # training=True启用dropout

            for key in mc_predictions.keys():
                if key in pred:
                    mc_predictions[key].append(pred[key].numpy())

        return mc_predictions

    def _process_mc_ratio_prediction(
        self, mc_predictions: List, task_type: str = "ratio"
    ) -> int:
        """处理Monte Carlo比例预测结果（V3.2增强温度采样）"""
        # 计算每个类别的平均概率和不确定性
        avg_probs = np.mean(mc_predictions, axis=0)[0]
        uncertainty = np.std(mc_predictions, axis=0)[0]
        avg_uncertainty = np.mean(uncertainty)

        # V3.2增强温度计算
        temperature = self._calculate_adaptive_temperature(avg_uncertainty, task_type)

        # V3.2温度采样增加多样性
        scaled_probs = self._apply_temperature_scaling(avg_probs, temperature)

        # V3.2多源噪声注入
        scaled_probs = self._apply_multi_source_noise(scaled_probs, temperature)

        # V3.2历史避免机制
        if self.config.enable_history_avoidance:
            scaled_probs = self._apply_history_avoidance(scaled_probs, task_type)

        # V3.2增强采样策略
        predicted_class = self._enhanced_sampling(scaled_probs)

        # 更新历史记录
        self._update_prediction_history(predicted_class, task_type)

        return predicted_class

    def _calculate_adaptive_temperature(
        self, uncertainty: float, task_type: str
    ) -> float:
        """V3.2增强自适应温度计算"""
        if not self.config.adaptive_temperature:
            return (self.config.temperature_min + self.config.temperature_max) / 2

        # 基础温度映射
        if self.config.temperature_mapping == "sigmoid":
            # Sigmoid映射：更敏感的温度调整
            normalized_uncertainty = uncertainty * self.config.temperature_sensitivity
            sigmoid_value = 1 / (1 + np.exp(-normalized_uncertainty))
            temperature = (
                self.config.temperature_min
                + (self.config.temperature_max - self.config.temperature_min)
                * sigmoid_value
            )
        elif self.config.temperature_mapping == "exponential":
            # 指数映射：非线性增长
            exp_value = np.exp(uncertainty * self.config.temperature_sensitivity) - 1
            max_exp = np.exp(self.config.temperature_sensitivity) - 1
            normalized_exp = exp_value / max_exp
            temperature = (
                self.config.temperature_min
                + (self.config.temperature_max - self.config.temperature_min)
                * normalized_exp
            )
        else:
            # 线性映射（默认）
            temperature = (
                self.config.temperature_min
                + (self.config.temperature_max - self.config.temperature_min)
                * uncertainty
            )

        # 动态调整：基于历史多样性
        if hasattr(self, "prediction_history") and task_type in self.prediction_history:
            history = self.prediction_history[task_type]
            if len(history) >= 3:
                # 如果最近预测过于相似，提高温度
                recent_diversity = len(set(history[-3:])) / 3.0
                if recent_diversity < 0.7:  # 多样性不足
                    temperature *= 1.3  # 提高温度

        return np.clip(
            temperature, self.config.temperature_min, self.config.temperature_max
        )

    def _apply_temperature_scaling(
        self, probs: np.ndarray, temperature: float
    ) -> np.ndarray:
        """V3.2增强温度缩放"""
        # 使用更强的温度缩放公式
        scaled_probs = np.power(probs, 1.0 / temperature)
        scaled_probs = scaled_probs / np.sum(scaled_probs)
        return scaled_probs

    def _apply_multi_source_noise(
        self, probs: np.ndarray, temperature: float
    ) -> np.ndarray:
        """V3.2多源噪声注入"""
        enhanced_probs = probs.copy()

        # 1. 高斯噪声（与温度相关）
        gaussian_noise = np.random.normal(
            0, self.config.gaussian_noise_scale * temperature, len(probs)
        )
        enhanced_probs += gaussian_noise

        # 2. 均匀噪声（增加随机性）
        uniform_noise = np.random.uniform(
            -self.config.uniform_noise_scale,
            self.config.uniform_noise_scale,
            len(probs),
        )
        enhanced_probs += uniform_noise

        # 3. 周期性噪声（基于时间模式）
        time_factor = np.sin(np.arange(len(probs)) * 2 * np.pi / len(probs))
        periodic_noise = time_factor * self.config.periodic_noise_scale * temperature
        enhanced_probs += periodic_noise

        # 确保概率为正并归一化
        enhanced_probs = np.maximum(enhanced_probs, 0.001)
        enhanced_probs = enhanced_probs / np.sum(enhanced_probs)

        return enhanced_probs

    def _apply_history_avoidance(self, probs: np.ndarray, task_type: str) -> np.ndarray:
        """V3.2历史避免机制"""
        if task_type not in self.prediction_history:
            return probs

        history = self.prediction_history[task_type]
        if len(history) == 0:
            return probs

        # 获取最近的预测历史
        recent_history = history[-self.config.history_window :]

        # 计算每个类别的出现频率
        class_counts = np.zeros(len(probs))
        for pred in recent_history:
            if pred < len(class_counts):
                class_counts[pred] += 1

        # 降低频繁出现类别的概率
        if len(recent_history) > 0:
            frequency_penalty = (
                class_counts / len(recent_history) * self.config.avoidance_strength
            )
            adjusted_probs = probs * (1 - frequency_penalty)
            adjusted_probs = np.maximum(adjusted_probs, 0.001)
            adjusted_probs = adjusted_probs / np.sum(adjusted_probs)
            return adjusted_probs

        return probs

    def _enhanced_sampling(self, probs: np.ndarray) -> int:
        """V3.2增强采样策略"""
        if self.config.sampling_strategy == "top_k":
            # Top-k采样
            top_k_indices = np.argsort(probs)[-self.config.top_k :]
            top_k_probs = probs[top_k_indices]
            top_k_probs = top_k_probs / np.sum(top_k_probs)
            local_choice = np.random.choice(len(top_k_probs), p=top_k_probs)
            return top_k_indices[local_choice]

        elif self.config.sampling_strategy == "nucleus":
            # 核采样（Nucleus sampling）
            sorted_indices = np.argsort(probs)[::-1]
            sorted_probs = probs[sorted_indices]
            cumsum_probs = np.cumsum(sorted_probs)

            # 找到累积概率超过nucleus_p的位置
            nucleus_size = np.searchsorted(cumsum_probs, self.config.nucleus_p) + 1
            nucleus_size = min(nucleus_size, len(probs))

            # 从核心集合中采样
            nucleus_indices = sorted_indices[:nucleus_size]
            nucleus_probs = probs[nucleus_indices]
            nucleus_probs = nucleus_probs / np.sum(nucleus_probs)
            local_choice = np.random.choice(len(nucleus_probs), p=nucleus_probs)
            return nucleus_indices[local_choice]

        else:
            # 标准采样
            return np.random.choice(len(probs), p=probs)

    def _update_prediction_history(self, prediction: int, task_type: str):
        """更新预测历史记录"""
        if task_type not in self.prediction_history:
            self.prediction_history[task_type] = []

        self.prediction_history[task_type].append(prediction)

        # 保持历史窗口大小
        max_history = self.config.history_window * 2  # 保留更多历史用于分析
        if len(self.prediction_history[task_type]) > max_history:
            self.prediction_history[task_type] = self.prediction_history[task_type][
                -max_history:
            ]

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息（V3.2版本）"""
        return {
            "model_type": "深度学习命中率优化器 V3.2",
            "version": "3.2",
            "tensorflow_available": TENSORFLOW_AVAILABLE,
            "is_trained": self.is_trained_flag,
            "config": {
                "sequence_length": self.config.sequence_length,
                "lstm_units": self.config.lstm_units,
                "ratio_subnet_depth": self.config.ratio_subnet_depth,
                "ratio_subnet_units": self.config.ratio_subnet_units,
                "mc_dropout_samples": self.config.mc_dropout_samples,
                "temperature_range": f"{self.config.temperature_min}-{self.config.temperature_max}",
                "adaptive_temperature": self.config.adaptive_temperature,
                "temperature_mapping": self.config.temperature_mapping,
                "sampling_strategy": self.config.sampling_strategy,
                "history_avoidance": self.config.enable_history_avoidance,
                "ratio_prediction_weight": self.config.ratio_prediction_weight,
            },
            "performance_metrics": self.performance_metrics,
            "v3_1_innovations": [
                "自适应温度采样",
                "增强子网络架构（4层深度，512单元）",
                "优化损失权重分配（5.0）",
                "增强Monte Carlo采样（15次）",
                "噪声增强多样性预测",
            ],
        }
