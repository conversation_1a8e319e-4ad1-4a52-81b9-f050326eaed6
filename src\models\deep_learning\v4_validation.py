"""
V4.0 Transformer预测器验证脚本
验证V4.0实现的核心功能和接口兼容性
"""

import pandas as pd
import numpy as np
from typing import Dict, Any
import sys
import os

def validate_v4_config():
    """验证V4配置功能"""
    print("=== V4配置验证 ===")
    
    try:
        from v4_transformer_config import V4TransformerConfig, V4ConfigTemplates
        
        # 测试默认配置
        config = V4TransformerConfig()
        print(f"[OK] 默认配置创建成功")
        print(f"[OK] 配置验证: {config.validate()}")

        # 测试配置模板
        templates = ['fast', 'high_accuracy', 'lightweight']
        for template in templates:
            if template == 'fast':
                test_config = V4ConfigTemplates.get_fast_training_config()
            elif template == 'high_accuracy':
                test_config = V4ConfigTemplates.get_high_accuracy_config()
            else:
                test_config = V4ConfigTemplates.get_lightweight_config()

            print(f"[OK] {template}配置模板创建成功，验证: {test_config.validate()}")

        print("[OK] V4配置验证通过\n")
        return True
        
    except Exception as e:
        print(f"[ERROR] V4配置验证失败: {e}")
        return False


def validate_v4_predictor():
    """验证V4预测器功能"""
    print("=== V4预测器验证 ===")
    
    try:
        from v4_transformer_config import V4TransformerConfig
        from v4_transformer_predictor import V4TransformerPredictor
        
        # 创建预测器
        config = V4TransformerConfig()
        predictor = V4TransformerPredictor(config)
        
        print(f"[OK] 预测器创建成功: {predictor.name}")
        print(f"[OK] 预测器类型: {predictor.prediction_type}")
        print(f"[OK] 初始训练状态: {predictor.is_trained}")

        # 创建测试数据
        test_data = create_sample_data(50)
        print(f"[OK] 测试数据创建成功，形状: {test_data.shape}")

        # 测试预测功能
        result = predictor.predict(test_data, 30)
        print(f"[OK] 预测执行成功")
        print(f"[OK] 预测结果类型: {type(result)}")
        print(f"[OK] 预测成功标志: {result.metadata.get('success', False)}")
        print(f"[OK] 预测置信度: {result.confidence}")

        # 验证预测结果格式
        if result.value:
            required_keys = ['red_odd_even_ratio', 'red_size_ratio', 'blue_size_ratio',
                           'red_ball_candidates', 'blue_ball_candidates']
            for key in required_keys:
                if key in result.value:
                    print(f"[OK] 预测结果包含 {key}: {result.value[key]}")
                else:
                    print(f"[WARN] 预测结果缺少 {key}")

        print("[OK] V4预测器验证通过\n")
        return True
        
    except Exception as e:
        print(f"[ERROR] V4预测器验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def validate_tensorflow_integration():
    """验证TensorFlow集成"""
    print("=== TensorFlow集成验证 ===")
    
    try:
        import tensorflow as tf
        print(f"[OK] TensorFlow版本: {tf.__version__}")
        print(f"[OK] TensorFlow可用")
        
        # 测试基本TensorFlow功能
        x = tf.constant([1, 2, 3, 4])
        y = tf.constant([2, 3, 4, 5])
        z = tf.add(x, y)
        print(f"[OK] TensorFlow基本运算测试通过: {z.numpy()}")
        
        # 测试Keras功能
        from tensorflow import keras
        from tensorflow.keras import layers
        
        # 创建简单模型测试
        model = keras.Sequential([
            layers.Dense(10, activation='relu', input_shape=(5,)),
            layers.Dense(1, activation='sigmoid')
        ])
        print(f"[OK] Keras模型创建测试通过")
        
        # 测试模型编译
        model.compile(optimizer='adam', loss='binary_crossentropy')
        print(f"[OK] Keras模型编译测试通过")
        
        print("[OK] TensorFlow集成验证通过\n")
        return True
        
    except ImportError:
        print("[WARN] TensorFlow未安装，将使用备用方案")
        return True
    except Exception as e:
        print(f"[ERROR] TensorFlow集成验证失败: {e}")
        return False


def create_sample_data(num_periods: int = 100) -> pd.DataFrame:
    """创建示例彩票数据"""
    data = []
    
    for i in range(num_periods):
        period = f"240{i:02d}"
        
        # 生成随机红球（1-35，选5个）
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        
        # 生成随机蓝球（1-12，选2个）
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        
        row = {
            '期号': period,
            '红球1': red_balls[0],
            '红球2': red_balls[1],
            '红球3': red_balls[2],
            '红球4': red_balls[3],
            '红球5': red_balls[4],
            '蓝球1': blue_balls[0],
            '蓝球2': blue_balls[1]
        }
        
        data.append(row)
    
    return pd.DataFrame(data)


def validate_data_processing():
    """验证数据处理功能"""
    print("=== 数据处理验证 ===")
    
    try:
        # 创建测试数据
        test_data = create_sample_data(100)
        print(f"[OK] 测试数据创建成功，形状: {test_data.shape}")
        
        # 验证数据格式
        expected_columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        for col in expected_columns:
            if col in test_data.columns:
                print(f"[OK] 数据包含列: {col}")
            else:
                print(f"[ERROR] 数据缺少列: {col}")
                return False
        
        # 验证数据范围
        for i in range(1, 6):
            col = f'红球{i}'
            min_val, max_val = test_data[col].min(), test_data[col].max()
            if 1 <= min_val and max_val <= 35:
                print(f"[OK] {col}范围正确: {min_val}-{max_val}")
            else:
                print(f"[ERROR] {col}范围错误: {min_val}-{max_val}")
                return False
        
        for i in range(1, 3):
            col = f'蓝球{i}'
            min_val, max_val = test_data[col].min(), test_data[col].max()
            if 1 <= min_val and max_val <= 12:
                print(f"[OK] {col}范围正确: {min_val}-{max_val}")
            else:
                print(f"[ERROR] {col}范围错误: {min_val}-{max_val}")
                return False
        
        # 测试特征提取
        sample_row = test_data.iloc[0]
        red_balls = [sample_row[f'红球{i}'] for i in range(1, 6)]
        blue_balls = [sample_row[f'蓝球{i}'] for i in range(1, 3)]
        
        red_odd_count = sum(1 for x in red_balls if x % 2 == 1)
        red_large_count = sum(1 for x in red_balls if x > 17)
        blue_large_count = sum(1 for x in blue_balls if x > 6)
        
        print(f"[OK] 特征提取测试 - 红球奇数: {red_odd_count}, 红球大数: {red_large_count}, 蓝球大数: {blue_large_count}")
        
        print("[OK] 数据处理验证通过\n")
        return True
        
    except Exception as e:
        print(f"[ERROR] 数据处理验证失败: {e}")
        return False


def validate_prediction_format():
    """验证预测结果格式"""
    print("=== 预测格式验证 ===")
    
    try:
        # 模拟预测结果
        mock_result = {
            'red_odd_even_ratio': '2:3',
            'red_size_ratio': '3:2',
            'blue_size_ratio': '1:1',
            'red_ball_candidates': [1, 5, 12, 23, 35],
            'blue_ball_candidates': [3, 9],
            'predicted_hit_rate': 0.75
        }
        
        # 验证比例格式
        for ratio_key in ['red_odd_even_ratio', 'red_size_ratio']:
            ratio = mock_result[ratio_key]
            parts = ratio.split(':')
            if len(parts) == 2:
                left, right = int(parts[0]), int(parts[1])
                if left + right == 5 and left >= 0 and right >= 0:
                    print(f"[OK] {ratio_key}格式正确: {ratio}")
                else:
                    print(f"[ERROR] {ratio_key}数值错误: {ratio}")
                    return False
            else:
                print(f"[ERROR] {ratio_key}格式错误: {ratio}")
                return False
        
        # 验证蓝球比例
        blue_ratio = mock_result['blue_size_ratio']
        parts = blue_ratio.split(':')
        if len(parts) == 2:
            left, right = int(parts[0]), int(parts[1])
            if left + right == 2 and left >= 0 and right >= 0:
                print(f"[OK] blue_size_ratio格式正确: {blue_ratio}")
            else:
                print(f"[ERROR] blue_size_ratio数值错误: {blue_ratio}")
                return False
        else:
            print(f"[ERROR] blue_size_ratio格式错误: {blue_ratio}")
            return False
        
        # 验证号码候选
        red_candidates = mock_result['red_ball_candidates']
        if len(red_candidates) == 5 and all(1 <= x <= 35 for x in red_candidates):
            print(f"[OK] 红球候选格式正确: {red_candidates}")
        else:
            print(f"[ERROR] 红球候选格式错误: {red_candidates}")
            return False
        
        blue_candidates = mock_result['blue_ball_candidates']
        if len(blue_candidates) == 2 and all(1 <= x <= 12 for x in blue_candidates):
            print(f"[OK] 蓝球候选格式正确: {blue_candidates}")
        else:
            print(f"[ERROR] 蓝球候选格式错误: {blue_candidates}")
            return False
        
        # 验证命中率
        hit_rate = mock_result['predicted_hit_rate']
        if 0.0 <= hit_rate <= 1.0:
            print(f"[OK] 预测命中率格式正确: {hit_rate}")
        else:
            print(f"[ERROR] 预测命中率格式错误: {hit_rate}")
            return False
        
        print("[OK] 预测格式验证通过\n")
        return True
        
    except Exception as e:
        print(f"[ERROR] 预测格式验证失败: {e}")
        return False


def main():
    """主验证函数"""
    print("V4.0 Transformer预测器完整验证\n")
    
    validation_results = []
    
    # 执行各项验证
    validations = [
        ("TensorFlow集成", validate_tensorflow_integration),
        ("数据处理", validate_data_processing),
        ("预测格式", validate_prediction_format),
        ("V4配置", validate_v4_config),
        ("V4预测器", validate_v4_predictor)
    ]
    
    for name, validation_func in validations:
        print(f"[CHECK] 开始{name}验证...")
        try:
            result = validation_func()
            validation_results.append((name, result))
            if result:
                print(f"[OK] {name}验证通过")
            else:
                print(f"[ERROR] {name}验证失败")
        except Exception as e:
            print(f"[ERROR] {name}验证异常: {e}")
            validation_results.append((name, False))
        print("-" * 50)
    
    # 输出验证摘要
    print("\n[SUMMARY] 验证结果摘要:")
    passed = sum(1 for _, result in validation_results if result)
    total = len(validation_results)
    
    for name, result in validation_results:
        status = "[OK] 通过" if result else "[ERROR] 失败"
        print(f"  {name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项验证通过")
    success_rate = (passed / total) * 100
    print(f"成功率: {success_rate:.1f}%")
    
    if passed == total:
        print("\n[SUCCESS] 所有验证通过！V4.0 Transformer预测器准备就绪")
        return True
    else:
        print(f"\n[WARN] 部分验证失败，需要修复 {total - passed} 个问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)