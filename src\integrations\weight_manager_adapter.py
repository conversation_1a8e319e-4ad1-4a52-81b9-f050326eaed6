#!/usr/bin/env python3
"""
权重管理器适配器 - Phase 3: 权重系统重构
统一管理所有权重配置，实现智能动态调整
"""

import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path
from collections import defaultdict, deque
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.config.lottery_config import LotteryConfig


@dataclass
class WeightConfig:
    """权重配置类"""

    name: str
    category: str  # 'ensemble', 'model', 'feature', 'loss'
    base_weight: float
    min_weight: float = 0.01
    max_weight: float = 1.0
    update_frequency: int = 10  # 更新频率
    performance_window: int = 20  # 性能评估窗口
    learning_rate: float = 0.1  # 学习率


@dataclass
class PerformanceMetrics:
    """性能指标类"""

    accuracy: float = 0.0
    hit_rate_2_plus_1: float = 0.0  # 2+1命中率
    stability: float = 0.0
    confidence: float = 0.0
    trend: float = 0.0
    timestamp: float = field(default_factory=time.time)


class WeightPerformanceTracker:
    """权重性能跟踪器"""

    def __init__(self, window_size: int = 50):
        self.window_size = window_size
        self.performance_history: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=window_size)
        )
        self.weight_history: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=window_size)
        )
        self.correlation_cache: Dict[str, float] = {}

    def record_performance(
        self, weight_name: str, weight_value: float, metrics: PerformanceMetrics
    ):
        """记录权重性能"""
        self.weight_history[weight_name].append(weight_value)
        self.performance_history[weight_name].append(metrics)

        # 清除相关缓存
        if weight_name in self.correlation_cache:
            del self.correlation_cache[weight_name]

    def get_weight_performance_correlation(self, weight_name: str) -> float:
        """获取权重与性能的相关性"""
        if weight_name in self.correlation_cache:
            return self.correlation_cache[weight_name]

        if len(self.weight_history[weight_name]) < 5:
            return 0.0

        weights = list(self.weight_history[weight_name])
        performances = [
            m.hit_rate_2_plus_1 for m in self.performance_history[weight_name]
        ]

        if len(weights) != len(performances) or len(weights) < 2:
            return 0.0

        try:
            # 简单相关性计算
            n = len(weights)
            sum_w = sum(weights)
            sum_p = sum(performances)
            sum_wp = sum(w * p for w, p in zip(weights, performances))
            sum_w2 = sum(w * w for w in weights)
            sum_p2 = sum(p * p for p in performances)

            numerator = n * sum_wp - sum_w * sum_p
            denominator = (
                (n * sum_w2 - sum_w * sum_w) * (n * sum_p2 - sum_p * sum_p)
            ) ** 0.5

            if denominator == 0:
                return 0.0

            correlation = numerator / denominator
            correlation = (
                correlation if not (correlation != correlation) else 0.0
            )  # 检查NaN
            self.correlation_cache[weight_name] = correlation
            return correlation
        except:
            return 0.0

    def get_recent_performance(
        self, weight_name: str, window: int = 10
    ) -> PerformanceMetrics:
        """获取最近性能"""
        if weight_name not in self.performance_history:
            return PerformanceMetrics()

        recent_metrics = list(self.performance_history[weight_name])[-window:]
        if not recent_metrics:
            return PerformanceMetrics()

        # 计算平均性能
        avg_accuracy = sum(m.accuracy for m in recent_metrics) / len(recent_metrics)
        avg_hit_rate = sum(m.hit_rate_2_plus_1 for m in recent_metrics) / len(
            recent_metrics
        )
        avg_stability = sum(m.stability for m in recent_metrics) / len(recent_metrics)
        avg_confidence = sum(m.confidence for m in recent_metrics) / len(recent_metrics)

        # 计算趋势
        if len(recent_metrics) >= 3:
            recent_hit_rates = [m.hit_rate_2_plus_1 for m in recent_metrics[-3:]]
            trend = (recent_hit_rates[-1] - recent_hit_rates[0]) / len(recent_hit_rates)
        else:
            trend = 0.0

        return PerformanceMetrics(
            accuracy=avg_accuracy,
            hit_rate_2_plus_1=avg_hit_rate,
            stability=avg_stability,
            confidence=avg_confidence,
            trend=trend,
        )


class SmartWeightManager:
    """智能权重管理器"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "config/smart_weights.json"

        # 权重配置
        self.weight_configs: Dict[str, WeightConfig] = {}
        self.current_weights: Dict[str, float] = {}

        # 性能跟踪
        self.performance_tracker = WeightPerformanceTracker()

        # 更新策略
        self.update_strategies = {
            "gradient_based": self._gradient_based_update,
            "correlation_based": self._correlation_based_update,
            "performance_based": self._performance_based_update,
            "adaptive_learning": self._adaptive_learning_update,
        }

        # 初始化默认权重配置
        self._init_default_weights()

        # 加载配置
        self._load_config()

    def _init_default_weights(self):
        """初始化默认权重配置"""
        # 集成学习权重（从LotteryConfig获取）
        ensemble_weights = [
            (name, "ensemble", weight)
            for name, weight in LotteryConfig.ENSEMBLE_WEIGHTS.items()
        ]

        # 模型权重（从LotteryConfig获取）
        model_weights = [
            (name, "model", weight)
            for name, weight in LotteryConfig.MODEL_WEIGHTS.items()
        ]

        # 特征权重（从LotteryConfig获取）
        feature_weights = [
            (name, "feature", weight)
            for name, weight in LotteryConfig.FEATURE_WEIGHTS.items()
        ]

        # 损失权重（从LotteryConfig获取）
        loss_weights = [
            (name, "loss", weight)
            for name, weight in LotteryConfig.LOSS_WEIGHTS.items()
        ]

        # 创建权重配置
        all_weights = ensemble_weights + model_weights + feature_weights + loss_weights

        for name, category, base_weight in all_weights:
            self.weight_configs[name] = WeightConfig(
                name=name,
                category=category,
                base_weight=base_weight,
                min_weight=0.01 if category != "loss" else 0.01,
                max_weight=1.0 if category != "loss" else 5.0,
                learning_rate=0.1 if category == "feature" else 0.05,
            )
            self.current_weights[name] = base_weight

    def _load_config(self):
        """加载权重配置"""
        try:
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    config_data = json.load(f)

                # 更新当前权重
                if "current_weights" in config_data:
                    self.current_weights.update(config_data["current_weights"])

                print(f"权重配置已从 {self.config_path} 加载")
        except Exception as e:
            print(f"加载权重配置失败: {e}")

    def save_config(self):
        """保存权重配置"""
        try:
            config_file = Path(self.config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)

            config_data = {
                "current_weights": self.current_weights,
                "last_updated": time.time(),
                "version": "1.0",
            }

            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            print(f"权重配置已保存到 {self.config_path}")
        except Exception as e:
            print(f"保存权重配置失败: {e}")

    def get_weight(self, weight_name: str) -> float:
        """获取权重值"""
        return self.current_weights.get(weight_name, 0.0)

    def get_weights_by_category(self, category: str) -> Dict[str, float]:
        """按类别获取权重"""
        result = {}
        for name, config in self.weight_configs.items():
            if config.category == category:
                result[name] = self.current_weights.get(name, config.base_weight)
        return result

    def set_weight(self, weight_name: str, value: float, save: bool = True):
        """设置权重值"""
        if weight_name in self.weight_configs:
            config = self.weight_configs[weight_name]
            # 限制权重范围
            value = max(config.min_weight, min(config.max_weight, value))
            self.current_weights[weight_name] = value

            if save:
                self.save_config()
        else:
            print(f"未知权重名称: {weight_name}")

    def update_performance(self, weight_name: str, metrics: PerformanceMetrics):
        """更新权重性能"""
        if weight_name in self.current_weights:
            weight_value = self.current_weights[weight_name]
            self.performance_tracker.record_performance(
                weight_name, weight_value, metrics
            )

    def optimize_weights(
        self,
        strategy: str = "adaptive_learning",
        target_metric: str = "hit_rate_2_plus_1",
    ):
        """优化权重"""
        if strategy not in self.update_strategies:
            print(f"未知优化策略: {strategy}")
            return

        try:
            updated_weights = self.update_strategies[strategy](target_metric)

            # 应用更新
            for weight_name, new_value in updated_weights.items():
                self.set_weight(weight_name, new_value, save=False)

            # 保存配置
            self.save_config()

            print(f"权重优化完成，使用策略: {strategy}")

        except Exception as e:
            print(f"权重优化失败: {e}")

    def _adaptive_learning_update(self, target_metric: str) -> Dict[str, float]:
        """自适应学习权重更新"""
        updated_weights = {}

        for weight_name, config in self.weight_configs.items():
            recent_perf = self.performance_tracker.get_recent_performance(weight_name)
            correlation = self.performance_tracker.get_weight_performance_correlation(
                weight_name
            )

            # 综合评分
            if target_metric == "hit_rate_2_plus_1":
                performance_score = recent_perf.hit_rate_2_plus_1
            else:
                performance_score = recent_perf.accuracy

            stability_score = recent_perf.stability
            trend_score = max(0, recent_perf.trend)  # 只考虑正趋势

            # 自适应学习率
            adaptive_lr = config.learning_rate * (1 + stability_score)

            # 计算调整量
            if performance_score > 0.5:  # 性能良好
                if correlation > 0:  # 正相关，增强
                    adjustment = adaptive_lr * (performance_score + trend_score)
                else:  # 负相关，减弱
                    adjustment = -adaptive_lr * (1 - performance_score)
            else:  # 性能不佳
                if correlation > 0:  # 正相关但性能差，减弱
                    adjustment = -adaptive_lr * (1 - performance_score)
                else:  # 负相关且性能差，增强
                    adjustment = adaptive_lr * (0.5 - performance_score)

            # 应用调整
            current_weight = self.current_weights[weight_name]
            new_weight = current_weight + adjustment
            new_weight = max(config.min_weight, min(config.max_weight, new_weight))
            updated_weights[weight_name] = new_weight

        return updated_weights

    def _gradient_based_update(self, target_metric: str) -> Dict[str, float]:
        """基于梯度的权重更新"""
        updated_weights = {}

        for weight_name, config in self.weight_configs.items():
            if weight_name not in self.current_weights:
                continue

            # 获取性能相关性
            correlation = self.performance_tracker.get_weight_performance_correlation(
                weight_name
            )

            # 计算梯度
            gradient = correlation * config.learning_rate

            # 更新权重
            current_weight = self.current_weights[weight_name]
            new_weight = current_weight + gradient

            # 限制范围
            new_weight = max(config.min_weight, min(config.max_weight, new_weight))
            updated_weights[weight_name] = new_weight

        return updated_weights

    def _correlation_based_update(self, target_metric: str) -> Dict[str, float]:
        """基于相关性的权重更新"""
        updated_weights = {}

        for weight_name, config in self.weight_configs.items():
            correlation = self.performance_tracker.get_weight_performance_correlation(
                weight_name
            )

            # 基于相关性调整权重
            if correlation > 0.3:  # 正相关，增加权重
                adjustment = config.learning_rate * correlation
            elif correlation < -0.3:  # 负相关，减少权重
                adjustment = config.learning_rate * correlation
            else:  # 相关性不明显，保持不变
                adjustment = 0.0

            current_weight = self.current_weights[weight_name]
            new_weight = current_weight + adjustment
            new_weight = max(config.min_weight, min(config.max_weight, new_weight))
            updated_weights[weight_name] = new_weight

        return updated_weights

    def _performance_based_update(self, target_metric: str) -> Dict[str, float]:
        """基于性能的权重更新"""
        updated_weights = {}

        # 计算所有权重的性能分数
        performance_scores = {}
        for weight_name in self.weight_configs:
            recent_perf = self.performance_tracker.get_recent_performance(weight_name)

            if target_metric == "hit_rate_2_plus_1":
                score = recent_perf.hit_rate_2_plus_1
            else:
                score = recent_perf.accuracy

            performance_scores[weight_name] = score

        # 归一化性能分数
        total_score = sum(performance_scores.values())
        if total_score > 0:
            for weight_name, config in self.weight_configs.items():
                if config.category == "ensemble":  # 只更新集成权重
                    normalized_score = performance_scores[weight_name] / total_score

                    # 平滑更新
                    current_weight = self.current_weights[weight_name]
                    new_weight = 0.7 * current_weight + 0.3 * normalized_score
                    new_weight = max(
                        config.min_weight, min(config.max_weight, new_weight)
                    )
                    updated_weights[weight_name] = new_weight
                else:
                    updated_weights[weight_name] = self.current_weights[weight_name]

        return updated_weights

    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        report = {
            "total_weights": len(self.weight_configs),
            "categories": {},
            "performance_summary": {},
            "recommendations": [],
        }

        # 按类别统计
        for category in ["ensemble", "model", "feature", "loss"]:
            category_weights = self.get_weights_by_category(category)
            report["categories"][category] = {
                "count": len(category_weights),
                "weights": category_weights,
                "total_weight": sum(category_weights.values()),
            }

        # 性能摘要
        for weight_name in self.weight_configs:
            recent_perf = self.performance_tracker.get_recent_performance(weight_name)
            correlation = self.performance_tracker.get_weight_performance_correlation(
                weight_name
            )

            report["performance_summary"][weight_name] = {
                "current_weight": self.current_weights[weight_name],
                "hit_rate_2_plus_1": recent_perf.hit_rate_2_plus_1,
                "correlation": correlation,
                "stability": recent_perf.stability,
            }

        # 生成建议
        report["recommendations"] = self._generate_recommendations()

        return report

    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []

        # 检查2+1命中率相关权重
        feature_weights = self.get_weights_by_category("feature")
        if feature_weights.get("red_odd_even", 0) < 0.5:
            recommendations.append("建议增加红球奇偶比权重以提升2+1命中率")

        if feature_weights.get("red_size", 0) < 0.5:
            recommendations.append("建议增加红球大小比权重以提升2+1命中率")

        # 检查集成权重平衡
        ensemble_weights = self.get_weights_by_category("ensemble")
        if ensemble_weights:
            max_weight = max(ensemble_weights.values())
            min_weight = min(ensemble_weights.values())
            if max_weight / min_weight > 10:
                recommendations.append("集成权重不平衡，建议重新调整权重分配")

        return recommendations


class WeightManagerAdapter:
    """权重管理器适配器 - 与现有系统集成"""

    def __init__(self):
        self.weight_manager = SmartWeightManager()
        self.is_initialized = True

    def get_ensemble_weights(self) -> Dict[str, float]:
        """获取集成学习权重"""
        return self.weight_manager.get_weights_by_category("ensemble")

    def get_model_weights(self) -> Dict[str, float]:
        """获取模型权重"""
        return self.weight_manager.get_weights_by_category("model")

    def get_feature_weights(self) -> Dict[str, float]:
        """获取特征权重"""
        return self.weight_manager.get_weights_by_category("feature")

    def get_loss_weights(self) -> Dict[str, float]:
        """获取损失权重"""
        return self.weight_manager.get_weights_by_category("loss")

    def update_weight_performance(
        self, weight_name: str, hit_rate_2_plus_1: float, accuracy: float = 0.0
    ):
        """更新权重性能"""
        metrics = PerformanceMetrics(
            accuracy=accuracy,
            hit_rate_2_plus_1=hit_rate_2_plus_1,
            stability=0.8,  # 默认稳定性
            confidence=0.7,  # 默认置信度
        )
        self.weight_manager.update_performance(weight_name, metrics)

    def optimize_for_2_plus_1(self):
        """针对2+1命中率优化权重"""
        self.weight_manager.optimize_weights("adaptive_learning", "hit_rate_2_plus_1")

    def get_status(self) -> Dict[str, Any]:
        """获取权重管理器状态"""
        return {
            "initialized": self.is_initialized,
            "total_weights": len(self.weight_manager.weight_configs),
            "categories": ["ensemble", "model", "feature", "loss"],
            "optimization_available": True,
        }


def create_weight_manager_adapter() -> WeightManagerAdapter:
    """创建权重管理器适配器实例"""
    return WeightManagerAdapter()


if __name__ == "__main__":
    # 测试权重管理器适配器
    adapter = create_weight_manager_adapter()

    print("权重管理器适配器测试")
    print("=" * 50)

    # 显示当前权重
    print("当前权重配置:")
    print(f"  集成权重: {adapter.get_ensemble_weights()}")
    print(f"  模型权重: {adapter.get_model_weights()}")
    print(f"  特征权重: {adapter.get_feature_weights()}")
    print(f"  损失权重: {adapter.get_loss_weights()}")

    # 显示状态
    status = adapter.get_status()
    print(f"\n状态: {status}")

    print("\n权重管理器适配器创建成功！")
